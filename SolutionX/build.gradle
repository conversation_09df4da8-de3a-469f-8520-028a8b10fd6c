plugins {
    id 'com.android.library'
    id 'kotlin-android'
}
apply from: "${project.rootDir}/config.gradle"

android {
    namespace 'com.demon.qfsolution'
    compileSdkVersion project.ext.compileSdkVer
    defaultConfig {

        minSdkVersion project.ext.minSdkVer
        targetSdkVersion project.ext.targetSdkVer
    }
    compileOptions {
        sourceCompatibility 17
        targetCompatibility 17
    }
}

dependencies {
    implementation project.ext.dependencies.get("coreKtx")
    implementation project.ext.dependencies.get("appcompat")
    implementation project.ext.dependencies.get("material")
    implementation project.ext.dependencies.get("lifecycleKtx")
}