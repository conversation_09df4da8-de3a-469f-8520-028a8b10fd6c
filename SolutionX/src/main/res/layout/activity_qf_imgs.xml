<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".activity.QFImgsActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/nav_top_bar"
        android:layout_width="match_parent"
        android:layout_height="?android:attr/actionBarSize"
        android:elevation="2dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/Boxing.ToolbarTheme">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:drawablePadding="8dp"
                android:gravity="center_vertical"
                android:text="@string/qf_pick_photo"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <Button
                android:id="@+id/btn_qf_ok"
                android:layout_width="wrap_content"
                android:layout_height="30dp"
                android:layout_marginEnd="8dp"
                android:background="@drawable/qf_btn_ok"
                android:text="@string/qf_ok"
                android:textColor="@color/qf_white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </LinearLayout>
    </androidx.appcompat.widget.Toolbar>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_imgs"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:listitem="@layout/list_qf_img" />
</LinearLayout>