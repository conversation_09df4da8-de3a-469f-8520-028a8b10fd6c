<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/qf_img"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:background="@color/qf_black"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/qf_pick"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_alignParentEnd="true"
        android:layout_margin="8dp"
        android:scaleType="fitXY"
        android:src="@drawable/qf_unchecked" />
</RelativeLayout>