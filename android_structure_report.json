{"architecture": {"scenes": {"total_scenes": 96, "by_category": {"personal": [{"name": "PersonalScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/PersonalScene.java", "package": "com.smartcar.easylauncher.ui.personal", "size": 96107, "lines": 2477, "type": "Scene"}, {"name": "PersonalSceneConfig", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/PersonalSceneConfig.java", "package": "com.smartcar.easylauncher.ui.personal", "size": 2724, "lines": 121, "type": "Scene"}, {"name": "ExpenseDetailsScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/expense/ExpenseDetailsScene.java", "package": "com.smartcar.easylauncher.ui.personal.expense", "size": 5182, "lines": 145, "type": "Scene"}, {"name": "ExpenseHomeScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/expense/ExpenseHomeScene.java", "package": "com.smartcar.easylauncher.ui.personal.expense", "size": 17450, "lines": 436, "type": "Scene"}, {"name": "ExpenseListScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/expense/ExpenseListScene.java", "package": "com.smartcar.easylauncher.ui.personal.expense", "size": 10236, "lines": 226, "type": "Scene"}, {"name": "AddExpenseScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/expense/AddExpenseScene.java", "package": "com.smartcar.easylauncher.ui.personal.expense", "size": 19083, "lines": 531, "type": "Scene"}, {"name": "ExpenseTabNavigationScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/expense/ExpenseTabNavigationScene.java", "package": "com.smartcar.easylauncher.ui.personal.expense", "size": 2621, "lines": 105, "type": "Scene"}, {"name": "CarTabNavigationScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/car/CarTabNavigationScene.java", "package": "com.smartcar.easylauncher.ui.personal.car", "size": 1825, "lines": 74, "type": "Scene"}, {"name": "AddCarScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/car/AddCarScene.java", "package": "com.smartcar.easylauncher.ui.personal.car", "size": 14744, "lines": 371, "type": "Scene"}, {"name": "_CarViewPagerScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/car/_CarViewPagerScene.java", "package": "com.smartcar.easylauncher.ui.personal.car", "size": 2862, "lines": 68, "type": "Scene"}, {"name": "CarListScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/car/CarListScene.java", "package": "com.smartcar.easylauncher.ui.personal.car", "size": 9347, "lines": 234, "type": "Scene"}, {"name": "MultiStackTabChildScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/test/MultiStackTabChildScene.java", "package": "com.smartcar.easylauncher.ui.personal.test", "size": 3616, "lines": 99, "type": "Scene"}, {"name": "CostViewPagerScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/test/CostViewPagerScene.java", "package": "com.smartcar.easylauncher.ui.personal.test", "size": 4656, "lines": 113, "type": "Scene"}, {"name": "NoticeDetailsScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/notice/NoticeDetailsScene.java", "package": "com.smartcar.easylauncher.ui.personal.notice", "size": 3844, "lines": 116, "type": "Scene"}, {"name": "NotifyScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/notice/NotifyScene.java", "package": "com.smartcar.easylauncher.ui.personal.notice", "size": 4500, "lines": 108, "type": "Scene"}, {"name": "BulletinScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/notice/BulletinScene.java", "package": "com.smartcar.easylauncher.ui.personal.notice", "size": 4580, "lines": 111, "type": "Scene"}, {"name": "NoticeTabNavigationScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/notice/NoticeTabNavigationScene.java", "package": "com.smartcar.easylauncher.ui.personal.notice", "size": 4574, "lines": 148, "type": "Scene"}, {"name": "TripDetailsScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/trip/TripDetailsScene.java", "package": "com.smartcar.easylauncher.ui.personal.trip", "size": 5092, "lines": 132, "type": "Scene"}, {"name": "TodayTripScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/trip/TodayTripScene.java", "package": "com.smartcar.easylauncher.ui.personal.trip", "size": 4647, "lines": 117, "type": "Scene"}, {"name": "TripTabNavigationScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/trip/TripTabNavigationScene.java", "package": "com.smartcar.easylauncher.ui.personal.trip", "size": 4565, "lines": 148, "type": "Scene"}, {"name": "TotalTripScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/trip/TotalTripScene.java", "package": "com.smartcar.easylauncher.ui.personal.trip", "size": 4456, "lines": 112, "type": "Scene"}], "app": [{"name": "APPCardScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/app/APPCardScene.java", "package": "com.smartcar.easylauncher.ui.app", "size": 16808, "lines": 558, "type": "Scene"}], "more": [{"name": "MoreScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/more/MoreScene.java", "package": "com.smartcar.easylauncher.ui.more", "size": 26922, "lines": 893, "type": "Scene"}], "time": [{"name": "TimeCardScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/time/TimeCardScene.java", "package": "com.smartcar.easylauncher.ui.time", "size": 13468, "lines": 385, "type": "Scene"}], "theme": [{"name": "FindThemeScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/theme/FindThemeScene.java", "package": "com.smartcar.easylauncher.ui.theme", "size": 12246, "lines": 384, "type": "Scene"}, {"name": "LocalThemeDetailScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/theme/LocalThemeDetailScene.java", "package": "com.smartcar.easylauncher.ui.theme", "size": 41195, "lines": 1301, "type": "Scene"}, {"name": "MyThemeScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/theme/MyThemeScene.java", "package": "com.smartcar.easylauncher.ui.theme", "size": 8183, "lines": 283, "type": "Scene"}, {"name": "ThemeDetailScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/theme/ThemeDetailScene.java", "package": "com.smartcar.easylauncher.ui.theme", "size": 28995, "lines": 936, "type": "Scene"}, {"name": "ThemeHelpScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/theme/ThemeHelpScene.java", "package": "com.smartcar.easylauncher.ui.theme", "size": 7519, "lines": 278, "type": "Scene"}, {"name": "ThemeTabNavigationScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/theme/ThemeTabNavigationScene.java", "package": "com.smartcar.easylauncher.ui.theme", "size": 2659, "lines": 104, "type": "Scene"}, {"name": "LocalThemeScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/theme/LocalThemeScene.java", "package": "com.smartcar.easylauncher.ui.theme", "size": 20022, "lines": 633, "type": "Scene"}], "task": [{"name": "FindTaskDetailsScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/task/FindTaskDetailsScene.java", "package": "com.smartcar.easylauncher.ui.task", "size": 11159, "lines": 309, "type": "Scene"}, {"name": "MyTasksScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/task/MyTasksScene.java", "package": "com.smartcar.easylauncher.ui.task", "size": 9467, "lines": 315, "type": "Scene"}, {"name": "TaskHelpScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/task/TaskHelpScene.java", "package": "com.smartcar.easylauncher.ui.task", "size": 8515, "lines": 203, "type": "Scene"}, {"name": "NewTaskScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/task/NewTaskScene.java", "package": "com.smartcar.easylauncher.ui.task", "size": 43503, "lines": 956, "type": "Scene"}, {"name": "FindTasksScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/task/FindTasksScene.java", "package": "com.smartcar.easylauncher.ui.task", "size": 12662, "lines": 404, "type": "Scene"}, {"name": "TaskTabNavigationScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/task/TaskTabNavigationScene.java", "package": "com.smartcar.easylauncher.ui.task", "size": 3342, "lines": 120, "type": "Scene"}], "relax": [{"name": "MapWoodenFishScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/relax/MapWoodenFishScene.java", "package": "com.smartcar.easylauncher.ui.relax", "size": 19213, "lines": 564, "type": "Scene"}], "weather": [{"name": "WeatherMusicCardScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/weather/WeatherMusicCardScene.java", "package": "com.smartcar.easylauncher.ui.weather", "size": 21898, "lines": 558, "type": "Scene"}, {"name": "WeatherCardMapScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/weather/WeatherCardMapScene.java", "package": "com.smartcar.easylauncher.ui.weather", "size": 12634, "lines": 389, "type": "Scene"}, {"name": "WeatherDetailsScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/weather/WeatherDetailsScene.java", "package": "com.smartcar.easylauncher.ui.weather", "size": 8906, "lines": 243, "type": "Scene"}], "compass": [{"name": "CompassCardScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/compass/CompassCardScene.java", "package": "com.smartcar.easylauncher.ui.compass", "size": 6972, "lines": 197, "type": "Scene"}, {"name": "CompassDetailsScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/compass/CompassDetailsScene.java", "package": "com.smartcar.easylauncher.ui.compass", "size": 13926, "lines": 391, "type": "Scene"}], "nav": [{"name": "NavMapCardScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/nav/NavMapCardScene.java", "package": "com.smartcar.easylauncher.ui.nav", "size": 24797, "lines": 604, "type": "Scene"}, {"name": "NavCardScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/nav/NavCardScene.java", "package": "com.smartcar.easylauncher.ui.nav", "size": 9008, "lines": 241, "type": "Scene"}, {"name": "NavMapDetailsScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/nav/NavMapDetailsScene.java", "package": "com.smartcar.easylauncher.ui.nav", "size": 18882, "lines": 552, "type": "Scene"}], "setting": [{"name": "SystemSettingsScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/setting/SystemSettingsScene.java", "package": "com.smartcar.easylauncher.ui.setting", "size": 20834, "lines": 509, "type": "Scene"}, {"name": "PersonalizedSettingScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/setting/PersonalizedSettingScene.java", "package": "com.smartcar.easylauncher.ui.setting", "size": 15887, "lines": 444, "type": "Scene"}, {"name": "SettingTabNavigationScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/setting/SettingTabNavigationScene.java", "package": "com.smartcar.easylauncher.ui.setting", "size": 3982, "lines": 133, "type": "Scene"}, {"name": "DesktopSettingScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/setting/DesktopSettingScene.java", "package": "com.smartcar.easylauncher.ui.setting", "size": 23699, "lines": 549, "type": "Scene"}, {"name": "BaseSettingScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/setting/BaseSettingScene.java", "package": "com.smartcar.easylauncher.ui.setting", "size": 11176, "lines": 319, "type": "Scene"}, {"name": "AboutUsScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/setting/AboutUsScene.java", "package": "com.smartcar.easylauncher.ui.setting", "size": 12606, "lines": 359, "type": "Scene"}, {"name": "SettingViewPagerScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/setting/SettingViewPagerScene.java", "package": "com.smartcar.easylauncher.ui.setting", "size": 3879, "lines": 92, "type": "Scene"}, {"name": "LanguageSelectionScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/setting/LanguageSelectionScene.java", "package": "com.smartcar.easylauncher.ui.setting", "size": 6865, "lines": 226, "type": "Scene"}], "smartbar": [{"name": "SmartBarScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/smartbar/SmartBarScene.java", "package": "com.smartcar.easylauncher.ui.smartbar", "size": 79104, "lines": 1595, "type": "Scene"}], "tpms": [{"name": "TpmsMapScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/tpms/TpmsMapScene.java", "package": "com.smartcar.easylauncher.ui.tpms", "size": 7705, "lines": 198, "type": "Scene"}, {"name": "TpmsCardScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/tpms/TpmsCardScene.java", "package": "com.smartcar.easylauncher.ui.tpms", "size": 7425, "lines": 189, "type": "Scene"}, {"name": "TpmsHelpScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/tpms/setting/TpmsHelpScene.java", "package": "com.smartcar.easylauncher.ui.tpms.setting", "size": 2364, "lines": 79, "type": "Scene"}, {"name": "TpmsBatteryScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/tpms/setting/TpmsBatteryScene.java", "package": "com.smartcar.easylauncher.ui.tpms.setting", "size": 3691, "lines": 93, "type": "Scene"}, {"name": "TpmsLearnModeScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/tpms/setting/TpmsLearnModeScene.java", "package": "com.smartcar.easylauncher.ui.tpms.setting", "size": 15472, "lines": 457, "type": "Scene"}, {"name": "TpmsTabNavigationScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/tpms/setting/TpmsTabNavigationScene.java", "package": "com.smartcar.easylauncher.ui.tpms.setting", "size": 4135, "lines": 136, "type": "Scene"}, {"name": "TpmsExchangeScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/tpms/setting/TpmsExchangeScene.java", "package": "com.smartcar.easylauncher.ui.tpms.setting", "size": 13863, "lines": 392, "type": "Scene"}, {"name": "TpmsSettingScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/tpms/setting/TpmsSettingScene.java", "package": "com.smartcar.easylauncher.ui.tpms.setting", "size": 13925, "lines": 341, "type": "Scene"}], "onboarding": [{"name": "OnboardingDesktopModeScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/onboarding/OnboardingDesktopModeScene.java", "package": "com.smartcar.easylauncher.ui.onboarding", "size": 6045, "lines": 187, "type": "Scene"}, {"name": "BaseOnboardingScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/onboarding/BaseOnboardingScene.java", "package": "com.smartcar.easylauncher.ui.onboarding", "size": 8940, "lines": 253, "type": "Scene"}, {"name": "OnboardingLanguageScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/onboarding/OnboardingLanguageScene.java", "package": "com.smartcar.easylauncher.ui.onboarding", "size": 8522, "lines": 265, "type": "Scene"}, {"name": "OnboardingPrivacyScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/onboarding/OnboardingPrivacyScene.java", "package": "com.smartcar.easylauncher.ui.onboarding", "size": 8655, "lines": 247, "type": "Scene"}, {"name": "OnboardingPermissionScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/onboarding/OnboardingPermissionScene.java", "package": "com.smartcar.easylauncher.ui.onboarding", "size": 8772, "lines": 223, "type": "Scene"}], "other": [{"name": "KeyHomeScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/other/keysetting/KeyHomeScene.java", "package": "com.smartcar.easylauncher.ui.other.keysetting", "size": 5013, "lines": 171, "type": "Scene"}, {"name": "KeySettingTabNavigationScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/other/keysetting/KeySettingTabNavigationScene.java", "package": "com.smartcar.easylauncher.ui.other.keysetting", "size": 2206, "lines": 79, "type": "Scene"}, {"name": "KeyMappingEditScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/other/keysetting/KeyMappingEditScene.java", "package": "com.smartcar.easylauncher.ui.other.keysetting", "size": 50021, "lines": 1325, "type": "Scene"}, {"name": "BroadcastPresetScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/other/keysetting/BroadcastPresetScene.java", "package": "com.smartcar.easylauncher.ui.other.keysetting", "size": 12782, "lines": 390, "type": "Scene"}, {"name": "FindKeyMappingDetailsScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/other/keysetting/FindKeyMappingDetailsScene.java", "package": "com.smartcar.easylauncher.ui.other.keysetting", "size": 8073, "lines": 250, "type": "Scene"}, {"name": "FindKeyMappingScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/other/keysetting/FindKeyMappingScene.java", "package": "com.smartcar.easylauncher.ui.other.keysetting", "size": 12715, "lines": 355, "type": "Scene"}], "home": [{"name": "StatusBarSettingScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/home/<USER>/StatusBarSettingScene.java", "package": "com.smartcar.easylauncher.ui.home.setting", "size": 7326, "lines": 190, "type": "Scene"}, {"name": "SmartBarSettingScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/home/<USER>/SmartBarSettingScene.java", "package": "com.smartcar.easylauncher.ui.home.setting", "size": 4248, "lines": 100, "type": "Scene"}, {"name": "MapLayoutSettingScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/home/<USER>/MapLayoutSettingScene.java", "package": "com.smartcar.easylauncher.ui.home.setting", "size": 5292, "lines": 152, "type": "Scene"}, {"name": "CardLayoutSettingScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/home/<USER>/CardLayoutSettingScene.java", "package": "com.smartcar.easylauncher.ui.home.setting", "size": 3625, "lines": 106, "type": "Scene"}, {"name": "HomeSettingTabNavigationScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/home/<USER>/HomeSettingTabNavigationScene.java", "package": "com.smartcar.easylauncher.ui.home.setting", "size": 5842, "lines": 185, "type": "Scene"}, {"name": "HomeMapScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/home/<USER>/HomeMapScene.java", "package": "com.smartcar.easylauncher.ui.home.scene", "size": 24113, "lines": 848, "type": "Scene"}, {"name": "HomeHybridScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/home/<USER>/HomeHybridScene.java", "package": "com.smartcar.easylauncher.ui.home.scene", "size": 21038, "lines": 695, "type": "Scene"}, {"name": "HomeCardScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/home/<USER>/HomeCardScene.java", "package": "com.smartcar.easylauncher.ui.home.scene", "size": 33469, "lines": 1154, "type": "Scene"}, {"name": "ScenePoolManager", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/home/<USER>/manager/ScenePoolManager.java", "package": "com.smartcar.easylauncher.ui.home.scene.manager", "size": 8067, "lines": 261, "type": "Scene"}], "music": [{"name": "MusicSettingScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/music/setting/MusicSettingScene.java", "package": "com.smartcar.easylauncher.ui.music.setting", "size": 15905, "lines": 403, "type": "Scene"}, {"name": "MusicMapScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/music/scene/MusicMapScene.java", "package": "com.smartcar.easylauncher.ui.music.scene", "size": 17966, "lines": 491, "type": "Scene"}, {"name": "MusicCarScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/music/scene/MusicCarScene.java", "package": "com.smartcar.easylauncher.ui.music.scene", "size": 21511, "lines": 584, "type": "Scene"}, {"name": "MusicCardScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/music/scene/MusicCardScene.java", "package": "com.smartcar.easylauncher.ui.music.scene", "size": 16374, "lines": 454, "type": "Scene"}]}, "average_size": 364.1979166666667, "large_scenes": [{"name": "PersonalScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/PersonalScene.java", "package": "com.smartcar.easylauncher.ui.personal", "size": 96107, "lines": 2477, "type": "Scene"}, {"name": "APPCardScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/app/APPCardScene.java", "package": "com.smartcar.easylauncher.ui.app", "size": 16808, "lines": 558, "type": "Scene"}, {"name": "MoreScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/more/MoreScene.java", "package": "com.smartcar.easylauncher.ui.more", "size": 26922, "lines": 893, "type": "Scene"}, {"name": "LocalThemeDetailScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/theme/LocalThemeDetailScene.java", "package": "com.smartcar.easylauncher.ui.theme", "size": 41195, "lines": 1301, "type": "Scene"}, {"name": "ThemeDetailScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/theme/ThemeDetailScene.java", "package": "com.smartcar.easylauncher.ui.theme", "size": 28995, "lines": 936, "type": "Scene"}, {"name": "LocalThemeScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/theme/LocalThemeScene.java", "package": "com.smartcar.easylauncher.ui.theme", "size": 20022, "lines": 633, "type": "Scene"}, {"name": "NewTaskScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/task/NewTaskScene.java", "package": "com.smartcar.easylauncher.ui.task", "size": 43503, "lines": 956, "type": "Scene"}, {"name": "MapWoodenFishScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/relax/MapWoodenFishScene.java", "package": "com.smartcar.easylauncher.ui.relax", "size": 19213, "lines": 564, "type": "Scene"}, {"name": "WeatherMusicCardScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/weather/WeatherMusicCardScene.java", "package": "com.smartcar.easylauncher.ui.weather", "size": 21898, "lines": 558, "type": "Scene"}, {"name": "NavMapCardScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/nav/NavMapCardScene.java", "package": "com.smartcar.easylauncher.ui.nav", "size": 24797, "lines": 604, "type": "Scene"}, {"name": "NavMapDetailsScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/nav/NavMapDetailsScene.java", "package": "com.smartcar.easylauncher.ui.nav", "size": 18882, "lines": 552, "type": "Scene"}, {"name": "SystemSettingsScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/setting/SystemSettingsScene.java", "package": "com.smartcar.easylauncher.ui.setting", "size": 20834, "lines": 509, "type": "Scene"}, {"name": "DesktopSettingScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/setting/DesktopSettingScene.java", "package": "com.smartcar.easylauncher.ui.setting", "size": 23699, "lines": 549, "type": "Scene"}, {"name": "SmartBarScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/smartbar/SmartBarScene.java", "package": "com.smartcar.easylauncher.ui.smartbar", "size": 79104, "lines": 1595, "type": "Scene"}, {"name": "KeyMappingEditScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/other/keysetting/KeyMappingEditScene.java", "package": "com.smartcar.easylauncher.ui.other.keysetting", "size": 50021, "lines": 1325, "type": "Scene"}, {"name": "HomeMapScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/home/<USER>/HomeMapScene.java", "package": "com.smartcar.easylauncher.ui.home.scene", "size": 24113, "lines": 848, "type": "Scene"}, {"name": "HomeHybridScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/home/<USER>/HomeHybridScene.java", "package": "com.smartcar.easylauncher.ui.home.scene", "size": 21038, "lines": 695, "type": "Scene"}, {"name": "HomeCardScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/home/<USER>/HomeCardScene.java", "package": "com.smartcar.easylauncher.ui.home.scene", "size": 33469, "lines": 1154, "type": "Scene"}, {"name": "MusicCarScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/music/scene/MusicCarScene.java", "package": "com.smartcar.easylauncher.ui.music.scene", "size": 21511, "lines": 584, "type": "Scene"}, {"name": "AddExpenseScene", "path": "app-headunit-open/src/main/java/com/smartcar/easylauncher/ui/personal/expense/AddExpenseScene.java", "package": "com.smartcar.easylauncher.ui.personal.expense", "size": 19083, "lines": 531, "type": "Scene"}]}, "activities": 10, "fragments": 19, "adapters": 32, "managers": 45, "utils": 47}, "packages": {"com.smartcar.easylauncher": {"file_count": 1, "total_lines": 515, "types": {"Other": 1}}, "com.smartcar.easylauncher.skin": {"file_count": 9, "total_lines": 2679, "types": {"Other": 7, "Util": 1, "Manager": 1}}, "com.smartcar.easylauncher.widget": {"file_count": 1, "total_lines": 300, "types": {"Other": 1}}, "com.smartcar.easylauncher.touch": {"file_count": 2, "total_lines": 169, "types": {"Other": 2}}, "com.smartcar.easylauncher.driver": {"file_count": 10, "total_lines": 2314, "types": {"Manager": 1, "Other": 9}}, "com.smartcar.easylauncher.entity": {"file_count": 2, "total_lines": 106, "types": {"Other": 2}}, "com.smartcar.easylauncher.constants": {"file_count": 7, "total_lines": 1415, "types": {"Other": 7}}, "com.smartcar.easylauncher.utils": {"file_count": 23, "total_lines": 5715, "types": {"Other": 13, "Util": 9, "Scene": 1}}, "com.smartcar.easylauncher.adapter": {"file_count": 32, "total_lines": 4110, "types": {"Adapter": 27, "Scene": 3, "Fragment": 2}}, "com.smartcar.easylauncher.dialog": {"file_count": 22, "total_lines": 3812, "types": {"Other": 21, "Adapter": 1}}, "com.smartcar.easylauncher.manager": {"file_count": 19, "total_lines": 7584, "types": {"Manager": 16, "Activity": 1, "Other": 2}}, "com.smartcar.easylauncher.receiver": {"file_count": 9, "total_lines": 1164, "types": {"Other": 9}}, "com.smartcar.easylauncher.model": {"file_count": 75, "total_lines": 10522, "types": {"Other": 75}}, "com.smartcar.easylauncher.amap": {"file_count": 4, "total_lines": 1930, "types": {"Other": 4}}, "com.smartcar.easylauncher.view": {"file_count": 22, "total_lines": 5014, "types": {"Other": 22}}, "com.smartcar.easylauncher.service": {"file_count": 3, "total_lines": 310, "types": {"Other": 3}}, "com.smartcar.easylauncher.data": {"file_count": 2, "total_lines": 764, "types": {"Other": 2}}, "com.smartcar.easylauncher.event": {"file_count": 10, "total_lines": 320, "types": {"Other": 10}}, "com.smartcar.easylauncher.base": {"file_count": 9, "total_lines": 1594, "types": {"Fragment": 3, "Adapter": 1, "Other": 1, "Scene": 3, "Activity": 1}}, "com.smartcar.easylauncher.interfaces": {"file_count": 7, "total_lines": 254, "types": {"Other": 7}}, "com.smartcar.easylauncher.tpms.core": {"file_count": 5, "total_lines": 993, "types": {"Other": 4, "Manager": 1}}, "com.smartcar.easylauncher.tpms.test": {"file_count": 2, "total_lines": 203, "types": {"Other": 2}}, "com.smartcar.easylauncher.tpms.utils": {"file_count": 1, "total_lines": 264, "types": {"Other": 1}}, "com.smartcar.easylauncher.tpms.protocol": {"file_count": 2, "total_lines": 132, "types": {"Other": 2}}, "com.smartcar.easylauncher.tpms.device": {"file_count": 2, "total_lines": 383, "types": {"Other": 1, "Manager": 1}}, "com.smartcar.easylauncher.tpms.device.usb": {"file_count": 2, "total_lines": 798, "types": {"Other": 2}}, "com.smartcar.easylauncher.tpms.protocol.v3": {"file_count": 4, "total_lines": 1021, "types": {"Other": 4}}, "com.smartcar.easylauncher.tpms.protocol.base": {"file_count": 2, "total_lines": 201, "types": {"Other": 2}}, "com.smartcar.easylauncher.tpms.protocol.base.model": {"file_count": 2, "total_lines": 234, "types": {"Other": 2}}, "com.smartcar.easylauncher.event.scope.music": {"file_count": 1, "total_lines": 31, "types": {"Other": 1}}, "com.smartcar.easylauncher.event.scope.location": {"file_count": 4, "total_lines": 87, "types": {"Other": 4}}, "com.smartcar.easylauncher.event.scope.layout": {"file_count": 3, "total_lines": 75, "types": {"Other": 3}}, "com.smartcar.easylauncher.event.scope.notice": {"file_count": 1, "total_lines": 32, "types": {"Other": 1}}, "com.smartcar.easylauncher.event.scope.device": {"file_count": 3, "total_lines": 106, "types": {"Other": 3}}, "com.smartcar.easylauncher.service.weather": {"file_count": 6, "total_lines": 1253, "types": {"Other": 5, "Manager": 1}}, "com.smartcar.easylauncher.service.weather.provider": {"file_count": 3, "total_lines": 723, "types": {"Other": 3}}, "com.smartcar.easylauncher.db.database": {"file_count": 1, "total_lines": 264, "types": {"Other": 1}}, "com.smartcar.easylauncher.db.dao": {"file_count": 2, "total_lines": 248, "types": {"Other": 2}}, "com.smartcar.easylauncher.db.model": {"file_count": 6, "total_lines": 1312, "types": {"Other": 6}}, "com.smartcar.easylauncher.db.dbmager": {"file_count": 2, "total_lines": 284, "types": {"Manager": 2}}, "com.smartcar.easylauncher.view.behavior": {"file_count": 2, "total_lines": 320, "types": {"Other": 2}}, "com.smartcar.easylauncher.view.selector": {"file_count": 3, "total_lines": 639, "types": {"Other": 3}}, "com.smartcar.easylauncher.view.platenum": {"file_count": 9, "total_lines": 2627, "types": {"Other": 7, "Util": 2}}, "com.smartcar.easylauncher.view.popup": {"file_count": 3, "total_lines": 789, "types": {"Other": 3}}, "com.smartcar.easylauncher.view.psd": {"file_count": 4, "total_lines": 785, "types": {"Other": 4}}, "com.smartcar.easylauncher.view.segmentcontrlo": {"file_count": 4, "total_lines": 719, "types": {"Other": 4}}, "com.smartcar.easylauncher.view.pagergridlayoutmanager": {"file_count": 4, "total_lines": 2808, "types": {"Other": 3, "Manager": 1}}, "com.smartcar.easylauncher.view.progressbar": {"file_count": 6, "total_lines": 2446, "types": {"Other": 6}}, "com.smartcar.easylauncher.view.lrc": {"file_count": 3, "total_lines": 1189, "types": {"Other": 2, "Util": 1}}, "com.smartcar.easylauncher.view.manager": {"file_count": 6, "total_lines": 1057, "types": {"Manager": 6}}, "com.smartcar.easylauncher.view.easylayout": {"file_count": 3, "total_lines": 1986, "types": {"Other": 3}}, "com.smartcar.easylauncher.view.tire": {"file_count": 3, "total_lines": 2099, "types": {"Other": 3}}, "com.smartcar.easylauncher.view.clock": {"file_count": 3, "total_lines": 1493, "types": {"Other": 3}}, "com.smartcar.easylauncher.view.signal.data": {"file_count": 3, "total_lines": 1438, "types": {"Other": 3}}, "com.smartcar.easylauncher.gesture.ui": {"file_count": 9, "total_lines": 3125, "types": {"Other": 9}}, "com.smartcar.easylauncher.gesture.fragments": {"file_count": 6, "total_lines": 952, "types": {"Fragment": 6}}, "com.smartcar.easylauncher.gesture.util": {"file_count": 15, "total_lines": 998, "types": {"Other": 14, "Util": 1}}, "com.smartcar.easylauncher.gesture.shell": {"file_count": 3, "total_lines": 266, "types": {"Other": 3}}, "com.smartcar.easylauncher.gesture.other": {"file_count": 15, "total_lines": 1823, "types": {"Other": 13, "Activity": 2}}, "com.smartcar.easylauncher.gesture.remote": {"file_count": 1, "total_lines": 164, "types": {"Other": 1}}, "com.smartcar.easylauncher.dialog.flexible": {"file_count": 5, "total_lines": 515, "types": {"Other": 5}}, "com.smartcar.easylauncher.adapter.provider": {"file_count": 2, "total_lines": 98, "types": {"Other": 2}}, "com.smartcar.easylauncher.adapter.channel": {"file_count": 1, "total_lines": 330, "types": {"Adapter": 1}}, "com.smartcar.easylauncher.adapter.channel.touchhelper": {"file_count": 5, "total_lines": 312, "types": {"Other": 5}}, "com.smartcar.easylauncher.utils.music": {"file_count": 7, "total_lines": 1813, "types": {"Other": 4, "Util": 3}}, "com.smartcar.easylauncher.utils.ui": {"file_count": 5, "total_lines": 833, "types": {"Util": 3, "Other": 2}}, "com.smartcar.easylauncher.utils.cache": {"file_count": 2, "total_lines": 380, "types": {"Util": 2}}, "com.smartcar.easylauncher.utils.util": {"file_count": 1, "total_lines": 148, "types": {"Other": 1}}, "com.smartcar.easylauncher.utils.file": {"file_count": 2, "total_lines": 557, "types": {"Util": 2}}, "com.smartcar.easylauncher.utils.location": {"file_count": 6, "total_lines": 1249, "types": {"Other": 3, "Util": 3}}, "com.smartcar.easylauncher.utils.time": {"file_count": 5, "total_lines": 1577, "types": {"Other": 3, "Util": 2}}, "com.smartcar.easylauncher.utils.system": {"file_count": 8, "total_lines": 1653, "types": {"Other": 2, "Util": 6}}, "com.smartcar.easylauncher.utils.download": {"file_count": 3, "total_lines": 166, "types": {"Other": 2, "Util": 1}}, "com.smartcar.easylauncher.utils.permission": {"file_count": 4, "total_lines": 323, "types": {"Util": 3, "Activity": 1}}, "com.smartcar.easylauncher.utils.thread": {"file_count": 1, "total_lines": 139, "types": {"Util": 1}}, "com.smartcar.easylauncher.utils.interceptor": {"file_count": 2, "total_lines": 132, "types": {"Other": 2}}, "com.smartcar.easylauncher.utils.encipher": {"file_count": 3, "total_lines": 248, "types": {"Util": 3}}, "com.smartcar.easylauncher.utils.utility": {"file_count": 1, "total_lines": 21, "types": {"Util": 1}}, "com.smartcar.easylauncher.network.test": {"file_count": 1, "total_lines": 1413, "types": {"Other": 1}}, "com.smartcar.easylauncher.network.state": {"file_count": 5, "total_lines": 607, "types": {"Other": 5}}, "com.smartcar.easylauncher.network.manager": {"file_count": 2, "total_lines": 2064, "types": {"Manager": 2}}, "com.smartcar.easylauncher.network.receiver": {"file_count": 1, "total_lines": 351, "types": {"Other": 1}}, "com.smartcar.easylauncher.network.event": {"file_count": 2, "total_lines": 70, "types": {"Other": 2}}, "com.smartcar.easylauncher.touch.listener": {"file_count": 1, "total_lines": 28, "types": {"Other": 1}}, "com.smartcar.easylauncher.touch.widget": {"file_count": 4, "total_lines": 912, "types": {"Other": 4}}, "com.smartcar.easylauncher.touch.util": {"file_count": 5, "total_lines": 437, "types": {"Other": 2, "Util": 2, "Manager": 1}}, "com.smartcar.easylauncher.touch.provider": {"file_count": 2, "total_lines": 99, "types": {"Other": 2}}, "com.smartcar.easylauncher.touch.controller": {"file_count": 3, "total_lines": 652, "types": {"Other": 3}}, "com.smartcar.easylauncher.touch.constant": {"file_count": 1, "total_lines": 95, "types": {"Other": 1}}, "com.smartcar.easylauncher.touch.receiver": {"file_count": 1, "total_lines": 77, "types": {"Other": 1}}, "com.smartcar.easylauncher.touch.model": {"file_count": 4, "total_lines": 141, "types": {"Other": 4}}, "com.smartcar.easylauncher.touch.floating": {"file_count": 9, "total_lines": 1016, "types": {"Other": 7, "Util": 1, "Manager": 1}}, "com.smartcar.easylauncher.touch.service": {"file_count": 2, "total_lines": 391, "types": {"Other": 2}}, "com.smartcar.easylauncher.touch.setting": {"file_count": 1, "total_lines": 167, "types": {"Other": 1}}, "com.smartcar.easylauncher.touch.base": {"file_count": 1, "total_lines": 59, "types": {"Fragment": 1}}, "com.smartcar.easylauncher.touch.floating.action": {"file_count": 6, "total_lines": 263, "types": {"Other": 6}}, "com.smartcar.easylauncher.touch.util.json": {"file_count": 2, "total_lines": 52, "types": {"Other": 2}}, "com.smartcar.easylauncher.touch.ui.activity": {"file_count": 1, "total_lines": 129, "types": {"Activity": 1}}, "com.smartcar.easylauncher.widget.layout": {"file_count": 3, "total_lines": 821, "types": {"Other": 2, "Manager": 1}}, "com.smartcar.easylauncher.widget.navigation": {"file_count": 2, "total_lines": 389, "types": {"Scene": 2}}, "com.smartcar.easylauncher.widget.button": {"file_count": 6, "total_lines": 1723, "types": {"Other": 6}}, "com.smartcar.easylauncher.widget.layout.map": {"file_count": 9, "total_lines": 1210, "types": {"Other": 8, "Manager": 1}}, "com.smartcar.easylauncher.widget.layout.smartbar": {"file_count": 6, "total_lines": 1843, "types": {"Other": 5, "Manager": 1}}, "com.smartcar.easylauncher.skin.iface": {"file_count": 7, "total_lines": 372, "types": {"Other": 6, "Manager": 1}}, "com.smartcar.easylauncher.skin.attr": {"file_count": 15, "total_lines": 704, "types": {"Other": 15}}, "com.smartcar.easylauncher.ui.personal": {"file_count": 2, "total_lines": 2598, "types": {"Scene": 2}}, "com.smartcar.easylauncher.ui.home": {"file_count": 1, "total_lines": 932, "types": {"Activity": 1}}, "com.smartcar.easylauncher.ui.app": {"file_count": 1, "total_lines": 558, "types": {"Scene": 1}}, "com.smartcar.easylauncher.ui.other": {"file_count": 4, "total_lines": 765, "types": {"Activity": 3, "Other": 1}}, "com.smartcar.easylauncher.ui.more": {"file_count": 1, "total_lines": 893, "types": {"Scene": 1}}, "com.smartcar.easylauncher.ui.time": {"file_count": 2, "total_lines": 507, "types": {"Scene": 1, "Fragment": 1}}, "com.smartcar.easylauncher.ui.theme": {"file_count": 7, "total_lines": 3919, "types": {"Scene": 7}}, "com.smartcar.easylauncher.ui.task": {"file_count": 6, "total_lines": 2307, "types": {"Scene": 6}}, "com.smartcar.easylauncher.ui.relax": {"file_count": 1, "total_lines": 564, "types": {"Scene": 1}}, "com.smartcar.easylauncher.ui.weather": {"file_count": 3, "total_lines": 1190, "types": {"Scene": 3}}, "com.smartcar.easylauncher.ui.compass": {"file_count": 2, "total_lines": 588, "types": {"Scene": 2}}, "com.smartcar.easylauncher.ui.nav": {"file_count": 3, "total_lines": 1397, "types": {"Scene": 3}}, "com.smartcar.easylauncher.ui.setting": {"file_count": 8, "total_lines": 2631, "types": {"Scene": 8}}, "com.smartcar.easylauncher.ui.smartbar": {"file_count": 1, "total_lines": 1595, "types": {"Scene": 1}}, "com.smartcar.easylauncher.ui.tpms": {"file_count": 2, "total_lines": 387, "types": {"Scene": 2}}, "com.smartcar.easylauncher.ui.onboarding": {"file_count": 6, "total_lines": 1374, "types": {"Scene": 5, "Manager": 1}}, "com.smartcar.easylauncher.ui.onboarding.adapter": {"file_count": 2, "total_lines": 336, "types": {"Adapter": 2}}, "com.smartcar.easylauncher.ui.onboarding.model": {"file_count": 2, "total_lines": 129, "types": {"Other": 2}}, "com.smartcar.easylauncher.ui.tpms.setting": {"file_count": 6, "total_lines": 1498, "types": {"Scene": 6}}, "com.smartcar.easylauncher.ui.nav.setting": {"file_count": 1, "total_lines": 254, "types": {"Fragment": 1}}, "com.smartcar.easylauncher.ui.compass.setting": {"file_count": 1, "total_lines": 86, "types": {"Fragment": 1}}, "com.smartcar.easylauncher.ui.weather.setting": {"file_count": 1, "total_lines": 107, "types": {"Fragment": 1}}, "com.smartcar.easylauncher.ui.more.setting": {"file_count": 1, "total_lines": 228, "types": {"Fragment": 1}}, "com.smartcar.easylauncher.ui.other.keysetting": {"file_count": 6, "total_lines": 2570, "types": {"Scene": 6}}, "com.smartcar.easylauncher.ui.home.setting": {"file_count": 6, "total_lines": 1048, "types": {"Scene": 5, "Fragment": 1}}, "com.smartcar.easylauncher.ui.home.scene": {"file_count": 4, "total_lines": 4353, "types": {"Scene": 3, "Other": 1}}, "com.smartcar.easylauncher.ui.home.scene.manager": {"file_count": 9, "total_lines": 2500, "types": {"Manager": 6, "Other": 2, "Scene": 1}}, "com.smartcar.easylauncher.ui.music.setting": {"file_count": 2, "total_lines": 824, "types": {"Scene": 1, "Fragment": 1}}, "com.smartcar.easylauncher.ui.music.scene": {"file_count": 3, "total_lines": 1529, "types": {"Scene": 3}}, "com.smartcar.easylauncher.ui.personal.expense": {"file_count": 5, "total_lines": 1443, "types": {"Scene": 5}}, "com.smartcar.easylauncher.ui.personal.car": {"file_count": 4, "total_lines": 747, "types": {"Scene": 4}}, "com.smartcar.easylauncher.ui.personal.test": {"file_count": 2, "total_lines": 212, "types": {"Scene": 2}}, "com.smartcar.easylauncher.ui.personal.notice": {"file_count": 4, "total_lines": 483, "types": {"Scene": 4}}, "com.smartcar.easylauncher.ui.personal.trip": {"file_count": 4, "total_lines": 509, "types": {"Scene": 4}}, "androidx.appcompat.app": {"file_count": 1, "total_lines": 91, "types": {"Other": 1}}}, "resources": {"layouts": {"total": 233, "scene_layouts": 32, "item_layouts": 35, "dialog_layouts": 16}, "strings": {"total_qualifiers": 12, "total_strings": 524}, "drawables": 527, "colors": 0}, "recommendations": [{"type": "Scene优化", "issue": "发现20个大型Scene(>500行)", "suggestion": "考虑将大型Scene拆分为更小的组件或使用Fragment"}, {"type": "包结构优化", "issue": "发现6个文件过多的包", "suggestion": "考虑按功能模块进一步细分包结构"}, {"type": "资源组织", "issue": "布局文件数量较多(233个)", "suggestion": "考虑按模块组织布局文件，使用子目录分类"}]}