apply plugin: 'com.android.library'
apply from: "${project.rootDir}/config.gradle"

android {
    namespace 'com.github.gzuliyujiang.wheelview'
    compileSdkVersion project.ext.compileSdkVer
    defaultConfig {

        minSdkVersion project.ext.minSdkVer
        targetSdkVersion project.ext.targetSdkVer

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility 17
        targetCompatibility 17
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project.ext.dependencies.get("annotation")
}
