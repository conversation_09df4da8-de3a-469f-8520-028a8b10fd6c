<!--
  ~ Copyright (c) 2016-present 贵州纳雍穿青人李裕江<<EMAIL>>
  ~
  ~ The software is licensed under the Mulan PSL v2.
  ~ You can use this software according to the terms and conditions of the Mulan PSL v2.
  ~ You may obtain a copy of Mulan PSL v2 at:
  ~     http://license.coscl.org.cn/MulanPSL2
  ~ THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR
  ~ IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR
  ~ PURPOSE.
  ~ See the Mulan PSL v2 for more details.
  -->

<resources>

    <attr name="WheelStyle" format="reference" />

    <attr name="wheel_itemTextSize" format="dimension|reference" />
    <attr name="wheel_itemTextSizeSelected" format="dimension|reference" />
    <attr name="wheel_itemTextColor" format="color|reference" />
    <attr name="wheel_itemTextColorSelected" format="color|reference" />
    <attr name="wheel_itemTextBoldSelected" format="boolean|reference" />
    <attr name="wheel_itemSpace" format="dimension|reference" />
    <attr name="wheel_itemTextAlign" format="enum">
        <enum name="center" value="0" />
        <enum name="left" value="1" />
        <enum name="right" value="2" />
    </attr>
    <attr name="wheel_visibleItemCount" format="integer|reference" />
    <attr name="wheel_sameWidthEnabled" format="boolean|reference" />
    <attr name="wheel_maxWidthText" format="string|reference" />
    <attr name="wheel_cyclicEnabled" format="boolean|reference" />
    <attr name="wheel_indicatorEnabled" format="boolean|reference" />
    <attr name="wheel_indicatorColor" format="color|reference" />
    <attr name="wheel_indicatorSize" format="dimension|reference" />
    <attr name="wheel_curtainEnabled" format="boolean|reference" />
    <attr name="wheel_curtainColor" format="color|reference" />
    <attr name="wheel_curtainCorner" format="enum">
        <enum name="none" value="0" />
        <enum name="all" value="1" />
        <enum name="top" value="2" />
        <enum name="bottom" value="3" />
        <enum name="left" value="4" />
        <enum name="right" value="5" />
    </attr>
    <attr name="wheel_curtainRadius" format="dimension|reference" />
    <attr name="wheel_atmosphericEnabled" format="boolean|reference" />
    <attr name="wheel_curvedEnabled" format="boolean|reference" />
    <attr name="wheel_curvedMaxAngle" format="integer|reference" />
    <attr name="wheel_curvedIndicatorSpace" format="dimension|reference" />

    <declare-styleable name="WheelView">
        <attr name="wheel_visibleItemCount" />
        <attr name="wheel_itemTextSize" />
        <attr name="wheel_itemTextSizeSelected" />
        <attr name="wheel_itemTextColor" />
        <attr name="wheel_itemTextColorSelected" />
        <attr name="wheel_itemTextBoldSelected" />
        <attr name="wheel_itemSpace" />
        <attr name="wheel_itemTextAlign" />
        <attr name="wheel_sameWidthEnabled" />
        <attr name="wheel_maxWidthText" />
        <attr name="wheel_cyclicEnabled" />
        <attr name="wheel_indicatorEnabled" />
        <attr name="wheel_indicatorColor" />
        <attr name="wheel_indicatorSize" />
        <attr name="wheel_curtainEnabled" />
        <attr name="wheel_curtainColor" />
        <attr name="wheel_curtainCorner" />
        <attr name="wheel_curtainRadius" />
        <attr name="wheel_atmosphericEnabled" />
        <attr name="wheel_curvedEnabled" />
        <attr name="wheel_curvedMaxAngle" />
        <attr name="wheel_curvedIndicatorSpace" />
    </declare-styleable>

</resources>