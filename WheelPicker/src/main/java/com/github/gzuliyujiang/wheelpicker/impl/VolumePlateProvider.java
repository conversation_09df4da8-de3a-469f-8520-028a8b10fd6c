/*
 * Copyright (c) 2016-present 贵州纳雍穿青人李裕江<<EMAIL>>
 *
 * The software is licensed under the Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 *     http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR
 * PURPOSE.
 * See the Mulan PSL v2 for more details.
 */

package com.github.gzuliyujiang.wheelpicker.impl;

import androidx.annotation.NonNull;

import com.github.gzuliyujiang.wheelpicker.contract.LinkageProvider;
import com.github.gzuliyujiang.wheelpicker.entity.ConditionalEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据参见 http://www.360doc.com/content/12/0602/07/3899427_215339300.shtml
 *
 * <AUTHOR>
 * @since 2021/6/9 11:31
 */
public class VolumePlateProvider implements LinkageProvider {
    private static final String[] ABBREVIATIONS = {
            "增加", "减少"};

    @Override
    public boolean firstLevelVisible() {
        return true;
    }

    @Override
    public boolean thirdLevelVisible() {
        return false;
    }

    @NonNull
    @Override
    public List<ConditionalEntity> provideFirstData() {
        List<ConditionalEntity> provinces = new ArrayList<>();
        provinces.add(new ConditionalEntity(0, "增加", true));
        provinces.add(new ConditionalEntity(1, "减少", false));
        provinces.add(new ConditionalEntity(2, "定值", false));
        return provinces;
    }

    @NonNull
    @Override
    public List<String> linkageSecondData(int firstIndex) {
        List<String> letters = new ArrayList<>();
        if (firstIndex == INDEX_NO_FOUND) {
            firstIndex = 0;
        }
        String province = provideFirstData().get(firstIndex).getName();
        switch (province) {
            case "增加":
                for (int i = 1; i <= 15; i++) {
                    letters.add(String.valueOf(i));
                }
                break;
            case "减少":
                for (int i = 1; i <= 15; i++) {
                    letters.add(String.valueOf(i));
                }
                break;
            case "定值":
                for (int i = 0; i <= 100; i++) {
                    letters.add(String.valueOf(i));
                }
                break;

            default:

        }
        return letters;
    }

    @NonNull
    @Override
    public List<?> linkageThirdData(int firstIndex, int secondIndex) {
        return new ArrayList<>();
    }

    @Override
    public int findFirstIndex(Object firstValue) {
        if (firstValue == null || !(firstValue instanceof ConditionalEntity)) {
            return INDEX_NO_FOUND;
        }
        ConditionalEntity firstValueBean = (ConditionalEntity) firstValue;
        List<ConditionalEntity> firstData = provideFirstData();
        for (int i = 0, n = firstData.size(); i < n; i++) {
            ConditionalEntity bean = firstData.get(i);
            if (bean.getName().equals(firstValueBean.getName())) {
                return i;
            }
        }
        return INDEX_NO_FOUND;
    }

    @Override
    public int findSecondIndex(int firstIndex, Object secondValue) {
        if (secondValue == null || !(secondValue instanceof String)) {
            return INDEX_NO_FOUND;
        }
        String secondValueStr = (String) secondValue;
        List<String> letters = linkageSecondData(firstIndex);
        for (int i = 0, n = letters.size(); i < n; i++) {
            String letter = letters.get(i);
            if (letter.equals(secondValueStr)) {
                return i;
            }
        }
        return INDEX_NO_FOUND;
    }
    @Override
    public int findThirdIndex(int firstIndex, int secondIndex, Object thirdValue) {
        return 0;
    }

}
