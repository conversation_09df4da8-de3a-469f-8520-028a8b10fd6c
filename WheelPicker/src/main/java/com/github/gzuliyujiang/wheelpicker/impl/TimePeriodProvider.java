package com.github.gzuliyujiang.wheelpicker.impl;

import androidx.annotation.NonNull;

import com.github.gzuliyujiang.wheelpicker.contract.LinkageProvider;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class TimePeriodProvider implements LinkageProvider {
    private static final List<String> TIMES = new ArrayList<>();

    static {
        for (int i = 0; i < 24; i++) {
            for (int j = 0; j < 60; j += 30) {
                TIMES.add(String.format("%02d:%02d", i, j));
            }
        }
    }

    @Override
    public boolean firstLevelVisible() {
        return true;
    }

    @Override
    public boolean thirdLevelVisible() {
        return false;
    }

    @NonNull
    @Override
    public List<String> provideFirstData() {
        return TIMES;
    }

    @NonNull
    @Override
    public List<String> linkageSecondData(int firstIndex) {
        List<String> secondData = new ArrayList<>(TIMES.subList(firstIndex + 1, TIMES.size()));
        secondData.add("23:59");
        return secondData;
    }

    @NonNull
    @Override
    public List<?> linkageThirdData(int firstIndex, int secondIndex) {
        return new ArrayList<>();
    }

    @Override
    public int findFirstIndex(Object firstValue) {
        return TIMES.indexOf(firstValue.toString());
    }

    @Override
    public int findSecondIndex(int firstIndex, Object secondValue) {
        return TIMES.subList(firstIndex + 1, TIMES.size()).indexOf(secondValue.toString());
    }

    @Override
    public int findThirdIndex(int firstIndex, int secondIndex, Object thirdValue) {
        return 0;
    }
}