/*
 * Copyright (c) 2016-present 贵州纳雍穿青人李裕江<<EMAIL>>
 *
 * The software is licensed under the Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 *     http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR
 * PURPOSE.
 * See the Mulan PSL v2 for more details.
 */

package com.github.gzuliyujiang.wheelpicker.impl;

import androidx.annotation.NonNull;

import com.github.gzuliyujiang.wheelpicker.contract.LinkageProvider;
import com.github.gzuliyujiang.wheelpicker.entity.ConditionalEntity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/6/7 12:22
 */
public class CycleLikeProvider implements LinkageProvider {

    @Override
    public boolean firstLevelVisible() {
        return true;
    }

    @Override
    public boolean thirdLevelVisible() {
        return true;
    }

    @NonNull
    @Override
    public List<ConditionalEntity> provideFirstData() {
        List<ConditionalEntity> provinces = new ArrayList<>();
        provinces.add(new ConditionalEntity(0, "每周", true));
        provinces.add(new ConditionalEntity(1, "每月", false));
        return provinces;
    }

    @NonNull
    @Override
    public List<ConditionalEntity> linkageSecondData(int firstIndex) {
        switch (firstIndex) {
            case 0:
                List<ConditionalEntity> firstData = new ArrayList<>();
                firstData.add(new ConditionalEntity(1, "周一", true));
                firstData.add(new ConditionalEntity(2, "周二", false));
                firstData.add(new ConditionalEntity(3, "周三", false));
                firstData.add(new ConditionalEntity(4, "周四", false));
                firstData.add(new ConditionalEntity(5, "周五", false));
                firstData.add(new ConditionalEntity(6, "周六", false));
                firstData.add(new ConditionalEntity(7, "周天", false));
                return firstData;
            case 1:
                List<ConditionalEntity> data = new ArrayList<>();
                for (int i = 1; i <= 31; i++) {
                    data.add(new ConditionalEntity(i, i + "日", false));
                }
                return data;
            default:
        }
        return new ArrayList<>();
    }

    @NonNull
    @Override
    public List<ConditionalEntity> linkageThirdData(int firstIndex, int secondIndex) {
        List<ConditionalEntity> firstData = new ArrayList<>();
        switch (firstIndex) {
            case 0:
                firstData.add(new ConditionalEntity(1, "周一", true));
                firstData.add(new ConditionalEntity(2, "周二", false));
                firstData.add(new ConditionalEntity(3, "周三", false));
                firstData.add(new ConditionalEntity(4, "周四", false));
                firstData.add(new ConditionalEntity(5, "周五", false));
                firstData.add(new ConditionalEntity(6, "周六", false));
                firstData.add(new ConditionalEntity(7, "周天", false));
                if (secondIndex < firstData.size()) {
                    firstData = firstData.subList(secondIndex, firstData.size());
                }
                break;
            case 1:
                List<ConditionalEntity> data = new ArrayList<>();
                for (int i = secondIndex + 1; i <= 31; i++) {
                    data.add(new ConditionalEntity(i, i + "日", false));
                }
                return data;
            default:
        }
        return firstData;
    }

    @Override
    public int findFirstIndex(Object firstValue) {
        return 0;
    }

    @Override
    public int findSecondIndex(int firstIndex, Object secondValue) {
        return 0;
    }

    @Override
    public int findThirdIndex(int firstIndex, int secondIndex, Object thirdValue) {
        return 0;
    }

}
