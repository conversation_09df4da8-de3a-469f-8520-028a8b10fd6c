# 这些设置用于配置Gradle构建系统
# IDE（如Android Studio）会使用这些设置
# 当您在IDE中打开Gradle项目时，这些配置*将会*生效
# 有关更多的Gradle设置信息，请参考：
# 更多关于在gradle.properties中配置构建环境的信息，请访问：
# http://www.gradle.org/docs/current/userguide/build_environment.html

# 指定守护进程的JVM参数配置
# 默认值取决于您的操作系统
# 这里设置JVM最大堆内存为1536MB
org.gradle.jvmargs=-Xmx1536m

# 启用或禁用Gradle守护进程的并行构建功能
# 这允许多个项目并行构建，提高构建效率
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true  # 取消注释并设置为true可启用并行构建功能

#android.enableBuildCache=false  # 是否启用Android Gradle插件的构建缓存，可以提高构建速度

# 启用AndroidX支持（Android Jetpack的一部分）
android.useAndroidX=true

# 控制资源ID是否在编译时固定，这会影响APK的资源ID生成
android.nonFinalResIds=false

# 启用Jetifier工具（Jetifier可以帮助将依赖项迁移到AndroidX）
android.enableJetifier=true

# 允许测试版本的应用安装
android.injected.testOnly=false

# Kotlin代码风格设置，"official"是推荐的，"obsolete"是过时的风格
kotlin.code.style=official

# 控制R类的生成方式，设置为true可以优化R类的生成
# 使R类不具有传递性
android.nonTransitiveRClass=true

#-----------------------------------版本信息---------------------------------------------------------#
## 旧版本号
#LOCAL_VERSION_CODE=26
#
## 旧版本名称
#LOCAL_VERSION_NAME=2.0.6

# 当前版本号
LOCAL_VERSION_CODE=28

# 当前版本名称
LOCAL_VERSION_NAME=2.0.8

## 测试版本号
#LOCAL_VERSION_CODE= 15
#
## 测试版本名称
#LOCAL_VERSION_NAME=1.4.8

# 是否引用源代码，true表示引用，false表示不引用（用于控制是否从Maven仓库下载依赖）
CITING_THE_SOURCE_CODE=true

# 是否启用新的Gradle配置缓存功能，可以提升构建性能
org.gradle.configuration-cache=true