/**
 * 项目根目录的构建脚本
 * 此文件配置整个项目的构建设置，包括插件版本和全局任务
 */

// 声明项目使用的Gradle插件及其版本
plugins {
    // Android应用插件 - 用于构建Android应用程序
    id 'com.android.application' version '8.2.0' apply false
    
    // Android库插件 - 用于构建Android库模块
    id 'com.android.library' version '8.2.0' apply false
    
    // Kotlin Android插件 - 支持在Android项目中使用Kotlin语言
    id 'org.jetbrains.kotlin.android' version '1.9.0' apply false
    
    // Kotlin符号处理器(KSP)插件 - 用于注解处理，比kapt更高效
    id 'com.google.devtools.ksp' version '1.9.0-1.0.13' apply false
}

/**
 * 定义一个clean任务，用于删除构建目录
 * 这是一个全局任务，可以通过命令行执行: ./gradlew clean
 * 执行此任务将删除rootProject.buildDir目录下的所有构建输出文件
 */
task clean(type: Delete) {
    delete rootProject.buildDir
}
