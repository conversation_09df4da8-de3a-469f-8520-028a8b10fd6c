apply plugin: 'com.android.library'
apply from: "${project.rootDir}/config.gradle"

android {
    namespace 'com.flyco.tablayout'
    compileSdkVersion project.ext.compileSdkVer
    defaultConfig {

        minSdkVersion project.ext.minSdkVer
        targetSdkVersion project.ext.targetSdkVer

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility 17
        targetCompatibility 17
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project.ext.dependencies.get("annotation") // Java 8 流支持
    implementation project.ext.dependencies.get("fragment") // Fragment 组件

}


