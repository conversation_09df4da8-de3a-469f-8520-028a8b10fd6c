<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- indicator -->
    <!-- 设置显示器颜色 -->
    <attr name="tl_indicator_color" format="color"/>
    <!-- 设置显示器高度 -->
    <attr name="tl_indicator_height" format="dimension"/>
    <!-- 设置显示器固定宽度 -->
    <attr name="tl_indicator_width" format="dimension"/>
    <!-- 设置显示器margin,当indicator_width大于0,无效 -->
    <attr name="tl_indicator_margin_left" format="dimension"/>
    <attr name="tl_indicator_margin_top" format="dimension"/>
    <attr name="tl_indicator_margin_right" format="dimension"/>
    <attr name="tl_indicator_margin_bottom" format="dimension"/>
    <!-- 设置显示器圆角弧度-->
    <attr name="tl_indicator_corner_radius" format="dimension"/>
    <!-- 设置显示器上方还是下方,只对圆角矩形有用-->
    <attr name="tl_indicator_gravity" format="enum">
        <enum name="TOP" value="48"/>
        <enum name="BOTTOM" value="80"/>
    </attr>
    <!-- 设置显示器为常规|三角形|背景色块|-->
    <attr name="tl_indicator_style" format="enum">
        <enum name="NORMAL" value="0"/>
        <enum name="TRIANGLE" value="1"/>
        <enum name="BLOCK" value="2"/>
    </attr>
    <!-- 设置显示器长度与title一样长,只有在STYLE_NORMAL并且indicatorWidth小于零有效-->
    <attr name="tl_indicator_width_equal_title" format="boolean"/>
    <!-- 设置显示器支持动画-->
    <attr name="tl_indicator_anim_enable" format="boolean"/>
    <!-- 设置显示器动画时间-->
    <attr name="tl_indicator_anim_duration" format="integer"/>
    <!-- 设置显示器支持动画回弹效果-->
    <attr name="tl_indicator_bounce_enable" format="boolean"/>

    <!-- underline -->
    <!-- 设置下划线颜色 -->
    <attr name="tl_underline_color" format="color"/>
    <!-- 设置下划线高度 -->
    <attr name="tl_underline_height" format="dimension"/>
    <!-- 设置下划线上方还是下方-->
    <attr name="tl_underline_gravity" format="enum">
        <enum name="TOP" value="48"/>
        <enum name="BOTTOM" value="80"/>
    </attr>

    <!-- divider -->
    <!-- 设置分割线颜色 -->
    <attr name="tl_divider_color" format="color"/>
    <!-- 设置分割线宽度 -->
    <attr name="tl_divider_width" format="dimension"/>
    <!-- 设置分割线的paddingTop和paddingBottom -->
    <attr name="tl_divider_padding" format="dimension"/>

    <!-- tab -->
    <!-- 设置tab的paddingLeft和paddingRight -->
    <attr name="tl_tab_padding" format="dimension"/>
    <!-- 设置tab大小等分 -->
    <attr name="tl_tab_space_equal" format="boolean"/>
    <!-- 设置tab固定大小 -->
    <attr name="tl_tab_width" format="dimension"/>

    <!-- title -->
    <!-- 设置字体大小 -->
    <attr name="tl_textsize" format="dimension"/>
    <!-- 设置字体选中颜色 -->
    <attr name="tl_textSelectColor" format="color"/>
    <!-- 设置字体未选中颜色 -->
    <attr name="tl_textUnselectColor" format="color"/>
    <!-- 设置字体加粗 -->
    <attr name="tl_textBold" format="boolean"/>
    <!-- 设置字体全大写 -->
    <attr name="tl_textAllCaps" format="boolean"/>

    <!-- 设置字体大小 -->
    <attr name="tl_subtextsize" format="dimension" />
    <!-- 设置字体选中颜色 -->
    <attr name="tl_subtextSelectColor" format="color" />
    <!-- 设置字体未选中颜色 -->
    <attr name="tl_subtextUnselectColor" format="color" />
    <!-- 设置字体加粗 -->
    <attr name="tl_subtextBold" format="boolean" />
    <!-- 设置字体全大写 -->
    <attr name="tl_subtextAllCaps" format="boolean" />


    <declare-styleable name="SlidingTabLayout">
        <!-- indicator -->
        <attr name="tl_indicator_color"/>
        <attr name="tl_indicator_height"/>
        <attr name="tl_indicator_width"/>
        <attr name="tl_indicator_margin_left"/>
        <attr name="tl_indicator_margin_top"/>
        <attr name="tl_indicator_margin_right"/>
        <attr name="tl_indicator_margin_bottom"/>
        <attr name="tl_indicator_corner_radius"/>
        <attr name="tl_indicator_gravity"/>
        <attr name="tl_indicator_style"/>
        <attr name="tl_indicator_width_equal_title"/>

        <!-- underline -->
        <attr name="tl_underline_color"/>
        <attr name="tl_underline_height"/>
        <attr name="tl_underline_gravity"/>

        <!-- divider -->
        <attr name="tl_divider_color"/>
        <attr name="tl_divider_width"/>
        <attr name="tl_divider_padding"/>

        <!-- tab -->
        <attr name="tl_tab_padding"/>
        <attr name="tl_tab_space_equal"/>
        <attr name="tl_tab_width"/>

        <!-- title -->
        <attr name="tl_textsize"/>
        <attr name="tl_textSelectColor"/>
        <attr name="tl_textUnselectColor"/>
        <attr name="tl_textBold"/>
        <attr name="tl_textAllCaps"/>

    </declare-styleable>

    <declare-styleable name="CommonTabLayout">
        <!-- indicator -->
        <attr name="tl_indicator_color"/>
        <attr name="tl_indicator_height"/>
        <attr name="tl_indicator_width"/>
        <attr name="tl_indicator_margin_left"/>
        <attr name="tl_indicator_margin_top"/>
        <attr name="tl_indicator_margin_right"/>
        <attr name="tl_indicator_margin_bottom"/>
        <attr name="tl_indicator_corner_radius"/>
        <attr name="tl_indicator_gravity"/>
        <attr name="tl_indicator_style"/>
        <attr name="tl_indicator_anim_enable"/>
        <attr name="tl_indicator_anim_duration"/>
        <attr name="tl_indicator_bounce_enable"/>

        <!-- underline -->
        <attr name="tl_underline_color"/>
        <attr name="tl_underline_height"/>
        <attr name="tl_underline_gravity"/>

        <!-- divider -->
        <attr name="tl_divider_color"/>
        <attr name="tl_divider_width"/>
        <attr name="tl_divider_padding"/>

        <!-- tab -->
        <attr name="tl_tab_padding"/>
        <attr name="tl_tab_space_equal"/>
        <attr name="tl_tab_width"/>

        <!-- title -->
        <attr name="tl_textsize"/>
        <attr name="tl_textSelectColor"/>
        <attr name="tl_textUnselectColor"/>
        <attr name="tl_textBold"/>
        <attr name="tl_textAllCaps"/>

        <!-- subtitle -->
        <attr name="tl_subtextsize" />
        <attr name="tl_subtextSelectColor" />
        <attr name="tl_subtextUnselectColor" />
        <attr name="tl_subtextBold" />
        <attr name="tl_subtextAllCaps" />

        <!-- icon -->
        <!-- 设置icon宽度 -->
        <attr name="tl_iconWidth" format="dimension"/>
        <!-- 设置icon高度 -->
        <attr name="tl_iconHeight" format="dimension"/>
        <!-- 设置icon是否可见 -->
        <attr name="tl_iconVisible" format="boolean"/>
        <!-- 设置icon显示位置,对应Gravity中常量值 -->
        <attr name="tl_iconGravity" format="enum">
            <enum name="LEFT" value="3"/>
            <enum name="TOP" value="48"/>
            <enum name="RIGHT" value="5"/>
            <enum name="BOTTOM" value="80"/>
        </attr>
        <!-- 设置icon与文字间距 -->
        <attr name="tl_iconMargin" format="dimension"/>

    </declare-styleable>

    <declare-styleable name="SegmentTabLayout">
        <!-- indicator -->
        <attr name="tl_indicator_color"/>
        <attr name="tl_indicator_height"/>
        <attr name="tl_indicator_margin_left"/>
        <attr name="tl_indicator_margin_top"/>
        <attr name="tl_indicator_margin_right"/>
        <attr name="tl_indicator_margin_bottom"/>
        <attr name="tl_indicator_corner_radius"/>
        <attr name="tl_indicator_anim_enable"/>
        <attr name="tl_indicator_anim_duration"/>
        <attr name="tl_indicator_bounce_enable"/>

        <!-- divider -->
        <attr name="tl_divider_color"/>
        <attr name="tl_divider_width"/>
        <attr name="tl_divider_padding"/>

        <!-- tab -->
        <attr name="tl_tab_padding"/>
        <attr name="tl_tab_space_equal"/>
        <attr name="tl_tab_width"/>

        <!-- title -->
        <attr name="tl_textsize"/>
        <attr name="tl_textSelectColor"/>
        <attr name="tl_textUnselectColor"/>
        <attr name="tl_textBold"/>
        <attr name="tl_textAllCaps"/>

        <attr name="tl_bar_color" format="color"/>
        <attr name="tl_bar_stroke_color" format="color"/>
        <attr name="tl_bar_stroke_width" format="dimension"/>

    </declare-styleable>

    <declare-styleable name="MsgView">
        <!-- 圆角矩形背景色 -->
        <attr name="mv_backgroundColor" format="color"/>
        <!-- 圆角弧度,单位dp-->
        <attr name="mv_cornerRadius" format="dimension"/>
        <!-- 圆角弧度,单位dp-->
        <attr name="mv_strokeWidth" format="dimension"/>
        <!-- 圆角边框颜色-->
        <attr name="mv_strokeColor" format="color"/>
        <!-- 圆角弧度是高度一半-->
        <attr name="mv_isRadiusHalfHeight" format="boolean"/>
        <!-- 圆角矩形宽高相等,取较宽高中大值-->
        <attr name="mv_isWidthHeightEqual" format="boolean"/>
    </declare-styleable>
</resources>