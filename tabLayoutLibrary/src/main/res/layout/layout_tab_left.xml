<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">

    <LinearLayout
        android:id="@+id/ll_tap"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_tab_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/tv_tab_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true" />

        <TextView
            android:id="@+id/tv_tab_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true" />
    </LinearLayout>

    <com.flyco.tablayout.widget.MsgView xmlns:mv="http://schemas.android.com/apk/res-auto"
        android:id="@+id/rtv_msg_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toRightOf="@+id/ll_tap"
        android:gravity="center"
        android:textColor="#ffffff"
        android:textSize="11.5sp"
        android:visibility="gone"
        mv:mv_backgroundColor="#FD481F"
        mv:mv_isRadiusHalfHeight="true"
        mv:mv_strokeColor="#ffffff"
        mv:mv_strokeWidth="1dp" />

</RelativeLayout>