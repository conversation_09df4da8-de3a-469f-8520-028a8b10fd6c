package com.smartcar.easylauncher.infrastructure.interfaces;

import com.smartcar.easylauncher.data.model.system.AppInfo;

/**
 * 应用选择回调接口
 *
 * <AUTHOR>
 * @since YourVersion
 */
public interface AppSelectionCallback {

    /**
     * 当应用被选中时回调此方法
     *
     * @param appInfo 被选中的应用的详细信息
     */
    void onAppSelected(AppInfo appInfo);

    /**
     * 当应用被卸载时回调此方法
     *
     * @param position 被卸载应用的位置（在列表中的索引）
     */
    void onAppUninstalled(int position);
}
