package com.smartcar.easylauncher.infrastructure.skin;

import android.content.Context;
import android.content.ContextWrapper;
import android.view.LayoutInflater;

/**
 * 自定义Context包装类，用于在布局加载时应用皮肤
 * 通过重写getSystemService方法，拦截对LAYOUT_INFLATER_SERVICE的获取请求，以返回自定义的SkinLayoutInflater实例
 * <AUTHOR>
 */
public class LayoutInflaterContext extends ContextWrapper {

    /**
     * 自定义的SkinLayoutInflater实例，用于处理带皮肤的布局加载
     */
    private SkinLayoutInflater layoutInflater;

    /**
     * 构造函数，接收一个基础的Context对象进行包装
     *
     * @param base 要被包装的基础Context对象
     */
    public LayoutInflaterContext(Context base) {
        super(base);
    }

    /**
     * 重写getSystemService方法，用于拦截对LAYOUT_INFLATER_SERVICE的获取请求
     * 当请求的服务为LAYOUT_INFLATER_SERVICE时，返回自定义的SkinLayoutInflater实例
     * 否则，调用父类的getSystemService方法处理其他服务请求
     *
     * @param name 请求的服务名称
     * @return 对应的系统服务对象，对于LAYOUT_INFLATER_SERVICE，返回自定义的SkinLayoutInflater实例
     */
    @Override
    public Object getSystemService(String name) {
        // 判断请求的服务是否为LAYOUT_INFLATER_SERVICE
        if (LAYOUT_INFLATER_SERVICE.equals(name)) {
            // 如果layoutInflater为空，则进行初始化
            if (layoutInflater == null) {
                // 从基础Context获取原始的LayoutInflater实例，并克隆到当前Context中
                LayoutInflater original = LayoutInflater.from(getBaseContext()).cloneInContext(this);
                // 初始化SkinLayoutInflater实例，并传入原始的LayoutInflater实例和基础Context
                layoutInflater = new SkinLayoutInflater(original, getBaseContext());
            }
            // 返回自定义的SkinLayoutInflater实例
            return layoutInflater;
        }
        // 如果请求的服务不是LAYOUT_INFLATER_SERVICE，则调用父类的方法处理
        return super.getSystemService(name);
    }
}
