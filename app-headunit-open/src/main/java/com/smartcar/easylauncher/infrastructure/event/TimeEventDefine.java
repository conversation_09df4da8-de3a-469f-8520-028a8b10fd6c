package com.smartcar.easylauncher.infrastructure.event;


import com.smartcar.easylauncher.data.model.system.TimeModel;

import java.util.List;
import java.util.Map;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 时间通知事件定义
 */
@EventGroup(value = "TimeScope", active = true)
public class TimeEventDefine {
    @Event(value = "TimeeventInt", multiProcess = false)
    Integer eventInt;

    @Event(value = "TimeeventString", multiProcess = true)
    String eventString;

    @Event(value = "TimeeventBean", multiProcess = true)
    TimeModel eventBean;

    @Event(value = "TimeeventBean", multiProcess = true)
    List<String> eventList;

    @Event(value = "TimeeventMap", multiProcess = true)
    Map<String, List<String>> eventMap;
}