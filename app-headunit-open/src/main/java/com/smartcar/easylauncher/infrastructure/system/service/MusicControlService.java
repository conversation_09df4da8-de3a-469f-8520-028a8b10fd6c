package com.smartcar.easylauncher.infrastructure.system.service;

import android.content.Intent;
import android.os.Build;
import android.service.notification.NotificationListenerService;
import android.service.notification.StatusBarNotification;

import androidx.annotation.RequiresApi;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * 音乐推送服务
 * <AUTHOR>
 */
@RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
public class MusicControlService extends NotificationListenerService {

    final String TAG = "MusicControlService";

    @Override
    public void onCreate() {
        MyLog.v(TAG, "音乐推送服务已启动");
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public void onNotificationPosted(StatusBarNotification sbn) {
        MyLog.v(TAG, "收到通知");
        LocalBroadcastManager.getInstance(this).sendBroadcast(new Intent(Const.NOTIFY_POSTED));
    }

    @Override
    public void onNotificationRemoved(StatusBarNotification sbn) {
        MyLog.v(TAG, "移除通知");
        LocalBroadcastManager.getInstance(this).sendBroadcast(new Intent(Const.NOTIFY_REMOVED));
    }
}
