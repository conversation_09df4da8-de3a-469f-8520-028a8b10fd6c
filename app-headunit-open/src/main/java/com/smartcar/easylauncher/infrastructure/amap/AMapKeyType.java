package com.smartcar.easylauncher.infrastructure.amap;

public class AMapKeyType {
    /**
     * 高德发送的广播ACTION
     */
    public static final String ACTION_SEND = "AUTONAVI_STANDARD_BROADCAST_SEND";
    /**
     * 高德接收的广播ACTION
     */
    public static final String ACTION_RECV = "AUTONAVI_STANDARD_BROADCAST_RECV";
    /**
     * 通过Key值来区分不同的协议接⼝
     */
    public static final String KEY_TYPE = "KEY_TYPE";


    //最小化导航
    public static final int MINI_NAV = 10031;
    //查询导航状态
    public static final int NAV_STATUS = 12404;
    //设置昼夜模式
    public static final int DAY_NIGHT_MODE = 10048;
    //大灯状态
    public static final int GET_LAMP = 10017;
    //acc on 通知
    public static final int ACC_ON = 10073;
    //acc off 通知
    public static final int ACC_OFF = 10018;

}
