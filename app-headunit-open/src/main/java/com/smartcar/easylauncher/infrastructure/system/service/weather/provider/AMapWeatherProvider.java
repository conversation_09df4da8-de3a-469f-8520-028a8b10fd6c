package com.smartcar.easylauncher.infrastructure.system.service.weather.provider;


import android.content.Context;

import androidx.annotation.NonNull;
import com.amap.api.services.weather.LocalWeatherForecastResult;
import com.amap.api.services.weather.LocalWeatherLiveResult;
import com.amap.api.services.weather.WeatherSearch;
import com.amap.api.services.weather.WeatherSearchQuery;
import com.elvishew.xlog.XLog;
import com.smartcar.easylauncher.data.processor.WeatherDataProcessor;
import com.smartcar.easylauncher.core.manager.AuthorityManager;
import com.smartcar.easylauncher.data.model.navigation.LocateInforModel;
import com.smartcar.easylauncher.data.model.weather.WeatherDataModel;
import com.smartcar.easylauncher.infrastructure.system.service.weather.ErrorCode;
import com.smartcar.easylauncher.infrastructure.system.service.weather.WeatherCallback;
import com.smartcar.easylauncher.infrastructure.system.service.weather.WeatherError;
import com.smartcar.easylauncher.infrastructure.system.service.weather.WeatherProvider;

/**
 * 高德天气API实现
 * 实现高德地图的天气数据获取
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-03-21
 */
public class AMapWeatherProvider implements WeatherProvider {

    /**
     * 日志标签
     */
    private static final String TAG = AMapWeatherProvider.class.getSimpleName();

    /**
     * 提供者优先级
     */
    private static final int PRIORITY = 3;

    /**
     * API成功返回码
     */
    private static final int AMAP_SUCCESS_CODE = 1000;

    /**
     * API密钥
     */
    private final String apiKey;

    /**
     * 应用上下文
     */
    private final Context context;

    /**
     * 实时天气数据
     */
    private LocalWeatherLiveResult liveResult;

    /**
     * 天气预报数据
     */
    private LocalWeatherForecastResult forecastResult;

    public AMapWeatherProvider(Context context) {
        this.context = context.getApplicationContext();
        this.apiKey = AuthorityManager.getAmapKey();
    }

    @Override
    public int getPriority() {
        return PRIORITY;
    }

    @NonNull
    @Override
    public String getProviderName() {
        return "MapWeather";
    }

    @Override
    public void fetchWeatherData(@NonNull LocateInforModel location, @NonNull WeatherCallback callback) {
        XLog.tag(TAG).i("开始获取高德天气数据 - 城市: %s, 区域: %s", location.getCity(), location.getDistrict());
        
        // 检查API密钥
        if (!isAvailable()) {
            XLog.tag(TAG).e("高德地图API密钥无效");
            callback.onError(new WeatherError(ErrorCode.UNKNOWN_ERROR, "高德地图API密钥无效"));
            return;
        }

        // 检查位置信息
        if (!isValidLocation(location)) {
            XLog.tag(TAG).e("无效的位置信息: district=%s", location.getDistrict());
            callback.onError(new WeatherError(ErrorCode.INVALID_LOCATION, "无效的位置信息"));
            return;
        }

        // 重置数据
        liveResult = null;
        forecastResult = null;

        // 获取实时天气
        fetchLiveWeather(location, callback);
    }

    /**
     * 获取实时天气数据
     */
    private void fetchLiveWeather(LocateInforModel location, WeatherCallback callback) {
        try {
            XLog.tag(TAG).d("开始请求实时天气 - 区域: %s", location.getDistrict());
            
            WeatherSearchQuery query = new WeatherSearchQuery(
                    location.getDistrict(),
                    WeatherSearchQuery.WEATHER_TYPE_LIVE
            );

            WeatherSearch weatherSearch = new WeatherSearch(context);
            weatherSearch.setQuery(query);

            weatherSearch.setOnWeatherSearchListener(new WeatherSearch.OnWeatherSearchListener() {
                @Override
                public void onWeatherLiveSearched(LocalWeatherLiveResult result, int rCode) {
                    if (rCode == AMAP_SUCCESS_CODE && result != null) {
                        XLog.tag(TAG).d("实时天气获取成功: %s", result.getLiveResult().toString());
                        liveResult = result;
                        fetchForecastWeather(location, callback);
                    } else {
                        XLog.tag(TAG).e("实时天气获取失败: code=%d", rCode);
                        callback.onError(new WeatherError(ErrorCode.API_CALL_FAILED, 
                            "高德实时天气获取失败: " + rCode));
                    }
                }

                @Override
                public void onWeatherForecastSearched(LocalWeatherForecastResult result, int rCode) {
                    // 不处理预报回调
                }
            });

            weatherSearch.searchWeatherAsyn();

        } catch (Exception e) {
            XLog.tag(TAG).e("实时天气请求异常: %s", e.getMessage());
            callback.onError(new WeatherError(ErrorCode.API_CALL_FAILED, 
                "高德实时天气请求异常: " + e.getMessage()));
        }
    }

    /**
     * 获取天气预报数据
     */
    private void fetchForecastWeather(LocateInforModel location, WeatherCallback callback) {
        try {
            WeatherSearchQuery query = new WeatherSearchQuery(
                    location.getDistrict(),
                    WeatherSearchQuery.WEATHER_TYPE_FORECAST
            );

            WeatherSearch weatherSearch = new WeatherSearch(context);
            weatherSearch.setQuery(query);

            weatherSearch.setOnWeatherSearchListener(new WeatherSearch.OnWeatherSearchListener() {
                @Override
                public void onWeatherLiveSearched(LocalWeatherLiveResult result, int rCode) {
                    // 不处理实时天气回调
                }

                @Override
                public void onWeatherForecastSearched(LocalWeatherForecastResult result, int rCode) {
                    if (rCode == AMAP_SUCCESS_CODE && result != null) {
                        forecastResult = result;
                        // 合并数据并回调
                        WeatherDataProcessor weatherDataProcessor = new WeatherDataProcessor();
                        WeatherDataModel weatherDataModel = weatherDataProcessor.processAmapWeatherData(location.getCity(),
                                location.getDistrict(),
                                location.getLatitude(),
                                location.getLongitude(),
                                getProviderName(),
                                forecastResult, liveResult);
                        callback.onWeatherDataReceived(weatherDataModel);
                    } else {
                        XLog.tag(TAG).e("天气预报获取失败: %d", rCode);
                        callback.onError(new WeatherError(ErrorCode.API_CALL_FAILED,
                                "高德天气预报获取失败: " + rCode));
                    }
                }
            });

            weatherSearch.searchWeatherAsyn();

        } catch (Exception e) {
            XLog.tag(TAG).e("天气预报请求异常: %s", e.getMessage());
            callback.onError(new WeatherError(ErrorCode.API_CALL_FAILED,
                    "高德天气预报请求异常: " + e.getMessage()));
        }
    }

//    /**
//     * 合并天气数据
//     */
//    private WeatherDataModel mergeWeatherData(LocateInforModel location) {
//        WeatherData weatherData = new WeatherData();
//        weatherData.setLocation(location);
//        weatherData.setProvider(getProviderName());
//        weatherData.setTimestamp(System.currentTimeMillis());
//
//        if (liveResult != null && liveResult.getLiveResult() != null) {
//            weatherData.setCurrentWeather(convertLiveResult(liveResult.getLiveResult()));
//        }
//
//        if (forecastResult != null && forecastResult.getForecastResult() != null) {
//            weatherData.setForecast(convertForecastResult(forecastResult.getForecastResult()));
//        }
//
//        return weatherData;
//    }

    @Override
    public boolean isAvailable() {
        return apiKey != null && !apiKey.isEmpty();
    }

    @Override
    public String getApiKey() {
        return apiKey;
    }

    /**
     * 检查位置信息是否有效
     */
    private boolean isValidLocation(LocateInforModel location) {
        return location != null
                && location.getLatitude() != 0.0
                && location.getLongitude() != 0.0
                && location.getDistrict() != null
                && !location.getDistrict().isEmpty();
    }

    /**
     * 安全解析浮点数
     */
    private float parseFloat(String value) {
        try {
            return Float.parseFloat(value);
        } catch (NumberFormatException e) {
            return 0.0f;
        }
    }
}