package com.smartcar.easylauncher.infrastructure.skin.iface;

/**
 * 皮肤变更观察者接口
 * <p>
 * 定义了当皮肤发生变更时应调用的方法。实现此接口的类将能够接收到皮肤变更的通知，并据此执行相应的操作。
 * <AUTHOR>
 */
public interface OnSkinObserver {

    /**
     * 当皮肤发生变更时调用的方法
     * <p>
     * 当皮肤管理器检测到皮肤发生变更（如加载了新的皮肤、恢复到默认皮肤等）时，会调用此方法通知所有注册的皮肤观察者。
     * 通过参数`isHasSkin`可以得知当前是否已有皮肤被加载。
     *
     * @param isHasSkin 指示当前是否已有皮肤被加载的布尔值。如果为`true`，则表示已有皮肤被加载；如果为`false`，则表示当前没有皮肤被加载或已恢复到默认主题。
     */
    void onSkinChange(boolean isHasSkin);
}
