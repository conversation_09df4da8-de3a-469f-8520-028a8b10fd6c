package com.smartcar.easylauncher.infrastructure.driver;

import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;
import android.hardware.usb.UsbEndpoint;
import android.hardware.usb.UsbInterface;
import android.util.Log;

import com.smartcar.easylauncher.shared.utils.MyLog;

import java.io.IOException;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * ProlificSerialDriver类实现了UsbSerialDriver接口，用于与Prolific USB串口设备进行通信。
 */
public class ProlificSerialDriver implements UsbSerialDriver {
    private final String TAG = ProlificSerialDriver.class.getSimpleName(); // 日志标签
    private final UsbDevice mDevice; // USB设备
    private final UsbSerialPort mPort; // USB串口

    /**
     * 构造函数，初始化ProlificSerialDriver。
     *
     * @param usbDevice USB设备
     */
    public ProlificSerialDriver(UsbDevice usbDevice) {
        this.mDevice = usbDevice;
        this.mPort = new ProlificSerialPort(usbDevice, 0); // 初始化串口
    }

    /**
     * 获取支持的设备列表。
     *
     * @return 支持的设备映射
     */
    public static Map<Integer, int[]> getSupportedDevices() {
        LinkedHashMap<Integer, int[]> linkedHashMap = new LinkedHashMap<>();
        linkedHashMap.put(1659, new int[]{8963}); // 添加支持的设备ID
        return linkedHashMap;
    }

    @Override
    public List<UsbSerialPort> getPorts() {
        return Collections.singletonList(this.mPort); // 返回串口列表
    }

    @Override
    public UsbDevice getDevice() {
        return this.mDevice; // 返回USB设备
    }

    /**
     * ProlificSerialPort类实现了UsbSerialPort接口，用于与Prolific USB串口设备进行通信。
     */
    class ProlificSerialPort extends CommonUsbSerialPort {
        private static final int CONTROL_DTR = 1; // 数据终端就绪控制线
        private static final int CONTROL_RTS = 2; // 请求发送控制线
        private static final int DEVICE_TYPE_0 = 1; // 设备类型0
        private static final int DEVICE_TYPE_1 = 2; // 设备类型1
        private static final int DEVICE_TYPE_HX = 0; // HX设备类型
        private static final int FLUSH_RX_REQUEST = 8; // 清空接收缓冲区请求
        private static final int FLUSH_TX_REQUEST = 9; // 清空发送缓冲区请求
        private static final int INTERRUPT_ENDPOINT = 129; // 中断端点
        private static final int PROLIFIC_CTRL_OUT_REQTYPE = 33; // 控制请求类型
        private static final int PROLIFIC_VENDOR_IN_REQTYPE = 192; // 厂商输入请求类型
        private static final int PROLIFIC_VENDOR_OUT_REQTYPE = 64; // 厂商输出请求类型
        private static final int PROLIFIC_VENDOR_READ_REQUEST = 1; // 厂商读取请求
        private static final int PROLIFIC_VENDOR_WRITE_REQUEST = 1; // 厂商写入请求
        private static final int READ_ENDPOINT = 131; // 读取端点
        private static final int SET_CONTROL_REQUEST = 34; // 设置控制请求
        private static final int SET_LINE_REQUEST = 32; // 设置线路请求
        private static final int STATUS_BUFFER_SIZE = 10; // 状态缓冲区大小
        private static final int STATUS_BYTE_IDX = 8; // 状态字节索引
        private static final int STATUS_FLAG_CD = 1; // 载波检测状态标志
        private static final int STATUS_FLAG_CTS = 128; // 清除发送状态标志
        private static final int STATUS_FLAG_DSR = 2; // 数据集准备状态标志
        private static final int STATUS_FLAG_RI = 8; // 振铃指示状态标志
        private static final int USB_READ_TIMEOUT_MILLIS = 1000; // USB读取超时
        private static final int USB_RECIP_INTERFACE = 1; // 接口接收
        private static final int USB_WRITE_TIMEOUT_MILLIS = 5000; // USB写入超时
        private static final int WRITE_ENDPOINT = 2; // 写入端点

        private final Object mReadStatusThreadLock = new Object(); // 读取状态线程锁
        private boolean mStopReadStatusThread = false; // 停止读取状态线程标志
        private int mBaudRate = -1; // 波特率
        private int mControlLinesValue = 0; // 控制线值
        private int mDataBits = -1; // 数据位
        private int mDeviceType = 0; // 设备类型
        private UsbEndpoint mInterruptEndpoint; // 中断端点
        private int mParity = -1; // 校验位
        private UsbEndpoint mReadEndpoint; // 读取端点
        private IOException mReadStatusException = null; // 读取状态异常
        private volatile Thread mReadStatusThread = null; // 读取状态线程
        private int mStatus = 0; // 状态
        private int mStopBits = -1; // 停止位
        private UsbEndpoint mWriteEndpoint; // 写入端点

        /**
         * 构造函数，初始化ProlificSerialPort。
         *
         * @param usbDevice USB设备
         * @param i         设备索引
         */
        public ProlificSerialPort(UsbDevice usbDevice, int i) {
            super(usbDevice, i); // 调用父类构造函数
        }

        @Override
        public UsbSerialDriver getDriver() {
            return ProlificSerialDriver.this; // 返回ProlificSerialDriver实例
        }

        /**
         * 执行控制传输。
         *
         * @param i   请求类型
         * @param i2  请求
         * @param i3  值
         * @param i4  索引
         * @param i5  数据长度
         * @return 控制传输的字节数组
         * @throws IOException 输入输出异常
         */
        private byte[] inControlTransfer(int i, int i2, int i3, int i4, int i5) throws IOException {
            byte[] bArr = new byte[i5]; // 创建字节数组
            int controlTransfer = this.mConnection.controlTransfer(i, i2, i3, i4, bArr, i5, 1000); // 执行控制传输
            if (controlTransfer == i5) {
                return bArr; // 返回字节数组
            }
            throw new IOException(String.format("ControlTransfer with value 0x%x failed: %d", Integer.valueOf(i3), Integer.valueOf(controlTransfer))); // 抛出异常
        }

        /**
         * 执行控制输出传输。
         *
         * @param i      请求类型
         * @param i2     请求
         * @param i3     值
         * @param i4     索引
         * @param bArr   数据数组
         * @throws IOException 输入输出异常
         */
        private void outControlTransfer(int i, int i2, int i3, int i4, byte[] bArr) throws IOException {
            int length = bArr == null ? 0 : bArr.length; // 获取数据长度
            int controlTransfer = this.mConnection.controlTransfer(i, i2, i3, i4, bArr, length, 5000); // 执行控制输出传输
            if (controlTransfer != length) {
                throw new IOException(String.format("ControlTransfer with value 0x%x failed: %d", Integer.valueOf(i3), Integer.valueOf(controlTransfer))); // 抛出异常
            }
        }

        /**
         * 执行厂商输入传输。
         *
         * @param i   请求
         * @param i2  索引
         * @param i3  数据长度
         * @return 输入的字节数组
         * @throws IOException 输入输出异常
         */
        private byte[] vendorIn(int i, int i2, int i3) throws IOException {
            return inControlTransfer(192, 1, i, i2, i3); // 执行厂商输入传输
        }

        /**
         * 执行厂商输出传输。
         *
         * @param i    请求
         * @param i2   索引
         * @param bArr 数据数组
         * @throws IOException 输入输出异常
         */
        private void vendorOut(int i, int i2, byte[] bArr) throws IOException {
            outControlTransfer(64, 1, i, i2, bArr); // 执行厂商输出传输
        }

        /**
         * 重置设备，清空硬件缓冲区。
         *
         * @throws IOException 输入输出异常
         */
        private void resetDevice() throws IOException {
            purgeHwBuffers(true, true); // 清空硬件缓冲区
        }

        /**
         * 执行控制输出传输。
         *
         * @param i    请求类型
         * @param i2   请求
         * @param i3   值
         * @param bArr 数据数组
         * @throws IOException 输入输出异常
         */
        private void ctrlOut(int i, int i2, int i3, byte[] bArr) throws IOException {
            outControlTransfer(33, i, i2, i3, bArr); // 执行控制输出传输
        }

        /**
         * 执行黑魔法操作，具体实现依赖于设备。
         *
         * @throws IOException 输入输出异常
         */
        private void doBlackMagic() throws IOException {
            vendorIn(33924, 0, 1); // 执行厂商输入
            vendorOut(1028, 0, null); // 执行厂商输出
            vendorIn(33924, 0, 1); // 执行厂商输入
            vendorIn(33667, 0, 1); // 执行厂商输入
            vendorIn(33924, 0, 1); // 执行厂商输入
            vendorOut(1028, 1, null); // 执行厂商输出
            vendorIn(33924, 0, 1); // 执行厂商输入
            vendorIn(33667, 0, 1); // 执行厂商输入
            vendorOut(0, 1, null); // 执行厂商输出
            vendorOut(1, 0, null); // 执行厂商输出
            vendorOut(2, this.mDeviceType == 0 ? 68 : 36, null); // 执行厂商输出
        }

        /**
         * 设置控制线。
         *
         * @param i 控制线值
         * @throws IOException 输入输出异常
         */
        private void setControlLines(int i) throws IOException {
            ctrlOut(34, i, 0, null); // 设置控制线
            this.mControlLinesValue = i; // 更新控制线值
        }

        /**
         * 读取状态线程函数。
         */
        private void readStatusThreadFunction() {
            while (!this.mStopReadStatusThread) { // 循环直到停止标志为真
                try {
                    byte[] bArr = new byte[10]; // 创建状态缓冲区
                    int bulkTransfer = this.mConnection.bulkTransfer(this.mInterruptEndpoint, bArr, 10, 500); // 执行批量传输
                    if (bulkTransfer > 0) {
                        if (bulkTransfer == 10) {
                            this.mStatus = bArr[8] & 255; // 更新状态
                        } else {
                            throw new IOException(String.format("Invalid CTS / DSR / CD / RI status buffer received, expected %d bytes, but received %d", 10, Integer.valueOf(bulkTransfer))); // 抛出异常
                        }
                    }
                } catch (IOException e) {
                    this.mReadStatusException = e; // 捕获异常
                    return; // 退出线程
                }
            }
        }

        /**
         * 获取状态。
         *
         * @return 状态值
         * @throws IOException 输入输出异常
         */
        private int getStatus() throws IOException {
            if (this.mReadStatusThread == null && this.mReadStatusException == null) { // 检查线程和异常
                synchronized (this.mReadStatusThreadLock) { // 加锁
                    if (this.mReadStatusThread == null) { // 检查线程是否为null
                        byte[] bArr = new byte[10]; // 创建状态缓冲区
                        if (this.mConnection.bulkTransfer(this.mInterruptEndpoint, bArr, 10, 100) != 10) {
                            Log.w(ProlificSerialDriver.this.TAG, "Could not read initial CTS / DSR / CD / RI status"); // 读取状态失败
                        } else {
                            this.mStatus = bArr[8] & 255; // 更新状态
                        }
                        this.mReadStatusThread = new Thread(new Runnable() {
                            @Override
                            public void run() {
                                ProlificSerialPort.this.readStatusThreadFunction(); // 启动状态读取线程
                            }
                        });
                        this.mReadStatusThread.setDaemon(true); // 设置为守护线程
                        this.mReadStatusThread.start(); // 启动线程
                    }
                }
            }
            IOException iOException = this.mReadStatusException; // 获取异常
            if (iOException == null) {
                return this.mStatus; // 返回状态
            }
            this.mReadStatusException = null; // 清除异常
            throw iOException; // 抛出异常
        }

        /**
         * 测试状态标志。
         *
         * @param i 状态标志
         * @return 是否设置
         * @throws IOException 输入输出异常
         */
        private boolean testStatusFlag(int i) throws IOException {
            return (getStatus() & i) == i; // 检查状态标志
        }

        @Override
        public void open(UsbDeviceConnection usbDeviceConnection) throws IOException {
            if (this.mConnection == null) { // 检查连接是否为null
                UsbInterface usbInterface = this.mDevice.getInterface(0); // 获取USB接口
                if (usbDeviceConnection.claimInterface(usbInterface, true)) { // 声明接口
                    this.mConnection = usbDeviceConnection; // 设置连接
                    for (int i = 0; i < usbInterface.getEndpointCount(); i++) { // 遍历端点
                        try {
                            UsbEndpoint endpoint = usbInterface.getEndpoint(i); // 获取端点
                            int address = endpoint.getAddress(); // 获取端点地址
                            if (address == 2) {
                                this.mWriteEndpoint = endpoint; // 设置写入端点
                            } else if (address == INTERRUPT_ENDPOINT) {
                                this.mInterruptEndpoint = endpoint; // 设置中断端点
                            } else if (address == READ_ENDPOINT) {
                                this.mReadEndpoint = endpoint; // 设置读取端点
                            }
                        } catch (Throwable th) {
                            this.mConnection = null; // 清除连接
                            usbDeviceConnection.releaseInterface(usbInterface); // 释放接口
                            throw new IOException(); // 抛出异常
                        }
                    }
                    if (this.mDevice.getDeviceClass() == 2) {
                        this.mDeviceType = 1; // 设置设备类型
                    } else {
                        try {
                            if (((byte[]) this.mConnection.getClass().getMethod("getRawDescriptors", new Class[0]).invoke(this.mConnection, new Object[0]))[7] == 64) {
                                this.mDeviceType = 0; // 设置设备类型
                            } else {
                                if (this.mDevice.getDeviceClass() != 0) {
                                    if (this.mDevice.getDeviceClass() != 255) {
                                        Log.w(ProlificSerialDriver.this.TAG, "Could not detect PL2303 subtype, Assuming that it is a HX device"); // 检测设备类型失败
                                        this.mDeviceType = 0; // 设置为HX设备
                                    }
                                }
                                this.mDeviceType = 2; // 设置设备类型
                            }
                        } catch (NoSuchMethodException unused) {
                            Log.w(ProlificSerialDriver.this.TAG, "Method UsbDeviceConnection.getRawDescriptors, required for PL2303 subtype detection, not available! Assuming that it is a HX device"); // 方法不可用
                            this.mDeviceType = 0; // 设置为HX设备
                        } catch (Exception e) {
                            MyLog.e("An unexpected exception occured while trying to detect PL2303 subtype", e.getMessage()); // 捕获异常
                        }
                    }
                    setControlLines(this.mControlLinesValue); // 设置控制线
                    resetDevice(); // 重置设备
                    doBlackMagic(); // 执行黑魔法
                    return; // 返回
                }
                throw new IOException("Error claiming Prolific interface 0"); // 抛出异常
            }
            throw new IOException("Already open"); // 抛出异常
        }

        @Override
        public void close() throws IOException {
            if (this.mConnection != null) { // 检查连接是否为null
                try {
                    this.mStopReadStatusThread = true; // 设置停止标志
                    synchronized (this.mReadStatusThreadLock) { // 加锁
                        if (this.mReadStatusThread != null) { // 检查线程是否为null
                            try {
                                this.mReadStatusThread.join(); // 等待线程结束
                            } catch (Exception e) {
                                Log.w(ProlificSerialDriver.this.TAG, "An error occured while waiting for status read thread", e); // 捕获异常
                            }
                        }
                    }
                    resetDevice(); // 重置设备
                    try {
                        this.mConnection.releaseInterface(this.mDevice.getInterface(0)); // 释放接口
                    } finally {
                        this.mConnection = null; // 清除连接
                    }
                } catch (Throwable th) {
                    this.mConnection.releaseInterface(this.mDevice.getInterface(0)); // 释放接口
                    throw new IOException(); // 抛出异常
                } finally {
                    this.mConnection = null; // 清除连接
                }
            } else {
                throw new IOException("Already closed"); // 抛出异常
            }
        }

        @Override
        public int read(byte[] bArr, int i) throws IOException {
            synchronized (this.mReadBufferLock) { // 加锁
                int bulkTransfer = this.mConnection.bulkTransfer(this.mReadEndpoint, this.mReadBuffer, Math.min(bArr.length, this.mReadBuffer.length), i); // 执行批量读取
                if (bulkTransfer < 0) {
                    return 0; // 返回0表示没有读取到数据
                }
                System.arraycopy(this.mReadBuffer, 0, bArr, 0, bulkTransfer); // 复制数据
                return bulkTransfer; // 返回读取的字节数
            }
        }

        @Override
        public int write(byte[] bArr, int i) throws IOException {
            int min; // 最小值
            byte[] bArr2; // 临时数组
            int bulkTransfer; // 批量传输字节数
            int i2 = 0; // 当前偏移量
            while (i2 < bArr.length) { // 循环直到写入完成
                synchronized (this.mWriteBufferLock) { // 加锁
                    min = Math.min(bArr.length - i2, this.mWriteBuffer.length); // 计算最小值
                    if (i2 == 0) {
                        bArr2 = bArr; // 如果是第一次写入，直接使用原数组
                    } else {
                        System.arraycopy(bArr, i2, this.mWriteBuffer, 0, min); // 复制数据到写入缓冲区
                        bArr2 = this.mWriteBuffer; // 使用写入缓冲区
                    }
                    bulkTransfer = this.mConnection.bulkTransfer(this.mWriteEndpoint, bArr2, min, i); // 执行批量写入
                }
                if (bulkTransfer > 0) {
                    i2 += bulkTransfer; // 更新偏移量
                } else {
                    throw new IOException("Error writing " + min + " bytes at offset " + i2 + " length=" + bArr.length); // 抛出异常
                }
            }
            return i2; // 返回写入的字节数
        }

        @Override
        public void setParameters(int i, int i2, int i3, int i4) throws IOException {
            if (this.mBaudRate != i || this.mDataBits != i2 || this.mStopBits != i3 || this.mParity != i4) { // 检查参数是否变化
                byte[] bArr = new byte[7]; // 创建参数数组
                bArr[0] = (byte) (i & 255); // 波特率低字节
                bArr[1] = (byte) ((i >> 8) & 255); // 波特率中低字节
                bArr[2] = (byte) ((i >> 16) & 255); // 波特率中高字节
                bArr[3] = (byte) ((i >> 24) & 255); // 波特率高字节
                if (i3 == 1) {
                    bArr[4] = 0; // 停止位为1
                } else if (i3 == 2) {
                    bArr[4] = 2; // 停止位为2
                } else if (i3 == 3) {
                    bArr[4] = 1; // 停止位为1.5
                } else {
                    throw new IllegalArgumentException("Unknown stopBits value: " + i3); // 抛出异常
                }
                if (i4 == 0) {
                    bArr[5] = 0; // 无校验
                } else if (i4 == 1) {
                    bArr[5] = 1; // 奇校验
                } else if (i4 == 2) {
                    bArr[5] = 2; // 偶校验
                } else if (i4 == 3) {
                    bArr[5] = 3; // 标记校验
                } else if (i4 == 4) {
                    bArr[5] = 4; // 空校验
                } else {
                    throw new IllegalArgumentException("Unknown parity value: " + i4); // 抛出异常
                }
                bArr[6] = (byte) i2; // 数据位
                ctrlOut(32, 0, 0, bArr); // 设置参数
                resetDevice(); // 重置设备
                this.mBaudRate = i; // 更新波特率
                this.mDataBits = i2; // 更新数据位
                this.mStopBits = i3; // 更新停止位
                this.mParity = i4; // 更新校验位
            }
        }

        @Override
        public boolean getCD() throws IOException {
            return testStatusFlag(1); // 获取载波检测状态
        }

        @Override
        public boolean getCTS() throws IOException {
            return testStatusFlag(128); // 获取清除发送状态
        }

        @Override
        public boolean getDSR() throws IOException {
            return testStatusFlag(2); // 获取数据集准备状态
        }

        @Override
        public boolean getDTR() throws IOException {
            return (this.mControlLinesValue & 1) == 1; // 获取数据终端就绪状态
        }

        @Override
        public void setDTR(boolean z) throws IOException {
            int i;
            if (z) {
                i = this.mControlLinesValue | 1; // 设置数据终端就绪
            } else {
                i = this.mControlLinesValue & -2; // 清除数据终端就绪
            }
            setControlLines(i); // 更新控制线
        }

        @Override
        public boolean getRI() throws IOException {
            return testStatusFlag(8); // 获取振铃指示状态
        }

        @Override
        public boolean getRTS() throws IOException {
            return (this.mControlLinesValue & 2) == 2; // 获取请求发送状态
        }

        @Override
        public void setRTS(boolean z) throws IOException {
            int i;
            if (z) {
                i = this.mControlLinesValue | 2; // 设置请求发送
            } else {
                i = this.mControlLinesValue & -3; // 清除请求发送
            }
            setControlLines(i); // 更新控制线
        }

        @Override
        public boolean purgeHwBuffers(boolean z, boolean z2) throws IOException {
            if (z) {
                vendorOut(8, 0, null); // 清空接收缓冲区
            }
            if (z2) {
                vendorOut(9, 0, null); // 清空发送缓冲区
            }
            return z || z2; // 返回是否清空
        }
    }
}