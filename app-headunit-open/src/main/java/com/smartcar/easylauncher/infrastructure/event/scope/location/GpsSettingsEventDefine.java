package com.smartcar.easylauncher.infrastructure.event.scope.location;


import com.smartcar.easylauncher.data.model.system.GpsSettingsModel;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * GPS设置事件定义
 */
@EventGroup(value = "GpsSettingsScope", active = true)
public class GpsSettingsEventDefine {

    @Event(value = "GpsSettingseventBean", multiProcess = true)
    GpsSettingsModel eventBean;

}