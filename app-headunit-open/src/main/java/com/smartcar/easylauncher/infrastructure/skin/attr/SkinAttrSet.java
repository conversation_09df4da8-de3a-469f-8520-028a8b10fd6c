package com.smartcar.easylauncher.infrastructure.skin.attr;

import android.util.AttributeSet;
import android.view.View;

import com.smartcar.easylauncher.infrastructure.skin.iface.ISkinItem;

import java.util.HashMap;
import java.util.Map;

public abstract class SkinAttrSet implements ISkinItem {

    protected View view;
    protected AttributeSet attrs;
    protected Map<String, IAttr> hashSet;

    public SkinAttrSet(View view,AttributeSet attrs){
        this.view = view;
        this.attrs = attrs;
        hashSet = new HashMap<>();
    }

    public View getView(){
        return view;
    }

    public AttributeSet getAttrs(){
        return attrs;
    }

    public Map<String, IAttr> getAttrSet(){
        return hashSet;
    }

    public abstract boolean isIncludeAttr(String attributeName);

    public final void addAttr(IAttr attrSet){
        hashSet.put(attrSet.attrName,attrSet);
    }
    
    /**
     * 清理对View、属性集和属性的引用，防止内存泄漏
     */
    public void clearReferences() {
        view = null;
        attrs = null;
        if (hashSet != null) {
            hashSet.clear();
        }
    }
}
