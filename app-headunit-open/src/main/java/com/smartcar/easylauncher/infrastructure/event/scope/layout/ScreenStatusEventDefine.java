package com.smartcar.easylauncher.infrastructure.event.scope.layout;



import com.smartcar.easylauncher.data.model.system.ScreenStatusModel;

import java.util.List;
import java.util.Map;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 屏幕状态事件定义
 */
@EventGroup(value = "ScreenStatusScope", active = true)
public class ScreenStatusEventDefine {
    @Event(value = " ScreenStatuseventInt", multiProcess = false)
    Integer eventInt;

    @Event(value = " ScreenStatuseventString", multiProcess = true)
    String eventString;

    @Event(value = " ScreenStatuseventBean", multiProcess = true)
    ScreenStatusModel eventBean;

    @Event(value = " ScreenStatuseventBean", multiProcess = true)
    List<String> eventList;

    @Event(value = " ScreenStatuseventMap", multiProcess = true)
    Map<String, List<String>> eventMap;
}