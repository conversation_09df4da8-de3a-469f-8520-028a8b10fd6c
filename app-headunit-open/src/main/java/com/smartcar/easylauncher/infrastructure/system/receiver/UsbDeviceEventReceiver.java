package com.smartcar.easylauncher.infrastructure.system.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;

import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * USB通知接收器，用于监听USB设备的插拔事件。
 *
 * <p>此类通过广播接收器机制，监听系统发出的USB设备插拔广播，
 * 并根据动作类型（插入或拔出）发布相应的USB消息事件。</p>
 *
 * <p>注意：此类必须在AndroidManifest.xml中注册为广播接收器，
 * 并且需要声明相应的USB权限。</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since [创建日期]
 */
public class UsbDeviceEventReceiver extends BroadcastReceiver {

    /**
     * 日志标签，用于输出日志信息。
     *
     * <p>该标签将根据类名自动生成，方便日志追踪和定位问题。</p>
     */
    private static final String TAG = UsbDeviceEventReceiver.class.getSimpleName();

    /**
     * 空构造函数。
     *
     * <p>这是BroadcastReceiver的要求，不需要在这里做任何事情。</p>
     */
    public UsbDeviceEventReceiver() {
        // 不需要实现任何逻辑
    }

    /**
     * 当接收到广播时调用此方法。
     *
     * <p>此方法会根据Intent的动作类型（USB设备插拔）执行相应的逻辑，
     * 并发布USB消息事件。</p>
     *
     * <p>注意：在调用此方法之前，系统会检查Intent是否为空，如果为空则不会调用此方法。
     * 但在方法内部，我们仍然进行了空指针检查，以防止潜在的异常。</p>
     *
     * @param context 上下文对象，用于访问应用的资源和类。
     * @param intent  包含广播详细信息的Intent对象。
     */
    @Override
    public void onReceive(Context context, Intent intent) {
        // 打印日志，记录接收到的广播
        MyLog.d(TAG, "Received USB notification");

        // 检查Intent是否为空，防止空指针异常（理论上系统已经检查，但此处再次检查以增强健壮性）
        if (intent == null) {
            MyLog.e(TAG, "Received null intent in onReceive");
            return;
        }

        // 获取Intent的动作
        String action = intent.getAction();

        try {
            // 处理USB设备拔出的动作
            if (UsbManager.ACTION_USB_DEVICE_DETACHED.equals(action)) {
                MyLog.d(TAG, "USB 设备已被拔出");
                // 发布USB设备拔出事件
                //UsbMessageScopeBus.eventBean().post(new UsbMessageModel(intent));
            }
            // 处理USB设备插入的动作
            else if (UsbManager.ACTION_USB_DEVICE_ATTACHED.equals(action)) {
                MyLog.d(TAG, "USB 设备已被插入");
                UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                if (device != null) {
                    MyLog.d(TAG, "已连接的USB设备信息: " + "供应商" + device.getVendorId() + " 产品" + device.getProductId());
                    // 处理USB设备连接事件，并获取设备信息
                    // 例如：device.getVendorId() 获取供应商ID
                    //      device.getProductId() 获取产品ID
                    //      ...
                }
                // 发布USB设备插入事件
            //    UsbMessageScopeBus.eventBean().post(new UsbMessageModel(intent));
            }
            // 注意：此处未处理其他可能的USB动作，如有需要请添加相应逻辑。
        } catch (Exception e) {
            // 捕获并处理异常，防止应用崩溃
            // 记录异常信息，包括异常类型和错误消息
            MyLog.e(TAG, "Error handling USB notification: " + e.getMessage());
            // 注意：此处未进行异常恢复操作，如有需要请根据业务需求添加。
        }
    }
}
