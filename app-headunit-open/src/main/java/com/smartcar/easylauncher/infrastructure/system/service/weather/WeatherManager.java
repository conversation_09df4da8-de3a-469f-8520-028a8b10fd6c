package com.smartcar.easylauncher.infrastructure.system.service.weather;


import android.content.Context;

import androidx.annotation.NonNull;

import com.smartcar.easylauncher.infrastructure.amap.LocationService;
import com.smartcar.easylauncher.core.constants.StatusCodeModel;
import com.smartcar.easylauncher.infrastructure.event.cody.NetworkStateScopeBus;
import com.smartcar.easylauncher.infrastructure.event.cody.WeatherDataScopeBus;
import com.smartcar.easylauncher.infrastructure.event.scope.location.cody.LocatelnfoScopeBus;
import com.smartcar.easylauncher.data.model.navigation.LocateInforModel;
import com.smartcar.easylauncher.data.model.weather.WeatherDataModel;
import com.smartcar.easylauncher.data.network.state.NetworkAvailabilityState;
import com.smartcar.easylauncher.infrastructure.system.service.weather.provider.AMapWeatherProvider;
import com.smartcar.easylauncher.infrastructure.system.service.weather.provider.OpenWeatherProvider;
import com.smartcar.easylauncher.infrastructure.system.service.weather.provider.WeatherofferrProvider;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import cody.bus.ObserverWrapper;

/**
 * 天气服务管理类
 * 统一管理天气数据的获取、处理和分发
 * <p>
 * 核心流程图:
 * <p>
 * 1. 初始化流程
 * ┌─────────────────────────────────────┐
 * │ WeatherManager.initialize()         │
 * ├─────────────────────────────────────┤
 * │ ┌→ 注册网络监听                     │
 * │ ├→ 注册位置监听                     │
 * │ ├→ 初始化默认天气提供者             │
 * │ │  - WeatherofferrProvider(优先级1) │
 * │ │  - OpenWeatherProvider(优先级2)   │
 * │ │  - AMapWeatherProvider(优先级3)   │
 * │ └→ 加载缓存数据并通知UI             │
 * └─────────────────────────────────────┘
 * <p>
 * 2. 位置更新处理流程
 * ┌─────────────────────────────────────┐
 * │ 位置变化触发                        │
 * ├─────────────────────────────────────┤
 * │ ┌→ 检查位置信息                     │
 * │ │  └→ 是否包含城市信息?            │
 * │ │      ├→ 是: 检查是否需要更新     │
 * │ │      │   ├→ 城市变化?           │
 * │ │      │   ├→ 距离>5000m?        │
 * │ │      │   └→ 首次定位?          │
 * │ │      └→ 否: 计算位置变化        │
 * │ │          ├→ >5000m: 请求地理编码│
 * │ │          └→ <5000m: 保留原信息  │
 * │ └→ 更新位置信息                     │
 * └─────────────────────────────────────┘
 * <p>
 * 3. 天气更新流程
 * ┌─────────────────────────────────────┐
 * │ updateWeather(force)                │
 * ├─────────────────────────────────────┤
 * │ ┌→ 检查更新条件                     │
 * │ │  ├→ 是否强制更新?                │
 * │ │  ├→ 缓存是否过期?                │
 * │ │  └→ 是否正在更新?                │
 * │ ├→ 检查必要条件                     │
 * │ │  ├→ 网络是否可用?                │
 * │ │  └→ 位置是否有效?                │
 * │ ├→ 按优先级尝试天气提供者           │
 * │ │  ├→ WeatherofferrProvider        │
 * │ │  ├→ OpenWeatherProvider          │
 * │ │  └→ AMapWeatherProvider          │
 * │ └→ 处理结果                         │
 * │     ├→ 成功: 更新缓存+通知         │
 * │     └→ 失败: 尝试下一个/报错       │
 * └─────────────────────────────────────┘
 * <p>
 * 4. 网络状态处理流程
 * ┌─────────────────────────────────────┐
 * │ 网络状态变化                        │
 * ├─────────────────────────────────────┤
 * │ ┌→ 更新网络状态                     │
 * │ └→ 网络可用时                       │
 * │     └→ 请求更新天气                 │
 * └─────────────────────────────────────┘
 * <p>
 * 关键参数:
 * - MIN_UPDATE_INTERVAL_MINUTES: 30 (天气更新最小间隔)
 * - MIN_LOCATION_CHANGE_METERS: 5000 (触发更新的最小距离)
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-03-21
 */
public class WeatherManager {

    /**
     * 日志标签
     */
    private static final String TAG = WeatherManager.class.getSimpleName();

    /**
     * 天气数据更新最小间隔（分钟）
     */
    private static final int MIN_UPDATE_INTERVAL_MINUTES = 30;

    /**
     * 位置变化触发更新的最小距离（米）
     */
    private static final int MIN_LOCATION_CHANGE_METERS = 5000;

    /**
     * 天气API提供者列表
     */
    private final List<WeatherProvider> weatherProviders;

    /**
     * 天气数据缓存
     */
    private final WeatherCache weatherCache;

    /**
     * 当前位置信息
     */
    private LocateInforModel currentLocation;

    /**
     * 网络状态
     */
    private boolean hasNetwork;

    /**
     * 是否正在更新天气
     */
    private volatile boolean isUpdating;

    /**
     * 网络状态监听器
     */
    ObserverWrapper<NetworkAvailabilityState> observerWrapper;
    /**
     * 位置信息监听器
     */
    ObserverWrapper<LocateInforModel> aMapobserverWrapper;

    /**
     * 上次更新时的位置信息
     */
    private LocateInforModel lastUpdateLocation;

    /**
     * 私有构造方法，使用单例模式
     */
    private WeatherManager() {
        this.weatherProviders = new ArrayList<>();
        this.weatherCache = new WeatherCache();
    }

    /**
     * 获取单例实例
     */
    public static WeatherManager getInstance() {
        return SingletonHolder.INSTANCE;
    }

    private static class SingletonHolder {
        private static final WeatherManager INSTANCE = new WeatherManager();
    }

    /**
     * 初始化天气服务
     *
     * @param context 应用上下文
     */
    public void initialize(@NonNull Context context) {
        // 注册网络状态监听
        registerNetworkListener();
        // 注册位置信息监听
        registerLocationListener();
        // 初始化默认天气提供者
        initializeDefaultProviders(context);

        // 加载并通知缓存的天气数据
        WeatherDataModel cachedWeather = weatherCache.getCachedWeather();
        if (cachedWeather != null) {
            //XLog.tag(TAG).d("加载缓存天气数据 - 时间: %s", cachedWeather.getDataTime());
            notifyWeatherUpdate(cachedWeather);
        }
    }

    /**
     * 初始化默认的天气提供者
     */
    private void initializeDefaultProviders(Context context) {
        // 添加WeatherofferrProvider提供者（优先级1）
        addWeatherProvider(new WeatherofferrProvider());
        // 添加OpenWeather提供者（优先级2）
        addWeatherProvider(new OpenWeatherProvider());
        // 添加高德天气提供者（优先级3）
        addWeatherProvider(new AMapWeatherProvider(context));
    }

    /**
     * 添加天气数据提供者
     *
     * @param provider 天气提供者实现
     */
    public void addWeatherProvider(@NonNull WeatherProvider provider) {
        if (!weatherProviders.contains(provider)) {
            weatherProviders.add(provider);
            // 按优先级排序
            sortProviders();
        }
    }

    /**
     * 对提供者按优先级排序
     */
    private void sortProviders() {
        Collections.sort(weatherProviders, (p1, p2) ->
                Integer.compare(p1.getPriority(), p2.getPriority()));
    }


    /**
     * 从提供者获取天气数据
     */
    private void fetchWeatherFromProviders() {
        if (weatherProviders.isEmpty()) {
            //XLog.tag(TAG).e("没有可用的天气提供者");
            isUpdating = false;
            notifyError(new WeatherDataModel(StatusCodeModel.FAIL,new WeatherError(ErrorCode.NO_PROVIDER, "无可用的天气提供者")));
            return;
        }

        //XLog.tag(TAG).d("开始尝试获取天气数据，可用提供者数量: %d", weatherProviders.size());
        tryNextProvider(0, currentLocation);
    }

    /**
     * 尝试使用下一个提供者
     *
     * @param index    当前提供者索引
     * @param location 位置信息
     */
    private void tryNextProvider(int index, LocateInforModel location) {
        if (index >= weatherProviders.size()) {
            //XLog.tag(TAG).e("所有天气提供者都失败");
            isUpdating = false;
            notifyError(new WeatherDataModel(StatusCodeModel.FAIL,new WeatherError(ErrorCode.ALL_PROVIDERS_FAILED, "所有天气提供者都失败")));
            return;
        }

        WeatherProvider provider = weatherProviders.get(index);
        //XLog.tag(TAG).d("尝试使用提供者[%d]: %s", index, provider.getProviderName());

        provider.fetchWeatherData(location, new WeatherCallback() {
            @Override
            public void onWeatherDataReceived(WeatherDataModel data) {
                //XLog.tag(TAG).i("成功获取天气数据 - 提供者: %s", provider.getProviderName());
                processWeatherData(data);
                isUpdating = false;
            }

            @Override
            public void onError(WeatherError error) {
                //XLog.tag(TAG).w("提供者[%s]获取失败: %s", provider.getClass().getSimpleName(), error.getMessage());
                tryNextProvider(index + 1, location);
            }
        });
    }

    /**
     * 处理获取到的天气数据
     */
    private void processWeatherData(WeatherDataModel weatherData) {
        if (weatherData == null) {
            //XLog.tag(TAG).e("收到空的天气数据");
            return;
        }

        //XLog.tag(TAG).i("处理天气数据 - 时间: %s, 温度: %s",weatherData.getDataTime(),weatherData.getCurrentWeather().getHumidity());

        // 更新缓存
        weatherCache.cacheWeatherData(weatherData);
        // 通知数据更新
        notifyWeatherUpdate(weatherData);

        //XLog.tag(TAG).d("天气数据处理完成，已更新缓存和通知观察者");
    }


    /**
     * 获取当前天气数据
     * 优先返回缓存数据
     *
     * @return 天气数据，可能为null
     */
    public WeatherDataModel getCurrentWeather() {
        return weatherCache.getCachedWeather();
    }

    /**
     * 获取天气更新状态
     *
     * @return 是否正在更新
     */
    public boolean isUpdating() {
        return isUpdating;
    }

    /**
     * 注册网络状态监听
     */
    private void registerNetworkListener() {
        NetworkStateScopeBus.networkAvailabilityState().observeForever(observerWrapper = new ObserverWrapper<>(true) {
            @Override
            public void onChanged(final NetworkAvailabilityState value) {
                //XLog.tag(TAG).v("网络状态变化 - 类型: %s, 是否可用: %b", value.networkType, value.hasNetwork);
                hasNetwork = value.isAvailable();
                if (value.isAvailable()) {
                    requestWeatherUpdate();
                }
            }
        });

    }

    /**
     * 注册位置变化监听
     */
    private void registerLocationListener() {
        LocatelnfoScopeBus.eventBean().observeForever(aMapobserverWrapper = new ObserverWrapper<>(true) {
            @Override
            public void onChanged(final LocateInforModel location) {
                if (location != null) {
                    updateLocationInfo(location);
                }
            }
        });
    }

    /**
     * 更新位置信息
     * <p>
     * 位置更新处理逻辑：
     * 1. 收到新位置信息
     * 2. 判断是否包含地理编码（城市信息）：
     * - 包含：直接更新位置并触发天气更新检查
     * - 不包含：需要进一步处理
     * a. 检查与当前位置的距离
     * b. 如果距离超过5000米或无当前位置，请求地理编码
     * c. 如果距离小于5000米，保持当前位置信息不变
     */
    private void updateLocationInfo(LocateInforModel newLocation) {
        //XLog.tag(TAG).v("收到位置更新: %s", new Gson().toJson(newLocation));

        if (newLocation.isHasGeocode()) {
            // 有地理编码信息，直接更新并检查是否需要更新天气
            currentLocation = newLocation;
            requestWeatherUpdate();
        } else if (hasNetwork) {
            // 无地理编码信息，计算位置变化决定是否请求
            float[] results = new float[1];
            float distance = 0;

            if (currentLocation != null) {
                android.location.Location.distanceBetween(
                        currentLocation.getLatitude(), currentLocation.getLongitude(),
                        newLocation.getLatitude(), newLocation.getLongitude(),
                        results
                );
                distance = results[0];
            }

            if (currentLocation == null || distance >= MIN_LOCATION_CHANGE_METERS) {
                //XLog.tag(TAG).d("位置变化显著(%.2f米)，请求地理编码", distance);
                LocationService.getInstance().getAddress();
            } else {
                //XLog.tag(TAG).d("位置变化较小(%.2f米)，保持当前位置信息", distance);
            }
        }
    }

    /**
     * 检查是否需要更新天气
     * <p>
     * 更新条件判断逻辑：
     * 1. 基础条件检查（任一不满足则不更新）：
     * - 是否正在更新中
     * - 是否有网络连接
     * - 是否有有效的位置信息
     * <p>
     * 2. 更新触发条件（满足任一则更新）：
     * - 强制更新
     * - 缓存已失效（超过3小时或非当天）
     * - 位置发生显著变化（>5000米或城市变化）
     *
     * @param force 是否强制更新
     * @return true 如果需要更新
     */
    private boolean shouldUpdateWeather(boolean force) {
        // 1. 基础检查
        if (isUpdating) {
            //XLog.tag(TAG).w("天气更新正在进行中，跳过本次更新请求");
            return false;
        }

        if (!hasNetwork) {
            //XLog.tag(TAG).w("无网络连接，无法更新天气");
            return false;
        }

        if (!isLocationValid()) {
            //XLog.tag(TAG).w("无有效位置信息，无法更新天气");
            return false;
        }

        // 2. 更新条件检查（满足任一条件即更新）
        if (force) {
            //XLog.tag(TAG).d("强制更新天气");
            return true;
        }

        if (!weatherCache.isCacheValid(false)) {
            //XLog.tag(TAG).d("缓存已失效，需要更新天气");
            return true;
        }

        if (hasSignificantLocationChange()) {
            //XLog.tag(TAG).d("位置发生显著变化，需要更新天气");
            return true;
        }

        //XLog.tag(TAG).d("无需更新天气数据");
        return false;
    }

    /**
     * 检查位置信息是否有效
     * <p>
     * 有效条件：
     * 1. 位置信息不为空
     * 2. 包含地理编码信息
     * 3. 包含城市信息
     */
    private boolean isLocationValid() {
        return currentLocation != null &&
                currentLocation.isHasGeocode() &&
                currentLocation.getCity() != null;
    }

    /**
     * 检查是否发生显著的位置变化
     * <p>
     * 显著变化判定条件（满足任一）：
     * 1. 首次定位
     * 2. 城市或区县发生变化
     * 3. 距离变化超过5000米
     * <p>
     * 注意：这里比较的是当前位置与上次更新天气时的位置
     */
    private boolean hasSignificantLocationChange() {
        if (lastUpdateLocation == null) {

            LocateInforModel cachedWeatherLocation = weatherCache.getCachedWeatherLocation();
            if (cachedWeatherLocation != null) {
                boolean distanceChanged = isDistanceChanged(cachedWeatherLocation.getLatitude(), cachedWeatherLocation.getLongitude());
                //XLog.tag(TAG).d("首次更新: 位置变化 %s)",distanceChanged ? "显著" : "不显著");
                return distanceChanged;
            }
            //XLog.tag(TAG).d("首次更新位置");
            return true;
        }

        // 检查城市变化
        boolean cityChanged = !currentLocation.getCity().equals(lastUpdateLocation.getCity()) ||
                !currentLocation.getDistrict().equals(lastUpdateLocation.getDistrict());

        if (cityChanged) {
            //XLog.tag(TAG).d("城市信息变化 - 原城市: %s, 新城市: %s",lastUpdateLocation.getCity(), currentLocation.getCity());
            return true;
        }

        boolean distanceChanged = isDistanceChanged(lastUpdateLocation.getLatitude(), lastUpdateLocation.getLongitude());

        return distanceChanged;
    }

    private boolean isDistanceChanged(double latitude, double longitude) {
        // 检查距离变化
        float[] results = new float[1];
        android.location.Location.distanceBetween(
                latitude, longitude,
                currentLocation.getLatitude(), currentLocation.getLongitude(),
                results
        );
        float distance = results[0];

        boolean distanceChanged = distance >= MIN_LOCATION_CHANGE_METERS;
        //XLog.tag(TAG).d("位置变化距离: %.2f米 (最小更新距离: %d米)",distance, MIN_LOCATION_CHANGE_METERS);
        return distanceChanged;
    }

    /**
     * 通知天气数据更新
     */
    private void notifyWeatherUpdate(WeatherDataModel weatherData) {
        WeatherDataScopeBus.eventBean().post(weatherData);
    }

    /**
     * 通知错误信息
     */
    private void notifyError(WeatherDataModel error) {
        WeatherDataScopeBus.eventBean().post(error);
    }

    /**
     * 清理资源
     */
    public void destroy() {
        if (observerWrapper != null) {
            NetworkStateScopeBus.networkAvailabilityState().removeObserver(observerWrapper);
            observerWrapper = null;
        }
        if (aMapobserverWrapper != null) {
            LocatelnfoScopeBus.eventBean().removeObserver(aMapobserverWrapper);
            aMapobserverWrapper = null;
        }
        weatherProviders.clear();
        isUpdating = false;
    }

    /**
     * 请求更新天气数据
     * 遵循更新间隔限制
     */
    public void requestWeatherUpdate() {
        updateWeather(false);
    }

    /**
     * 强制刷新天气数据
     * 忽略更新间隔限制
     */
    public void forceRefreshWeather() {
        updateWeather(true);
    }

    /**
     * 更新天气数据
     * <p>
     * 更新流程：
     * 1. 检查是否需要更新
     * 2. 如果不需要更新：
     * - 有缓存则使用缓存
     * - 无缓存且无网络则报错
     * - 无缓存且无有效位置则报错
     * 3. 如果需要更新：
     * - 标记更新状态
     * - 记录当前位置
     * - 开始获取天气数据
     *
     * @param force 是否强制更新
     * @return true 如果开始更新，false 如果使用缓存或出错
     */
    private boolean updateWeather(boolean force) {
        WeatherDataModel cachedWeather = weatherCache.getCachedWeather();

        if (!shouldUpdateWeather(force)) {
            if (cachedWeather != null) {
                //XLog.tag(TAG).d("使用缓存数据 - 时间: %s", cachedWeather.getDataTime());
                notifyWeatherUpdate(cachedWeather);
            } else if (!hasNetwork) {
                notifyError(new WeatherDataModel(StatusCodeModel.FAIL,new WeatherError(ErrorCode.NO_NETWORK, "网络异常")));
            } else if (!isLocationValid()) {
                notifyError(new WeatherDataModel(StatusCodeModel.FAIL,new WeatherError(ErrorCode.INVALID_LOCATION, "正在定位")));
            }
            return false;
        }

        //XLog.tag(TAG).i("开始更新天气数据 - 强制更新: %b, 位置: %s",force, currentLocation.getCity());

        isUpdating = true;
        lastUpdateLocation = currentLocation; // 记录本次更新时的位置
        fetchWeatherFromProviders();
        return true;
    }
}