package com.smartcar.easylauncher.infrastructure.driver;

import android.hardware.usb.UsbDevice;

import java.util.List;

/**
 * UsbSerialDriver接口定义了USB串口驱动应该具备的方法。
 * <p>
 * 该接口提供了获取USB设备和USB串口列表的方法。
 * 注意：实现该接口的类需要确保在多线程环境下的线程安全性。
 * </p>
 */
public interface UsbSerialDriver {

    /**
     * 获取USB设备对象
     *
     * @return UsbDevice对象，表示当前USB设备
     * @throws IllegalStateException 如果设备未连接或不可用
     * <p>
     * 该方法用于获取当前USB设备的实例。
     * 注意：调用该方法前应确保设备已连接。
     * </p>
     */
    UsbDevice getDevice();

    /**
     * 获取所有USB串口列表
     *
     * @return 包含UsbSerialPort对象的List集合，表示可用的USB串口
     * @throws IllegalStateException 如果设备未连接或不可用
     * <p>
     * 该方法返回当前USB设备的所有可用串口列表。
     * 注意：返回的列表可能为空，表示没有可用的串口。
     * </p>
     */
    List<UsbSerialPort> getPorts();
}