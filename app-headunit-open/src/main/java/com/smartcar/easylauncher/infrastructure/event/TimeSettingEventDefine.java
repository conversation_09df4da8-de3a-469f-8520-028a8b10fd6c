package com.smartcar.easylauncher.infrastructure.event;



import com.smartcar.easylauncher.data.model.system.TimeSettingModel;

import java.util.List;
import java.util.Map;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 时间设置事件定义
 */
@EventGroup(value = "TimeSettingScope", active = true)
public class TimeSettingEventDefine {
    @Event(value = "TimeSettingeventInt", multiProcess = false)
    Integer eventInt;

    @Event(value = "TimeSettingeventString", multiProcess = true)
    String eventString;

    @Event(value = "TimeSettingeventBean", multiProcess = true)
    TimeSettingModel eventBean;

    @Event(value = "TimeSettingeventBean", multiProcess = true)
    List<String> eventList;

    @Event(value = "TimeSettingeventMap", multiProcess = true)
    Map<String, List<String>> eventMap;
}