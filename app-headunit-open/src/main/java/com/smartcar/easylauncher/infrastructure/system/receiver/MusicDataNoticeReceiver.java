 
package com.smartcar.easylauncher.infrastructure.system.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.smartcar.easylauncher.core.manager.QQMusicParser;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.thread.ThreadPoolUtil;

import java.util.ArrayList;

/**
 * monitor apps installed and uninstalled
 * 音乐数据广播接收器
 */

public class MusicDataNoticeReceiver extends BroadcastReceiver {
    private static final String TAG = "MusicDataNoticeReceiver";
    private MusicDataNoticeCallback mCallback;

    public MusicDataNoticeReceiver(MusicDataNoticeCallback callback) {
        mCallback = callback;
    }

    public MusicDataNoticeReceiver() {

    }

    @Override
    public void onReceive(Context context, Intent intent) {
        MyLog.d(TAG, "SystemNetworkReceiver" + "");
        String action = intent.getAction();
        // MyLog.d(TAG, "action" + new Gson().toJson(intent).toString());
        if ("SDATA_MUSIC_INSIDE_COVER".equals(action)) {
            MyLog.v("酷我酷我      ", "收到广播数据");
        } else if ("cn.kuwo.kwmusicauto.action.PLAYER_STATUS".equals(action)) {
            //Bundle[{play_music_name=闪耀, PLAYERSTATUS=3 暂停, play_music_artist=汪苏泷, play_music_album=闪耀}]
            //Bundle[{play_music_name=Super Star, PLAYERSTATUS=2, play_music_artist=S.H.E, play_music_album=Super Star}]   PLAYERSTATUS=0 下一曲开始  PLAYERSTATUS=1播放 PLAYERSTATUS=2 准备播放 PLAYERSTATUS=3暂停 PLAYERSTATUS=4播放完毕
            // RxBus2.getInstance().post(new MusicMessageEvent(intent));
//            mCallback.onMusicData(intent.getStringExtra("play_music_name"), intent.getStringExtra("play_music_artist"), "", "", "", totalTime, currTime, 6);
            int PLAYERSTATUS = intent.getIntExtra("PLAYERSTATUS", 0);
            String stringExtra = intent.getStringExtra("play_music_name");
            String stringExtra2 = intent.getStringExtra("play_music_artist");
            String stringExtra3 = intent.getStringExtra("play_music_album");
            ThreadPoolUtil.getThreadPoolExecutor().submit(new Runnable() {
                @Override
                public void run() {
                 //   MusicParser.getInstance().analyzingKuWoMusic(intent);
                }
            });
            MyLog.v("酷我酷我      ", "play_music_name   " + stringExtra + "   play_music_artist   " + stringExtra2 + "   play_music_album   " + stringExtra3 + "   PLAYERSTATUS   " + PLAYERSTATUS);
        } else if ("cn.kuwo.kwmusicauto.action.PLAY_CLIENT_MUSICS".equals(action)) {
            ArrayList arrayList = new ArrayList();
            int intExtra = intent.getIntExtra("play_client_musics_search_state", 0);
            if (intExtra == -1) {
                MyLog.v("酷我酷我12123      ", "play_client_musics_search_state   " + "stringExtra3");
            } else if (intExtra != 0) {
                if (intExtra == 1) {
                    MyLog.v("酷我酷我12123      ", intent.getStringExtra("play_client_musics_search_json"));
                }
            } else {
            }

            MyLog.v("酷我酷我12123      ", "进来了");
        } else if ("cn.kuwo.kwmusicauto.action.ENTER".equals(action)) {
            MyLog.v("酷我酷我", "ENTER   " + "stringExtra3");
            String stringExtra = intent.getStringExtra("play_music_name");
            String stringExtra2 = intent.getStringExtra("play_music_artist");
        } else if ("cn.kuwo.kwmusicauto.action.AUDIOFOCUS".equals(action)) {
            MyLog.v("酷我酷我", "AUDIOFOCUS   " + "stringExtra3");
            String stringExtra = intent.getStringExtra("play_music_name");//Bundle[{play_client_play_mv=false, play_client_audio_focus_state=2}]
            String stringExtra2 = intent.getStringExtra("play_music_artist");
        } else if ("cn.kuwo.kwmusicauto.action.PLAY_END".equals(action)) {
            MyLog.v("酷我酷我", "PLAY_END   " + "stringExtra3");
            //Bundle[{ENDTYPE=1}] 人为干预   Bundle[{ENDTYPE=0}]自然播放完成
            String stringExtra = intent.getStringExtra("play_music_name");
            String stringExtra2 = intent.getStringExtra("play_music_artist");
        } else if ("cn.kuwo.kwmusicauto.action.PLAYER_MODE".equals(action)) {
            MyLog.v("酷我酷我", "PLAYER_MODE   " + "stringExtra3");//Bundle[{PLAYERMODE=3}] 随机   Bundle[{PLAYERMODE=0}]单曲 Bundle[{PLAYERMODE=1}] 顺序 Bundle[{PLAYERMODE=2}] 循环
            String stringExtra = intent.getStringExtra("play_music_name");
            String stringExtra2 = intent.getStringExtra("play_music_artist");
        } else if ("cn.kuwo.kwmusicauto.action.EXIT".equals(action)) {
            MyLog.v("酷我酷我", "EXIT   " + "stringExtra3");
            String stringExtra = intent.getStringExtra("play_music_name");
            String stringExtra2 = intent.getStringExtra("play_music_artist");
        } else if ("cn.kuwo.kwmusicauto.action.OPEN_TOAST".equals(action)) {
            MyLog.v("酷我酷我", "OPEN_TOAST   " + "stringExtra3");
            String stringExtra = intent.getStringExtra("play_music_name");
            String stringExtra2 = intent.getStringExtra("play_music_artist");
        } else if ("cn.kuwo.kwmusicauto.action.CLOSE_TOAST".equals(action)) {
            MyLog.v("酷我酷我", "CLOSE_TOAST   " + "stringExtra3");
            //Bundle[{cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}]
            String stringExtra = intent.getStringExtra("play_music_name");
            String stringExtra2 = intent.getStringExtra("play_music_artist");
        } else if ("cn.kuwo.kwmusicauto.action.MEDIA_BUTTON".equals(action)) {
            MyLog.v("MEDIA_BUTTON", "MEDIA_BUTTON   " + "stringExtra3");//Bundle[{EXTRA=MEDIA_NEXT, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 下一曲  Bundle[{EXTRA=MEDIA_PAUSE, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 暂停  Bundle[{EXTRA=MEDIA_PLAY, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 播放  Bundle[{EXTRA=MEDIA_PRE, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 上一曲
            String stringExtra = intent.getStringExtra("play_music_name");
            String stringExtra2 = intent.getStringExtra("play_music_artist");
        } else if ("com.tencent.qqmusiccar.action.PLAY_COMMAND_SEND_FOR_THIRD".equals(intent.getAction()) && intent.getStringExtra("com.tencent.qqmusiccar.EXTRA_COMMAND_DATA") != null) {
            MyLog.v("MEDIA_BUTTON", "MEDIA_BUTTON   " + "stringExtra3");
            ThreadPoolUtil.getThreadPoolExecutor().submit(new Runnable() {
                @Override
                public void run() {
                    QQMusicParser.getInstance().analyzingQQMusic(intent);
                }
            });

        } else if ("com.kugou.android.auto.monitor.reconnect".equals(action)) {
            MyLog.v("MEDIA_BUTTON", "MEDIA_BUTTON   " + "stringExtra3");//Bundle[{EXTRA=MEDIA_NEXT, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 下一曲  Bundle[{EXTRA=MEDIA_PAUSE, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 暂停  Bundle[{EXTRA=MEDIA_PLAY, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 播放  Bundle[{EXTRA=MEDIA_PRE, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 上一曲
            String stringExtra = intent.getStringExtra("play_music_name");
            String stringExtra2 = intent.getStringExtra("play_music_artist");
        } else if ("com.kugou.android.auto.music.musicservicecommand.next".equals(action)) {
            MyLog.v("MEDIA_BUTTON", "MEDIA_BUTTON   " + "stringExtra3");//Bundle[{EXTRA=MEDIA_NEXT, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 下一曲  Bundle[{EXTRA=MEDIA_PAUSE, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 暂停  Bundle[{EXTRA=MEDIA_PLAY, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 播放  Bundle[{EXTRA=MEDIA_PRE, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 上一曲
            String stringExtra = intent.getStringExtra("play_music_name");
            String stringExtra2 = intent.getStringExtra("play_music_artist");
        } else if ("com.kugou.android.auto.music.musicservicecommand.pause".equals(action)) {
            MyLog.v("MEDIA_BUTTON", "MEDIA_BUTTON   " + "stringExtra3");//Bundle[{EXTRA=MEDIA_NEXT, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 下一曲  Bundle[{EXTRA=MEDIA_PAUSE, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 暂停  Bundle[{EXTRA=MEDIA_PLAY, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 播放  Bundle[{EXTRA=MEDIA_PRE, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 上一曲
            String stringExtra = intent.getStringExtra("play_music_name");
            String stringExtra2 = intent.getStringExtra("play_music_artist");
        } else if ("com.kugou.android.auto.music.musicservicecommand.previous".equals(action)) {
            MyLog.v("MEDIA_BUTTON", "MEDIA_BUTTON   " + "stringExtra3");//Bundle[{EXTRA=MEDIA_NEXT, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 下一曲  Bundle[{EXTRA=MEDIA_PAUSE, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 暂停  Bundle[{EXTRA=MEDIA_PLAY, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 播放  Bundle[{EXTRA=MEDIA_PRE, cn.kuwo.kwmusicauto.version=3, kuwo_key=auto}] 上一曲
            String stringExtra = intent.getStringExtra("play_music_name");
            String stringExtra2 = intent.getStringExtra("play_music_artist");
        }
        if (mCallback != null) {
        }


    }

    public interface MusicDataNoticeCallback {

        void onMusicData(String song, String singer, String keyAlbum, String lyric, String albumUrl, long totalTime, int currTime, int state);
    }
}
