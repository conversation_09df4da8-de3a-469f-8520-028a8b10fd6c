package com.smartcar.easylauncher.infrastructure.amap;

import android.annotation.SuppressLint;
import android.content.Context;
import android.location.Address;
import android.location.Criteria;
import android.location.Geocoder;
import android.location.Location;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;

import androidx.annotation.NonNull;

import com.amap.api.location.AMapLocation;
import com.elvishew.xlog.XLog;
import com.google.gson.Gson;
import com.king.location.LocationClient;
import com.king.location.LocationErrorCode;
import com.king.location.LocationMode;
import com.king.location.LocationOption;
import com.king.location.OnExceptionListener;
import com.king.location.OnLocationListener;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.infrastructure.event.cody.NetworkStateScopeBus;
import com.smartcar.easylauncher.infrastructure.event.scope.location.cody.LocatelnfoScopeBus;
import com.smartcar.easylauncher.infrastructure.event.scope.location.cody.NavigationSettingsScopeBus;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.core.manager.ThemeHelper;
import com.smartcar.easylauncher.data.model.navigation.GeocodeModel;
import com.smartcar.easylauncher.data.model.navigation.LocateInforModel;
import com.smartcar.easylauncher.data.model.navigation.NavigationSettingsModel;
import com.smartcar.easylauncher.data.network.state.NetworkAvailabilityState;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.location.AmapUtils;
import com.smartcar.easylauncher.shared.utils.location.SpeedConversionUtils;
import com.smartcar.easylauncher.shared.utils.thread.ThreadPoolUtil;
import com.smartcar.easylauncher.shared.utils.time.TimeUnitKt;
import com.smartcar.easylauncher.shared.utils.time.TimedTasksUtilsKt;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import cody.bus.ObserverWrapper;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.schedulers.Schedulers;
import kotlinx.coroutines.Job;
import rxhttp.RxHttp;

/**
 * 定位服务
 *
 * <AUTHOR>
 */
public class LocationService {
    public static final String TAG = "LocationService";
    /**
     * 10公里，单位：米
     */
    private static final float SIGNIFICANT_DISTANCE_THRESHOLD = 10000;
    /**
     * 1分钟，单位：毫秒
     */
    private static final long MIN_TIME_BETWEEN_UPDATES = 60000;
    /**
     * 标记是否已经尝试过定位
     */
    private boolean hasTriedLocation = false;
    /**
     * 上次更新时间，以毫秒为单位。
     * 初始化为0，表示尚未更新过时间。
     */
    private long lastUpdateTime = 0;
    /**
     * 标记是否有网络连接
     */
    private boolean mHasNetwork = false;
    
    /**
     * 标记是否是日产车机设备
     */
    private boolean isJapaneseCarDevice = false;

    /**
     * 上次更新的位置信息。
     * 初始化为null，表示尚未更新过位置信息。
     */
    private Location lastLocation = null;

    private static final String API_URL = "https://api.tianditu.gov.cn/geocoder";
    private static final String API_KEY = "778d4e5deba3bd8b417be3f109b69fbf";
    private Job job;
    private TimedTasksUtilsKt timedTasksUtilsKt = TimedTasksUtilsKt.Companion.getInstance();

    private LocationClient locationClient = null;
    private Context mContext;
    /**
     * 逆地理编码
     */
    private Geocoder geocoder;
    private LocateInforModel locationInfo;
    private Gson gson = new Gson();
    ObserverWrapper<NetworkAvailabilityState> observerWrapper;
    private Observable<Address> observable;
    private ObserverWrapper<NavigationSettingsModel> navSettingsObserver;

    private LocationService() {
    }

    private static class SingletonHolder {
        private static final LocationService INSTANCE = new LocationService();
    }

    public static LocationService getInstance() {
        return LocationService.SingletonHolder.INSTANCE;
    }


    public synchronized LocationService init(Context context) {
        this.mContext = context.getApplicationContext();
        if (geocoder == null && Geocoder.isPresent()) {
            this.geocoder = new Geocoder(context.getApplicationContext());
            XLog.v("Geocoder Initialized");
        }
        // 初始化设备类型判断
        this.isJapaneseCarDevice = SpeedConversionUtils.isJapaneseCarDevice();
        // 监听导航设置变化
        navSettingsNotification();
        return this;
    }

    /**
     * 监听导航设置变化
     */
    private void navSettingsNotification() {
        NavigationSettingsScopeBus.eventBean().observeForever(navSettingsObserver = new ObserverWrapper<NavigationSettingsModel>(true) {
            @Override
            public void onChanged(final NavigationSettingsModel value) {
                if (value != null && value.getType() == 2) {
                    // 更新速度显示修复模式
                    isJapaneseCarDevice = value.getJapaneseCarSpeedMode();
                    XLog.v("LocationService: 速度显示修复模式已" + (isJapaneseCarDevice ? "开启" : "关闭"));
                }
            }
        });
    }

    /**
     * 初始化定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    public void initLocation() {
        if (locationClient != null && locationClient.isStarted()) {
            XLog.v("initLocation: 定位服务已经初始化");
            return;
        }

        try {
            networkNotification();
            locationClient = new LocationClient(mContext);
            LocationOption defaultOption = getDefaultOption(1);
            //设置定位参数
            locationClient.setLocationOption(defaultOption);
            locationClient.setAutoReconnect(true);
            // 设置定位监听
            locationClient.setOnLocationListener(locationListener);
            locationClient.setOnExceptionListener(onExceptionListener);
            //如果已经开始定位，则先停止定位
            if (locationClient.isStarted()) {
                locationClient.stopLocation();
            }
            locationClient.startLocation();
            XLog.v("initLocation: 初始化定位成功 开始定位");
        } catch (Exception e) {
            XLog.e("message:  ", e);
        }
    }

    /**
     * 监听网络状态通知
     */
    private void networkNotification() {
        // 监听网络状态事件，并永远观察（直到手动移除）
        // ObserverWrapper 是一个自定义的包装类，用于处理观察者逻辑，并接受一个布尔值来决定是否需要处理旧数据
        NetworkStateScopeBus.networkAvailabilityState().observeForever(observerWrapper = new ObserverWrapper<>(true) {
            @Override
            public void onChanged(final NetworkAvailabilityState value) {
                XLog.json(value.toString());
                // 更新当前对象的 hasNetwork 状态
                mHasNetwork = value.isAvailable();
                // 只有在定位超时且有网络的情况下才调用高德定位
                if (locationInfo == null && mHasNetwork && hasTriedLocation) {
                    amapLocation();
                }
            }
        });
    }

    /**
     * 默认定位参数
     *
     * @return
     */
    private LocationOption getDefaultOption(int type) {
        return new LocationOption()
                //设置位置精度：高精度
                .setAccuracy(Criteria.ACCURACY_FINE)
                //设置电量消耗：高电量消耗
                .setPowerRequirement(Criteria.POWER_HIGH)
                //设置位置更新最小时间间隔（单位：毫秒）； 默认间隔：10000毫秒，最小间隔：1000毫秒
                .setMinTime(1000)
                //超时时间：默认11000毫秒
                .setTimeout(type == 1 ? 11000 : 60000)
                //设置位置更新最小距离（单位：米）；默认距离：0米
                .setMinDistance(0)
                //设置是否需要速度，默认为 false
                .setSpeedRequired(true)
                //设置是否需要方向，默认为 false
                .setBearingRequired(true)
                //设置是否需要海拔，默认为 false
                .setAltitudeRequired(true)
                .setLocationMode(LocationMode.HIGH_ACCURACY)
                //设置方向精度：高精度
                .setBearingAccuracy(Criteria.ACCURACY_FINE)
                //设置速度精度：高精度
                .setSpeedAccuracy(Criteria.ACCURACY_HIGH)
                //设置是否只定位一次，默认为 false，当设置为 true 时，则只定位一次后，会自动停止定位
                .setOnceLocation(false)
                .setLastKnownLocation(true);
    }


    /**
     * 定位监听
     */
    OnLocationListener locationListener = new OnLocationListener() {
        @SuppressLint("CheckResult")
        @Override
        public void onLocationChanged(@NonNull Location location) {
            XLog.v("  持续定位中");
            // 标记已经尝试过定位
            hasTriedLocation = false;
            try {
                // 组合定位信息
                locationInfo = createLocationInfo(location);
            } catch (Exception e) {
                XLog.e("createLocationInfo() failed" , e);
            }
            // 检查位置是否发生显著变化
            if (shouldUpdateLocation(location)) {
                try {
                    getAddress();
                } catch (Exception e) {
                    XLog.e("getAddress() failed" , e);
                }

                try {
                    inspectionTopic();
                } catch (Exception e) {
                    XLog.e("inspectionTopic() failed" , e);
                }
            }
            // 发送定位信息
            LocatelnfoScopeBus.eventBean().post(locationInfo);
        }


        @Override
        public void onProviderEnabled(@NonNull String provider) {
            super.onProviderEnabled(provider);
            XLog.v("  定位服务开启");
        }

        @Override
        public void onProviderDisabled(@NonNull String provider) {
            super.onProviderDisabled(provider);
            XLog.v("  定位服务关闭");
        }

    };

    /**
     * 异常监听
     */
    OnExceptionListener onExceptionListener = (errorCode, e) -> {
        //如果是超时
        switch (errorCode) {
            case LocationErrorCode.UNKNOWN_EXCEPTION:
                XLog.v("  未知异常");
                break;
            case LocationErrorCode.PERMISSION_EXCEPTION:
                XLog.v("  权限异常");
                break;
            case LocationErrorCode.PROVIDER_EXCEPTION:
                XLog.v("  定位服务异常");
                break;
            case LocationErrorCode.TIMEOUT_EXCEPTION:
                XLog.v("  定位超时");
                LocateInforModel cachedLocationInfo = getLocationInfoFromCache();

                if (cachedLocationInfo != null && isLocationInfoValid(cachedLocationInfo)) {
                    locationInfo = cachedLocationInfo;
                    LocatelnfoScopeBus.eventBean().post(locationInfo); // 发送缓存的定位信息
                } else {
                    if (mHasNetwork) {
                        amapLocation();
                    }
                }
                // 标记已经尝试过定位
                hasTriedLocation = true;
//                //设置定位参数
//                locationClient.setLocationOption(getDefaultOption(0));
//                locationClient.startLocation();
                break;
            default:
        }
    };

    /**
     * 高德辅助定位
     */
    private void amapLocation() {
        AMapLocationHelper.getInstance().init(mContext).singleLocation(location -> {
            AmapUtils.getLocationStr(location);
            locationInfo = amapInfoLocation(location);
            saveLocationInfoToCache(locationInfo);
            inspectionTopic();
        });
    }

    /**
     * 高德辅助定位数据处理
     */
    private LocateInforModel amapInfoLocation(AMapLocation location) {
        locationInfo = new LocateInforModel();
        // 创建一个定位信息模型实例
        if (null != location) {
            hasTriedLocation = false;
            locationInfo.setErrorCode(location.getErrorCode());
            //errCode等于0代表定位成功，其他的为定位失败，具体的可以参照官网定位错误码说明
            if (location.getErrorCode() == 0) {
                //高德定位
                locationInfo.setLocationProvider(1);
                //是否有逆地理编码
                locationInfo.setHasGeocode(location.getCity().isEmpty() ? false : true);
                locationInfo.setLocationType(location.getLocationType());
                locationInfo.setLatitude(location.getLatitude());
                locationInfo.setLongitude(location.getLongitude());
                locationInfo.setAccuracy(location.getAccuracy());
                locationInfo.setAltitude(location.getAltitude());
                locationInfo.setProvider(location.getProvider());
                // 根据设备类型处理速度，确保输出统一为km/h单位
                float speed = location.getSpeed();
                if (isJapaneseCarDevice) {
                    // 日产等特殊设备速度已经是km/h，不需要转换
                    locationInfo.setSpeed(speed);
                } else {
                    // 其他设备速度是m/s，需要转换为km/h
                    locationInfo.setSpeed((float)(speed * SpeedConversionUtils.MS_TO_KMH_CONVERSION_FACTOR));
                }
                locationInfo.setBearing(location.getBearing());
                locationInfo.setSatellites(location.getSatellites());
                locationInfo.setCountry(location.getCountry());
                locationInfo.setProvince(location.getProvince());
                locationInfo.setCity(location.getCity());
                locationInfo.setCityCode(location.getCityCode());
                locationInfo.setDistrict(location.getDistrict());
                locationInfo.setAdCode(location.getAdCode());
                locationInfo.setAddress(location.getAddress());
                locationInfo.setPoiName(location.getPoiName());
                locationInfo.setTime(location.getTime());
            } else {
                //定位失败
                //错误信息
                locationInfo.setErrorInfo(location.getErrorInfo());
                //定位详情
                locationInfo.setLocationDetail(location.getLocationDetail());
            }

        } else {
            XLog.v("定位失败----loc is null");
        }
        //发送定位信息
        LocatelnfoScopeBus.eventBean().post(locationInfo);
        return locationInfo;
    }


    /**
     * 根据经纬度获取逆地理编码
     */

    @SuppressLint("CheckResult")
    public void getAddress() {
        if (locationInfo == null) {
            XLog.v("  定位信息为空");
            return;
        }
        if (!mHasNetwork) {
            XLog.v("  无网络");
            return;
        }
        if (Geocoder.isPresent()) {
            observable = Observable.create(emitter -> {
                if (geocoder == null) {
                    geocoder = new Geocoder(mContext);
                }
                try {
                    List<Address> fromLocation = geocoder.getFromLocation(locationInfo.getLatitude(), locationInfo.getLongitude(), 1);
                    if (fromLocation != null && !fromLocation.isEmpty()) {
                        emitter.onNext(fromLocation.get(0));
                    } else {
                        emitter.onComplete(); // 如果没有找到地址，也完成Observable
                    }
                } catch (IOException e) {
                    emitter.onError(e);
                }
            });
            observable
                    // 在IO线程上执行Geocoder
                    .subscribeOn(Schedulers.io())
                    // 回到主线程处理结果
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(
                            address -> updateLocationInfoWithGeocode(address),
                            throwable -> mapWorldAddress(),
                            () -> {}
                    );
        } else {
            mapWorldAddress();
        }
    }

    @SuppressLint("CheckResult")
    private void mapWorldAddress() {
        String url = String.format("%s?postStr={\"lon\":%f,\"lat\":%f,\"ver\":1}&type=geocode&tk=%s", API_URL, locationInfo.getLongitude(), locationInfo.getLatitude(), API_KEY);
        RxHttp.get(url)
                .toObservable(GeocodeModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(geocodeModel -> {
                    if (geocodeModel.getStatus() == 0) {
                        GeocodeModel.ResultDTO.AddressComponentDTO addressComponent = geocodeModel.getResult().getAddressComponent();
                        updateLocationInfoWithGeocode(addressComponent);
                    } else {
                        XLog.e("逆地理编码失败" + geocodeModel.getStatus());
                    }
                }, throwable -> {
                    XLog.e("逆地理编码失败" , throwable);
                });
    }

    /**
     * 更新原生定位信息
     *
     * @param location
     * @return
     */
    private LocateInforModel createLocationInfo(Location location) {
        // 创建一个定位信息模型实例
        LocateInforModel info = new LocateInforModel();
        // 设置定位服务提供者标识，这里假设0为某个特定的服务提供者
        info.setLocationProvider(0);
        // 设置定位状态码，这里假设0表示定位成功
        info.setErrorCode(0);
        // 设置经度信息
        info.setLongitude(location.getLongitude());
        // 设置纬度信息
        info.setLatitude(location.getLatitude());
        // 设置定位精度
        info.setAccuracy(location.getAccuracy());
        // 设置海拔信息
        info.setAltitude(location.getAltitude());
        // 设置方向信息（即移动方向）
        info.setBearing(location.getBearing());
        // 根据设备类型处理速度，确保输出统一为km/h单位
        float speed = location.getSpeed();
        if (isJapaneseCarDevice) {
            // 日产等特殊设备速度已经是km/h，不需要转换
            info.setSpeed(speed);
        } else {
            // 其他设备速度是m/s，需要转换为km/h
            info.setSpeed((float)(speed * SpeedConversionUtils.MS_TO_KMH_CONVERSION_FACTOR));
        }
        // 设置定位信息的提供者
        info.setProvider(location.getProvider());
        // 标记为没有逆地理编码信息（即没有将经纬度转换为地址信息）
        info.setHasGeocode(false);
        info.setTime(location.getTime());
        // 尝试获取星数信息
        if (location.getExtras() != null) {
            // 从location的额外信息中获取Bundle对象
            Bundle extras = location.getExtras();
            // 检查Bundle中是否包含"satellites"键
            if (extras.containsKey("satellites")) {
                // 如果包含，则获取星数并设置到info模型中
                int satellites = extras.getInt("satellites");
                info.setSatellites(satellites);
            } else {
                // 如果不包含，则将星数设置为0
                info.setSatellites(0);
            }
        } else {
            // 如果location对象或其额外信息为空，则不执行星数设置操作
            // 这里可以添加日志输出或其他处理逻辑，但在此示例中未实现
        }
        // 缓存位置信息
        saveLocationInfoToCache(info);
        // 返回填充好的定位信息模型
        return info;
    }

    /**
     * 更新原生逆地理编码信息
     *
     * @param address
     */
    private void updateLocationInfoWithGeocode(Address address) {
        // 创建一个StringBuilder对象，用于拼接字符串
        StringBuilder sb = new StringBuilder();
        // 设置locationInfo对象的国家信息
        locationInfo.setCountry(address.getCountryName());
        // 设置locationInfo对象的省份信息
        locationInfo.setProvince(address.getAdminArea());
        // 设置locationInfo对象的城市信息
        locationInfo.setCity(address.getLocality());
        // 设置locationInfo对象的区县信息
        locationInfo.setDistrict(address.getSubLocality());
        // 设置locationInfo对象的兴趣点名称（POI），如果address对象中存在特征名称则使用，否则设置为"未知"
        locationInfo.setPoiName(address.getFeatureName() != null ? address.getFeatureName() : "未知");
        // 设置locationInfo对象的详细地址信息，通常使用地址的第一行
        locationInfo.setAddress(address.getAddressLine(0));
        // 标记locationInfo对象为有逆地理编码信息
        locationInfo.setHasGeocode(true);
        // 设置逆地理编码信息的提供者标识，这里假设0为某个特定的提供者
        locationInfo.setGeocodeProvider(0);
        // 使用StringBuilder拼接逆地理编码结果的详细信息字符串
        sb.append("国家：").append(address.getCountryName()).append("\n")
                .append("省：").append(address.getAdminArea()).append("\n")
                .append("城市：").append(address.getLocality()).append("\n")
                // 注意：这里可能是一个错误，通常区应该使用getSubLocality()，但根据上下文使用了getSubAdminArea()
                .append("区：").append(address.getSubLocality()).append("\n") // 假设这里应该是getSubLocality()，如果不是则根据实际API调整
                .append("街道：").append(address.getThoroughfare()).append("\n") // 注意：这里使用了SubAdminArea作为街道，可能需要根据实际情况调整
                .append("地址：").append(address.getAddressLine(0)).append("\n")
                .append("邮编：").append(address.getPostalCode()).append("\n") // 注意：某些情况下PostalCode可能为null
                .append("电话：").append(address.getPhone()).append("\n") // 注意：getPhone()可能并不总是可用，取决于数据源
                .append("特征名称：").append(address.getFeatureName() != null ? address.getFeatureName() : "未知").append("\n")
                .append("街道：").append(address.getThoroughfare() != null ? address.getThoroughfare() : "未知").append("\n"); // 街道的另一种表示方式

        // 使用自定义的日志工具打印逆地理编码的详细信息
        XLog.v("  使用高德解析逆地理编码：\n" + sb.toString());
        saveLocationInfoToCache(locationInfo);
        // 将locationInfo对象发布到事件总线上，以便其他组件可以接收并使用这些信息
        LocatelnfoScopeBus.eventBean().post(locationInfo);

    }

    /**
     * 根据经纬度获取逆地理编码
     *
     * @param addressComponent
     */
    private void updateLocationInfoWithGeocode(GeocodeModel.ResultDTO.AddressComponentDTO addressComponent) {
        locationInfo.setCountry(addressComponent.getNation());
        locationInfo.setProvince(addressComponent.getProvince());
        locationInfo.setCity(addressComponent.getCity());
        locationInfo.setDistrict(addressComponent.getCounty());
        locationInfo.setAddress(addressComponent.getAddress());
        // 标记为有逆地理编码信息
        locationInfo.setHasGeocode(true);
        locationInfo.setGeocodeProvider(1);
        StringBuilder sb = new StringBuilder();
        sb.append("国家：").append(addressComponent.getNation()).append("\n")
                .append("省：").append(addressComponent.getProvince()).append("\n")
                .append("城市：").append(addressComponent.getCity()).append("\n")
                .append("区：").append(addressComponent.getCounty()).append("\n")
                .append("地址：").append(addressComponent.getAddress()).append("\n");
        XLog.v("  使用高德解析逆地理编码：\n" + sb.toString());
        saveLocationInfoToCache(locationInfo);
        // 发送包含逆地理编码信息的定位信息
        LocatelnfoScopeBus.eventBean().post(locationInfo);
    }

    /**
     * 主题检查器
     */
    private void inspectionTopic() {
        if (job != null) {
            return;
        }
        if (locationInfo == null) {
            return;
        }
        Handler handler = new Handler(Looper.getMainLooper());
        job = timedTasksUtilsKt.beginExecutorScan(new TimedTasksUtilsKt.TimerCallback() {
            @Override
            public void onTimeUpdate() {
                XLog.v("  主题检查器在检测");
                ThreadPoolUtil.getThreadPoolExecutor().submit(() -> {
                    handler.post(() -> ThemeHelper.updateThemeIfNeeded(locationInfo.getLongitude(), locationInfo.getLatitude()));
                });
            }

            @Override
            public void onTimerEnd() {

            }
        }, 0, Const.UPDATE_THEME, TimeUnitKt.MINUTES);

    }

    /**
     * 保存定位信息到本地缓存
     */
    private void saveLocationInfoToCache(LocateInforModel locationInfo) {
        String locationInfoJson = gson.toJson(locationInfo);
        DataManager.setLocation(locationInfoJson);
    }

    /**
     * 从本地缓存获取定位信息
     */
    private LocateInforModel getLocationInfoFromCache() {
        String locationInfoJson = DataManager.getLocation();
        return locationInfoJson != null ? gson.fromJson(locationInfoJson, LocateInforModel.class) : null;
    }

    /**
     * 检查定位信息是否在2小时内有效
     */
    private boolean isLocationInfoValid(LocateInforModel locationInfo) {
        long currentTime = System.currentTimeMillis();
        return (currentTime - locationInfo.getTime()) <= TimeUnit.HOURS.toMillis(2);
    }

    // 检查位置是否发生显著变化的方法
    private boolean hasLocationChangedSignificantly(Location newLocation) {
        if (locationInfo == null) {
            return true;
        }
        float[] results = new float[1];
        Location.distanceBetween(locationInfo.getLatitude(), locationInfo.getLongitude(),
                newLocation.getLatitude(), newLocation.getLongitude(), results);
        return results[0] > SIGNIFICANT_DISTANCE_THRESHOLD;
    }

    /**
     * 判断是否应该更新位置信息。
     *
     * @param newLocation 新的位置信息。
     * @return 如果应该更新位置信息，则返回true；否则返回false。
     */
    private boolean shouldUpdateLocation(Location newLocation) {
        // 获取当前时间
        long currentTime = System.currentTimeMillis();

        // 如果当前时间与上一次更新时间之差小于最小更新间隔，则不更新位置信息
        if (currentTime - lastUpdateTime < MIN_TIME_BETWEEN_UPDATES) {
            MyLog.v(TAG, "距离上次更新的时间小于最小更新间隔。");
            return false;
        }

        // 如果上一次的位置信息为空，则直接更新为新位置信息，并更新最后更新时间
        if (lastLocation == null) {
            lastLocation = newLocation;
            lastUpdateTime = currentTime;
            MyLog.v(TAG, "上次位置信息为空，更新为新位置信息。");
            return true;
        }

        // 计算新位置与上一次位置之间的距离
        float[] results = new float[1];
        Location.distanceBetween(lastLocation.getLatitude(), lastLocation.getLongitude(),
                newLocation.getLatitude(), newLocation.getLongitude(), results);
        MyLog.v(TAG, "上次位置与新位置之间的距离: " + results[0] + " 米。");
        lastUpdateTime = currentTime;
        // 如果新位置与上一次位置之间的距离大于显著距离阈值，则更新位置信息，并更新最后更新时间
        if (results[0] > SIGNIFICANT_DISTANCE_THRESHOLD) {
            lastLocation = newLocation;
            MyLog.v(TAG, "超过显著距离阈值，更新位置信息。");
            return true;
        }

        // 如果不满足上述条件，则不更新位置信息
        MyLog.v(TAG, "位置变化不足以更新。");
        return false;
    }

    /**
     * 获取最近一次的定位信息
     *
     * @return 最近一次的定位信息
     */
    public LocateInforModel getLastKnownLocationInfo() {
        return locationInfo;
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        // 移除网络状态观察者
        if (observerWrapper != null) {
            NetworkStateScopeBus.networkAvailabilityState().removeObserver(observerWrapper);
            observerWrapper = null;
        }
        
        // 移除导航设置观察者
        if (navSettingsObserver != null) {
            NavigationSettingsScopeBus.eventBean().removeObserver(navSettingsObserver);
            navSettingsObserver = null;
        }
        
        // 停止定位
        if (locationClient != null && locationClient.isStarted()) {
            locationClient.stopLocation();
            locationClient = null;
        }
    }
}
