package com.smartcar.easylauncher.infrastructure.event.scope.layout;


import com.smartcar.easylauncher.data.model.common.LayoutUPModel;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 布局信息事件定义
 */
@EventGroup(value = "LyoutScope", active = true)
public class LayoutEventDefine {

    @Event(value = "LyouteventBean", multiProcess = true)
    LayoutUPModel eventBean;


}