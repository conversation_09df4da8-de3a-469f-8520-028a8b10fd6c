package com.smartcar.easylauncher.infrastructure.driver;

import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;
import android.hardware.usb.UsbEndpoint;
import android.hardware.usb.UsbInterface;

import com.smartcar.easylauncher.shared.utils.MyLog;

import java.io.IOException;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Cp21xxSerialDriver类实现了UsbSerialDriver接口，用于管理CP21xx系列USB串口设备的连接和通信。
 */
public class Cp21xxSerialDriver implements UsbSerialDriver {
    private static final String TAG = Cp21xxSerialDriver.class.getSimpleName(); // 日志标签
    private final UsbDevice mDevice; // USB设备
    private final UsbSerialPort mPort; // USB串口

    /**
     * 构造函数，初始化Cp21xxSerialDriver对象。
     *
     * @param usbDevice USB设备
     */
    public Cp21xxSerialDriver(UsbDevice usbDevice) {
        this.mDevice = usbDevice;
        this.mPort = new Cp21xxSerialPort(usbDevice, 0); // 初始化串口
    }

    /**
     * 获取支持的设备列表。
     *
     * @return 支持的设备映射
     */
    public static Map<Integer, int[]> getSupportedDevices() {
        LinkedHashMap<Integer, int[]> linkedHashMap = new LinkedHashMap<>();
        linkedHashMap.put(4292, new int[]{60000, 60016, 60017, 60032}); // 添加支持的设备ID
        return linkedHashMap;
    }

    @Override
    public UsbDevice getDevice() {
        return this.mDevice; // 返回USB设备
    }

    @Override
    public List<UsbSerialPort> getPorts() {
        return Collections.singletonList(this.mPort); // 返回串口列表
    }

    /**
     * Cp21xxSerialPort类用于管理CP21xx系列USB串口的具体实现。
     */
    public class Cp21xxSerialPort extends CommonUsbSerialPort {
        private static final int BAUD_RATE_GEN_FREQ = 3686400; // 波特率生成频率
        private static final int CONTROL_WRITE_DTR = 256; // 控制写入DTR
        private static final int CONTROL_WRITE_RTS = 512; // 控制写入RTS
        private static final int DEFAULT_BAUD_RATE = 9600; // 默认波特率
        private static final int FLUSH_READ_CODE = 10; // 刷新读取代码
        private static final int FLUSH_WRITE_CODE = 5; // 刷新写入代码
        private static final int MCR_ALL = 3; // MCR所有标志
        private static final int MCR_DTR = 1; // MCR DTR标志
        private static final int MCR_RTS = 2; // MCR RTS标志
        private static final int REQTYPE_HOST_TO_DEVICE = 65; // 主机到设备请求类型
        private static final int SILABSER_FLUSH_REQUEST_CODE = 18; // SiLabs串口刷新请求代码
        private static final int SILABSER_IFC_ENABLE_REQUEST_CODE = 0; // SiLabs串口接口使能请求代码
        private static final int SILABSER_SET_BAUDDIV_REQUEST_CODE = 1; // SiLabs设置波特率分频请求代码
        private static final int SILABSER_SET_BAUDRATE = 30; // SiLabs设置波特率
        private static final int SILABSER_SET_LINE_CTL_REQUEST_CODE = 3; // SiLabs设置线路控制请求代码
        private static final int SILABSER_SET_MHS_REQUEST_CODE = 7; // SiLabs设置MHS请求代码
        private static final int UART_DISABLE = 0; // UART禁用
        private static final int UART_ENABLE = 1; // UART启用
        private static final int USB_WRITE_TIMEOUT_MILLIS = 5000; // USB写入超时时间（毫秒）

        private UsbEndpoint mReadEndpoint; // 读取端点
        private UsbEndpoint mWriteEndpoint; // 写入端点

        /**
         * 构造函数，初始化Cp21xxSerialPort对象。
         *
         * @param usbDevice USB设备
         * @param i 端口号
         */
        public Cp21xxSerialPort(UsbDevice usbDevice, int i) {
            super(usbDevice, i); // 调用父类构造函数
        }

        @Override
        public boolean getCD() throws IOException {
            return false; // 返回CD状态
        }

        @Override
        public boolean getCTS() throws IOException {
            return false; // 返回CTS状态
        }

        @Override
        public boolean getDSR() throws IOException {
            return false; // 返回DSR状态
        }

        @Override
        public boolean getDTR() throws IOException {
            return true; // 返回DTR状态
        }

        @Override
        public void setDTR(boolean z) throws IOException {
            // 设置DTR状态，当前未实现具体逻辑
        }

        @Override
        public int getPortNumber() {
            return super.getPortNumber(); // 返回端口号
        }

        @Override
        public boolean getRI() throws IOException {
            return false; // 返回RI状态
        }

        @Override
        public boolean getRTS() throws IOException {
            return true; // 返回RTS状态
        }

        @Override
        public void setRTS(boolean z) throws IOException {
            // 设置RTS状态，当前未实现具体逻辑
        }

        @Override
        public String getSerial() {
            return super.getSerial(); // 返回串口号
        }

        @Override
        public String toString() {
            return super.toString(); // 返回对象字符串表示
        }

        @Override
        public UsbSerialDriver getDriver() {
            return Cp21xxSerialDriver.this; // 返回驱动
        }

        /**
         * 设置单个配置。
         *
         * @param i 请求类型
         * @param i2 请求值
         * @return 返回控制传输的结果
         */
        private int setConfigSingle(int i, int i2) {
            return this.mConnection.controlTransfer(REQTYPE_HOST_TO_DEVICE, i, i2, 0, null, 0, 5000); // 执行控制传输
        }

        @Override
        public void open(UsbDeviceConnection usbDeviceConnection) throws IOException {
            if (this.mConnection == null) { // 检查连接是否已打开
                this.mConnection = usbDeviceConnection; // 设置连接
                for (int i = 0; i < this.mDevice.getInterfaceCount(); i++) { // 遍历设备接口
                    try {
                        if (this.mConnection.claimInterface(this.mDevice.getInterface(i), true)) { // 声明接口
                            MyLog.v(Cp21xxSerialDriver.TAG, "claimInterface " + i + " SUCCESS"); // 日志记录成功
                        } else {
                            MyLog.v(Cp21xxSerialDriver.TAG, "claimInterface " + i + " FAIL"); // 日志记录失败
                        }
                    } catch (Throwable th) {
                        try {
                            close(); // 关闭连接
                        } catch (IOException e) {
                            e.printStackTrace(); // 打印异常
                        }
                        throw new IOException("Failed to claim all interfaces", th); // 抛出异常
                    }
                }
                UsbInterface usbInterface = this.mDevice.getInterface(this.mDevice.getInterfaceCount() - 1); // 获取最后一个接口
                for (int i2 = 0; i2 < usbInterface.getEndpointCount(); i2++) { // 遍历接口的端点
                    UsbEndpoint endpoint = usbInterface.getEndpoint(i2);
                    if (endpoint.getType() == 2) { // 检查端点类型
                        if (endpoint.getDirection() == 128) { // 读取方向
                            this.mReadEndpoint = endpoint; // 设置读取端点
                        } else {
                            this.mWriteEndpoint = endpoint; // 设置写入端点
                        }
                    }
                }
                setConfigSingle(0, 1); // 设置配置
                setConfigSingle(7, 771); // 设置配置
                setConfigSingle(1, 384); // 设置配置
                return;
            }
            throw new IOException("Already opened."); // 抛出已打开异常
        }

        @Override
        public void close() throws IOException {
            if (this.mConnection != null) { // 检查连接是否已打开
                try {
                    setConfigSingle(0, 0); // 关闭配置
                    this.mConnection.close(); // 关闭连接
                } finally {
                    this.mConnection = null; // 清空连接
                }
            } else {
                throw new IOException("Already closed"); // 抛出已关闭异常
            }
        }

        @Override
        public int read(byte[] bArr, int i) throws IOException {
            synchronized (this.mReadBufferLock) { // 同步读取缓冲区
                int bulkTransfer = this.mConnection.bulkTransfer(this.mReadEndpoint, this.mReadBuffer, Math.min(bArr.length, this.mReadBuffer.length), i); // 执行批量传输
                if (bulkTransfer < 0) {
                    return 0; // 返回0表示读取失败
                }
                System.arraycopy(this.mReadBuffer, 0, bArr, 0, bulkTransfer); // 复制读取数据
                return bulkTransfer; // 返回读取的字节数
            }
        }

        @Override
        public int write(byte[] bArr, int i) throws IOException {
            int min; // 最小值
            byte[] bArr2; // 临时数组
            int bulkTransfer; // 批量传输结果
            int i2 = 0; // 已写入字节数
            while (i2 < bArr.length) { // 循环写入数据
                synchronized (this.mWriteBufferLock) { // 同步写入缓冲区
                    min = Math.min(bArr.length - i2, this.mWriteBuffer.length); // 计算最小写入长度
                    if (i2 == 0) {
                        bArr2 = bArr; // 第一次直接使用原数组
                    } else {
                        System.arraycopy(bArr, i2, this.mWriteBuffer, 0, min); // 复制数据到写入缓冲区
                        bArr2 = this.mWriteBuffer; // 使用写入缓冲区
                    }
                    bulkTransfer = this.mConnection.bulkTransfer(this.mWriteEndpoint, bArr2, min, i); // 执行批量传输
                }
                if (bulkTransfer > 0) { // 检查写入结果
                    MyLog.v(Cp21xxSerialDriver.TAG, "Wrote amt=" + bulkTransfer + " attempted=" + min); // 日志记录写入情况
                    i2 += bulkTransfer; // 更新已写入字节数
                } else {
                    throw new IOException("Error writing " + min + " bytes at offset " + i2 + " length=" + bArr.length); // 抛出写入错误异常
                }
            }
            return i2; // 返回总写入字节数
        }

        /**
         * 设置波特率。
         *
         * @param i 波特率值
         * @throws IOException 如果设置波特率失败
         */
        private void setBaudRate(int i) throws IOException {
            if (this.mConnection.controlTransfer(REQTYPE_HOST_TO_DEVICE, SILABSER_SET_BAUDRATE, 0, 0, new byte[]{(byte) (i & 255), (byte) ((i >> 8) & 255), (byte) ((i >> 16) & 255), (byte) ((i >> 24) & 255)}, 4, 5000) < 0) {
                throw new IOException("Error setting baud rate."); // 抛出设置波特率错误异常
            }
        }

        @Override
        public void setParameters(int i, int i2, int i3, int i4) throws IOException {
            setBaudRate(i); // 设置波特率
            int i5 = 2048; // 初始化参数
            if (i2 == 5) {
                i5 = 1280; // 设置参数
            } else if (i2 == 6) {
                i5 = 1536; // 设置参数
            } else if (i2 == 7) {
                i5 = 1792; // 设置参数
            }
            if (i4 == 1) {
                i5 |= 16; // 设置参数
            } else if (i4 == 2) {
                i5 |= 32; // 设置参数
            }
            if (i3 == 1) {
                i5 |= 0; // 设置参数
            } else if (i3 == 2) {
                i5 |= 2; // 设置参数
            }
            setConfigSingle(3, i5); // 设置配置
        }

        @Override
        public boolean purgeHwBuffers(boolean z, boolean z2) throws IOException {
            int i = 0; // 初始化
            int i2 = z ? 10 : 0; // 根据参数设置值
            if (z2) {
                i = 5; // 根据参数设置值
            }
            int i3 = i2 | i; // 合并设置值
            if (i3 == 0) {
                return true; // 如果没有需要清除的缓冲区，返回true
            }
            setConfigSingle(18, i3); // 执行清除操作
            return true; // 返回true表示成功
        }
    }
}