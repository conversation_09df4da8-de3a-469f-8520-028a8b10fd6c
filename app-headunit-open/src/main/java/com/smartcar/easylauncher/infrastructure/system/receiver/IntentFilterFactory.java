package com.smartcar.easylauncher.infrastructure.system.receiver;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.wifi.WifiManager;

public class IntentFilterFactory {

    //USB广播action
    public static IntentFilter usbCreate() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("android.hardware.usb.action.USB_DEVICE_DETACHED");
        intentFilter.addAction("android.hardware.usb.action.USB_DEVICE_ATTACHED");
        return intentFilter;
    }

    //酷我音乐广播action
    public static IntentFilter kuwoCreate() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.setPriority(Integer.MAX_VALUE);
        intentFilter.addAction("cn.kuwo.kwmusicauto.action.PLAYER_STATUS");
        intentFilter.addAction("cn.kuwo.kwmusicauto.action.PLAY_CLIENT_MUSICS");
        intentFilter.addAction("cn.kuwo.kwmusicauto.action.ENTER");
        intentFilter.addAction("cn.kuwo.kwmusicauto.action.AUDIOFOCUS");
        intentFilter.addAction("cn.kuwo.kwmusicauto.action.PLAY_END");
        intentFilter.addAction("cn.kuwo.kwmusicauto.action.PLAYER_MODE");
        intentFilter.addAction("cn.kuwo.kwmusicauto.action.PLAYER_MODE");
        intentFilter.addAction("cn.kuwo.kwmusicauto.action.EXIT");
        intentFilter.addAction("cn.kuwo.kwmusicauto.action.OPEN_TOAST");
        intentFilter.addAction("cn.kuwo.kwmusicauto.action.CLOSE_TOAST");
        intentFilter.addAction("cn.kuwo.kwmusicauto.action.MEDIA_BUTTON");
        return intentFilter;
    }

    //酷狗音乐广播action
    public static IntentFilter kugouCreate() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("com.kugou.android.auto.monitor.reconnect");
        intentFilter.addAction("com.kugou.android.auto.music.musicservicecommand.next");
        intentFilter.addAction("com.kugou.android.auto.music.musicservicecommand.pause");
        intentFilter.addAction("com.kugou.android.auto.music.musicservicecommand.previous");
        intentFilter.addAction("com.kugou.android.auto.music.musicservicecommand.play");
        return intentFilter;
    }

    //QQ音乐广播action
    public static IntentFilter qqCreate() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("com.tencent.qqmusiccar.action.PLAY_COMMAND_SEND_FOR_THIRD");
        return intentFilter;
    }

    //高德地图广播action
    public static IntentFilter gaodeCreate() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("AUTONAVI_STANDARD_BROADCAST_SEND");
        intentFilter.addAction("AUTONAVI_STANDARD_BROADCAST_RECV");
        return intentFilter;
    }

    //系统网络广播action
    public static IntentFilter networkCreate() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
        filter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
        filter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED);
        filter.addAction(WifiManager.RSSI_CHANGED_ACTION); //当 WiFi 信号强度发生变化时发送。
        filter.addAction(WifiManager.SUPPLICANT_STATE_CHANGED_ACTION); //当 WiFi 认证状态发生变化时发送，例如正在连接或已连接。
        filter.addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION); //当 WiFi 网络连接状态发生变化时发送，例如连接到一个新的 WiFi 网络或断开连接。
        filter.addAction(WifiManager.WIFI_STATE_CHANGED_ACTION); //当 WiFi 的状态发生变化时发送，例如打开或关闭 WiFi。
        filter.addAction("android.net.conn.CONNECTIVITY_CHANGE");
        filter.addAction("android.intent.action.SIG_STR");//手机信号强度
        return filter;
    }


    //系统熄屏亮屏广播action
    public static IntentFilter screenCreate() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Intent.ACTION_SCREEN_OFF);
        intentFilter.addAction(Intent.ACTION_SCREEN_ON);
        return intentFilter;
    }

    //系统休眠唤醒广播action
    public static IntentFilter sleepCreate() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Intent.ACTION_DREAMING_STARTED);
        intentFilter.addAction(Intent.ACTION_DREAMING_STOPPED);
        intentFilter.addAction(Intent.ACTION_DREAMING_STARTED);
        intentFilter.addAction(Intent.ACTION_DREAMING_STOPPED);
        intentFilter.addAction(Intent.ACTION_DREAMING_STARTED);
        intentFilter.addAction(Intent.ACTION_DREAMING_STOPPED);
        intentFilter.addAction(Intent.ACTION_DREAMING_STARTED);
        intentFilter.addAction(Intent.ACTION_DREAMING_STOPPED);
        intentFilter.addAction(Intent.ACTION_DREAMING_STARTED);
        intentFilter.addAction(Intent.ACTION_DREAMING_STOPPED);
        intentFilter.addAction(Intent.ACTION_DREAMING_STARTED);
        intentFilter.addAction(Intent.ACTION_DREAMING_STOPPED);
        intentFilter.addAction(Intent.ACTION_DREAMING_STARTED);
        intentFilter.addAction(Intent.ACTION_DREAMING_STOPPED);
        intentFilter.addAction(Intent.ACTION_DREAMING_STARTED);
        intentFilter.addAction(Intent.ACTION_DREAMING_STOPPED);
        return intentFilter;
    }


    //应用程序安装卸载更新广播action
    public static IntentFilter appCreate() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Intent.ACTION_PACKAGE_ADDED);
        intentFilter.addAction(Intent.ACTION_PACKAGE_REMOVED);
        intentFilter.addAction(Intent.ACTION_PACKAGE_REPLACED);
        intentFilter.addAction(Intent.ACTION_BOOT_COMPLETED);
        intentFilter.addAction(Intent.ACTION_SCREEN_ON);
        intentFilter.addAction(Intent.ACTION_SCREEN_OFF);
        intentFilter.addDataScheme("package");
        return intentFilter;
    }

    //系统状态广播action
    public static IntentFilter systemCreate() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("android.intent.action.BOOT_COMPLETED");
        intentFilter.addAction("android.intent.action.ACTION_POWER_CONNECTED");
        intentFilter.addAction("android.intent.action.ACTION_POWER_DISCONNECTED");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN");
        intentFilter.addAction("android.intent.action.ACTION_REBOOT");
        intentFilter.addAction("android.intent.action.ACTION_PRE_SHUTDOWN");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO_BOOTUP");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO_SHUTDOWN");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO_SHUTDOWN");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO_BOOTUP");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO_SHUTDOWN");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO_BOOTUP");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO_SHUTDOWN");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO_BOOTUP");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO_SHUTDOWN");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO_BOOTUP");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO_SHUTDOWN");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO_BOOTUP");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO_SHUTDOWN");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO_BOOTUP");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO_SHUTDOWN");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO_BOOTUP");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO_SHUTDOWN");
        intentFilter.addAction("android.intent.action.ACTION_SHUTDOWN_IPO");
        return intentFilter;
    }


}
