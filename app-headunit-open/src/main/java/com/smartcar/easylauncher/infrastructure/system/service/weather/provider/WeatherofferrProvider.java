package com.smartcar.easylauncher.infrastructure.system.service.weather.provider;


import androidx.annotation.NonNull;

import com.elvishew.xlog.XLog;
import com.smartcar.easylauncher.data.processor.WeatherDataProcessor;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import rxhttp.RxHttp;

import java.util.List;

import com.google.gson.Gson;
import com.smartcar.easylauncher.data.model.navigation.LocateInforModel;
import com.smartcar.easylauncher.data.model.weather.CityListResponse;
import com.smartcar.easylauncher.data.model.weather.WeatherDataModel;
import com.smartcar.easylauncher.data.model.weather.WeatherofferResponse;
import com.smartcar.easylauncher.infrastructure.system.service.weather.ErrorCode;
import com.smartcar.easylauncher.infrastructure.system.service.weather.WeatherCallback;
import com.smartcar.easylauncher.infrastructure.system.service.weather.WeatherError;
import com.smartcar.easylauncher.infrastructure.system.service.weather.WeatherProvider;

/**
 * OpenWeather API实现
 * 实现OpenWeatherMap的天气数据获取
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-03-21
 */
public class WeatherofferrProvider implements WeatherProvider {

    /**
     * 日志标签
     */
    private static final String TAG = WeatherofferrProvider.class.getSimpleName();

    /**
     * API基础URL
     */
    private static final String BASE_URL = "https://weatheroffer.com/api/extfans";

    /**
     * 提供者优先级
     */
    private static final int PRIORITY = 1;

    /**
     * RxJava销毁器
     */
    private final CompositeDisposable disposables;
    private String currentCid;  // 存储当前城市的 cid

    public WeatherofferrProvider() {
        this.disposables = new CompositeDisposable();
    }

    @Override
    public int getPriority() {
        return PRIORITY;
    }

    @NonNull
    @Override
    public String getProviderName() {
        return "WeatherOffer";
    }

    @Override
    public void fetchWeatherData(@NonNull LocateInforModel location, @NonNull WeatherCallback callback) {
        XLog.tag(TAG).i("开始获取WeatherOffer天气数据 - 城市: %s", location.getDistrict());
        
        // 检查位置信息
        if (!isValidLocation(location)) {
            XLog.tag(TAG).e("无效的位置信息: city=%s", location.getDistrict());
            callback.onError(new WeatherError(ErrorCode.INVALID_LOCATION, "无效的位置信息"));
            return;
        }

        // 先获取城市 cid
        fetchCityId(location, callback);
    }

    /**
     * 获取城市ID
     */
    private void fetchCityId(LocateInforModel location, WeatherCallback callback) {
        try {
            //这里通过更细致的区县来获取
            String encodedCity = java.net.URLEncoder.encode(location.getDistrict(), "UTF-8");
            String url = String.format("%s/city/list?lang=zh-CN&searchkey=%s", BASE_URL, encodedCity);

            XLog.tag(TAG).d("请求城市列表数据: %s", url);

            disposables.add(RxHttp.get(url)
                    .connectTimeout(30000)
                    .toObservable(CityListResponse.class)
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(
                            response -> {
                                if (response.isSuccess() && response.getCities() != null && !response.getCities().isEmpty()) {
                                    // 找到最匹配的城市
                                    CityListResponse.CityInfo matchedCity = findMatchingCity(response.getCities(), location);
                                    if (matchedCity != null) {
                                        XLog.tag(TAG).d("找到匹配的城市 - CID: %s, 城市: %s", 
                                            matchedCity.getCid(), matchedCity.getCity());
                                        currentCid = matchedCity.getCid();
                                        // 获取天气数据
                                        fetchWeatherWithCid(location, callback);
                                    } else {
                                        XLog.tag(TAG).e("未找到匹配的城市信息");
                                        callback.onError(new WeatherError(ErrorCode.INVALID_LOCATION, "未找到匹配的城市信息"));
                                    }
                                } else {
                                    XLog.tag(TAG).e("城市列表数据无效");
                                    callback.onError(new WeatherError(ErrorCode.API_CALL_FAILED, "城市列表数据无效"));
                                }
                            },
                            throwable -> {
                                XLog.tag(TAG).e("获取城市列表失败: %s", throwable.getMessage());
                                callback.onError(new WeatherError(ErrorCode.API_CALL_FAILED, 
                                    "获取城市列表失败: " + throwable.getMessage()));
                            }
                    ));

        } catch (Exception e) {
            XLog.tag(TAG).e("请求城市列表异常: %s", e.getMessage());
            callback.onError(new WeatherError(ErrorCode.API_CALL_FAILED, 
                "请求城市列表异常: " + e.getMessage()));
        }
    }

    /**
     * 在城市列表中找到最匹配的城市
     */
    private CityListResponse.CityInfo findMatchingCity(List<CityListResponse.CityInfo> cities, 
                                                     LocateInforModel location) {
        // 优先匹配区县
        for (CityListResponse.CityInfo city : cities) {
            if (city.getCity().equals(location.getDistrict())) {
                return city;
            }
        }
        
        // 如果没有找到区县，则匹配城市
        for (CityListResponse.CityInfo city : cities) {
            if (city.getCity().equals(location.getCity())) {
                return city;
            }
        }
        
        // 如果都没有找到，返回第一个城市
        return cities.isEmpty() ? null : cities.get(0);
    }

    private void fetchWeatherWithCid(LocateInforModel location, WeatherCallback callback) {
        try {
            String url = String.format("%s/weather/forecast?lang=zh-CN&cid=%s", BASE_URL, currentCid);
            XLog.tag(TAG).d("请求天气数据: %s", url);

            disposables.add(RxHttp.get(url)
                    .connectTimeout(30000)
                    .toObservable(WeatherofferResponse.class)
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(
                            response -> {
                                try {
                                    XLog.tag(TAG).d("收到天气数据响应: %s", new Gson().toJson(response));
                                    
                                    if (response.isSuccess() && response.getForecast() != null 
                                            && response.getForecast().getItems() != null 
                                            && !response.getForecast().getItems().isEmpty()) {
                                        
                                        // 使用 WeatherDataProcessor 处理数据
                                        WeatherDataProcessor processor = new WeatherDataProcessor();
                                        
                                        XLog.tag(TAG).d("开始处理天气数据 - 城市: %s, 区县: %s, 经度: %f, 纬度: %f", 
                                            location.getCity(), location.getDistrict(), location.getLongitude(), location.getLatitude());
                                        
                                        WeatherDataModel weatherData = processor.processWeatherOfferData(
                                            location.getCity(),
                                            location.getDistrict(),
                                            location.getLatitude(),
                                            location.getLongitude(),
                                            getProviderName(),
                                            response
                                        );
                                        
                                        if (weatherData != null) {
                                            XLog.tag(TAG).i("天气数据处理完成: %s", new Gson().toJson(weatherData));
                                            callback.onWeatherDataReceived(weatherData);
                                        } else {
                                            XLog.tag(TAG).e("天气数据处理返回null");
                                            callback.onError(new WeatherError(ErrorCode.DATA_PARSE_ERROR, 
                                                "天气数据处理失败: 返回null"));
                                        }
                                    } else {
                                        XLog.tag(TAG).e("天气数据无效: %s", new Gson().toJson(response));
                                        callback.onError(new WeatherError(ErrorCode.API_CALL_FAILED, 
                                            "天气数据无效: " + new Gson().toJson(response)));
                                    }
                                } catch (Exception e) {
                                    XLog.tag(TAG).e("处理天气数据时发生异常: %s", e.getMessage());
                                    e.printStackTrace();
                                    callback.onError(new WeatherError(ErrorCode.DATA_PARSE_ERROR, 
                                        "处理天气数据时发生异常: " + e.getMessage()));
                                }
                            },
                            throwable -> {
                                XLog.tag(TAG).e("获取天气数据失败: %s", throwable.getMessage());
                                throwable.printStackTrace();
                                callback.onError(new WeatherError(ErrorCode.API_CALL_FAILED, 
                                    "获取天气数据失败: " + throwable.getMessage()));
                            }
                    ));

        } catch (Exception e) {
            XLog.tag(TAG).e("请求天气数据异常: %s", e.getMessage());
            e.printStackTrace();
            callback.onError(new WeatherError(ErrorCode.API_CALL_FAILED, 
                "请求天气数据异常: " + e.getMessage()));
        }
    }

    @Override
    public boolean isAvailable() {
        return true; // WeatherOffer 不需要 API key
    }

    @Override
    public String getApiKey() {
        return "";
    }

    private boolean isValidLocation(LocateInforModel location) {
        return location != null && location.getCity() != null && !location.getCity().isEmpty();
    }

    /**
     * 清理资源
     */
    public void destroy() {
        XLog.tag(TAG).d("清理WeatherOffer提供者资源");
        disposables.clear();
    }
}