
package com.smartcar.easylauncher.infrastructure.system.receiver;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.telephony.PhoneStateListener;
import android.telephony.SignalStrength;
import android.telephony.TelephonyManager;

import com.smartcar.easylauncher.app.App;
import com.smartcar.easylauncher.infrastructure.event.cody.NetworkInfoScopeBus;
import com.smartcar.easylauncher.infrastructure.event.cody.NetworkStatusScopeBus;
import com.smartcar.easylauncher.infrastructure.event.scope.device.cody.BluetoothInfoScopeBus;
import com.smartcar.easylauncher.data.model.network.NetworkModel;
import com.smartcar.easylauncher.data.model.network.NetworkStatusModel;
import com.smartcar.easylauncher.data.model.system.BluetoothDeviceModel;
import com.smartcar.easylauncher.shared.utils.AppUtils;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.NetworkUtils;
import com.smartcar.easylauncher.shared.utils.system.SystemNetworkUtils;
import com.smartcar.easylauncher.shared.utils.thread.ThreadPoolUtil;

/**
 * monitor apps installed and uninstalled
 * 系统网络状态监控
 */

public class SystemNetworkReceiver extends BroadcastReceiver {
    private static final String TAG = "SystemNetworkReceiver";
    public static boolean network = false;
    private static Context mContext = App.getContextInstance();


    @Override
    public void onReceive(Context context, Intent intent) {
        //更新蓝牙状态
        switch (intent.getAction()) {
            case BluetoothDevice.ACTION_ACL_CONNECTED:
                MyLog.d(TAG, "接收到了蓝牙设备连接的消息");
                final BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                if (device != null) {
                    BluetoothInfoScopeBus.eventBean().post(new BluetoothDeviceModel(device.getName(), device.getAddress()));
                }
                break;
            case BluetoothAdapter.ACTION_STATE_CHANGED:
                BluetoothAdapter mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
                if (mBluetoothAdapter == null) {
                    NetworkInfoScopeBus.eventBean().post(new NetworkModel(SystemNetworkUtils.INSTANCE.getBLE_OFF(),
                            SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_BLE()));
                } else {
                    if (mBluetoothAdapter.isEnabled()) {
                        NetworkInfoScopeBus.eventBean().post(new NetworkModel(SystemNetworkUtils.INSTANCE.getBLE_NO(),
                                SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_BLE()));
                    } else {
                        NetworkInfoScopeBus.eventBean().post(new NetworkModel(SystemNetworkUtils.INSTANCE.getBLE_OFF(),
                                SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_BLE()));
                    }
                }
                break;
            case ConnectivityManager.CONNECTIVITY_ACTION:
                //获取网络类型
                checkNetwork();
                updateNetworkStatus(context);
                //MyLog.d(TAG, "获取网络类型    " + intent.getAction());
                break;
            case "android.intent.action.SIG_STR":
                //刷新网络信号
                updateNetworkStatus(context);
                MyLog.d(TAG, "刷新网络信息    " + intent.getAction());
                break;
            default:
        }

    }

    private void updateNetworkStatus(Context context) {
        //WiFi
        ConnectivityManager mConnectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        MyLog.v(TAG, "执行次数");

        NetworkInfo info = mConnectivityManager.getActiveNetworkInfo();
        if (info != null) {
       //     MyLog.v(TAG, "getTypeName(): " + info.getTypeName());
       //     MyLog.v(TAG, "getSubtypeName(): " + info.getSubtypeName());
            if (info.getType() == ConnectivityManager.TYPE_WIFI) {  //WiFi网络
                getWifiSignalLevelDesc(context);
            } else if (info.getType() == ConnectivityManager.TYPE_MOBILE) {   //移动网络
                switch (info.getSubtype()) {
                    case TelephonyManager.NETWORK_TYPE_GPRS:
                    case TelephonyManager.NETWORK_TYPE_EDGE:
                    case TelephonyManager.NETWORK_TYPE_CDMA:
                    case TelephonyManager.NETWORK_TYPE_1xRTT:
                    case TelephonyManager.NETWORK_TYPE_IDEN:
                        NetworkInfoScopeBus.eventBean().post(new NetworkModel(SystemNetworkUtils.INSTANCE.getTYPE_IDEN(), true,
                                true, SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_MOBILE()));
                        break;
                    case TelephonyManager.NETWORK_TYPE_UMTS:
                    case TelephonyManager.NETWORK_TYPE_EVDO_0:
                    case TelephonyManager.NETWORK_TYPE_EVDO_A:
                    case TelephonyManager.NETWORK_TYPE_HSDPA:
                    case TelephonyManager.NETWORK_TYPE_HSUPA:
                    case TelephonyManager.NETWORK_TYPE_HSPA:
                    case TelephonyManager.NETWORK_TYPE_EVDO_B:
                    case TelephonyManager.NETWORK_TYPE_EHRPD:
                    case TelephonyManager.NETWORK_TYPE_HSPAP:
                        NetworkInfoScopeBus.eventBean().post(new NetworkModel(SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_HSPAP(), true,
                                true, SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_MOBILE()));
                        break;
                    case TelephonyManager.NETWORK_TYPE_LTE:
                        NetworkInfoScopeBus.eventBean().post(new NetworkModel(SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_LTE(), true,
                                true, SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_MOBILE()));
                        break;
                    default:
                        NetworkInfoScopeBus.eventBean().post(new NetworkModel(SystemNetworkUtils.INSTANCE.getNO_NETWORK(), false,
                                false, SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_MOBILE()));
                        break;
                }
            }
        } else {   //没有网络
        //    MyLog.v(TAG, "发送的时候的网络状态  " + false);
            NetworkInfoScopeBus.eventBean().post(new NetworkModel(SystemNetworkUtils.INSTANCE.getNO_NETWORK(), false,
                    false, SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_MOBILE()));
            NetworkInfoScopeBus.eventBean().post(new NetworkModel(SystemNetworkUtils.INSTANCE.getNO_NETWORK(), false,
                    SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_WIFI()));

        }

    }

    public int getNetworkType(Context context) {
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        if (telephonyManager != null) {
            return telephonyManager.getNetworkType();
        }
        return TelephonyManager.NETWORK_TYPE_UNKNOWN;
    }


    private void getMobileNetworkSignal() {
        if (!AppUtils.hasSimCard(mContext)) {
            MyLog.v(TAG, "没有SIM卡    ");
            return;
        }
        TelephonyManager mTelephonyManager = (TelephonyManager) mContext.getSystemService(Context.TELEPHONY_SERVICE);
        if (mTelephonyManager != null) {
            mTelephonyManager.listen(new PhoneStateListener() {

                @Override
                public void onSignalStrengthsChanged(SignalStrength signalStrength) {
                    super.onSignalStrengthsChanged(signalStrength);
                    int asu = signalStrength.getGsmSignalStrength();
                    int lastSignal = -113 + 2 * asu;
                    if (lastSignal > 0) {
                        String mobileNetworkSignal = lastSignal + "dBm";
                        MyLog.v(TAG, "信号强度    " + mobileNetworkSignal);
                    }
                    MyLog.v(TAG, "Current mobileNetworkSignal：" + lastSignal + " dBm");
                }
            }, PhoneStateListener.LISTEN_SIGNAL_STRENGTHS);
        }
    }


    public static int getNetWorkType(Context context) {
        int mNetWorkType = -1;
        ConnectivityManager manager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo networkInfo = manager.getActiveNetworkInfo();
        if (networkInfo != null && networkInfo.isConnected()) {
            String type = networkInfo.getTypeName();
            if ("WIFI".equalsIgnoreCase(type)) {
                mNetWorkType = 0;
            } else if ("MOBILE".equalsIgnoreCase(type)) {
                return isFastMobileNetwork(context);
            }
        } else {
            mNetWorkType = 1;//没有网络
        }
        return mNetWorkType;
    }

    /**
     * 判断网络类型
     */
    private static int isFastMobileNetwork(Context context) {
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);

        switch (telephonyManager.getNetworkType()) {
            case TelephonyManager.NETWORK_TYPE_GPRS:
            case TelephonyManager.NETWORK_TYPE_EDGE:
            case TelephonyManager.NETWORK_TYPE_CDMA:
            case TelephonyManager.NETWORK_TYPE_1xRTT:
            case TelephonyManager.NETWORK_TYPE_IDEN:
                return 2;
            case TelephonyManager.NETWORK_TYPE_UMTS:
            case TelephonyManager.NETWORK_TYPE_EVDO_0:
            case TelephonyManager.NETWORK_TYPE_EVDO_A:
            case TelephonyManager.NETWORK_TYPE_HSDPA:
            case TelephonyManager.NETWORK_TYPE_HSUPA:
            case TelephonyManager.NETWORK_TYPE_HSPA:
            case TelephonyManager.NETWORK_TYPE_EVDO_B:
            case TelephonyManager.NETWORK_TYPE_EHRPD:
            case TelephonyManager.NETWORK_TYPE_HSPAP:
                return 3;
            case TelephonyManager.NETWORK_TYPE_LTE:
            //    MyLog.v(TAG, "isInternetAvailable    " + "4G");
                return 4;
            default:
            //    MyLog.v(TAG, "isInternetAvailable    " + " 没有网络");
                return 1;
        }
    }

    /**
     * 获取当前连接的Wifi信号强度等级的中文描述
     *
     * @param context 上下文
     * @return 信号强度等级的中文描述
     */
    public void getWifiSignalLevelDesc(Context context) {
        // 获取WifiManager实例
        WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        // 获取当前连接的WifiInfo
        WifiInfo wifiInfo = wifiManager.getConnectionInfo();
        // 获取当前连接的信号强度等级
        MyLog.v(TAG, "wifiInfo.getRssi()    " + wifiInfo.getRssi());
        int level = WifiManager.calculateSignalLevel(wifiInfo.getRssi(), 5);
        MyLog.v(TAG, "level    " + level);
        signalLevel(level);
    }

    private void signalLevel(int level) {

        // 将信号强度等级转换为中文描述
        String levelDesc;
        switch (level) {
            case 0:
                levelDesc = "无信号";
                NetworkInfoScopeBus.eventBean().post(new NetworkModel(SystemNetworkUtils.INSTANCE.getNO_NETWORK(), false,
                        SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_WIFI()));

                break;
            case 1:
                NetworkInfoScopeBus.eventBean().post(new NetworkModel(SystemNetworkUtils.INSTANCE.getLEVEL_1(), true,
                        SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_WIFI()));

                levelDesc = "极弱";
                break;
            case 2:
                NetworkInfoScopeBus.eventBean().post(new NetworkModel(SystemNetworkUtils.INSTANCE.getLEVEL_2(), true,
                        SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_WIFI()));

                levelDesc = "较弱";
                break;
            case 3:
                NetworkInfoScopeBus.eventBean().post(new NetworkModel(SystemNetworkUtils.INSTANCE.getLEVEL_3(), true,
                        SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_WIFI()));

                levelDesc = "良好";

                break;
            case 4:
                NetworkInfoScopeBus.eventBean().post(new NetworkModel(SystemNetworkUtils.INSTANCE.getLEVEL_4(), true,
                        SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_WIFI()));

                levelDesc = "强";
                break;
            default:
                levelDesc = "未知";
                NetworkInfoScopeBus.eventBean().post(new NetworkModel(SystemNetworkUtils.INSTANCE.getNO_NETWORK(), false,
                        SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_WIFI()));

                break;
        }
    }

    /**
     * 发送网络是否可以的方法
     */
    private void checkNetwork() {
        ThreadPoolUtil.getThreadPoolExecutor().submit(new Runnable() {
            @Override
            public void run() {
                if ( NetworkUtils.isInternetAvailableWithTimeoutV3()) {
                    network = true;
                    NetworkStatusScopeBus.eventBean().post(new NetworkStatusModel(network,
                            SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_IS_NETWORK()));
                } else {
                    network = false;
                    NetworkStatusScopeBus.eventBean().post(new NetworkStatusModel(network,
                            SystemNetworkUtils.INSTANCE.getNETWORK_TYPE_IS_NETWORK()));
                }
            }
        });

    }


}
