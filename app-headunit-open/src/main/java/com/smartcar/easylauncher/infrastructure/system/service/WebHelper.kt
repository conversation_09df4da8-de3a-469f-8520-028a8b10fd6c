package com.smartcar.easylauncher.infrastructure.system.service

import android.content.Context
import com.koushikdutta.async.http.server.AsyncHttpServer
import com.koushikdutta.async.AsyncServer
import com.koushikdutta.async.http.server.AsyncHttpServerRequest
import com.koushikdutta.async.http.server.AsyncHttpServerResponse
import org.json.JSONArray
import org.json.JSONObject
import org.json.JSONException
import com.koushikdutta.async.http.body.UrlEncodedFormBody
import com.jeremyliao.liveeventbus.LiveEventBus
import com.koushikdutta.async.http.body.MultipartFormDataBody
import com.koushikdutta.async.DataEmitter
import com.koushikdutta.async.ByteBufferList
import com.koushikdutta.async.callback.CompletedCallback
import kotlin.Throws
import android.text.TextUtils
import android.os.Environment
import android.util.Log
import com.smartcar.easylauncher.shared.utils.apportutil.get
import com.smartcar.easylauncher.shared.utils.apportutil.mmkv
import com.demon.qfsolution.utils.getExternalOrFilesDirPath
import com.koushikdutta.async.http.body.Part
import com.smartcar.easylauncher.app.App
import com.smartcar.easylauncher.core.constants.Constants
import java.io.*
import java.lang.Exception
import java.net.URLDecoder
import java.net.URLEncoder
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import android.os.Handler
import android.os.Looper
import android.util.Base64
import java.lang.ref.WeakReference

/**
 * <AUTHOR>
 * @date 2022/6/29
 * @email <EMAIL>
 * @desc
 */
class WebHelper private constructor() {
    // 修改为可空的 FileUploadHolder
    private var fileUploadHolder: FileUploadHolder? = null
    private val server: AsyncHttpServer?
    private val mAsyncServer: AsyncServer?
    var dir: File

    // 添加传输状态相关的变量
    private var transferSpeed: Long = 0
    private var receivedFiles: Int = 0
    private var totalSize: Long = 0
    private var lastUpdateTime: Long = 0
    private var lastSize: Long = 0

    // 添加新的状态变量
    private var currentFileSize: Long = 0      // 当前文件总大小
    private var currentReceived: Long = 0      // 当前接收大小
    private var speedSamples = mutableListOf<Pair<Long, Long>>() // 用于平滑速度计算
    private val SPEED_SAMPLE_COUNT = 5         // 保留最近5个样本用于计算平均速度

    // 修改状态回调，添加进度参数
    private var onTransferStatusUpdate: ((speed: Long, files: Int, totalSize: Long, progress: Float) -> Unit)? = null

    private var lastSpeedUpdateTime: Long = 0
    private var lastSpeedUpdateSize: Long = 0
    private var currentSpeed: Long = 0

    private var isTransferring = false  // 添加传输状态标志

    // 添加文件上传管理器
    private val uploadManager = FileUploadManager()

    init {
        // 移除 fileUploadHolder 的初始化
        server = AsyncHttpServer()
        mAsyncServer = AsyncServer()
        val def: String = App.getContextInstance().getExternalOrFilesDirPath(Environment.DIRECTORY_DCIM)
        dir = File(mmkv.get(Constants.MMKV_STORAGE_PATH, def))
    }

    fun startServer(context: Context) {
        // 使用ApplicationContext而不是Activity或Service的Context
        val appContext = context.applicationContext
        
        // 重置传输状态
        resetTransferStatus()
        
        server?.get("/images/.*") { request: AsyncHttpServerRequest, response: AsyncHttpServerResponse -> sendResources(appContext, request, response) }
        server?.get("/scripts/.*") { request: AsyncHttpServerRequest, response: AsyncHttpServerResponse -> sendResources(appContext, request, response) }
        server?.get("/css/.*") { request: AsyncHttpServerRequest, response: AsyncHttpServerResponse -> sendResources(appContext, request, response) }
        //index page
        server?.get("/") { request: AsyncHttpServerRequest?, response: AsyncHttpServerResponse ->
            try {
                response.send(getIndexContent(appContext))
            } catch (e: IOException) {
                e.printStackTrace()
                response.code(500).end()
            }
        }
        //query upload list
        server?.get("/files") { request: AsyncHttpServerRequest?, response: AsyncHttpServerResponse ->
            Log.d(TAG, "=== Start processing /files request ===")
            
            val array = JSONArray()
            
            try {
                // 获取查询参数，默认按时间排序
                val searchQuery = request?.query?.getString("search")
                val sortBy = request?.query?.getString("sort") ?: "time"  // 默认为时间排序
                
                Log.d(TAG, "Search query: '$searchQuery', Sort by: '$sortBy'")
                
                if (dir.exists() && dir.isDirectory) {
                    // 获取所有文件
                    var files = dir.listFiles()?.filter { it.exists() && it.isFile }?.toTypedArray()
                    
                    // 如果有搜索参数，过滤文件
                    if (!searchQuery.isNullOrEmpty()) {
                        val lowerQuery = searchQuery.lowercase()
                        files = files?.filter {
                            it.name.lowercase().contains(lowerQuery)
                        }?.toTypedArray()
                    }
                    
                    // 转换文件列表为 JSONArray
                    files?.forEach { file ->
                        try {
                            val jsonObject = JSONObject().apply {
                                put("name", file.name)
                                put("lastModified", file.lastModified())
                                put("time", SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                                    .format(Date(file.lastModified())))
                                val fileLen = file.length()
                                val df = DecimalFormat("0.00")
                                put("size", when {
                                    fileLen > 1024 * 1024 -> df.format(fileLen * 1f / 1024 / 1024) + "MB"
                                    fileLen > 1024 -> df.format(fileLen * 1f / 1024) + "KB"
                                    else -> fileLen.toString() + "B"
                                })
                                put("rawSize", fileLen)
                            }
                            array.put(jsonObject)
                        } catch (e: JSONException) {
                            Log.e(TAG, "Error creating JSON for file ${file.name}: ${e.message}")
                        }
                    }
                    
                    // 根据排序参数对文件列表进行排序
                    val sortedArray = when (sortBy) {
                        "name" -> {
                            Log.d(TAG, "Sorting by name")
                            val sorted = array.toList<JSONObject>()
                                .sortedBy { it.getString("name").toLowerCase() }
                            JSONArray(sorted)
                        }
                        "size" -> {
                            Log.d(TAG, "Sorting by size")
                            val sorted = array.toList<JSONObject>()
                                .sortedByDescending { it.getLong("rawSize") }
                            JSONArray(sorted)
                        }
                        else -> {  // 默认按时间排序（包括 "time" 和其他未知值）
                            Log.d(TAG, "Sorting by time (default)")
                            val sorted = array.toList<JSONObject>()
                                .sortedByDescending { it.getLong("lastModified") }
                            JSONArray(sorted)
                        }
                    }
                    
                    response.send(sortedArray.toString())
                } else {
                    response.send("[]")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error processing request: ${e.message}")
                e.printStackTrace()
                response.code(500).send("Error processing request")
            }
            
            Log.d(TAG, "=== End processing /files request ===")
        }
        //delete
        server?.post("/files/.*") { request: AsyncHttpServerRequest, response: AsyncHttpServerResponse ->
            try {
                val body = request.body as UrlEncodedFormBody
                if ("delete".equals(body.get().getString("_method"), ignoreCase = true)) {
                    var path: String? = request.path.replace("/files/", "")
                    try {
                        path = URLDecoder.decode(path, "utf-8")
                        val file = File(dir, path)
                        if (file.exists() && file.isFile) {
                            val deleted = file.delete()
                            if (deleted) {
                                Log.d(TAG, "File deleted successfully: $path")
                                LiveEventBus.get<Any>(Constants.LOAD_BOOK_LIST).post(0)
                                response.code(200).send("File deleted")
                            } else {
                                Log.e(TAG, "Failed to delete file: $path")
                                response.code(500).send("Failed to delete file")
                            }
                        } else {
                            Log.e(TAG, "File not found: $path")
                            response.code(404).send("File not found")
                        }
                    } catch (e: UnsupportedEncodingException) {
                        Log.e(TAG, "Error decoding file path: ${e.message}")
                        response.code(400).send("Invalid file name")
                    } catch (e: Exception) {
                        Log.e(TAG, "Error deleting file: ${e.message}")
                        response.code(500).send("Internal server error")
                    }
                } else {
                    response.code(400).send("Invalid method")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error processing delete request: ${e.message}")
                response.code(500).send("Internal server error")
            }
        }
        //download
        server?.get("/files/.*") { request: AsyncHttpServerRequest, response: AsyncHttpServerResponse ->
            var path: String? = request.path.replace("/files/", "")
            try {
                path = URLDecoder.decode(path, "utf-8")
            } catch (e: UnsupportedEncodingException) {
                e.printStackTrace()
            }
            val file = File(dir, path)
            if (file.exists() && file.isFile) {
                try {
                    response.headers.add("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.name, "utf-8"))
                } catch (e: UnsupportedEncodingException) {
                    e.printStackTrace()
                }
                response.sendFile(file)
            } else {
                response.code(404).send("Not found!")
            }
        }
        //upload
        server?.post("/files") { request: AsyncHttpServerRequest, response: AsyncHttpServerResponse ->
            try {
                val body = request.body as MultipartFormDataBody
                Log.d(TAG, "Receiving file upload request")
                
                var currentFileName: String? = null
                var currentHolder: FileUploadHolder? = null
                
                body.setMultipartCallback { part: Part ->
                    if (part.isFile) {
                        try {
                            // 1. 获取原始文件名和请求头中的原始文件名
                            val rawFileName = part.filename
                            val originalName = request.headers.get("X-Original-Name")
                            
                            Log.d(TAG, "Raw filename from part: $rawFileName")
                            Log.d(TAG, "Original name from header: $originalName")
                            
                            // 2. 尝试多种解码方式
                            currentFileName = try {
                                // 如果有原始文件名header，优先使用它
                                if (!originalName.isNullOrEmpty()) {
                                    String(Base64.decode(originalName, Base64.DEFAULT))
                                } else {
                                    // 否则尝试多种解码方式
                                    val decoded = URLDecoder.decode(rawFileName, "UTF-8")
                                    when {
                                        isValidUtf8(decoded) -> decoded
                                        else -> {
                                            // 尝试不同的字符集
                                            val bytes = rawFileName.toByteArray(Charsets.ISO_8859_1)
                                            String(bytes, Charsets.UTF_8)
                                        }
                                    }
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "Error decoding filename: ${e.message}")
                                // 如果所有解码方式都失败，使用原始文件名
                                rawFileName
                            }
                            
                            Log.d(TAG, "Final decoded filename: $currentFileName")
                            
                            var fileSize = part.length()
                            if (fileSize <= 0) {
                                fileSize = request.headers.get("Content-Length")?.toLongOrNull() ?: -1
                            }
                            
                            currentHolder = uploadManager.createUpload(currentFileName!!, fileSize)
                            onFileUploadStart(fileSize)
                            
                            body.setDataCallback { emitter: DataEmitter?, bb: ByteBufferList ->
                                try {
                                    val data = bb.allByteArray
                                    currentHolder?.write(data)
                                    bb.recycle()
                                } catch (e: Exception) {
                                    Log.e(TAG, "Error processing file data: ${e.message}")
                                    response.code(500).send("Upload failed")
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error processing file: ${e.message}")
                            throw e
                        }
                    } else {
                        // 当前部分不是文件，完成当前文件的处理
                        currentHolder?.let { holder ->
                            Log.d(TAG, "📁 非文件部分，关闭当前文件: ${holder.fileName}")
                            holder.close()
                            // removeUpload 已在 close() 方法中的 onFileUploadComplete 里调用，不要重复调用
                        }
                        currentHolder = null
                        currentFileName = null
                    }
                }
                
                request.setEndCallback { e: Exception? ->
                    try {
                        if (e == null) {
                            // 确保最后一个文件也被正确处理
                            currentHolder?.let { holder ->
                                Log.d(TAG, "📁 请求结束，关闭最后文件: ${holder.fileName}")
                                holder.close()
                                // removeUpload 已在 close() 方法中的 onFileUploadComplete 里调用，不要重复调用
                            }

                            // 不再发送全量刷新事件，由增量更新机制处理
                            response.send("Upload complete")
                        } else {
                            Log.e(TAG, "Upload error: ${e.message}")
                            response.code(500).send("Upload failed")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error completing upload: ${e.message}")
                        response.code(500).send("Upload failed")
                    } finally {
                        // 重置状态
                        currentHolder = null
                        currentFileName = null
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in file upload: ${e.message}")
                response.code(500).send("Upload failed: ${e.message}")
            }
        }
        server?.get("/progress/.*") { request: AsyncHttpServerRequest, response: AsyncHttpServerResponse ->
            val res = JSONObject()
            val path = request.path.replace("/progress/", "")
            
            try {
                uploadManager.getUpload(path)?.let { holder ->
                    res.apply {
                        put("fileName", path)
                        put("size", holder.expectedSize)
                        put("progress", (holder.currentSize * 100f / holder.expectedSize).coerceIn(0f, 100f))
                    }
                }
            } catch (e: JSONException) {
                Log.e(TAG, "Error creating progress response: ${e.message}")
            }
            
            response.send(res)
        }
        server?.errorCallback = CompletedCallback { ex: Exception? -> Log.e(TAG, "startServer: " + Log.getStackTraceString(ex)) }
        server?.listen(mAsyncServer, Constants.HTTP_PORT)
    }

    @Throws(IOException::class)
    private fun getIndexContent(context: Context): String {
        var bInputStream: BufferedInputStream? = null
        return try {
            bInputStream = BufferedInputStream(context.assets.open("wifi/index.html"))
            val baos = ByteArrayOutputStream()
            var len = 0
            val tmp = ByteArray(10240)
            while (bInputStream.read(tmp).also { len = it } > 0) {
                baos.write(tmp, 0, len)
            }
            baos.toString("utf-8")
        } catch (e: IOException) {
            e.printStackTrace()
            throw e
        } finally {
            if (bInputStream != null) {
                try {
                    bInputStream.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
    }

    private fun sendResources(context: Context, request: AsyncHttpServerRequest, response: AsyncHttpServerResponse) {
        try {
            var fullPath = request.path
            fullPath = fullPath.replace("%20", " ")
            var resourceName = fullPath
            if (resourceName.startsWith("/")) {
                resourceName = resourceName.substring(1)
            }
            if (resourceName.indexOf("?") > 0) {
                resourceName = resourceName.substring(0, resourceName.indexOf("?"))
            }
            if (!TextUtils.isEmpty(getContentTypeByResourceName(resourceName))) {
                response.setContentType(getContentTypeByResourceName(resourceName))
            }
            val bInputStream = BufferedInputStream(context.assets.open("wifi/$resourceName"))
            response.sendStream(bInputStream, bInputStream.available().toLong())
        } catch (e: IOException) {
            e.printStackTrace()
            response.code(404).end()
            return
        }
    }

    private fun getContentTypeByResourceName(resourceName: String): String {
        if (resourceName.endsWith(".css")) {
            return CSS_CONTENT_TYPE
        } else if (resourceName.endsWith(".js")) {
            return JS_CONTENT_TYPE
        } else if (resourceName.endsWith(".swf")) {
            return SWF_CONTENT_TYPE
        } else if (resourceName.endsWith(".png")) {
            return PNG_CONTENT_TYPE
        } else if (resourceName.endsWith(".jpg") || resourceName.endsWith(".jpeg")) {
            return JPG_CONTENT_TYPE
        } else if (resourceName.endsWith(".woff")) {
            return WOFF_CONTENT_TYPE
        } else if (resourceName.endsWith(".ttf")) {
            return TTF_CONTENT_TYPE
        } else if (resourceName.endsWith(".svg")) {
            return SVG_CONTENT_TYPE
        } else if (resourceName.endsWith(".eot")) {
            return EOT_CONTENT_TYPE
        } else if (resourceName.endsWith(".mp3")) {
            return MP3_CONTENT_TYPE
        } else if (resourceName.endsWith(".mp4")) {
            return MP4_CONTENT_TYPE
        }
        return ""
    }

    // 将 FileUploadHolder 移到 companion object 之前
    inner class FileUploadHolder(val fileName: String, val expectedSize: Long) {
        var uploadTime: Long = System.currentTimeMillis()
        private var receivedFile: File? = null
        private var fileOutputStream: BufferedOutputStream? = null
        var currentSize: Long = 0
        var currentSpeed: Long = 0
        private var lastUpdateTime: Long = System.currentTimeMillis()
        private var lastSize: Long = 0
        
        // 添加一个计算属性来获取总大小
        val totalSize: Long
            get() = expectedSize
        
        init {
            try {
                if (!dir.exists()) {
                    dir.mkdirs()
                }
                receivedFile = File(dir, fileName)
                fileOutputStream = BufferedOutputStream(FileOutputStream(receivedFile))
            } catch (e: Exception) {
                Log.e(TAG, "Error initializing FileUploadHolder: ${e.message}")
                throw e
            }
        }
        
        fun write(data: ByteArray) {
            try {
                fileOutputStream?.write(data)
                currentSize += data.size.toLong()
                
                // 计算这个文件的当前速度
                val now = System.currentTimeMillis()
                val timeDiff = now - lastUpdateTime
                if (timeDiff >= 500) { // 每500ms更新一次速度
                    val sizeDiff = currentSize - lastSize
                    currentSpeed = if (timeDiff > 0) (sizeDiff * 1000) / timeDiff else 0
                    lastUpdateTime = now
                    lastSize = currentSize
                }
                
                // 更新总体传输状态
                updateTransferStatus()
            } catch (e: IOException) {
                Log.e(TAG, "Error writing file: ${e.message}")
                throw e
            }
        }
        
        fun close() {
            try {
                fileOutputStream?.close()
                Log.i(TAG, "📁 文件关闭: $fileName, 大小: $expectedSize, 当前大小: $currentSize")

                // 发送增量文件添加事件，而不是全量刷新
                Log.d(TAG, "📁 发送增量文件添加事件: $fileName")
                LiveEventBus.get<String>(Constants.ADD_FILE_INCREMENTAL).post(fileName)

                // 调用上传完成处理 - 在移除之前调用
                Log.d(TAG, "📁 调用上传完成处理: $fileName")
                onFileUploadComplete(this)
            } catch (e: IOException) {
                Log.e(TAG, "❌ 关闭文件时出错: ${e.message}")
            } finally {
                fileOutputStream = null
            }
        }
    }

    fun stopService() {
        server?.stop()
        mAsyncServer?.stop()
    }

    /**
     * 完全释放资源，防止内存泄漏
     */
    fun release() {
        // 停止服务
        stopService()
        
        // 重置所有状态
        resetTransferStatus()
        
        // 清空所有回调
        onTransferStatusUpdate = null
        
        // 清理上传管理器
        uploadManager.resetAll()
    }

    /**
     * 通过running判断是否连接
     */
    fun isConnected(): Boolean = mAsyncServer?.isRunning == true

    companion object {
        private const val TAG = "WebHelper"

        // 使用弱引用持有单例实例，允许GC在需要时回收
        private var instanceRef: WeakReference<WebHelper>? = null

        val instance: WebHelper
            get() {
                var helper = instanceRef?.get()
                if (helper == null) {
                    helper = WebHelper()
                    instanceRef = WeakReference(helper)
                }
                return helper
            }

        private const val TEXT_CONTENT_TYPE = "text/html;charset=utf-8"
        private const val CSS_CONTENT_TYPE = "text/css;charset=utf-8"
        private const val BINARY_CONTENT_TYPE = "application/octet-stream"
        private const val JS_CONTENT_TYPE = "application/javascript"
        private const val PNG_CONTENT_TYPE = "application/x-png"
        private const val JPG_CONTENT_TYPE = "application/jpeg"
        private const val SWF_CONTENT_TYPE = "application/x-shockwave-flash"
        private const val WOFF_CONTENT_TYPE = "application/x-font-woff"
        private const val TTF_CONTENT_TYPE = "application/x-font-truetype"
        private const val SVG_CONTENT_TYPE = "image/svg+xml"
        private const val EOT_CONTENT_TYPE = "image/vnd.ms-fontobject"
        private const val MP3_CONTENT_TYPE = "audio/mp3"
        private const val MP4_CONTENT_TYPE = "video/mpeg4"
    }

    // 添加扩展函数，将 JSONArray 转换为 List
    private fun <T> JSONArray.toList(): List<T> {
        val list = mutableListOf<T>()
        for (i in 0 until length()) {
            @Suppress("UNCHECKED_CAST")
            list.add(get(i) as T)
        }
        return list
    }

    // 修改状态回调，添加进度参数
    fun setTransferStatusCallback(callback: (speed: Long, files: Int, totalSize: Long, progress: Float) -> Unit) {
        onTransferStatusUpdate = callback
    }

    private fun updateTransferStatus() {
        Log.d(TAG, "🔄 updateTransferStatus 被调用，isTransferring: $isTransferring")

        if (!isTransferring) {
            Log.d(TAG, "🔄 非传输状态，重置所有状态并发送(0,0,0,0)")
            uploadManager.resetAll()
            onTransferStatusUpdate?.invoke(0, 0, 0, 0f)
            return
        }

        val (totalReceived, totalSize, totalFiles) = uploadManager.getTotalProgress()
        val totalSpeed = uploadManager.getCurrentTotalSpeed()

        Log.d(TAG, "🔄 传输进度: 已接收=${totalReceived}B, 总大小=${totalSize}B, 文件数=${totalFiles}, 速度=${totalSpeed}B/s")

        // 计算总体进度百分比
        val progress = if (totalSize > 0) {
            (totalReceived * 100f / totalSize).coerceIn(0f, 100f)
        } else {
            0f
        }

        Log.d(TAG, "🔄 计算进度: ${progress}%")

        // 更新UI显示
        Log.d(TAG, "🔄 发送状态更新: 速度=${totalSpeed}, 文件=${totalFiles}, 大小=${totalSize}, 进度=${progress}%")
        onTransferStatusUpdate?.invoke(
            totalSpeed,
            totalFiles,
            totalSize,
            progress
        )
    }

    private fun onFileUploadComplete(holder: FileUploadHolder) {
        Log.i(TAG, "📁 文件上传完成回调: ${holder.fileName}")
        Log.d(TAG, "📁 移除前活跃上传数量: ${uploadManager.getActiveUploads().size}")
        Log.d(TAG, "📁 当前传输状态: isTransferring=$isTransferring")

        // 先从活跃上传中移除这个文件
        uploadManager.removeUpload(holder.fileName)

        Log.d(TAG, "📁 移除后活跃上传数量: ${uploadManager.getActiveUploads().size}")

        // 只有当所有活跃上传都完成时才结束传输
        if (uploadManager.getActiveUploads().isEmpty()) {
            Log.i(TAG, "🎉 所有文件传输完成！")
            isTransferring = false

            // 发送最后一次进度更新，显示100%
            Log.d(TAG, "📊 发送100%进度更新")
            onTransferStatusUpdate?.invoke(0, uploadManager.getTotalFiles(), uploadManager.totalExpectedSize, 100f)

            // 延迟一段时间后发送传输完成状态，确保UI有时间显示100%
            Log.d(TAG, "⏰ 设置延迟1秒后发送传输结束状态")
            Handler(Looper.getMainLooper()).postDelayed({
                Log.d(TAG, "⏰ 延迟时间到，发送传输结束状态(0,0,0,0)")
                // 发送传输完全结束的状态更新
                onTransferStatusUpdate?.invoke(0, 0, 0, 0f)
            }, 1000) // 延迟1秒

            // 重置所有状态
            uploadManager.resetAll()
            Log.d(TAG, "🔄 所有文件传输完成，传输状态已重置")
            // 传输完全结束时不再发送全量刷新事件，由增量更新处理
        } else {
            Log.d(TAG, "📤 还有${uploadManager.getActiveUploads().size}个文件在传输，继续更新进度")
            // 如果还有文件在传输，继续更新进度
            updateTransferStatus()
        }
    }

    private fun onFileUploadStart(size: Long) {
        isTransferring = true
        // 不再在这里累加 currentFileSize，因为现在由 FileUploadManager 统一管理
        Log.d(TAG, "Starting file upload, size: $size bytes")
        updateTransferStatus()  // 立即更新状态以显示新文件的进度
    }

    fun resetTransferStatus() {
        isTransferring = false
        uploadManager.resetAll()
        onTransferStatusUpdate?.invoke(0, 0, 0, 0f)
    }

    // 将 FileUploadManager 移到 WebHelper 类内部
    private inner class FileUploadManager {
        private val activeUploads = ConcurrentHashMap<String, FileUploadHolder>()
        private var totalExpectedFiles = 0  // 总文件数
        var totalExpectedSize = 0L  // 所有文件的总大小
        private var completedSize = 0L      // 已完成文件的总大小
        
        fun createUpload(fileName: String, fileSize: Long): FileUploadHolder {
            // 如果没有活跃上传，说明是新的上传批次，重置所有状态
            if (activeUploads.isEmpty()) {
                resetAll()
            }
            
            totalExpectedFiles++
            totalExpectedSize += fileSize
            Log.d(TAG, "Creating upload: file=$fileName, size=$fileSize, totalSize=$totalExpectedSize")
            val holder = FileUploadHolder(fileName, fileSize)
            activeUploads[fileName] = holder
            return holder
        }
        
        fun getUpload(fileName: String): FileUploadHolder? {
            return activeUploads[fileName]
        }
        
        fun removeUpload(fileName: String) {
            val holder = activeUploads[fileName]
            if (holder != null) {
                completedSize += holder.currentSize
                activeUploads.remove(fileName)
                Log.d(TAG, "🗑️ 移除上传: 文件=$fileName, 已完成大小=$completedSize, 总大小=$totalExpectedSize")
                Log.d(TAG, "🗑️ 剩余活跃上传数量: ${activeUploads.size}")
            } else {
                Log.w(TAG, "⚠️ 尝试移除不存在的上传: $fileName")
            }
        }

        fun getTotalProgress(): Triple<Long, Long, Int> {
            // 如果没有活跃上传且没有已完成的文件，返回0进度
            if (activeUploads.isEmpty() && completedSize == 0L) {
                return Triple(0L, 0L, 0)
            }
            
            var currentReceived = completedSize
            
            // 添加所有活跃上传的当前进度
            activeUploads.values.forEach { holder ->
                if (holder.expectedSize > 0) {
                    currentReceived += holder.currentSize
                }
            }
            
            return Triple(
                currentReceived.coerceAtMost(totalExpectedSize),
                totalExpectedSize,
                totalExpectedFiles
            )
        }

        fun resetAll() {
            Log.d(TAG, "Resetting all upload stats")
            activeUploads.clear()
            totalExpectedFiles = 0
            totalExpectedSize = 0
            completedSize = 0
        }

        // 获取总文件数
        fun getTotalFiles(): Int = totalExpectedFiles

        // 获取当前总速度
        fun getCurrentTotalSpeed(): Long {
            if (!isTransferring) return 0
            return activeUploads.values.sumOf { it.currentSpeed }
        }

        // 添加获取活跃上传的方法
        fun getActiveUploads(): Map<String, FileUploadHolder> = activeUploads.toMap()
    }

    // 添加辅助函数来检查字符串是否为有效的UTF-8
    private fun isValidUtf8(str: String?): Boolean {
        if (str == null) return false
        try {
            str.toByteArray(Charsets.UTF_8).toString(Charsets.UTF_8)
            return true
        } catch (e: Exception) {
            return false
        }
    }
}