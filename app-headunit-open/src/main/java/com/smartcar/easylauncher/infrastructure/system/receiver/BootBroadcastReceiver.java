package com.smartcar.easylauncher.infrastructure.system.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.core.constants.Constants;
import com.smartcar.easylauncher.infrastructure.event.scope.notice.cody.GeneralNotificationScopeBus;
import com.smartcar.easylauncher.core.manager.FloatManager;
import com.smartcar.easylauncher.data.model.common.GeneralNoticeModel;
import com.smartcar.easylauncher.modules.touch.util.FloatServiceUtil;
import com.smartcar.easylauncher.shared.utils.time.StandardTimeUtils;

/**
 * BootBroadcastReceiver 类，继承自 BroadcastReceiver，用于监听系统开机完成的广播。
 * <AUTHOR>
 */
public class BootBroadcastReceiver extends BroadcastReceiver {

    /**
     * 系统开机完成的广播动作常量。
     */
    public static final String ACTION_BOOT_COMPLETED = "android.intent.action.BOOT_COMPLETED";

    /**
     * 在接收到匹配的广播时调用此方法。
     *
     * @param context 应用程序的上下文对象，用于访问资源和类。
     * @param intent  包含广播详细信息的 Intent 对象。
     */
    @Override
    public void onReceive(Context context, Intent intent) {
        // 检查接收到的广播是否为开机完成的广播
        if (intent.getAction().equals(ACTION_BOOT_COMPLETED)) {
            // 获取悬浮窗功能的启用状态
            boolean isFloatWindowEnabled = FloatManager.getEnable();
            // 根据启用状态设置悬浮窗功能
            FloatServiceUtil.setEnableFloatWindow(context, isFloatWindowEnabled);

            // 调用时间更新服务
            StandardTimeUtils.getInstance(context).startUpdateTime();

            // 发送一个通知事件，参数可能表示通知的ID、内容和是否需要显示
            GeneralNotificationScopeBus.eventBean().post(new GeneralNoticeModel(Constants.NoticeType.BOOT_COMPLETE, "", true));
            MToast.makeTextShort("Boot系统广播时间更新");
        }
    }
}
