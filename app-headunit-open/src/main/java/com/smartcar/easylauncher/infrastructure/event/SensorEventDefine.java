package com.smartcar.easylauncher.infrastructure.event;


import com.smartcar.easylauncher.data.model.system.SensorModel;

import java.util.List;
import java.util.Map;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 传感器事件定义
 */
@EventGroup(value = "SensorScope", active = true)
public class SensorEventDefine {
    @Event(value = "SensoreventInt", multiProcess = false)
    Integer eventInt;

    @Event(value = "SensoreventString", multiProcess = true)
    String eventString;

    @Event(value = "SensoreventBean", multiProcess = true, description = "传感器数据事件")
    SensorModel eventBean;

    @Event(value = "SensoreventList", multiProcess = true)
    List<String> eventList;

    @Event(value = "SensoreventMap", multiProcess = true)
    Map<String, List<String>> eventMap;
} 