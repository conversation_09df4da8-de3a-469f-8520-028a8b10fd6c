package com.smartcar.easylauncher.infrastructure.skin.attr;


import java.util.HashMap;

/**
 * Created by _SOLID
 * Date:2016/4/14
 * Time:9:47
 */
public class SkinAttrFactory {

    private static HashMap<String, SkinAttr> sSupportAttr = new HashMap<>();

    static {
        BackgroundAttr backgroundAttr = new BackgroundAttr();
        sSupportAttr.put(backgroundAttr.getAttrName(), backgroundAttr);

        TextColorAttr textColorAttr = new TextColorAttr();
        sSupportAttr.put(textColorAttr.getAttrName(), textColorAttr);

        TextViewDrawableTintAttr textViewDrawableTintAttr = new TextViewDrawableTintAttr();
        sSupportAttr.put(textViewDrawableTintAttr.getAttrName(), textViewDrawableTintAttr);

        ImageViewSrcAttr imageViewSrcAttr = new ImageViewSrcAttr();
        sSupportAttr.put(imageViewSrcAttr.getAttrName(), imageViewSrcAttr);

        ImageViewTintAttr imageViewTintAttr = new ImageViewTintAttr();
        sSupportAttr.put(imageViewTintAttr.getAttrName(), imageViewTintAttr);

        ShapeSolidColor shapeConstraintLayoutSolidColor = new ShapeSolidColor();
        sSupportAttr.put(shapeConstraintLayoutSolidColor.getAttrName(), shapeConstraintLayoutSolidColor);

        ShapeShadowColor shapeShadowColor = new ShapeShadowColor();
        sSupportAttr.put(shapeShadowColor.getAttrName(), shapeShadowColor);

    }

    /**
     * 创建支持的attr
     *
     * @param attrName
     * @param attrType
     * @param resName
     * @param resId
     * @return
     */
    public static SkinAttr createAttr(String attrName, String attrType, String resName, int resId) {
        SkinAttr skinAttr = sSupportAttr.get(attrName);
        if (skinAttr != null) {
            return skinAttr.clone(attrType, resName, resId);
        }
        return null;
    }

    /**
     * 是否支持该类型的AttrName
     *
     * @param attrName
     * @return
     */
    public static boolean isSupportedAttr(String attrName) {
        return sSupportAttr.containsKey(attrName);
    }

    /**
     * 添加支持的类型
     *
     * @param skinAttr
     */
    public static void addSupportAttr(SkinAttr skinAttr) {
        sSupportAttr.put(skinAttr.getAttrName(), skinAttr);
    }

    public static void addSupportSrcAttr() {
        ImageViewSrcAttr srcAttr = new ImageViewSrcAttr();
        sSupportAttr.put(srcAttr.getAttrName(), srcAttr);
    }

}
