package com.smartcar.easylauncher.infrastructure.event;


import com.smartcar.easylauncher.data.model.weather.WeatherDataModel;

import java.util.List;
import java.util.Map;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 天气预报事件定义
 */
@EventGroup(value = "WeatherDataScope", active = true)
public class WeatherDataEventDefine {
    @Event(value = "WeatherDataeventInt", multiProcess = false)
    Integer eventInt;

    @Event(value = "WeatherDataeventString", multiProcess = true)
    String eventString;

    @Event(value = "WeatherDataeventBean", multiProcess = true)
    WeatherDataModel eventBean;

    @Event(value = "WeatherDataeventBean", multiProcess = true)
    List<String> eventList;

    @Event(value = "WeatherDataeventMap", multiProcess = true)
    Map<String, List<String>> eventMap;
}