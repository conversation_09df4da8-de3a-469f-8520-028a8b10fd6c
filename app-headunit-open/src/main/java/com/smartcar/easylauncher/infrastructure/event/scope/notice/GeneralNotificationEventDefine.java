package com.smartcar.easylauncher.infrastructure.event.scope.notice;


import com.smartcar.easylauncher.data.model.common.GeneralNoticeModel;

import java.util.List;
import java.util.Map;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 组件信息事件定义
 */
@EventGroup(value = "GeneralNotificationScope", active = true)
public class GeneralNotificationEventDefine {
    @Event(value = "GeneralNotificationEventInt", multiProcess = false)
    Integer eventInt;

    @Event(value = "GeneralNotificationEventString", multiProcess = true)
    String eventString;

    @Event(value = "GeneralNotificationEventBean", multiProcess = true)
    GeneralNoticeModel eventBean;

    @Event(value = "GeneralNotificationEventBean", multiProcess = true)
    List<String> eventList;

    @Event(value = "GeneralNotificationEventMap", multiProcess = true)
    Map<String, List<String>> eventMap;

    @Event(value = "CardOperationEvent", multiProcess = true)
    String cardOperationEvent;
}