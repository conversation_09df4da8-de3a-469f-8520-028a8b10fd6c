package com.smartcar.easylauncher.infrastructure.event.scope.music;




import com.smartcar.easylauncher.data.model.music.MusicInfor;

import java.util.List;
import java.util.Map;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 音乐信息事件定义
 */
@EventGroup(value = "MusicInforScope", active = true)
public class MusicInforEventDefine {
    @Event(value = "MusicInforeventInt", multiProcess = false)
    Integer eventInt;

    @Event(value = "MusicInforeventString", multiProcess = true)
    String eventString;

    @Event(value = "MusicInforeventBean", multiProcess = true)
    MusicInfor eventBean;

    @Event(value = "MusicInforeventBean", multiProcess = true)
    List<String> eventList;

    @Event(value = "MusicInforeventMap", multiProcess = true)
    Map<String, List<String>> eventMap;
}