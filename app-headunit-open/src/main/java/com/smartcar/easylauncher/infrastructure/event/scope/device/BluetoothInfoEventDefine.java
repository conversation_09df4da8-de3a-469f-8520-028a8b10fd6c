package com.smartcar.easylauncher.infrastructure.event.scope.device;


import com.smartcar.easylauncher.data.model.system.BluetoothDeviceModel;

import java.util.List;
import java.util.Map;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 蓝牙信息事件定义
 * <AUTHOR>
 * @date 2025 年 2 月 11 日 12:24:26
 */
@EventGroup(value = "BluetoothInfoScope", active = true)
public class BluetoothInfoEventDefine {
    @Event(value = "BluetoothInfoeventInt", multiProcess = false)
    Integer eventInt;

    @Event(value = "BluetoothInfoeventString", multiProcess = true)
    String eventString;

    @Event(value = "BluetoothInfoeventString", multiProcess = true)
    Boolean eventBoolean;

    @Event(value = "BluetoothInfoeventBean", multiProcess = true)
    BluetoothDeviceModel eventBean;

    @Event(value = "BluetoothInfoeventBean", multiProcess = true)
    List<String> eventList;

    @Event(value = "BluetoothInfoeventMap", multiProcess = true)
    Map<String, List<String>> eventMap;
}