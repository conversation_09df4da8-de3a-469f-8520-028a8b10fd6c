package com.smartcar.easylauncher.infrastructure.system.service.weather;


/**
 * 天气服务错误码定义
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-03-21
 */
public class ErrorCode {

    /**
     * 成功
     */
    public static final int SUCCESS = 200;

    /**
     * 无网络连接
     */
    public static final int NO_NETWORK = 1001;

    /**
     * 无可用的天气提供者
     */
    public static final int NO_PROVIDER = 1002;

    /**
     * 所有提供者都失败
     */
    public static final int ALL_PROVIDERS_FAILED = 1003;

    /**
     * 位置信息无效
     */
    public static final int INVALID_LOCATION = 1004;

    /**
     * API调用失败
     */
    public static final int API_CALL_FAILED = 1005;

    /**
     * 数据解析错误
     */
    public static final int DATA_PARSE_ERROR = 1006;

    /**
     * 缓存错误
     */
    public static final int CACHE_ERROR = 1007;

    /**
     * 未知错误
     */
    public static final int UNKNOWN_ERROR = 9999;

    private ErrorCode() {
        // 私有构造方法，防止实例化
    }
}