package com.smartcar.easylauncher.infrastructure.driver;

import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

/**
 * UsbSerialProber 类，用于检测 USB 串口设备并返回对应的 UsbSerialDriver 对象列表。
 * 该类负责管理 USB 串口驱动程序的注册和查找。
 */
public class UsbSerialProber {

    /**
     * 存储所有已注册的 USB 串口驱动程序的 ProbeTable 对象。
     * 该变量用于查找与 USB 设备匹配的驱动程序。
     */
    private final ProbeTable mProbeTable;

    /**
     * 构造函数，初始化 UsbSerialProber 对象并设置其 ProbeTable。
     *
     * @param probeTable 用于存储所有已注册的 USB 串口驱动程序的 ProbeTable 对象。
     *                   该参数不能为空，否则会导致后续操作失败。
     */
    public UsbSerialProber(ProbeTable probeTable) {
        if (probeTable == null) {
            throw new IllegalArgumentException("ProbeTable 不能为空");
        }
        this.mProbeTable = probeTable;
    }

    /**
     * 获取默认的 UsbSerialProber 对象，其 ProbeTable 包含所有默认的 USB 串口驱动程序。
     *
     * @return 默认的 UsbSerialProber 对象。
     */
    public static UsbSerialProber getDefaultProber() {
        return new UsbSerialProber(getDefaultProbeTable());
    }

    /**
     * 获取包含所有默认 USB 串口驱动程序的 ProbeTable 对象。
     *
     * @return 包含所有默认 USB 串口驱动程序的 ProbeTable 对象。
     */
    public static ProbeTable getDefaultProbeTable() {
        ProbeTable probeTable = new ProbeTable();
        // 添加默认的 USB 串口驱动程序到 ProbeTable 中
        probeTable.addDriver(FtdiSerialDriver.class);
        probeTable.addDriver(Ch34xSerialDriver.class);
        probeTable.addDriver(Cp21xxSerialDriver.class);
        probeTable.addDriver(ProlificSerialDriver.class);
        return probeTable;
    }

    /**
     * 查找并返回所有匹配的 USB 串口驱动程序。
     *
     * @param usbManager 用于获取 USB 设备列表的 UsbManager 对象。
     *                   该对象不能为空，否则会导致后续操作失败。
     * @return 包含所有匹配的 USB 串口驱动程序的列表。
     */
    public List<UsbSerialDriver> findAllDrivers(UsbManager usbManager) {
        if (usbManager == null) {
            throw new IllegalArgumentException("UsbManager 不能为空");
        }
        ArrayList<UsbSerialDriver> arrayList = new ArrayList<>();
        // 遍历 USB 设备列表
        for (UsbDevice usbDevice : usbManager.getDeviceList().values()) {
            // 探测设备并获取对应的 USB 串口驱动程序
            UsbSerialDriver probeDevice = probeDevice(usbDevice);
            // 如果找到了匹配的驱动程序，则添加到列表中
            if (probeDevice != null) {
                arrayList.add(probeDevice);
            }
        }
        return arrayList;
    }

    /**
     * 根据给定的 USB 设备探测其对应的 USB 串口驱动程序。
     *
     * @param usbDevice 需要探测的 USB 设备。
     *                  该参数不能为空，否则会导致后续操作失败。
     * @return 如果找到了匹配的驱动程序，则返回该驱动程序的实例；否则返回 null。
     */
    public UsbSerialDriver probeDevice(UsbDevice usbDevice) {
        if (usbDevice == null) {
            throw new IllegalArgumentException("UsbDevice 不能为空");
        }
        // 根据设备的厂商 ID 和产品 ID 在 ProbeTable 中查找匹配的驱动程序
        Class<? extends UsbSerialDriver> findDriver = this.mProbeTable.findDriver(usbDevice.getVendorId(), usbDevice.getProductId());
        // 如果没有找到匹配的驱动程序，则返回 null
        if (findDriver == null) {
            return null;
        }
        try {
            // 使用反射机制创建驱动程序的实例并返回
            return findDriver.getConstructor(UsbDevice.class).newInstance(usbDevice);
        } catch (NoSuchMethodException | IllegalArgumentException | InstantiationException | IllegalAccessException | InvocationTargetException e) {
            // 如果在创建实例过程中发生异常，则抛出运行时异常
            throw new RuntimeException("创建 USB 串口驱动程序实例失败", e);
        }
    }
}