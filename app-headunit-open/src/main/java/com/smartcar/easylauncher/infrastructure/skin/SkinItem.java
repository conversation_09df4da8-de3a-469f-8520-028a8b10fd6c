package com.smartcar.easylauncher.infrastructure.skin;

import android.view.View;

import com.smartcar.easylauncher.infrastructure.skin.attr.SkinAttr;
import com.smartcar.easylauncher.infrastructure.skin.iface.ISkinItem;

import java.util.ArrayList;
import java.util.List;

/**
 * SkinItem类用于表示一个可以应用皮肤属性的视图项。
 * 它实现了ISkinItem接口，允许对视图进行皮肤属性的应用。
 */
public class SkinItem implements ISkinItem {

    /**
     * 需要应用皮肤属性的视图对象。
     */
    private View view;

    /**
     * 视图关联的皮肤属性列表。
     */
    private List<SkinAttr> attrs;

    /**
     * 构造函数，接收一个视图对象和一组皮肤属性。
     *
     * @param view 需要应用皮肤属性的视图对象
     * @param attrs 视图对应的皮肤属性列表
     */
    public SkinItem(View view, List<SkinAttr> attrs) {
        this.view = view;
        this.attrs = attrs;
    }

    /**
     * 构造函数，仅接收一个视图对象，初始化时不包含任何皮肤属性。
     *
     * @param view 需要应用皮肤属性的视图对象
     */
    public SkinItem(View view) {
        this.view = view;
        // 初始化空的皮肤属性列表
        this.attrs = new ArrayList<>();
    }

    /**
     * 向皮肤属性列表中添加一个皮肤属性。
     *
     * @param attr 要添加的皮肤属性
     */
    public void addAttr(SkinAttr attr) {
        attrs.add(attr);
    }

    /**
     * 获取当前SkinItem关联的视图对象。
     *
     * @return 关联的视图对象
     */
    public View getView() {
        return view;
    }

    /**
     * 应用所有已关联的皮肤属性到视图上。
     * 如果视图或皮肤属性列表为空，则不进行任何操作。
     */
    @Override
    public void apply() {
        // 检查视图和属性列表是否为空
        if (view == null || attrs == null) {
            return;
        }
        // 遍历属性列表，逐个应用到视图上
        for (SkinAttr attr : attrs) {
            attr.applySkin(view);
        }
    }

    /**
     * 清理对View和属性的引用，防止内存泄漏
     */
    public void clearReferences() {
        view = null;
        if (attrs != null) {
            attrs.clear();
        }
    }
}
