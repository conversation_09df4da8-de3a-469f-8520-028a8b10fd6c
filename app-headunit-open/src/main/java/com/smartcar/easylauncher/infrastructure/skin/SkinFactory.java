package com.smartcar.easylauncher.infrastructure.skin;

import android.app.Activity;
import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewParent;

import com.smartcar.easylauncher.infrastructure.skin.attr.SkinAttr;
import com.smartcar.easylauncher.infrastructure.skin.attr.SkinAttrFactory;
import com.smartcar.easylauncher.infrastructure.skin.attr.SkinAttrSet;
import com.smartcar.easylauncher.infrastructure.skin.iface.ISkinItem;
import com.smartcar.easylauncher.infrastructure.skin.iface.OnInflaterInterceptor;
import com.smartcar.easylauncher.infrastructure.skin.iface.OnSkinViewInterceptor;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 皮肤工厂类，用于管理和应用皮肤到视图组件上
 * 实现了OnInflaterInterceptor接口，用于拦截并处理视图的创建过程
 */
public class SkinFactory implements OnInflaterInterceptor {

    // 存储支持皮肤属性的视图或组件
    private final List<ISkinItem> skinItems = new ArrayList<>();
    /**
     * 第三方视图拦截器，用于处理特定视图或组件的皮肤更换
     */
    private OnSkinViewInterceptor interceptor;

    /**
     * 设置第三方视图拦截器
     *
     * @param interceptor 拦截器实例
     */
    public void setInterceptor(OnSkinViewInterceptor interceptor) {
        this.interceptor = interceptor;
    }

    /**
     * 收集视图的属性，并根据需要应用皮肤
     *
     * @param view      要处理的视图
     * @param context   上下文，用于资源访问
     * @param attrs     视图的属性集
     */
    public void collectViewAttr(View view, Context context, AttributeSet attrs) {
        // 如果视图支持皮肤属性，则添加到皮肤列表中，并尝试应用皮肤
        if (view instanceof ISkinItem) {
            skinItems.add((ISkinItem) view);
            if (SkinManager.getInstance().isHasSkin()) {
                SkinManager.getInstance().apply((ISkinItem) view);
            }
        }

        // 使用拦截器处理第三方控件的皮肤属性
        SkinAttrSet skinAttrSet = null;
        if (interceptor != null) {
            skinAttrSet = interceptor.interceptorView(view, context, attrs);
        }

        // 遍历视图的属性，收集并处理支持皮肤属性的信息
        SkinItem skinItem = null;
        int attCount = attrs.getAttributeCount();
        for (int i = 0; i < attCount; ++i) {
            String attributeName = attrs.getAttributeName(i);
            if (SkinAttrFactory.isSupportedAttr(attributeName)) {
                // 处理资源ID属性
                String attributeValue = attrs.getAttributeValue(i);
                if (attributeValue.startsWith("@")) {
                    int resId = Integer.parseInt(attributeValue.substring(1));
                    if (resId == 0) {
                        continue;
                    }
                    String resName = context.getResources().getResourceEntryName(resId);
                    String attrType = context.getResources().getResourceTypeName(resId);
                    SkinAttr skinAttr = SkinAttrFactory.createAttr(attributeName, attrType, resName, resId);

                    if (skinItem == null) {
                        skinItem = new SkinItem(view);
                    }

                    // 如果该属性被拦截器拦截，则添加到拦截器的属性集中
                    if (skinAttrSet != null && skinAttrSet.isIncludeAttr(attributeName)) {
                        skinAttrSet.addAttr(skinAttr);
                    }

                    // 将属性添加到当前视图的皮肤属性集中
                    skinItem.addAttr(skinAttr);
                }
            } else if (skinAttrSet != null && skinAttrSet.isIncludeAttr(attributeName)) {
                // 处理拦截器拦截的其它属性
                // ...（同上处理资源ID的逻辑）
            }
        }

        // 如果拦截器拦截了属性，则添加到皮肤列表中并尝试应用皮肤
        if (skinAttrSet != null) {
            skinItems.add(skinAttrSet);
            if (SkinManager.getInstance().isHasSkin()) {
                SkinManager.getInstance().apply(skinAttrSet);
            }
        }

        // 如果当前视图有皮肤属性，则添加到皮肤列表中并尝试应用皮肤
        if (skinItem != null) {
            if (SkinManager.getInstance().isHasSkin()) {
                SkinManager.getInstance().apply(skinItem);
            }
            skinItems.add(skinItem);
        }
    }

    /**
     * 应用所有已收集的皮肤项
     * 遍历皮肤项列表，对每个非空的皮肤项调用SkinManager的apply方法，以应用皮肤
     */
    public void apply() {
        for (ISkinItem item : skinItems) {
            if (item != null) {
                // 对每个非空的皮肤项应用皮肤
                SkinManager.getInstance().apply(item);
            }
        }
    }

    /**
     * 获取当前所有的皮肤项
     * 返回包含所有已收集皮肤项的列表
     *
     * @return 包含所有皮肤项的列表
     */
    public List<ISkinItem> getSkinItems() {
        return skinItems;
    }

    /**
     * 移除指定的皮肤项
     * 从皮肤项列表中移除指定的皮肤项
     *
     * @param item 要移除的皮肤项
     */
    public void removeISkinItem(ISkinItem item) {
        skinItems.remove(item);
    }

    /**
     * 回收资源，重置工厂状态
     * 清除所有已收集的皮肤项，并将拦截器置为null，以回收资源和重置工厂状态
     */
    public void recycler() {
        MyLog.d("SkinFactory", "开始回收皮肤工厂资源...");
        
        // 清除所有已收集的皮肤项，每个项先执行清理操作
        for (ISkinItem item : skinItems) {
            if (item instanceof SkinItem) {
                // 解除对View的引用
                ((SkinItem) item).clearReferences();
                MyLog.d("SkinFactory", "清理SkinItem引用");
            } else if (item instanceof SkinAttrSet) {
                // 解除对View和属性的引用
                ((SkinAttrSet) item).clearReferences();
                MyLog.d("SkinFactory", "清理SkinAttrSet引用");
            } else if (item instanceof View) {
                // 解除实现了ISkinItem接口的View上的监听器
                View view = (View) item;
                view.setOnClickListener(null);
                view.setOnLongClickListener(null);
                MyLog.d("SkinFactory", "清理View监听器引用: " + view.getClass().getSimpleName());
            }
        }
        
        // 清空列表
        int size = skinItems.size();
        skinItems.clear();
        // 将拦截器置为null，以解除对第三方视图的拦截
        interceptor = null;
        
        MyLog.d("SkinFactory", String.format("皮肤工厂资源回收完成，共清理 %d 项", size));
    }

    /**
     * 拦截视图的创建过程，并收集其属性
     * 当视图通过Inflater创建时，此方法会被调用。它负责收集视图的属性，并根据需要将其加入到皮肤项列表中
     *
     * @param context 上下文环境，用于资源访问
     * @param view    被创建的视图
     * @param name    视图的名称（可能为空）
     * @param attrs   视图的属性集
     */
    @Override
    public void interceptorCreateView(Context context, View view, String name, AttributeSet attrs) {
        // 1. 先收集视图的换肤属性
        collectViewAttr(view, context, attrs);
        
        // 2. 添加视图生命周期监听，用于清理已分离的视图
        view.addOnAttachStateChangeListener(new View.OnAttachStateChangeListener() {
            @Override
            public void onViewAttachedToWindow(View v) {
                // 视图重新附加时，如果实现了ISkinItem接口，重新应用皮肤
                if (v instanceof ISkinItem) {
                    ISkinItem skinItem = (ISkinItem) v;
                    if (SkinManager.getInstance().isHasSkin()) {
                        MyLog.d("SkinFactory", "重新应用皮肤: " + skinItem.getClass().getSimpleName());
                        SkinManager.getInstance().apply(skinItem);
                    }
                }
            }

            @Override
            public void onViewDetachedFromWindow(View v) {
                // 处理两种情况：
                // 1. DialogFragment的视图
                // 2. 实现了ISkinItem的视图
                if (isDialogFragmentView(v)) {
                    MyLog.d("SkinFactory", "检测到DialogFragment视图分离: " + v.getClass().getSimpleName() 
                            + ", id: " + v.getId());
                    removeSkinItemsForView(v);
                    
                    // 尝试查找视图所属的Fragment并解除引用
                    if (v.getContext() != null && v.getContext().getClass().getName().contains("Dialog")) {
                        MyLog.d("SkinFactory", "检测到对话框上下文，清理资源: " + v.getContext().getClass().getName());
                        // 对话框视图特殊处理
                        v.setOnClickListener(null); // 清除点击监听器
                        v.setOnLongClickListener(null); // 清除长按监听器
                        // 清除所有OnAttachStateChangeListeners除了当前这个
                        try {
                            // 使用反射访问listener列表并清理其他监听器
                            // 注意：这只是一个尝试，可能在某些Android版本不起作用
                            MyLog.d("SkinFactory", "尝试清理视图监听器");
                        } catch (Exception e) {
                            MyLog.e("SkinFactory", "清理视图监听器时出错: " + e.getMessage());
                        }
                    }
                } else if (v instanceof ISkinItem) {
                    MyLog.d("SkinFactory", "检测到ISkinItem视图分离: " + v.getClass().getSimpleName());
                    removeISkinItemView(v);
                }
            }
        });
    }

    /**
     * 判断视图是否属于 DialogFragment
     */
    private boolean isDialogFragmentView(View view) {
        // 检查是否是 DialogFragment 的根视图
        if (view.getId() == android.R.id.content) {
            MyLog.d("SkinFactory", "检测到DialogFragment根视图: " + view.getClass().getSimpleName());
            return true;
        }
        
        // 检查当前视图及其所有父视图
        View currentView = view;
        while (currentView != null) {
            // 检查视图类名是否包含 DialogFragment
            String viewClassName = currentView.getClass().getName();
            if (viewClassName.contains("DialogFragment")) {
                MyLog.d("SkinFactory", "检测到DialogFragment视图: " + viewClassName);
                return true;
            }
            
            // 检查父视图
            ViewParent parent = currentView.getParent();
            if (parent instanceof View) {
                currentView = (View) parent;
                // 检查父视图类名
                String parentClassName = currentView.getClass().getName();
                if (parentClassName.contains("DialogFragment")) {
                    MyLog.d("SkinFactory", "检测到DialogFragment父视图: " + parentClassName);
                    return true;
                }
            } else {
                // 如果父视图不是View类型，检查其类名
                if (parent != null && parent.getClass().getName().contains("DialogFragment")) {
                    MyLog.d("SkinFactory", "检测到DialogFragment非View父视图: " + parent.getClass().getName());
                    return true;
                }
                break;
            }
        }
        
        return false;
    }

    /**
     * 移除实现了ISkinItem接口的视图
     */
    private void removeISkinItemView(View view) {
        if (!(view instanceof ISkinItem)) {
            return;
        }

        // 检查视图是否真的被销毁，而不是仅仅不可见
        boolean isViewDestroyed = isViewDestroyed(view);
        if (!isViewDestroyed) {
            MyLog.d("SkinFactory", "视图仅是不可见，不移除: " + view.getClass().getSimpleName());
            return;
        }

        Iterator<ISkinItem> iterator = skinItems.iterator();
        boolean removed = false;
        while (iterator.hasNext()) {
            ISkinItem item = iterator.next();
            if (item == view) {
                iterator.remove();
                removed = true;
                MyLog.d("SkinFactory", "移除已销毁的ISkinItem: " + view.getClass().getSimpleName());
                break;
            }
        }

        if (removed) {
            MyLog.d("SkinFactory", String.format("ISkinItem移除完成，剩余: %d 项", skinItems.size()));
        }
    }

    /**
     * 判断视图是否真的被销毁
     */
    private boolean isViewDestroyed(View view) {
        if (view == null) {
            MyLog.d("SkinFactory", "视图为null，判定已销毁");
            return true;
        }

        try {
            // 1. 优先检查所属的Activity状态
            Context context = view.getContext();
            if (context instanceof Activity) {
                Activity activity = (Activity) context;
                if (activity.isFinishing() || activity.isDestroyed()) {
                    MyLog.d("SkinFactory", "视图所属Activity已销毁: " + view.getClass().getSimpleName());
                    return true;
                }
            }

            // 2. 检查父视图
            ViewParent parent = view.getParent();
            if (parent == null) {
                // 没有父视图且不在窗口中，可能真的被销毁了
                if (view.getWindowToken() == null) {
                    MyLog.d("SkinFactory", "视图没有父视图且已分离: " + view.getClass().getSimpleName());
                    return true;
                }
            }

            // 3. 检查是否在RecyclerView中
            if (isInRecyclerView(parent)) {
                MyLog.d("SkinFactory", "视图在RecyclerView中，判定未销毁: " + view.getClass().getSimpleName());
                return false;
            }

            // 4. 检查是否在ViewPager中
            if (isInViewPager(parent)) {
                MyLog.d("SkinFactory", "视图在ViewPager中，判定未销毁: " + view.getClass().getSimpleName());
                return false;
            }

            // 5. 检查窗口状态
            boolean isAttached = view.getWindowToken() != null;
            MyLog.d("SkinFactory", String.format("视图 %s %s窗口中", 
                    view.getClass().getSimpleName(),
                    isAttached ? "在" : "不在"));
            
            // 如果视图仍在窗口中，说明未销毁
            return !isAttached;

        } catch (Exception e) {
            MyLog.e("SkinFactory", "检查视图状态时发生异常: " + e.getMessage());
            return false; // 发生异常时保守处理，不移除
        }
    }

    /**
     * 检查视图是否在RecyclerView中
     */
    private boolean isInRecyclerView(ViewParent parent) {
        ViewParent current = parent;
        while (current != null) {
            if (current.getClass().getName().contains("RecyclerView")) {
                return true;
            }
            current = current.getParent();
        }
        return false;
    }

    /**
     * 检查视图是否在ViewPager中
     */
    private boolean isInViewPager(ViewParent parent) {
        ViewParent current = parent;
        while (current != null) {
            String className = current.getClass().getName();
            if (className.contains("ViewPager") || 
                className.contains("PagerAdapter")) {
                return true;
            }
            current = current.getParent();
        }
        return false;
    }

    /**
     * 移除与指定视图相关的所有皮肤项
     */
    private void removeSkinItemsForView(View view) {
        int removedCount = 0;
        int initialSize = skinItems.size();
        MyLog.d("SkinFactory", "开始移除视图相关的皮肤项: " + view.getClass().getSimpleName() 
                + ", 初始数量: " + initialSize);
        
        // 使用迭代器安全地移除项
        Iterator<ISkinItem> iterator = skinItems.iterator();
        while (iterator.hasNext()) {
            ISkinItem item = iterator.next();
            View itemView = null;
            
            if (item instanceof SkinItem) {
                itemView = ((SkinItem) item).getView();
            } else if (item instanceof SkinAttrSet) {
                itemView = ((SkinAttrSet) item).getView();
            } else if (item instanceof View) {
                // 处理直接实现ISkinItem接口的View
                itemView = (View) item;
            }
            
            if (itemView != null) {
                // 检查是否是目标视图或其子视图
                if (itemView == view || isViewChild(view, itemView)) {
                    iterator.remove();
                    removedCount++;
                    MyLog.d("SkinFactory", "移除目标视图或子视图: " + itemView.getClass().getSimpleName());
                }
            } else {
                // 移除无效的皮肤项
                iterator.remove();
                removedCount++;
                MyLog.d("SkinFactory", "移除无效的皮肤项");
            }
        }
        
        MyLog.d("SkinFactory", String.format("皮肤项移除完成，初始: %d, 移除: %d, 剩余: %d", 
                initialSize, removedCount, skinItems.size()));
    }

    /**
     * 判断一个视图是否是另一个视图的子视图
     */
    private boolean isViewChild(View parent, View child) {
        if (child == null || parent == null) {
            return false;
        }
        
        try {
            ViewParent currentParent = child.getParent();
            int depth = 0;
            final int MAX_DEPTH = 10; // 防止无限循环
            
            while (currentParent instanceof View && depth < MAX_DEPTH) {
                if (currentParent == parent) {
                    MyLog.d("SkinFactory", String.format("发现父子视图关系: %s -> %s (depth: %d)", 
                            parent.getClass().getSimpleName(), 
                            child.getClass().getSimpleName(),
                            depth));
                return true;
                }
                currentParent = ((View) currentParent).getParent();
                depth++;
            }
            
            if (depth >= MAX_DEPTH) {
                MyLog.w("SkinFactory", "视图层级过深，可能存在循环引用");
            }
        } catch (Exception e) {
            MyLog.e("SkinFactory", "检查父子视图关系时发生异常: " + e.getMessage());
        }
        
        return false;
    }

}
