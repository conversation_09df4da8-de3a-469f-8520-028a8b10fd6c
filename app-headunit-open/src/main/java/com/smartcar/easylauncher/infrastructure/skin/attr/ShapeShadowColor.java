package com.smartcar.easylauncher.infrastructure.skin.attr;

import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;

import com.hjq.shape.layout.ShapeConstraintLayout;
import com.hjq.shape.view.ShapeButton;
import com.smartcar.easylauncher.infrastructure.skin.SkinManager;

public class ShapeShadowColor extends SkinAttr {

    public ShapeShadowColor() {
        super("shape_shadowColor");
    }


    public ShapeShadowColor(@NonNull String attrName, String attrType, String resName, int resId) {
        super(attrName, attrType, resName, resId);
    }

    @Override
    public void applySkin(View view) {
        if (view instanceof ShapeConstraintLayout) {
            // 正确处理ShapeConstraintLayout
            ShapeConstraintLayout layout = (ShapeConstraintLayout) view;
            // 设置形状、阴影、颜色等
            layout.getShapeDrawableBuilder().setShadowColor(SkinManager.getInstance().getColor(resName, resId)).intoBackground();
        } else if (view instanceof ShapeButton) {
            // 正确处理ShapeButton
            ShapeButton button = (ShapeButton) view;
            // 设置按钮的背景、颜色等
            button.getShapeDrawableBuilder().setShadowColor(SkinManager.getInstance().getColor(resName, resId)).intoBackground();
        } else {
            // 处理不支持的视图类型
            Log.e("Skin", "Unsupported view type: " + view.getClass().getName());
        }

    }
}
