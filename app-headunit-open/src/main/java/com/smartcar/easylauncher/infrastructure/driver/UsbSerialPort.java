package com.smartcar.easylauncher.infrastructure.driver;

import android.hardware.usb.UsbDeviceConnection;

import java.io.IOException;

/**
 * UsbSerialPort 接口定义了 USB 串口的基本操作。
 * 该接口提供了对串口的控制和数据传输功能。
 * 注意：实现该接口的类需要确保线程安全，避免并发访问导致的数据不一致。
 */
public interface UsbSerialPort {
    /** 数据位：5位 */
    public static final int DATABITS_5 = 5;
    /** 数据位：6位 */
    public static final int DATABITS_6 = 6;
    /** 数据位：7位 */
    public static final int DATABITS_7 = 7;
    /** 数据位：8位 */
    public static final int DATABITS_8 = 8;

    /** 流控制：无流控制 */
    public static final int FLOWCONTROL_NONE = 0;
    /** 流控制：RTS/CTS 输入 */
    public static final int FLOWCONTROL_RTSCTS_IN = 1;
    /** 流控制：RTS/CTS 输出 */
    public static final int FLOWCONTROL_RTSCTS_OUT = 2;
    /** 流控制：XON/XOFF 输入 */
    public static final int FLOWCONTROL_XONXOFF_IN = 4;
    /** 流控制：XON/XOFF 输出 */
    public static final int FLOWCONTROL_XONXOFF_OUT = 8;

    /** 校验位：偶校验 */
    public static final int PARITY_EVEN = 2;
    /** 校验位：标记校验 */
    public static final int PARITY_MARK = 3;
    /** 校验位：无校验 */
    public static final int PARITY_NONE = 0;
    /** 校验位：奇校验 */
    public static final int PARITY_ODD = 1;
    /** 校验位：空格校验 */
    public static final int PARITY_SPACE = 4;

    /** 停止位：1位 */
    public static final int STOPBITS_1 = 1;
    /** 停止位：1.5位 */
    public static final int STOPBITS_1_5 = 3;
    /** 停止位：2位 */
    public static final int STOPBITS_2 = 2;

    /**
     * 关闭串口连接。
     * @throws IOException 关闭过程中可能抛出 IO 异常
     */
    void close() throws IOException;

    /**
     * 获取数据载波状态。
     * @return true 表示有数据载波，false 表示没有
     * @throws IOException 获取过程中可能抛出 IO 异常
     */
    boolean getCD() throws IOException;

    /**
     * 获取清除发送请求状态。
     * @return true 表示 CTS 线为高，false 表示 CTS 线为低
     * @throws IOException 获取过程中可能抛出 IO 异常
     */
    boolean getCTS() throws IOException;

    /**
     * 获取数据发送准备状态。
     * @return true 表示 DSR 线为高，false 表示 DSR 线为低
     * @throws IOException 获取过程中可能抛出 IO 异常
     */
    boolean getDSR() throws IOException;

    /**
     * 获取数据终端准备状态。
     * @return true 表示 DTR 线为高，false 表示 DTR 线为低
     * @throws IOException 获取过程中可能抛出 IO 异常
     */
    boolean getDTR() throws IOException;

    /**
     * 设置数据终端准备状态。
     * @param z true 表示设置 DTR 线为高，false 表示设置 DTR 线为低
     * @throws IOException 设置过程中可能抛出 IO 异常
     */
    void setDTR(boolean z) throws IOException;

    /**
     * 获取与该串口关联的驱动程序。
     * @return UsbSerialDriver 驱动程序实例
     */
    UsbSerialDriver getDriver();

    /**
     * 获取串口号。
     * @return 串口号
     */
    int getPortNumber();

    /**
     * 获取响铃指示状态。
     * @return true 表示有响铃，false 表示没有
     * @throws IOException 获取过程中可能抛出 IO 异常
     */
    boolean getRI() throws IOException;

    /**
     * 获取请求发送状态。
     * @return true 表示 RTS 线为高，false 表示 RTS 线为低
     * @throws IOException 获取过程中可能抛出 IO 异常
     */
    boolean getRTS() throws IOException;

    /**
     * 设置请求发送状态。
     * @param z true 表示设置 RTS 线为高，false 表示设置 RTS 线为低
     * @throws IOException 设置过程中可能抛出 IO 异常
     */
    void setRTS(boolean z) throws IOException;

    /**
     * 获取串口的唯一标识符。
     * @return 串口的序列号
     */
    String getSerial();

    /**
     * 打开串口连接。
     * @param usbDeviceConnection USB 设备连接实例
     * @throws IOException 打开过程中可能抛出 IO 异常
     */
    void open(UsbDeviceConnection usbDeviceConnection) throws IOException;

    /**
     * 清除硬件缓冲区。
     * @param z true 表示清除输入缓冲区，false 表示不清除
     * @param z2 true 表示清除输出缓冲区，false 表示不清除
     * @return true 表示成功清除，false 表示失败
     * @throws IOException 清除过程中可能抛出 IO 异常
     */
    boolean purgeHwBuffers(boolean z, boolean z2) throws IOException;

    /**
     * 从串口读取数据。
     * @param bArr 存储读取数据的字节数组
     * @param i 读取的最大字节数
     * @return 实际读取的字节数
     * @throws IOException 读取过程中可能抛出 IO 异常
     */
    int read(byte[] bArr, int i) throws IOException;

    /**
     * 设置串口参数。
     * @param i 数据位
     * @param i2 停止位
     * @param i3 校验位
     * @param i4 流控制
     * @throws IOException 设置过程中可能抛出 IO 异常
     */
    void setParameters(int i, int i2, int i3, int i4) throws IOException;

    /**
     * 向串口写入数据。
     * @param bArr 要写入的字节数组
     * @param i 要写入的字节数
     * @return 实际写入的字节数
     * @throws IOException 写入过程中可能抛出 IO 异常
     */
    int write(byte[] bArr, int i) throws IOException;
}