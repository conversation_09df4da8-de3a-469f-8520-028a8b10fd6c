package com.smartcar.easylauncher.infrastructure.skin.attr;

import android.content.res.ColorStateList;
import android.view.View;
import android.widget.TextView;
import androidx.core.widget.TextViewCompat;

import com.smartcar.easylauncher.infrastructure.skin.SkinManager;

public class TextViewDrawableTintAttr extends SkinAttr {

    public TextViewDrawableTintAttr() {
        super("drawableTint");
    }

    public TextViewDrawableTintAttr(String attrName, String attrType, String resName, int resId) {
        super(attrName, attrType, resName, resId);
    }

    @Override
    public void applySkin(View view) {
        if (!(view instanceof TextView)) return;

        TextView tv = (TextView) view;
        if (isColor()) {
            try {
                // 使用 TextViewCompat 设置 drawable tint,支持所有 API 版本
                TextViewCompat.setCompoundDrawableTintList(tv,
                        ColorStateList.valueOf(SkinManager.getInstance().getColor(resName, resId))
                );
            } catch (Exception e) {
                // 处理可能的异常
            }
        }
    }
}