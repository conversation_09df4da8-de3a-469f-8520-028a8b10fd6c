package com.smartcar.easylauncher.infrastructure.system.service.weather.provider;


import androidx.annotation.NonNull;

import com.elvishew.xlog.XLog;
import com.smartcar.easylauncher.data.processor.WeatherDataProcessor;
import com.smartcar.easylauncher.core.manager.AuthorityManager;
import com.smartcar.easylauncher.data.model.navigation.LocateInforModel;
import com.smartcar.easylauncher.data.model.weather.RealTimeWeatherModel;
import com.smartcar.easylauncher.data.model.weather.WeatherDataModel;
import com.smartcar.easylauncher.data.model.weather.WeatherForecast;
import com.smartcar.easylauncher.infrastructure.system.service.weather.ErrorCode;
import com.smartcar.easylauncher.infrastructure.system.service.weather.WeatherCallback;
import com.smartcar.easylauncher.infrastructure.system.service.weather.WeatherError;
import com.smartcar.easylauncher.infrastructure.system.service.weather.WeatherProvider;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import rxhttp.RxHttp;

/**
 * OpenWeather API实现
 * 实现OpenWeatherMap的天气数据获取
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-03-21
 */
public class OpenWeatherProvider implements WeatherProvider {

    /**
     * 日志标签
     */
    private static final String TAG = OpenWeatherProvider.class.getSimpleName();

    /**
     * API基础URL
     */
    private static final String BASE_URL = "https://api.openweathermap.org/data/2.5";

    /**
     * 提供者优先级
     */
    private static final int PRIORITY = 2;

    /**
     * API密钥
     */
    private final String apiKey;

    /**
     * 实时天气数据
     */
    private RealTimeWeatherModel liveResult;

    /**
     * 天气预报数据
     */
    private WeatherForecast forecastResult;


    /**
     * RxJava销毁器
     */
    private final CompositeDisposable disposables;

    public OpenWeatherProvider() {
        this.apiKey = AuthorityManager.getOpenWeatherAPPId();
        this.disposables = new CompositeDisposable();
    }

    @Override
    public int getPriority() {
        return PRIORITY;
    }

    @NonNull
    @Override
    public String getProviderName() {
        return "OpenWeather";
    }

    @Override
    public void fetchWeatherData(@NonNull LocateInforModel location, @NonNull WeatherCallback callback) {
        XLog.tag(TAG).i("开始获取OpenWeather天气数据 - 位置: %s", location.getCity());
        
        // 检查API密钥
        if (!isAvailable()) {
            XLog.tag(TAG).e("OpenWeather API密钥无效");
            callback.onError(new WeatherError(ErrorCode.UNKNOWN_ERROR, "OpenWeather API密钥无效"));
            return;
        }

        // 检查位置信息
        if (!isValidLocation(location)) {
            XLog.tag(TAG).e("无效的位置信息: lat=%f, lon=%f", location.getLatitude(), location.getLongitude());
            callback.onError(new WeatherError(ErrorCode.INVALID_LOCATION, "无效的位置信息"));
            return;
        }
        
        // 先获取实时天气
        fetchLiveWeather(location, callback);
    }

    /**
     * 获取实时天气数据
     */
    private void fetchLiveWeather(LocateInforModel location, WeatherCallback callback) {
        try {
            String url = String.format("%s/weather?lat=%f&lon=%f&lang=zh_cn&units=metric&appid=%s",
                    BASE_URL, location.getLatitude(), location.getLongitude(), apiKey);

            XLog.tag(TAG).d("请求实时天气数据: %s", url.replace(apiKey, "API_KEY"));

            // 发起网络请求
            disposables.add(RxHttp.get(url)
                    .connectTimeout(30000)
                    .toObservable(RealTimeWeatherModel.class)
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(
                            weatherData -> {
                                XLog.tag(TAG).d("实时天气数据获取成功: %s", weatherData.toString());
                                liveResult = weatherData;
                                fetchForecastWeather(location, callback);
                            },
                            throwable -> {
                                XLog.tag(TAG).e("实时天气数据获取失败: %s", throwable.getMessage());
                                callback.onError(new WeatherError(ErrorCode.API_CALL_FAILED,
                                        "OpenWeather实时天气API调用失败: " + throwable.getMessage()));
                            }
                    ));

        } catch (Exception e) {
            XLog.tag(TAG).e("实时天气请求异常: %s", e.getMessage());
            callback.onError(new WeatherError(ErrorCode.API_CALL_FAILED, 
                "OpenWeather实时天气请求异常: " + e.getMessage()));
        }
    }

    /**
     * 获取天气预报数据
     */
    private void fetchForecastWeather(LocateInforModel location, WeatherCallback callback) {
        try {
            String url = String.format("%s/forecast?lat=%f&lon=%f&units=metric&lang=zh_cn&appid=%s",
                    BASE_URL, location.getLatitude(), location.getLongitude(), apiKey);

            XLog.tag(TAG).d("请求天气预报数据: %s", url.replace(apiKey, "API_KEY"));

            disposables.add(RxHttp.get(url)
                    .connectTimeout(30000)
                    .toObservable(WeatherForecast.class)
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(
                            forecast -> {
                                XLog.tag(TAG).d("天气预报数据获取成功: %s", forecast.toString());
                                forecastResult = forecast;
                                
                                // 合并数据并回调
                                WeatherDataProcessor weatherDataProcessor = new WeatherDataProcessor();
                                WeatherDataModel weatherDataModel = weatherDataProcessor.processWeatherData(
                                    location.getCity(),
                                    location.getDistrict(),
                                    location.getLatitude(),
                                    location.getLongitude(),
                                    getProviderName(),
                                    forecastResult, 
                                    liveResult
                                );
                                
                                XLog.tag(TAG).i("天气数据处理完成，准备回调");
                                callback.onWeatherDataReceived(weatherDataModel);
                            },
                            throwable -> {
                                XLog.tag(TAG).e("天气预报数据获取失败: %s", throwable.getMessage());
                                callback.onError(new WeatherError(ErrorCode.API_CALL_FAILED,
                                        "OpenWeather预报API调用失败: " + throwable.getMessage()));
                            }
                    ));

        } catch (Exception e) {
            XLog.tag(TAG).e("天气预报请求异常: %s", e.getMessage());
            callback.onError(new WeatherError(ErrorCode.API_CALL_FAILED,
                    "OpenWeather天气预报请求异常: " + e.getMessage()));
        }
    }

    @Override
    public boolean isAvailable() {
        return apiKey != null && !apiKey.isEmpty();
    }

    @Override
    public String getApiKey() {
        return apiKey;
    }

    /**
     * 检查位置信息是否有效
     *
     * @param location 位置信息
     * @return 是否有效
     */
    private boolean isValidLocation(LocateInforModel location) {
        boolean isValid = location != null
                && location.getLatitude() != 0.0
                && location.getLongitude() != 0.0;
                
        if (!isValid) {
            XLog.tag(TAG).w("位置信息无效 - latitude: %f, longitude: %f", 
                location != null ? location.getLatitude() : 0.0,
                location != null ? location.getLongitude() : 0.0);
        }
        
        return isValid;
    }

    /**
     * 清理资源
     */
    public void destroy() {
        XLog.tag(TAG).d("清理OpenWeather提供者资源");
        disposables.clear();
    }
}