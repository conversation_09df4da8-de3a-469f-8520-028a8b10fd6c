package com.smartcar.easylauncher.infrastructure.event;

import android.view.KeyEvent;


import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;


/**
 * 键盘事件总线定义
 * 
 * <AUTHOR>
 * @date 2024/07/18
 */
@EventGroup(value = "KeyEventScope", active = true)
public class KeyEventDefine {
    
    /**
     * 按键事件，用于传递按键代码和KeyEvent
     */
    @Event(value = "KeyCodeEvent", multiProcess = false)
    KeyCodeEvent keyCodeEvent;
    public static class KeyCodeEvent {
        private int keyCode;
        private KeyEvent keyEvent;
        
        public KeyCodeEvent(int keyCode, KeyEvent keyEvent) {
            this.keyCode = keyCode;
            this.keyEvent = keyEvent;
        }
        
        public int getKeyCode() {
            return keyCode;
        }
        
        public void setKeyCode(int keyCode) {
            this.keyCode = keyCode;
        }
        
        public KeyEvent getKeyEvent() {
            return keyEvent;
        }
        
        public void setKeyEvent(KeyEvent keyEvent) {
            this.keyEvent = keyEvent;
        }
    }
} 