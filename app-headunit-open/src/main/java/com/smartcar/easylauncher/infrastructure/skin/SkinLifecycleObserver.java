package com.smartcar.easylauncher.infrastructure.skin;

import android.app.Activity;

import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;

public class SkinLifecycleObserver implements LifecycleObserver {
    private final Activity activity;

    public SkinLifecycleObserver(Activity activity) {
        this.activity = activity;
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    public void onDestroy() {
        SkinManager.getInstance().unregisterSkin(activity);
    }
}