package com.smartcar.easylauncher.infrastructure.event.scope.device;


import com.smartcar.easylauncher.data.model.vehicle.TireCodeQueryModel;
import com.smartcar.easylauncher.data.model.vehicle.TireOperateResultModel;
import com.smartcar.easylauncher.modules.tpms.core.TpmsStateEvent;
import com.smartcar.easylauncher.modules.tpms.protocol.base.model.TireStatusData;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 胎压事件总线
 */
@EventGroup(value = "TirePressureScope", active = true)
public class TirePressureEventDefine {

    /**
     * 胎压编码查询
     */
    @Event(value = "TireCodeQueryBean", multiProcess = true)
    TireCodeQueryModel eventTireCodeQueryBean;

    /**
     * 胎压操作响应
     */
    @Event(value = "TireOperateResponseBean", multiProcess = true)
    TireOperateResultModel eventTireOperateResponse;

    /**
     * 轮胎状态信息
     */
    @Event(value = "TireStatusDataResponseBean", multiProcess = true)
    TireStatusData eventTireStatusDataResponse;

    /**
     * 设备状态通知
     */
    @Event(value = "TpmsStateEvent", multiProcess = true)
    TpmsStateEvent eventTpmsState;

}