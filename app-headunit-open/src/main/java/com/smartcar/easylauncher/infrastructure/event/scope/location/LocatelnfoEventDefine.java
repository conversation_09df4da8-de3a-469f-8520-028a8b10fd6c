package com.smartcar.easylauncher.infrastructure.event.scope.location;


import com.smartcar.easylauncher.data.model.navigation.LocateInforModel;

import java.util.List;
import java.util.Map;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 定位信息事件定义
 */
@EventGroup(value = "LocatelnfoScope", active = true)
public class LocatelnfoEventDefine {
    @Event(value = "LocatelnfoeventInt", multiProcess = false)
    Integer eventInt;

    @Event(value = "LocatelnfoeventString", multiProcess = true)
    String eventString;

    @Event(value = "LocatelnfoeventBean", multiProcess = true)
    LocateInforModel eventBean;

    @Event(value = "LocatelnfoeventBean", multiProcess = true)
    List<String> eventList;

    @Event(value = "LocatelnfoeventMap", multiProcess = true)
    Map<String, List<String>> eventMap;
}