package com.smartcar.easylauncher.infrastructure.system.service.weather;


import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import java.util.HashMap;
import java.util.Map;

/**
 * 天气错误信息模型
 * 用于封装天气服务的错误信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-03-21
 */
public class WeatherError {

    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误信息
     */
    private final String message;

    /**
     * 错误详情
     */
    private String details;

    /**
     * 发生时间
     */
    private final long timestamp;

    /**
     * 错误来源
     */
    private String source;

    /**
     * 额外信息
     */
    private final Map<String, Object> extras;

    /**
     * 构造方法
     *
     * @param code 错误码
     * @param message 错误信息
     */
    public WeatherError(int code, @NonNull String message) {
        this.code = code;
        this.message = message;
        this.timestamp = System.currentTimeMillis();
        this.extras = new HashMap<>();
    }

    /**
     * 构造方法
     *
     * @param code 错误码
     * @param message 错误信息
     * @param details 错误详情
     */
    public WeatherError(int code, @NonNull String message, String details) {
        this(code, message);
        this.details = details;
    }

    /**
     * 构造方法
     *
     * @param code 错误码
     * @param message 错误信息
     * @param source 错误来源
     * @param details 错误详情
     */
    public WeatherError(int code, @NonNull String message, String source, String details) {
        this(code, message, details);
        this.source = source;
    }

    /**
     * 获取错误码
     *
     * @return 错误码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取错误信息
     *
     * @return 错误信息
     */
    @NonNull
    public String getMessage() {
        return message;
    }

    /**
     * 获取错误详情
     *
     * @return 错误详情
     */
    @Nullable
    public String getDetails() {
        return details;
    }

    /**
     * 获取错误发生时间
     *
     * @return 时间戳
     */
    public long getTimestamp() {
        return timestamp;
    }

    /**
     * 获取错误来源
     *
     * @return 错误来源
     */
    @Nullable
    public String getSource() {
        return source;
    }

    /**
     * 添加额外信息
     *
     * @param key 键
     * @param value 值
     */
    public void addExtra(@NonNull String key, @Nullable Object value) {
        extras.put(key, value);
    }

    /**
     * 获取额外信息
     *
     * @param key 键
     * @return 值
     */
    @Nullable
    public Object getExtra(@NonNull String key) {
        return extras.get(key);
    }

    /**
     * 检查是否是网络错误
     *
     * @return 是否是网络错误
     */
    public boolean isNetworkError() {
        return code == ErrorCode.NO_NETWORK;
    }

    /**
     * 检查是否是API错误
     *
     * @return 是否是API错误
     */
    public boolean isApiError() {
        return code == ErrorCode.API_CALL_FAILED;
    }

    /**
     * 检查是否是数据错误
     *
     * @return 是否是数据错误
     */
    public boolean isDataError() {
        return code == ErrorCode.DATA_PARSE_ERROR;
    }

    /**
     * 检查是否是位置错误
     *
     * @return 是否是位置错误
     */
    public boolean isLocationError() {
        return code == ErrorCode.INVALID_LOCATION;
    }

    /**
     * 检查是否是缓存错误
     *
     * @return 是否是缓存错误
     */
    public boolean isCacheError() {
        return code == ErrorCode.CACHE_ERROR;
    }

    /**
     * 检查错误是否可恢复
     *
     * @return 是否可恢复
     */
    public boolean isRecoverable() {
        return code == ErrorCode.NO_NETWORK
                || code == ErrorCode.API_CALL_FAILED;
    }

    /**
     * 获取错误的用户友好描述
     *
     * @return 用户友好的错误描述
     */
    @NonNull
    public String getUserFriendlyMessage() {
        switch (code) {
            case ErrorCode.NO_NETWORK:
                return "网络连接不可用，请检查网络设置";
            case ErrorCode.NO_PROVIDER:
                return "无可用的天气服务提供者";
            case ErrorCode.ALL_PROVIDERS_FAILED:
                return "天气数据获取失败，请稍后重试";
            case ErrorCode.INVALID_LOCATION:
                return "位置信息无效，请检查位置权限";
            case ErrorCode.API_CALL_FAILED:
                return "天气服务暂时不可用，请稍后重试";
            case ErrorCode.DATA_PARSE_ERROR:
                return "天气数据解析失败";
            case ErrorCode.CACHE_ERROR:
                return "天气数据缓存异常";
            default:
                return "未知错误，请稍后重试";
        }
    }

    /**
     * 获取错误的调试信息
     *
     * @return 调试信息
     */
    @NonNull
    public String getDebugInfo() {
        StringBuilder debug = new StringBuilder();
        debug.append("Error Code: ").append(code).append("\n");
        debug.append("Message: ").append(message).append("\n");
        if (source != null) {
            debug.append("Source: ").append(source).append("\n");
        }
        if (details != null) {
            debug.append("Details: ").append(details).append("\n");
        }
        if (!extras.isEmpty()) {
            debug.append("Extras: ").append(extras).append("\n");
        }
        debug.append("Timestamp: ").append(timestamp);
        return debug.toString();
    }

    /**
     * 创建网络错误
     *
     * @param details 错误详情
     * @return WeatherError实例
     */
    public static WeatherError createNetworkError(String details) {
        return new WeatherError(ErrorCode.NO_NETWORK, "网络连接失败", details);
    }

    /**
     * 创建API错误
     *
     * @param source API来源
     * @param details 错误详情
     * @return WeatherError实例
     */
    public static WeatherError createApiError(String source, String details) {
        return new WeatherError(ErrorCode.API_CALL_FAILED, "API调用失败", source, details);
    }

    /**
     * 创建数据解析错误
     *
     * @param details 错误详情
     * @return WeatherError实例
     */
    public static WeatherError createParseError(String details) {
        return new WeatherError(ErrorCode.DATA_PARSE_ERROR, "数据解析失败", details);
    }

    @NonNull
    @Override
    public String toString() {
        return "WeatherError{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", source='" + source + '\'' +
                ", details='" + details + '\'' +
                ", timestamp=" + timestamp +
                ", extras=" + extras +
                '}';
    }
}