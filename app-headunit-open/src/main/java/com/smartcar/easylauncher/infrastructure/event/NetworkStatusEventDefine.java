package com.smartcar.easylauncher.infrastructure.event;


import com.smartcar.easylauncher.data.model.network.NetworkStatusModel;

import java.util.List;
import java.util.Map;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 网络状态事件定义
 */
@EventGroup(value = "NetworkStatusScope", active = true)
public class NetworkStatusEventDefine {
    @Event(value = "NetworkStatuseventInt", multiProcess = false)
    Integer eventInt;

    @Event(value = "NetworkStatuseventString", multiProcess = true)
    String eventString;

    @Event(value = "NetworkStatuseventString", multiProcess = true)
    Boolean eventBoolean;

    @Event(value = "NetworkStatuseventBean", multiProcess = true)
    NetworkStatusModel eventBean;

    @Event(value = "NetworkStatuseventBean", multiProcess = true)
    List<String> eventList;

    @Event(value = "NetworkStatuseventMap", multiProcess = true)
    Map<String, List<String>> eventMap;
}