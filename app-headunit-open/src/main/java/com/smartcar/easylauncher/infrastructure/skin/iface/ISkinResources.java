package com.smartcar.easylauncher.infrastructure.skin.iface;

import android.content.res.ColorStateList;
import android.graphics.drawable.Drawable;

/**
 * 皮肤资源接口
 * <p>
 * 定义了获取应用内皮肤资源的方法。这些方法允许访问颜色、颜色状态列表（如选择器）和可绘制对象（如图片）等皮肤资源。
 * 接口的实现应能够处理从默认资源到皮肤资源的切换，以便在应用中动态应用不同的皮肤主题。
 */
public interface ISkinResources {

    /**
     * 根据资源 id 获取颜色HEX字符串
     * <p>
     * 如果已设置皮肤资源，则尝试从皮肤资源中获取颜色字符串；否则，从默认资源中获取。
     *
     * @param resId 资源ID，用于在资源文件中查找颜色字符串
     * @return 相应的颜色字符串
     */
    String getColorHexString(int resId);


    /**
     * 根据资源ID获取颜色值
     * <p>
     * 如果已设置皮肤资源，则尝试从皮肤资源中获取颜色；否则，从默认资源中获取。
     *
     * @param resId 资源ID，用于在资源文件中查找颜色值
     * @return 相应的颜色值
     */
    int getColor(int resId);

    /**
     * 根据资源名称和ID获取颜色值
     * <p>
     * 如果已设置皮肤资源，则首先尝试根据资源名称从皮肤资源中获取颜色；如果未找到，则回退到根据资源ID从默认资源中获取。
     *
     * @param resName 资源名称，用于在皮肤资源中查找颜色值（如果已设置）
     * @param resId   资源ID，用于在默认资源中查找颜色值（如果皮肤资源中未找到）
     * @return 相应的颜色值
     */
    int getColor(String resName, int resId);

    /**
     * 加载指定资源ID的颜色状态列表（如选择器）
     * <p>
     * 如果已设置皮肤资源，则尝试从皮肤资源中加载颜色状态列表；否则，从默认资源中加载。
     *
     * @param resId 资源ID，用于在资源文件中查找颜色状态列表
     * @return 相应的颜色状态列表（ColorStateList）
     */
    ColorStateList getColorStateList(int resId);

    /**
     * 加载指定资源名称和ID的颜色状态列表（如选择器）
     * <p>
     * 如果已设置皮肤资源，则首先尝试根据资源名称从皮肤资源中加载颜色状态列表；如果未找到，则回退到根据资源ID从默认资源中加载。
     *
     * @param resName 资源名称，用于在皮肤资源中查找颜色状态列表（如果已设置）
     * @param resId   资源ID，用于在默认资源中查找颜色状态列表（如果皮肤资源中未找到）
     * @return 相应的颜色状态列表（ColorStateList）
     */
    ColorStateList getColorStateList(String resName, int resId);

    /**
     * 根据资源ID获取可绘制对象（如图片）
     * <p>
     * 如果已设置皮肤资源，则尝试从皮肤资源中获取可绘制对象；否则，从默认资源中获取。
     *
     * @param resId 资源ID，用于在资源文件中查找可绘制对象
     * @return 相应的可绘制对象（Drawable）
     */
    Drawable getDrawable(int resId);

    /**
     * 根据资源名称和ID获取可绘制对象（如图片）
     * <p>
     * 如果已设置皮肤资源，则首先尝试根据资源名称从皮肤资源中获取可绘制对象；如果未找到，则回退到根据资源ID从默认资源中获取。
     *
     * @param resName 资源名称，用于在皮肤资源中查找可绘制对象（如果已设置）
     * @param resId   资源ID，用于在默认资源中查找可绘制对象（如果皮肤资源中未找到）
     * @return 相应的可绘制对象（Drawable）
     */
    Drawable getDrawable(String resName, int resId);

/**
 * 根据资源名称、资源类型和ID获取可绘制对象（如图片）
 * <p>
 * 允许更精确地指定资源类型（如drawable、mipmap等），并从相应的皮肤资源或默认资源中加载可绘制对象。
 *
 * @param resName 资源名称，用于在资源中查找可绘制对象
 * @param resType 资源类型（如"drawable"、"mipmap"等）
 * @param resId   资源ID，作为回退选项（如果根据资源名称和类型未找到）
 */
    Drawable getDrawable(String resName,String resType,int resId);
}
