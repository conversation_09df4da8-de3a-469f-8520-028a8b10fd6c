package com.smartcar.easylauncher.infrastructure.event;




import com.smartcar.easylauncher.data.model.task.TaskNotificationModel;

import java.util.List;
import java.util.Map;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 任务通知事件定义
 */
@EventGroup(value = "TaskNotificationScope", active = true)
public class TaskNotificationEventDefine {
    @Event(value = "TaskNotificationEventInt", multiProcess = false)
    Integer eventInt;

    @Event(value = "TaskNotificationEventString", multiProcess = true)
    String eventString;

    @Event(value = "TaskNotificationEventBean", multiProcess = true)
    TaskNotificationModel eventBean;

    @Event(value = "TaskNotificationEventBean", multiProcess = true)
    List<String> eventList;

    @Event(value = "TaskNotificationEventMap", multiProcess = true)
    Map<String, List<String>> eventMap;
}