package com.smartcar.easylauncher.infrastructure.event.scope.location;



import com.smartcar.easylauncher.data.model.navigation.AMapLaneInfo;
import com.smartcar.easylauncher.data.model.navigation.NavInfo;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 导航事件
 */
@EventGroup(value = "NavInfoScope", active = true)
public class NavInfoEventDefine {

    @Event(value = "NavoperationStatuseventInt", multiProcess = false)
    Integer eventInt;

    @Event(value = "NavInfoeventBean", multiProcess = true)
    NavInfo eventBean;

    @Event(value = "AmapLaneInfoeventBean", multiProcess = true)
    AMapLaneInfo AmapLaneInfoeventBean;
}