package com.smartcar.easylauncher.infrastructure.event.scope.device;




import com.smartcar.easylauncher.data.model.system.UsbMessageModel;

import java.util.List;
import java.util.Map;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * USB消息事件定义
 */
@EventGroup(value = "UsbMessageScope", active = true)
public class USBMessageEventDefine {
    @Event(value = "UsbMessageeventInt", multiProcess = false)
    Integer eventInt;

    @Event(value = "UsbMessageeventString", multiProcess = true)
    String eventString;

    @Event(value = "UsbMessageeventBean", multiProcess = true)
    UsbMessageModel eventBean;

    @Event(value = "UsbMessageeventBean", multiProcess = true)
    List<String> eventList;

    @Event(value = "UsbMessageeventMap", multiProcess = true)
    Map<String, List<String>> eventMap;
}