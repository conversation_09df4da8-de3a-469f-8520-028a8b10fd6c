package com.smartcar.easylauncher.infrastructure.skin;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;

import java.lang.reflect.Field;

/**
 * SkinLayoutInflater类扩展了LayoutInflater，用于在布局加载过程中支持皮肤或主题的动态替换。
 * 它通过覆盖原有的Factory2机制，并引入SkinLayoutInflaterFactory来处理视图的自定义创建逻辑。
 */
public class SkinLayoutInflater extends LayoutInflater {

    /**
     * 构造函数，接收原始的LayoutInflater和新的Context。
     * 通过这个构造函数，我们可以将SkinLayoutInflaterFactory设置为默认的Factory2。
     *
     * @param original 原始的LayoutInflater实例
     * @param newContext 新的Context环境
     */
    public SkinLayoutInflater(LayoutInflater original, Context newContext) {
        super(original, newContext);
        Factory2 f2 = getFactory2(); // 获取当前的Factory2实例
        if (!(f2 instanceof SkinLayoutInflaterFactory)) { // 如果不是SkinLayoutInflaterFactory实例
            setPrivateFactory2().addFactory2(f2); // 则将其添加到新的SkinLayoutInflaterFactory中
        }
    }

    /**
     * 在新的Context中克隆这个LayoutInflater实例。
     * 这样做是为了确保在新的Context环境中，仍然能够使用SkinLayoutInflater的自定义逻辑。
     *
     * @param newContext 新的Context环境
     * @return 返回一个新的SkinLayoutInflater实例
     */
    @Override
    public LayoutInflater cloneInContext(Context newContext) {
        return new SkinLayoutInflater(this, newContext);
    }

    /**
     * 设置自定义的Factory2实例。
     * 如果当前的Factory2已经是SkinLayoutInflaterFactory的实例，则直接将新的Factory2添加到其中。
     * 否则，通过反射将Factory2设置为SkinLayoutInflaterFactory，并将新的Factory2添加到其中。
     *
     * @param factory 自定义的Factory2实例
     */
    @Override
    public void setFactory2(Factory2 factory) {
        Factory2 f2 = getFactory2(); // 获取当前的Factory2实例
        if (f2 instanceof SkinLayoutInflaterFactory) { // 如果已经是SkinLayoutInflaterFactory实例
            ((SkinLayoutInflaterFactory) f2).addFactory2(factory); // 直接将新的Factory2添加到其中
        } else {
            setPrivateFactory2().addFactory2(factory); // 否则，先设置SkinLayoutInflaterFactory，再将新的Factory2添加到其中
        }
    }

    /**
     * 私有方法，用于通过反射或正常方式设置Factory2为SkinLayoutInflaterFactory实例。
     * 如果通过正常方式设置失败，则尝试通过反射来设置。
     *
     * @return 返回设置后的SkinLayoutInflaterFactory实例
     */
    private SkinLayoutInflaterFactory setPrivateFactory2() {
        SkinLayoutInflaterFactory factory2 = new SkinLayoutInflaterFactory();
        try {
            super.setFactory2(factory2); // 尝试通过正常方式设置
            return factory2;
        } catch (Exception e) {
            try {
                Field field = LayoutInflater.class.getDeclaredField("mFactory2"); // 反射获取mFactory2字段
                field.setAccessible(true); // 取消语言访问检查
                field.set(this, factory2); // 通过反射设置mFactory2字段
            } catch (Exception ex) {
                Log.w("SkinSupport", "SkinLayoutInflater setPrivateFactory2反射设置失败;");
            }
            return factory2;
        }
    }
}
