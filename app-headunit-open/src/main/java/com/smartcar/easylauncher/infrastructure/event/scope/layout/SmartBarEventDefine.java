package com.smartcar.easylauncher.infrastructure.event.scope.layout;



import com.smartcar.easylauncher.data.model.common.SmartBarModel;
import com.smartcar.easylauncher.data.model.common.StatusLayoutNotificationModel;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 智能栏事件定义
 */
@EventGroup(value = "SmartBarScope", active = true)
public class SmartBarEventDefine {


    @Event(value = "NavBarEventBean", multiProcess = true)
    SmartBarModel navBarEventBean;

    @Event(value = "StatusBarEventBean", multiProcess = true)
    StatusLayoutNotificationModel statusBarEventBean;

}