package com.smartcar.easylauncher.infrastructure.system.service

import android.app.Notification
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.content.ContextCompat
import com.smartcar.easylauncher.shared.utils.apportutil.LogUtils
import com.smartcar.easylauncher.shared.utils.apportutil.NotificationUtils
import com.smartcar.easylauncher.shared.utils.apportutil.Tag
import com.smartcar.easylauncher.shared.utils.apportutil.WifiUtils

class WebService : Service() {


    override fun onBind(intent: Intent): IBinder? {
        return null
    }


    override fun onCreate() {
        super.onCreate()

        buildNotification()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        try {
            LogUtils.wtf(Tag, "WebService onStartCommand - flags: $flags, startId: $startId")
            
            if (intent == null) {
                LogUtils.wtf(Tag, "Intent is null, using default START_NOT_STICKY")
                return START_NOT_STICKY
            }

            val action = intent.action
            LogUtils.wtf(Tag, "Processing action: $action")

            when (action) {
                ACTION_START_WEB_SERVICE -> {
                    val ipAddr = WifiUtils.getIp()
                    LogUtils.wtf(Tag, "Starting web server, device ip=$ipAddr")
                    WebHelper.instance.startServer(this)
                    return START_STICKY
                }
                ACTION_STOP_WEB_SERVICE -> {
                    LogUtils.wtf(Tag, "Stopping web server")
                    WebHelper.instance.stopService()
                    stopSelf()
                    return START_NOT_STICKY
                }
                else -> {
                    LogUtils.wtf(Tag, "Unknown action: $action")
                    return START_NOT_STICKY
                }
            }
        } catch (e: Exception) {
            LogUtils.wtf(Tag, "Error in onStartCommand: ${e.message}")
            e.printStackTrace()
            return START_NOT_STICKY
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        WebHelper.instance.stopService()
        WebHelper.instance.release()
    }


    private fun buildNotification() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForeground(
                    GRAY_SERVICE_ID,
                    NotificationUtils.createNotificationByChannel(this)
                )
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                startForeground(GRAY_SERVICE_ID, NotificationUtils.buildNotification(this))
            } else {
                val innerIntent = Intent(this, GrayInnerService::class.java)
                startService(innerIntent)
                startForeground(GRAY_SERVICE_ID, Notification())
            }
        } catch (e: Exception) {
            Log.d(TAG, Log.getStackTraceString(e))
        }
    }

    class GrayInnerService : Service() {
        override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
            startForeground(GRAY_SERVICE_ID, Notification())
            stopForeground(true)
            stopSelf()
            return super.onStartCommand(intent, flags, startId)
        }

        override fun onBind(intent: Intent): IBinder? {
            return null
        }
    }

    companion object {
        private const val TAG = "WebService"
        private val GRAY_SERVICE_ID = 1001
        const val ACTION_START_WEB_SERVICE = "START_WEB_SERVICE"
        const val ACTION_STOP_WEB_SERVICE = "STOP_WEB_SERVICE"

        /**
         * 启动Web服务
         * @param context 上下文
         */
        fun start(context: Context) {
            try {
                val intent = Intent(context, WebService::class.java).apply {
                    action = ACTION_START_WEB_SERVICE
                }
                ContextCompat.startForegroundService(context, intent)
            } catch (e: Exception) {
                LogUtils.wtf(TAG, "Error starting service: ${e.message}")
                e.printStackTrace()
            }
        }

        /**
         * 停止Web服务
         * @param context 上下文
         */
        fun stop(context: Context) {
            try {
                val intent = Intent(context, WebService::class.java).apply {
                    action = ACTION_STOP_WEB_SERVICE
                }
                context.startService(intent)
            } catch (e: Exception) {
                LogUtils.wtf(TAG, "Error stopping service: ${e.message}")
                e.printStackTrace()
            }
        }
    }
}