 
package com.smartcar.easylauncher.infrastructure.system.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * monitor apps installed and uninstalled
 * USB状态监控
 */

public class KuwoMusicDataNoticeReceiver extends BroadcastReceiver {
    private static final String TAG = "KuwoMusicDataNoticeReceiver";
    private KuwoMusicDataNoticeCallback mCallback;
    public static boolean network = false;
    public KuwoMusicDataNoticeReceiver(KuwoMusicDataNoticeCallback callback) {
        mCallback = callback;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        MyLog.v(TAG, "KuwoMusicDataNoticeReceiver" + "");
            if (mCallback != null) {
                mCallback.onMusicData(intent);
            }


        }

        public interface KuwoMusicDataNoticeCallback {

            void onMusicData(Intent intent);
        }
    }
