package com.smartcar.easylauncher.infrastructure.event;


import com.smartcar.easylauncher.data.model.network.NetworkModel;

import java.util.List;
import java.util.Map;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 网络信息事件定义
 */
@EventGroup(value = "NetworkInfoScope", active = true)
public class NetworkInfoEventDefine {
    @Event(value = "NetworkInfoeventInt", multiProcess = false)
    Integer eventInt;

    @Event(value = "NetworkInfoeventString", multiProcess = true)
    String eventString;

    @Event(value = "NetworkInfoeventString", multiProcess = true)
    Boolean eventBoolean;

    @Event(value = "NetworkInfoeventBean", multiProcess = true)
    NetworkModel eventBean;

    @Event(value = "NetworkInfoeventBean", multiProcess = true)
    List<String> eventList;

    @Event(value = "NetworkInfoeventMap", multiProcess = true)
    Map<String, List<String>> eventMap;
}