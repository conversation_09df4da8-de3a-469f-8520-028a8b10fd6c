package com.smartcar.easylauncher.infrastructure.system.service;


import android.app.Notification;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.service.notification.NotificationListenerService;
import android.service.notification.StatusBarNotification;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.RequiresApi;

import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * Created by baina on 18-1-2.
 * 音乐通知相关服务
 */


@RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
public class MusicNotificationListenerService extends NotificationListenerService {

    private int musicInfoCount = 0; // 记录音乐信息的数量
    private String musicTitle, musicSinger; // 音乐标题和歌手
    private String mediaFocusPackageName; // 当前音乐播放器的包名

    @Override
    public void onCreate() {
        MyLog.v("MusicNotification", "onCreate: "); // 打印日志
    }

    @Override
    public void onNotificationPosted(StatusBarNotification sbn) {

        MyLog.v("MusicNotification", "onNotificationPosted: " + mediaFocusPackageName); // 打印日志
        String notificationPkg = sbn.getPackageName(); // 获取接收消息APP的包名
        MyLog.v("MusicNotification", "onNotificationPosted:   " + notificationPkg); // 打印日志
        if (notificationPkg != null ) { // 判断包名是否为空

            Bundle extras = null; // 获取通知栏信息
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.KITKAT) {
                extras = sbn.getNotification().extras;
            }
            musicTitle = extras.getString(Notification.EXTRA_TITLE); // 获取音乐标题
            musicSinger = extras.getString(Notification.EXTRA_TEXT); // 获取音乐歌手
            if (sbn.getNotification().bigContentView != null) { // 判断是否有大视图
                ViewGroup view = (ViewGroup) sbn.getNotification().bigContentView.apply(this, null); // 获取大视图
                musicInfoCount = 0; // 重置音乐信息数量
                getMusicInfo(view); // 获取音乐信息
            }

            // 通过广播更新音乐信息
            Intent intent = new Intent();
            intent.setAction("com.media.play");
            intent.putExtra("package_name", mediaFocusPackageName);
            intent.putExtra("music_title", musicTitle);
            intent.putExtra("music_singer", musicSinger);
            sendBroadcast(intent);
            MyLog.v("MusicNotification", "onNotificationPosted: name: " + mediaFocusPackageName +
                    "title: " + musicTitle +
                    "singer: " + musicSinger);
            mediaFocusPackageName = notificationPkg; // 更新当前音乐播放器的包名

        }
    }


    @Override
    public void onNotificationRemoved(StatusBarNotification sbn) {
        String notificationPkg = sbn.getPackageName();
        if (notificationPkg != null) {
//            if (mediaFocusPackageName.equals(notificationPkg)) {
//                //通知信息栏 移除音乐
//                Intent intent = new Intent();
//                intent.setAction("com.media.remove");
//                sendBroadcast(intent);
//
//            }
        }
    }

    // 获取所有子View
    private void getAllChildView(View view) {
        ViewGroup viewGroup = (ViewGroup) view;
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View viewchild = viewGroup.getChildAt(i);
            if (viewchild instanceof TextView) { // 如果是TextView
                MyLog.v("MusicNotification", "getAllChildView: " + ((TextView) viewchild).getText().toString()); // 打印日志
            }
            if (!(viewchild instanceof ViewGroup)) { // 如果不是ViewGroup
//                allchilden.add(viewchild);
                continue;
            }
            getAllChildView(viewchild); // 递归获取子View
        }
    }

    // 获取前两个TextView
    private void getMusicInfo(View view) {
        ViewGroup viewGroup = (ViewGroup) view;
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View viewchild = viewGroup.getChildAt(i);
//            if (musicInfoCount > 1) {
//                return;
//            }
            if (viewchild instanceof ImageView) { // 如果是ImageView

                Bitmap bmap = viewchild.getDrawingCache(); // 获取ImageView的Bitmap
                if (bmap!=null){
                    MyLog.v("MusicNotification", "getAllChildView: " + bmap.toString()); // 打印日志

                }
            }
            if (viewchild instanceof TextView) { // 如果是TextView

                MyLog.v("MusicNotification", "getAllChildView: " + ((TextView) viewchild).getText().toString()); // 打印日志
                if (!"".equals(((TextView) viewchild).getText().toString().trim())) { // 判断TextView是否为空
                    if (musicInfoCount == 0) { // 如果是第一个TextView
                        musicTitle = ((TextView) viewchild).getText().toString(); // 获取音乐标题
                    } else { // 如果是第二个TextView
                        musicSinger = ((TextView) viewchild).getText().toString(); // 获取音乐歌手
                    }
                    musicInfoCount++; // 音乐信息数量加1
                }
            }

            if (!(viewchild instanceof ViewGroup)) { // 如果不是ViewGroup
//                allchilden.add(viewchild);
                continue;
            }
            getMusicInfo(viewchild); // 递归获取子View
        }
    }
}

