package com.smartcar.easylauncher.infrastructure.skin.iface;

import android.app.Activity;
import android.content.Context;

import com.smartcar.easylauncher.infrastructure.skin.SkinFactory;

import java.io.File;
import java.io.InputStream;

/**
 * 皮肤管理接口，定义了皮肤管理的基本方法
 */
public interface ISkinManager extends ISkinResources {

    /**
     * 初始化皮肤管理器
     *
     * @param context 应用程序的上下文，用于获取系统资源和其他服务
     */
    void init(Context context);

    /**
     * 注册一个Activity以应用皮肤
     *
     * @param activity 需要应用皮肤的Activity
     */
    void registerSkin(Activity activity);

    /**
     * 添加皮肤观察者，用于监听皮肤变更事件
     *
     * @param skinObserver 皮肤观察者对象
     */
    void addSkinObserver(OnSkinObserver skinObserver);

    /**
     * 添加布局填充拦截器，用于在布局填充过程中应用皮肤
     *
     * @param context 上下文环境
     * @param interceptor 布局填充拦截器对象
     */
    void addOnInflaterInterceptor(Context context, OnInflaterInterceptor interceptor);

    /**
     * 移除皮肤观察者
     *
     * @param skinObserver 要移除的皮肤观察者对象
     */
    void removeSkinObserver(OnSkinObserver skinObserver);

    /**
     * 移除布局填充拦截器
     *
     * @param context 上下文环境
     * @param interceptor 要移除的布局填充拦截器对象
     */
    void removeOnInflaterInterceptor(Context context, OnInflaterInterceptor interceptor);

    /**
     * 注销Activity的皮肤应用
     *
     * @param activity 需要注销皮肤应用的Activity
     */
    void unregisterSkin(Activity activity);

    /**
     * 加载指定名称的皮肤
     *
     * @param name 皮肤文件的名称或标识符
     */
    void loadSkin(String name);

    /**
     * 加载上次使用的皮肤
     * 如果有保存的上次皮肤信息，则加载该皮肤
     */
    void loadLastSkin();

    /**
     * 重置为默认主题
     * 移除当前应用的所有皮肤，恢复到应用原始的默认主题
     */
    void restoreDefaultTheme();

    /**
     * 提交皮肤项的应用
     * 将指定的皮肤项应用到界面上
     *
     * @param skinItem 需要应用的皮肤项
     */
    void apply(ISkinItem skinItem);

    /**
     * 获取指定Activity的皮肤工厂
     *
     * @param activity 目标Activity
     * @return 对应的SkinFactory实例，用于管理该Activity的皮肤
     */
    SkinFactory getSkinFactory(Activity activity);

    /**
     * 判断是否已有皮肤被加载
     *
     * @return 如果已有皮肤被加载，则返回true；否则返回false
     */
    boolean isHasSkin();

    /**
     * 获取皮肤文件所在的目录
     *
     * @return 皮肤文件存储的目录对象
     */
    File getSkinDir();

    /**
     * 将assets目录中的皮肤文件复制到内存卡中的指定位置
     *
     * @param name 皮肤文件的名称，位于assets目录下
     */
    void registerAssetSkin(String name);

    /**
     * 将其他目录中的皮肤文件复制到内存卡中的指定位置
     *
     * @param filepath 皮肤文件的源路径
     * @param fileName 皮肤文件的名称
     */
    void otherFolderReplication(String filepath, String fileName);

    /**
     * 注册存储卡中的皮肤文件
     * 将存储卡中的指定皮肤文件注册到皮肤管理器中，以便后续加载使用
     *
     * @param fileName 皮肤文件的名称
     */
    void registerFileSkin(String fileName);

    /**
     * 通过输入流注册皮肤
     * 使用输入流加载皮肤数据，并将其注册到皮肤管理器中
     *
     * @param is 输入流，包含皮肤数据
     * @param name 皮肤的名称或标识符
     */
    void registerSkin(InputStream is, String name);
}
