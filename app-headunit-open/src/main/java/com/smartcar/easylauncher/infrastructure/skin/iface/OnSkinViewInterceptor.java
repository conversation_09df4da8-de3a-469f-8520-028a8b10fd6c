package com.smartcar.easylauncher.infrastructure.skin.iface;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;

import com.smartcar.easylauncher.infrastructure.skin.attr.SkinAttrSet;

/**
 * 视图拦截器接口
 * <p>
 * 该接口定义了一个方法，用于在视图创建或属性设置的过程中进行拦截。通过实现此接口，开发者可以在视图被加载到布局中之前，
 * 插入自定义逻辑，如修改视图的属性、应用皮肤属性等。
 * <AUTHOR>
 */
public interface OnSkinViewInterceptor {

    /**
     * 拦截视图的创建或属性设置过程
     * <p>
     * 在视图被创建或属性被设置时，此方法会被调用。通过此方法，开发者可以获取到视图的实例、上下文环境以及视图的属性集，
     * 进而对视图进行自定义处理。例如，可以根据皮肤配置修改视图的背景色、字体颜色等属性。
     *
     * @param view 被拦截的视图对象
     * @param context 上下文环境，用于获取系统服务、资源等
     * @param attrs 视图的属性集，包含从XML布局文件中解析的属性
     * @return 修改后的皮肤属性集（SkinAttrSet），如果不进行任何修改，也可以返回原始的属性集或null（具体取决于实现细节）
     */
    SkinAttrSet interceptorView(View view, Context context, AttributeSet attrs);
}
