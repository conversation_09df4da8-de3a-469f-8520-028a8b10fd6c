package com.smartcar.easylauncher.infrastructure.event;


import com.smartcar.easylauncher.data.model.theme.SkinModel;

import java.util.List;
import java.util.Map;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 主题切换事件定义
 */
@EventGroup(value = "SkinScope", active = true)
public class SkinEventDefine {
    @Event(value = "SkineventInt", multiProcess = false)
    Integer eventInt;

    @Event(value = "SkineventString", multiProcess = true)
    String eventString;

    @Event(value = "SkineventBean", multiProcess = true)
    SkinModel eventBean;

    @Event(value = "SkineventBean", multiProcess = true)
    List<String> eventList;

    @Event(value = "SkineventMap", multiProcess = true)
    Map<String, List<String>> eventMap;
}