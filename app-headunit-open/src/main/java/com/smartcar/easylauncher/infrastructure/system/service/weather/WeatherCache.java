package com.smartcar.easylauncher.infrastructure.system.service.weather;


import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.elvishew.xlog.XLog;
import com.google.gson.Gson;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.data.model.navigation.LocateInforModel;
import com.smartcar.easylauncher.data.model.weather.WeatherDataModel;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 天气数据缓存管理类
 * 负责天气数据的本地存储和读取
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-03-21
 */
public class WeatherCache {

    /**
     * 日志标签
     */
    private static final String TAG = WeatherCache.class.getSimpleName();

    /**
     * 缓存有效期（小时）
     */
    private static final int CACHE_VALIDITY_HOURS = 3;

    /**
     * 当前缓存的天气数据
     */
    private WeatherDataModel cachedWeather;

    /**
     * 最后缓存时间
     */
    private LocalDateTime lastCacheTime;

    /**
     * 构造方法
     *
     */
    public WeatherCache() {
    }

    /**
     * 缓存天气数据
     *
     * @param weatherData 要缓存的天气数据
     */
    public void cacheWeatherData(@NonNull WeatherDataModel weatherData) {
        XLog.tag(TAG).d("缓存天气数据 - 时间: %s", weatherData.getDataTime());
        
        // 更新内存缓存
        this.cachedWeather = weatherData;
        this.lastCacheTime = LocalDateTime.now();
        
        // 持久化到本地存储
        persistCache();
    }

    /**
     * 获取缓存的天气数据
     *
     * @return 缓存的天气数据，如果没有有效缓存则返回null
     */
    @Nullable
    public WeatherDataModel getCachedWeather() {
        // 如果内存中没有缓存，尝试从本地存储加载
        if (cachedWeather == null) {
            loadCacheFromDisk();
        }
        return cachedWeather;
    }

    /**
     * 获取缓存天气的经纬度
     */
    public LocateInforModel getCachedWeatherLocation() {
        if (cachedWeather != null) {
            LocateInforModel location = new LocateInforModel();
            location.setLatitude(cachedWeather.getLatitude());
            location.setLongitude(cachedWeather.getLongitude());
            return location;
        }
        return null;
    }

    /**
     * 将缓存持久化到本地存储
     */
    private void persistCache() {
        try {
            if (cachedWeather == null) {
                XLog.tag(TAG).w("无缓存数据，跳过持久化");
                return;
            }

            // 使用 DataManager 保存数据
            DataManager.setWeatherData(new Gson().toJson(cachedWeather));
            
            XLog.tag(TAG).d("天气数据已持久化到本地存储");
        } catch (Exception e) {
            XLog.tag(TAG).e("持久化缓存失败: %s", e.getMessage());
        }
    }

    /**
     * 从本地存储加载缓存
     */
    private void loadCacheFromDisk() {
        try {
            // 从 DataManager 加载数据
            WeatherDataModel weatherData = new Gson().fromJson(DataManager.getWeatherData(), WeatherDataModel.class);
            if (weatherData != null) {
                this.cachedWeather = weatherData;
                this.lastCacheTime = weatherData.getDataTime();
                XLog.tag(TAG).d("从本地存储加载缓存数据 - 时间: %s", lastCacheTime);
            }
        } catch (Exception e) {
            XLog.tag(TAG).e("加载缓存失败: %s", e.getMessage());
        }
    }

    /**
     * 清除缓存
     */
    public void clearCache() {
        // 清除内存缓存
        cachedWeather = null;
        lastCacheTime = null;

        // 清除本地存储
        try {
            DataManager.setWeatherData(null);
            XLog.tag(TAG).d("缓存已清除");
        } catch (Exception e) {
            XLog.tag(TAG).e("清除缓存失败: %s", e.getMessage());
        }
    }

    /**
     * 检查缓存是否有效
     * @param force 是否强制更新
     * @return true 如果缓存有效
     */
    public boolean isCacheValid(boolean force) {
        // 强制更新时缓存无效
        if (force) {
            XLog.tag(TAG).d("强制更新，缓存无效");
            return false;
        }

        WeatherDataModel cache = getCachedWeather();
        if (cache == null) {
            XLog.tag(TAG).d("无缓存数据");
            return false;
        }

        LocalDateTime weatherDateTime = cache.getDataTime();
        if (weatherDateTime == null) {
            XLog.tag(TAG).d("缓存数据时间无效");
            return false;
        }

        // 检查是否是当天的数据
        LocalDateTime now = LocalDateTime.now();
        if (!weatherDateTime.toLocalDate().equals(now.toLocalDate())) {
            XLog.tag(TAG).d("缓存数据不是当天的");
            return false;
        }

        // 检查是否在有效期内（3小时）
        LocalDateTime validityThreshold = now.minusHours(CACHE_VALIDITY_HOURS);
        boolean isValid = weatherDateTime.isAfter(validityThreshold);
        
        XLog.tag(TAG).d("缓存数据%s在%d小时有效期内", 
            isValid ? "在" : "不在", CACHE_VALIDITY_HOURS);
        
        return isValid;
    }

    /**
     * 获取缓存状态信息
     */
    @NonNull
    public String getCacheStatus() {
        if (cachedWeather == null) {
            return "无缓存数据";
        }

        LocalDateTime dataTime = cachedWeather.getDataTime();
        if (dataTime == null) {
            return "缓存时间未知";
        }

        long hoursSinceCache = ChronoUnit.HOURS.between(dataTime, LocalDateTime.now());
        boolean isToday = dataTime.toLocalDate().equals(LocalDateTime.now().toLocalDate());
        boolean isRecent = hoursSinceCache < CACHE_VALIDITY_HOURS;
        
        return String.format("缓存于%d小时前 (%s%s)", 
            hoursSinceCache,
            isToday ? "当天" : "非当天",
            isRecent ? "，有效" : "，已过期");
    }
}