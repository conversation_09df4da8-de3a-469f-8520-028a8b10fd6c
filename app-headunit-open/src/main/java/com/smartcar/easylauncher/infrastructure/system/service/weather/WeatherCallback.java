package com.smartcar.easylauncher.infrastructure.system.service.weather;


import androidx.annotation.NonNull;

import com.smartcar.easylauncher.data.model.weather.WeatherDataModel;


/**
 * 天气数据获取回调接口
 * 用于异步获取天气数据的结果回调
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-03-21
 */
public interface WeatherCallback {

    /**
     * 天气数据获取成功回调
     *
     * @param weatherData 获取到的天气数据
     */
    void onWeatherDataReceived(@NonNull WeatherDataModel weatherData);

    /**
     * 天气数据获取失败回调
     *
     * @param error 错误信息
     */
    void onError(@NonNull WeatherError error);
}