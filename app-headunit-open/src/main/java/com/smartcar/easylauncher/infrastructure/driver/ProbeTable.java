package com.smartcar.easylauncher.infrastructure.driver;

import android.util.Pair;

import java.lang.reflect.InvocationTargetException;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * ProbeTable 类用于管理 USB 驱动程序的映射表。
 * 该类提供了添加产品和查找驱动程序的方法。
 *
 * 注意：在使用反射调用方法时，可能会抛出多种异常，需谨慎处理。
 */
public class ProbeTable {
    /**
     * 存储产品与其对应的 USB 驱动程序的映射关系。
     * 键为产品的 Pair（包含两个整数），值为对应的 USB 驱动程序类。
     */
    private final Map<Pair<Integer, Integer>, Class<? extends UsbSerialDriver>> mProbeTable = new LinkedHashMap<>();

    /**
     * 添加产品到映射表中。
     *
     * @param i 产品的第一个标识符
     * @param i2 产品的第二个标识符
     * @param cls 对应的 USB 驱动程序类
     * @return 当前 ProbeTable 实例，以便进行链式调用
     */
    public ProbeTable addProduct(int i, int i2, Class<? extends UsbSerialDriver> cls) {
        // 将产品的标识符与驱动程序类添加到映射表中
        this.mProbeTable.put(Pair.create(Integer.valueOf(i), Integer.valueOf(i2)), cls);
        return this; // 返回当前实例以支持链式调用
    }

    /**
     * 根据驱动程序类添加支持的产品。
     *
     * @param driverClass USB 驱动程序类
     * @return 当前 ProbeTable 实例，以便进行链式调用
     * @throws RuntimeException 如果反射调用失败
     */
    public ProbeTable addDriver(Class<? extends UsbSerialDriver> driverClass) {
        try {
            // 通过反射获取支持的设备列表
            for (Map.Entry<Integer, int[]> entry : ((Map<Integer, int[]>) driverClass.getMethod("getSupportedDevices").invoke(null)).entrySet()) {
                int intValue = entry.getKey().intValue(); // 获取产品标识符
                for (int i : entry.getValue()) {
                    addProduct(intValue, i, driverClass); // 添加每个支持的产品
                }
            }
            return this; // 返回当前实例以支持链式调用
        } catch (IllegalArgumentException | IllegalAccessException | InvocationTargetException e) {
            // 捕获反射调用中的异常并抛出运行时异常
            throw new RuntimeException(e);
        } catch (SecurityException | NoSuchMethodException e) {
            // 捕获安全异常和方法不存在异常并抛出运行时异常
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据产品的标识符查找对应的 USB 驱动程序。
     *
     * @param i 产品的第一个标识符
     * @param i2 产品的第二个标识符
     * @return 对应的 USB 驱动程序类，如果未找到则返回 null
     */
    public Class<? extends UsbSerialDriver> findDriver(int i, int i2) {
        // 根据产品标识符从映射表中获取对应的驱动程序类
        return this.mProbeTable.get(Pair.create(Integer.valueOf(i), Integer.valueOf(i2)));
    }
}