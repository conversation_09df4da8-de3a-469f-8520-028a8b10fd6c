package com.smartcar.easylauncher.infrastructure.skin.iface;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;

/**
 * 布局填充拦截器接口
 * <p>
 * 该接口定义了一个方法，用于在布局填充过程中拦截视图的创建。通过实现此接口，开发者可以在视图被创建时
 * 插入自定义逻辑，如应用皮肤、修改视图属性等。
 * <AUTHOR>
 */
public interface OnInflaterInterceptor {

    /**
     * 拦截视图的创建过程
     * <p>
     * 在布局填充过程中，每当一个视图被创建时，此方法都会被调用。通过传递的上下文（Context）、视图（View）对象、
     * 视图名称（name，通常对应于XML布局文件中的标签名）和属性集（AttributeSet，包含视图的XML属性），
     * 开发者可以实现对视图创建的自定义处理。
     *
     * @param context 上下文环境，用于获取系统服务、资源等
     * @param view    被创建的视图对象
     * @param name    视图的名称，通常对应于XML布局文件中的标签名
     * @param attrs   视图的属性集，包含从XML布局文件中解析的属性
     */
    void interceptorCreateView(Context context, View view, String name, AttributeSet attrs);
}
