package com.smartcar.easylauncher.infrastructure.skin.iface;

import android.view.View;

/**
 * 皮肤应用接口
 * <p>
 * 定义了一个应用皮肤到指定视图（View）的方法。该接口允许开发者自定义皮肤加载和应用的逻辑，
 * 以便在不修改视图原始代码的情况下，实现视图的外观变化。
 */
public interface ISkin {

    /**
     * 应用皮肤到指定的视图
     * <p>
     * 此方法负责将皮肤资源应用到给定的视图上。具体实现时，可以根据视图的类型、属性等，
     * 从皮肤资源中查找对应的样式、颜色、图片等资源，并将其应用到视图上。
     *
     * @param view 需要应用皮肤的视图对象
     */
    void applySkin(View view);
}
