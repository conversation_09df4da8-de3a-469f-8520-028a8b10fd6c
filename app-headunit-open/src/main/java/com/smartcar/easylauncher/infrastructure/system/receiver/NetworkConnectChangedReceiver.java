 
package com.smartcar.easylauncher.infrastructure.system.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiManager;
import android.os.Parcelable;

import com.smartcar.easylauncher.infrastructure.interfaces.OnNetworkChangeListener;
import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * network changed broadcast
 */
public class NetworkConnectChangedReceiver extends BroadcastReceiver {

    private static final String TAG = "NetworkConnectChangedReceiver";
    private static OnNetworkChangeListener sOnNetworkChangeListener;

    private String getConnectionType(int type) {
        String connType = "";
        if (type == ConnectivityManager.TYPE_MOBILE) {
            connType = "3G";
        } else if (type == ConnectivityManager.TYPE_WIFI) {
            connType = "wifi";
        }
        return connType;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        MyLog.v(TAG, "NetworkConnectChangedReceiver" + "");
        if (WifiManager.WIFI_STATE_CHANGED_ACTION.equals(intent.getAction())) {
            int wifiState = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, 0);
            MyLog.v(TAG, "wifiState:" + wifiState);
            switch (wifiState) {
                case WifiManager.WIFI_STATE_DISABLED:
                    break;
                case WifiManager.WIFI_STATE_DISABLING:
                    break;
                default:
                    break;
            }
        }
        if (WifiManager.NETWORK_STATE_CHANGED_ACTION.equals(intent.getAction())) {
            Parcelable parcelableExtra = intent
                    .getParcelableExtra(WifiManager.EXTRA_NETWORK_INFO);
            if (null != parcelableExtra) {
                NetworkInfo networkInfo = (NetworkInfo) parcelableExtra;
                NetworkInfo.State state = networkInfo.getState();
                boolean isConnected = state == NetworkInfo.State.CONNECTED;
                MyLog.v(TAG, "isConnected:" + isConnected);
                // if (isConnected) {
                // } else {
                //
                // }
            }
        }

        if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
            NetworkInfo info = intent
                    .getParcelableExtra(ConnectivityManager.EXTRA_NETWORK_INFO);
            if (info != null) {
                if (NetworkInfo.State.CONNECTED == info.getState() && info.isAvailable()) {
                    if (info.getType() == ConnectivityManager.TYPE_WIFI
                            || info.getType() == ConnectivityManager.TYPE_MOBILE) {
                        MyLog.v(TAG, getConnectionType(info.getType()) + " connected");
                        if (sOnNetworkChangeListener != null) {
                            MyLog.v(TAG, "mOnLocationListener is set");
                            sOnNetworkChangeListener.onNetworkChange();
                        }
                    }
                } else {
                    MyLog.v(TAG, getConnectionType(info.getType()) + " disconnected");
                }
            }

        }
    }

    public static void setNetworkLtListener(OnNetworkChangeListener onNetworkChangeListener) {
        sOnNetworkChangeListener = onNetworkChangeListener;
    }
}
