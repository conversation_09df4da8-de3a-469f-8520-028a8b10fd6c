package com.smartcar.easylauncher.infrastructure.event;



import com.smartcar.easylauncher.core.base.BaseEntity;
import com.smartcar.easylauncher.core.constants.BroadcastPresets;
import com.smartcar.easylauncher.data.model.system.MoreSettingModel;
import com.smartcar.easylauncher.data.network.state.BluetoothState;
import com.smartcar.easylauncher.data.network.state.NetworkAvailabilityState;
import com.smartcar.easylauncher.data.network.state.NetworkState;

import java.util.List;
import java.util.Map;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 时间设置事件定义
 */
@EventGroup(value = "MoreSettingScope", active = true)
public class MoreSettingEventDefine {
    @Event(value = "MoreSettingeventInt", multiProcess = false)
    Integer eventInt;

    @Event(value = "MoreSettingeventString", multiProcess = true)
    String eventString;

    @Event(value = "MoreSettingeventBean", multiProcess = true)
    MoreSettingModel eventBean;

    @Event(value = "MoreSettingeventBean", multiProcess = true)
    List<String> eventList;

    @Event(value = "MoreSettingeventMap", multiProcess = true)
    Map<String, List<String>> eventMap;

    /**
     * 蓝牙状态事件定义
     */
    @EventGroup(value = "BluetoothStateScope", active = true)
    public static class BluetoothStateEventDefine {
        /**
         * 蓝牙状态变化事件
         */
        @Event(value = "BluetoothStateChanged", multiProcess = true)
        BluetoothState bluetoothState;

        /**
         * 蓝牙开关状态变化事件
         */
        @Event(value = "BluetoothEnabledChanged", multiProcess = true)
        Boolean isEnabled;

        /**
         * 蓝牙连接状态变化事件
         */
        @Event(value = "BluetoothConnectedChanged", multiProcess = true)
        Boolean isConnected;

        /**
         * 蓝牙设备信息变化事件
         * 当连接的蓝牙设备发生变化时触发
         * 包含设备名称信息的字符串，格式由发送方决定
         */
        @Event(value = "BluetoothDeviceChanged", multiProcess = true)
        String deviceInfo;
    }

    /**
     * 广播预设实体类
     *
     * <AUTHOR>
     * @date 2024/07/19
     */
    public static class BroadcastPresetEntity extends BaseEntity {
        private BroadcastPresets.PresetTemplate preset;
        private String type;  // 类型标识：系统、媒体、应用

        /**
         * 构造函数
         *
         * @param viewType 视图类型
         * @param preset 预设模板
         * @param type 类型标识
         */
        public BroadcastPresetEntity(int viewType, BroadcastPresets.PresetTemplate preset, String type) {
            super(viewType);
            this.preset = preset;
            this.type = type;
        }

        public BroadcastPresets.PresetTemplate getPreset() {
            return preset;
        }

        public void setPreset(BroadcastPresets.PresetTemplate preset) {
            this.preset = preset;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }
    }

    /**
     * 车型实体类
     *
     * <AUTHOR>
     * @date 2024/07/19
     */
    public static class CarModelEntity extends BaseEntity {
        private String id;
        private String name;
        private int iconResId;
        private boolean selected;

        /**
         * 构造函数
         *
         * @param viewType 视图类型
         * @param id 车型ID
         * @param name 车型名称
         * @param iconResId 图标资源ID
         */
        public CarModelEntity(int viewType, String id, String name, int iconResId) {
            super(viewType);
            this.id = id;
            this.name = name;
            this.iconResId = iconResId;
            this.selected = false;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getIconResId() {
            return iconResId;
        }

        public void setIconResId(int iconResId) {
            this.iconResId = iconResId;
        }

        public boolean isSelected() {
            return selected;
        }

        public void setSelected(boolean selected) {
            this.selected = selected;
        }
    }

    /**
     * 网络状态事件定义
     */
    @EventGroup(value = "NetworkStateScope", active = true)
    public static class NetworkStateEventDefine {
        /**
         * 移动数据网络状态变化事件
         * 包含完整的网络状态信息（连接状态、网络类型、信号强度等）
         */
        @Event(value = "MobileNetworkStateChanged", multiProcess = true)
        NetworkState mobileNetworkState;

        /**
         * WiFi网络状态变化事件
         * 包含完整的网络状态信息（连接状态、网络类型、信号强度等）
         */
        @Event(value = "WifiNetworkStateChanged", multiProcess = true)
        NetworkState wifiNetworkState;

        /**
         * 网络可用性状态变化事件
         * 包含网络是否能连接互联网及网络类型信息
         */
        @Event(value = "NetworkAvailabilityChanged", multiProcess = true)
        NetworkAvailabilityState networkAvailabilityState;
    }
}