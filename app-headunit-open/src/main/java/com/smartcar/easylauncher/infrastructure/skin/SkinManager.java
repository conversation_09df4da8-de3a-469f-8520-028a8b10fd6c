package com.smartcar.easylauncher.infrastructure.skin;

import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.AssetManager;
import android.content.res.ColorStateList;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.LayoutInflater;

import com.smartcar.easylauncher.infrastructure.event.cody.SkinScopeBus;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.data.model.theme.SkinModel;
import com.smartcar.easylauncher.infrastructure.skin.iface.ISkinItem;
import com.smartcar.easylauncher.infrastructure.skin.iface.ISkinManager;
import com.smartcar.easylauncher.infrastructure.skin.iface.OnInflaterInterceptor;
import com.smartcar.easylauncher.infrastructure.skin.iface.OnSkinObserver;
import com.smartcar.easylauncher.infrastructure.skin.iface.OnSkinViewInterceptor;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.ref.WeakReference;
import java.lang.reflect.Method;
import java.security.MessageDigest;
import java.security.DigestInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import java9.util.concurrent.CompletableFuture;

/**
 * 皮肤管理器
 * <p>
 * 该类负责皮肤主题的管理，包括皮肤的初始化、注册、加载、应用以及监听器的管理等。
 * 主要功能包括：
 * 1. 皮肤主题的加载和切换
 * 2. 夜间模式的检测和切换
 * 3. 皮肤资源的管理和应用
 * 4. 皮肤变化的事件通知
 * 5. Activity的皮肤注册和注销
 * <p>
 * 使用步骤：
 * 1. 在Application中初始化SkinManager
 * 2. 在Application中注册所有的皮肤文件，支持从Assets、文件等多种方式注册
 * 3. 通过SkinManager提供的接口加载、应用皮肤，以及监听皮肤变化
 * <p>
 * 实现原理：
 * 1. 通过自定义的LayoutInflater实现View的皮肤属性解析和应用
 * 2. 使用观察者模式实现皮肤变化的监听和通知
 * 3. 采用弱引用管理Activity的皮肤工厂，防止内存泄漏
 * 4. 支持多种皮肤加载方式：Assets、存储卡、网络下载等
 */
public final class SkinManager implements ISkinManager {

    /**
     * 日期正则表达式，用于从文件名中提取日期
     */
    private static final Pattern DATE_PATTERN = Pattern.compile("(\\d{4}-\\d{2}-\\d{2})");
    /**
     * 日志标签
     */
    private static final String TAG = "SkinManager";
    /**
     * SkinManager单例
     */
    private static final SkinManager M_INSTANCE = new SkinManager();
    /**
     * 皮肤文件存储路径的键
     */
    private static final String KEY = "skin_path";
    /**
     * 上下文环境
     */
    private Context context;
    /**
     * 皮肤资源管理类
     */
    private SkinResources skinResources;
    /**
     * 存储皮肤工厂的映射，使用弱引用防止内存泄漏
     */
    private Map<String, WeakReference<SkinFactory>> skinFactories = new HashMap<>();
    /**
     * 皮肤变化监听器列表
     */
    private List<OnSkinObserver> listeners = new ArrayList<>();
    /**
     * 当前皮肤路径
     */
    private String currentSkinPath;

    /**
     * 私有构造方法，防止外部实例化
     */
    private SkinManager() {
    }

    /**
     * 获取SkinManager单例
     */
    public static SkinManager getInstance() {
        return M_INSTANCE;
    }

    /**
     * 判断当前是否处于夜间模式。
     *
     * <p>此方法通过检查系统配置中的UI模式来确定是否开启了夜间模式。</p>
     *
     * @param context 上下文环境，用于访问系统资源和服务
     * @return 返回true表示当前处于夜间模式，否则返回false
     */
    public static boolean isNightMode(Context context) {
        MyLog.d(TAG, "检查是否是手机系统的夜间模式");
        String skinPath = SPUtil.get(context, KEY, "");
        MyLog.v("主题信息", "当前主题路径：" + skinPath);
        Configuration configuration = context.getResources().getConfiguration();
        int currentNightMode = configuration.uiMode & Configuration.UI_MODE_NIGHT_MASK;
        return currentNightMode == Configuration.UI_MODE_NIGHT_YES;
        // 备用方案，通过UiModeManager检查夜间模式（已注释）
        // UiModeManager uiModeManager = (UiModeManager)context.getSystemService(Context.UI_MODE_SERVICE);
        // return uiModeManager.getNightMode() == UiModeManager.MODE_NIGHT_YES;
    }

    /**
     * 为上下文环境附加皮肤布局工厂，以便支持主题切换。
     *
     * <p>此方法检查当前LayoutInflater是否已支持皮肤，如果不支持，则替换为自定义的SkinLayoutInflater。</p>
     *
     * @param newBase 原始的上下文环境
     * @return 返回已附加皮肤布局工厂的上下文环境
     */
    public static Context attachBaseContext(Context newBase) {
        final LayoutInflater inflater = LayoutInflater.from(newBase);
        if (inflater instanceof SkinLayoutInflater) {
            // 如果已经是SkinLayoutInflater，则无需替换
            return newBase;
        }
        // 否则，返回封装了SkinLayoutInflater的上下文
        return new LayoutInflaterContext(newBase);
    }

    /**
     * 判断当前实例是否处于夜间模式。
     *
     * <p>此方法是实例方法，通过调用静态方法{@link #isNightMode(Context)}并传入当前实例的上下文环境来判断。</p>
     *
     * @return 返回true表示当前实例处于夜间模式，否则返回false
     */
    public boolean isNightMode() {
        return isNightMode(context); // 假设context是当前实例的一个成员变量
    }

    /**
     * 判断当前是否应用了夜间主题。
     *
     * <p>通过检查主题路径中是否包含指定的夜间主题文件名来判断。</p>
     *
     * @return 返回true表示当前应用了夜间主题，否则返回false
     */
    public boolean isDarkTheme() {
        String skinPath = SPUtil.get(context, KEY, "");
        return skinPath.contains("night-2025-05-01.skin"); // 注意：这里的文件名应根据实际情况调整
    }

    /**
     * 判断当前是否处于配置的夜间主题模式。
     *
     * <p>通过检查主题路径中是否包含通过{@link SettingsManager#getNightTheme()}获取的主题文件名来判断。</p>
     *
     * @return 返回true表示当前处于配置的夜间主题模式，否则返回false
     */
    public boolean isNightModeNow() {
        String skinPath = SPUtil.get(context, KEY, "");
        return skinPath.contains(SettingsManager.getNightTheme()); // 假设SettingsManager是管理配置的类
    }

    /**
     * 通知所有观察者皮肤已更新
     * <p>
     * 该方法负责在皮肤发生变化时通知所有相关组件进行更新。主要执行以下操作：
     * 1. 获取当前皮肤状态
     * 2. 更新所有注册的皮肤工厂
     * 3. 通知所有皮肤变化监听器
     * 4. 发送皮肤变化事件到事件总线
     * <p>
     * 该方法包含完整的异常处理和资源管理机制：
     * - 使用局部变量存储临时列表，帮助GC
     * - 清理无效的弱引用
     * - 捕获并记录所有可能的异常
     * - 确保临时对象能被及时回收
     */
    private void notifySkinUpdate() {
        MyLog.d(TAG, "通知更新主题");

        try {
            // 在方法开始就获取 hasSkin 状态，避免多次调用
            final boolean hasSkin = isHasSkin();

            // 使用局部变量存储临时列表，帮助GC
            List<String> keys = null;
            try {
                keys = new ArrayList<>(skinFactories.keySet());
            } catch (Exception e) {
                MyLog.e(TAG, "获取skin factories失败: " + e.getMessage());
                return;
            }

            // 批量处理factory更新
            for (String key : keys) {
                WeakReference<SkinFactory> factoryRef = skinFactories.get(key);
                if (factoryRef != null) {
                    SkinFactory skinFactory = factoryRef.get();
                    if (skinFactory != null) {
                        try {
                            skinFactory.apply();
                        } catch (Exception e) {
                            MyLog.e(TAG, "应用皮肤到工厂失败: " + e.getMessage());
                        }
                    } else {
                        // 清理无效的引用
                        skinFactories.remove(key);
                    }
                }
            }

            // 使用局部变量存储监听器列表
            List<OnSkinObserver> listenersCopy = null;
            try {
                listenersCopy = new ArrayList<>(listeners);
            } catch (Exception e) {
                MyLog.e(TAG, "获取listeners失败: " + e.getMessage());
                return;
            }

            // 批量处理监听器通知
            for (OnSkinObserver listener : listenersCopy) {
                if (listener != null) {  // 额外的空值检查
                    try {
                        listener.onSkinChange(hasSkin);
                    } catch (Exception e) {
                        MyLog.e(TAG, "通知监听器失败: " + e.getMessage());
                    }
                }
            }

            // 事件总线通知
            try {
                SkinScopeBus.eventBean().post(new SkinModel(hasSkin));
            } catch (Exception e) {
                MyLog.e(TAG, "发送事件总线通知失败: " + e.getMessage());
            }

            MyLog.v("interceptorView", "notifySkinUpdate completed");
        } catch (Exception e) {
            MyLog.e(TAG, "皮肤更新过程发生异常: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 确保临时对象能被及时回收
            System.gc();
        }
    }

    /**
     * 为Activity安装皮肤支持
     * <p>
     * 该方法主要完成以下工作：
     * 1. 将Activity与皮肤工厂关联
     * 2. 设置皮肤视图拦截器（如果Activity实现了OnSkinViewInterceptor接口）
     * 3. 添加皮肤变化监听器（如果Activity实现了OnSkinObserver接口）
     * 4. 使用弱引用存储皮肤工厂，防止内存泄漏
     *
     * @param activity 需要安装皮肤支持的Activity实例
     * @param factory  用于处理皮肤逻辑的皮肤工厂实例
     */
    void installActivity(Activity activity, SkinFactory factory) {
        MyLog.d(TAG, "开始为Activity安装皮肤支持: " + activity.getClass().getSimpleName());
        String tag = activity.toString();
        try {
            if (activity instanceof OnSkinViewInterceptor) {
                factory.setInterceptor((OnSkinViewInterceptor) activity);
                MyLog.v(TAG, "设置皮肤视图拦截器成功");
            }
            if (activity instanceof OnSkinObserver) {
                addSkinObserver(((OnSkinObserver) activity));
                MyLog.v(TAG, "添加皮肤变化监听器成功");
            }
            skinFactories.put(tag, new WeakReference<>(factory));
            MyLog.d(TAG, "Activity皮肤支持安装完成");
        } catch (Exception e) {
            MyLog.e(TAG, "安装Activity皮肤支持失败: " + e.getMessage(), e);
        }
    }

    /**
     * 注册皮肤到指定的Activity中。
     *
     * @param activity 需要应用皮肤效果的Activity实例
     */
    @Override
    public void registerSkin(Activity activity) {
        // 初始化操作，可能是初始化一些必要的配置或状态
        init(activity);

        // 从Activity中获取LayoutInflater实例，用于后续创建视图
        LayoutInflater inflater = LayoutInflater.from(activity);

        // 获取当前LayoutInflater的Factory2实例，用于拦截和修改视图的创建过程
        LayoutInflater.Factory2 factory2 = inflater.getFactory2();

        // 创建SkinFactory实例，用于处理皮肤的逻辑
        SkinFactory skinFactory = new SkinFactory();

        // 判断当前的Factory2是否已经是SkinLayoutInflaterFactory的实例
        if (factory2 instanceof SkinLayoutInflaterFactory) {
            // 如果是，说明已经存在皮肤处理的拦截器，我们可以直接添加新的拦截器
            // 先检查Activity是否实现了OnInflaterInterceptor接口，如果是，则先添加Activity作为拦截器
            if (activity instanceof OnInflaterInterceptor) {
                ((SkinLayoutInflaterFactory) factory2).addOnInflaterInterceptor((OnInflaterInterceptor) activity);
            }

            // 添加SkinFactory作为拦截器，用于处理皮肤逻辑
            ((SkinLayoutInflaterFactory) factory2).addOnInflaterInterceptor(skinFactory);
        } else {
            // 如果当前的Factory2不是SkinLayoutInflaterFactory的实例，则需要创建一个新的SkinLayoutInflaterFactory
            SkinLayoutInflaterFactory inflaterFactory = new SkinLayoutInflaterFactory();

            // 同样地，先检查Activity是否实现了OnInflaterInterceptor接口，如果是，则先添加Activity作为拦截器
            if (activity instanceof OnInflaterInterceptor) {
                inflaterFactory.addOnInflaterInterceptor((OnInflaterInterceptor) activity);
            }

            // 添加SkinFactory作为拦截器
            inflaterFactory.addOnInflaterInterceptor(skinFactory);

            // 将新的SkinLayoutInflaterFactory设置为LayoutInflater的Factory2，以便拦截后续的视图创建过程
            inflater.setFactory2(inflaterFactory);
        }

        // 调用installActivity方法，将Activity和SkinFactory关联起来，可能用于后续的皮肤更新等操作
        installActivity(activity, skinFactory);
    }


    /**
     * 添加皮肤变化观察者
     * <p>
     * 将新的观察者添加到监听器列表中，当皮肤发生变化时，
     * 该观察者会收到通知。
     *
     * @param skinObserver 实现了OnSkinObserver接口的观察者对象
     */
    @Override
    public void addSkinObserver(OnSkinObserver skinObserver) {
        listeners.add(skinObserver);
    }

    /**
     * 添加布局填充拦截器
     * <p>
     * 该方法用于添加自定义的布局填充拦截器，可以在View创建过程中
     * 对View进行定制化处理。主要用于：
     * 1. 拦截View的创建过程
     * 2. 注入自定义的皮肤属性
     * 3. 处理特殊的View样式
     *
     * @param context     上下文环境
     * @param interceptor 实现了OnInflaterInterceptor接口的拦截器对象
     */
    @Override
    public void addOnInflaterInterceptor(Context context, OnInflaterInterceptor interceptor) {
        init(context);

        LayoutInflater inflater = LayoutInflater.from(context);
        LayoutInflater.Factory2 factory2 = inflater.getFactory2();

        if (factory2 instanceof SkinLayoutInflaterFactory) {
            //先添加别的拦截器
            ((SkinLayoutInflaterFactory) factory2).addOnInflaterInterceptor(interceptor);
        }
    }


    /**
     * 移除皮肤变化观察者
     * <p>
     * 从监听器列表中移除指定的观察者，移除后该观察者将不再
     * 收到皮肤变化的通知。这通常在组件销毁时调用，以防止内存泄漏。
     *
     * @param skinObserver 要移除的皮肤观察者对象
     */
    @Override
    public void removeSkinObserver(OnSkinObserver skinObserver) {
        listeners.remove(skinObserver);
    }

    /**
     * 移除布局填充拦截器
     * <p>
     * 移除之前添加的布局填充拦截器。这通常在不再需要特定的
     * View创建处理逻辑时调用，或在防止内存泄漏时使用。
     *
     * @param context     上下文环境
     * @param interceptor 要移除的拦截器对象
     */
    @Override
    public void removeOnInflaterInterceptor(Context context, OnInflaterInterceptor interceptor) {
        init(context);

        LayoutInflater inflater = LayoutInflater.from(context);
        LayoutInflater.Factory2 factory2 = inflater.getFactory2();

        if (factory2 instanceof SkinLayoutInflaterFactory) {
            //先添加别的拦截器
            ((SkinLayoutInflaterFactory) factory2).removeOnInflaterInterceptor(interceptor);
        }
    }

    /**
     * 取消activity的注册监听
     */
    @Override
    public void unregisterSkin(Activity activity) {
        MyLog.d(TAG, "开始注销Activity的皮肤支持: " + activity.getClass().getSimpleName());
        try {
            WeakReference<SkinFactory> factoryRef = skinFactories.remove(activity.toString());
            MyLog.v(TAG, "移除皮肤工厂引用");

            if (activity instanceof OnSkinObserver) {
                removeSkinObserver(((OnSkinObserver) activity));
                MyLog.v(TAG, "移除皮肤变化监听器");
            }

            if (factoryRef != null) {
                SkinFactory factory = factoryRef.get();
                if (factory != null) {
                    factory.recycler();
                    MyLog.v(TAG, "回收皮肤工厂资源");
                }
            }
            MyLog.d(TAG, "Activity皮肤支持注销完成");
        } catch (Exception e) {
            MyLog.e(TAG, "注销Activity皮肤支持失败: " + e.getMessage(), e);
        }
    }


    /**
     * 初始化皮肤管理器
     * <p>
     * 该方法负责初始化皮肤管理器的基本组件，包括：
     * 1. 保存应用程序上下文
     * 2. 初始化皮肤资源管理器
     * 3. 准备皮肤管理器的运行环境
     * <p>
     * 注意：该方法应在Application创建时调用，且只需调用一次
     *
     * @param context 应用程序上下文，用于访问应用资源
     */
    @Override
    public void init(Context context) {
        MyLog.d(TAG, "主题模块初始化开始");
        try {
            if (this.context == null) {
                this.context = context.getApplicationContext();
                MyLog.v(TAG, "初始化上下文环境完成");
            }
            if (this.skinResources == null) {
                this.skinResources = new SkinResources(context);
                MyLog.v(TAG, "初始化皮肤资源管理器完成");
            }
            MyLog.d(TAG, "主题模块初始化完成");
        } catch (Exception e) {
            MyLog.e(TAG, "主题模块初始化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 加载指定名称的皮肤资源
     * <p>
     * 该方法用于加载新的皮肤资源。加载过程是异步的，通过LoadTask来执行，
     * 以避免阻塞主线程。加载完成后会自动应用新皮肤。
     * <p>
     * 注意：
     * 1. 皮肤文件必须提前注册到皮肤管理器中
     * 2. 皮肤名称不能为空
     * 3. 加载过程是异步的，不会立即生效
     *
     * @param name 要加载的皮肤文件名称
     */
    @Override
    public void loadSkin(String name) {
        MyLog.d(TAG, "开始加载皮肤: " + name);
        if (TextUtils.isEmpty(name)) {
            MyLog.e(TAG, "皮肤名称为空，加载失败");
            return;
        }
        CompletableFuture.supplyAsync(() -> {
            try {
                File skinFile = new File(getSkinDir(), name);
                if (!skinFile.exists()) {
                    return null;
                }
                String skinPkgPath = skinFile.getAbsolutePath();
                MyLog.d(TAG, "皮肤路径" + skinPkgPath);
                PackageManager mPm = context.getPackageManager();
                PackageInfo mInfo = mPm.getPackageArchiveInfo(skinPkgPath, PackageManager.GET_ACTIVITIES);
                if (mInfo == null) {
                    return null;
                }
                AssetManager assetManager;
                try {
                    assetManager = AssetManager.class.newInstance();
                    Method addAssetPath = assetManager.getClass().getMethod("addAssetPath", String.class);
                    addAssetPath.invoke(assetManager, skinPkgPath);
                } catch (Exception e) {
                    MyLog.e(TAG, "创建AssetManager失败: " + e.getMessage());
                    return null;
                }
                Resources superRes = context.getResources();
                Resources skinResource;
                try {
                    skinResource = new Resources(assetManager,
                            superRes.getDisplayMetrics(),
                            superRes.getConfiguration());
                    // 设置包名
                    skinResources.setSkinPackageName(mInfo.packageName);
                } catch (Exception e) {
                    MyLog.e(TAG, "创建Resources失败: " + e.getMessage());
                    return null;
                }
                // 返回资源和路径
                return new Object[]{skinResource, skinPkgPath};
            } catch (Exception e) {
                MyLog.e(TAG, "加载皮肤失败: " + e.getMessage());
                e.printStackTrace();
                return null;
            }
        }).thenAccept(result -> {
            new Handler(Looper.getMainLooper()).post(() -> {
                try {
                    if (result != null) {
                        Object[] arr = (Object[]) result;
                        Resources resources = (Resources) arr[0];
                        String skinPkgPath = (String) arr[1];
                        skinResources.setSkinResources(resources);
                        if (skinPkgPath != null) {
                            SPUtil.put(context, KEY, skinPkgPath);
                        }
                        MyLog.d(TAG, "onPostExecute");
                        if (skinResources.isHasSkin()) {
                            notifySkinUpdate();
                        }
                    } else {
                        MyLog.e(TAG, "加载皮肤资源失败");
                    }
                } catch (Exception e) {
                    MyLog.e(TAG, "应用皮肤失败: " + e.getMessage());
                    e.printStackTrace();
                }
            });
        });
    }

    /**
     * 加载上次使用的皮肤
     * <p>
     * 该方法会尝试加载上一次成功应用的皮肤。主要流程：
     * 1. 从SharedPreferences中获取上次保存的皮肤路径
     * 2. 验证皮肤文件是否存在
     * 3. 如果存在且与当前皮肤不同，则加载该皮肤
     * <p>
     * 这个方法通常在应用启动时调用，用于恢复用户的皮肤设置
     */
    @Override
    public void loadLastSkin() {
        MyLog.d(TAG, "开始加载上次使用的皮肤");
        try {
            String lastSkinPath = SPUtil.get(context, KEY, "");
            MyLog.v(TAG, "获取到上次保存的皮肤路径: " + lastSkinPath);

            if (TextUtils.isEmpty(lastSkinPath)) {
                MyLog.d(TAG, "没有找到上次使用的皮肤记录");
                return;
            }

            if (lastSkinPath.equals(currentSkinPath)) {
                MyLog.d(TAG, "当前皮肤与上次使用的皮肤相同，无需重新加载");
                return;
            }

            File file = new File(lastSkinPath);
            if (file.exists()) {
                // 检查是否有更新版本的皮肤文件
                File skinDir = getSkinDir();
                File[] skinFiles = skinDir.listFiles((dir, filename) -> filename.startsWith("night") && filename.endsWith(".skin"));
                
                if (skinFiles != null && skinFiles.length > 0) {
                    File newestSkin = findNewestSkinFile(skinFiles);
                    
                    if (newestSkin != null && !newestSkin.getAbsolutePath().equals(lastSkinPath)) {
                        String lastSkinDate = getDateFromFileName(file.getName());
                        String newestSkinDate = getDateFromFileName(newestSkin.getName());
                        
                        if (newestSkinDate != null && lastSkinDate != null && 
                            newestSkinDate.compareTo(lastSkinDate) > 0) {
                            MyLog.d(TAG, "发现更新版本的皮肤文件: " + newestSkin.getName() + ", 版本: " + newestSkinDate);
                            MyLog.v(TAG, "加载更新版本的皮肤文件，而非上次使用的: " + file.getName() + ", 版本: " + lastSkinDate);
                            loadSkin(newestSkin.getName());
                            currentSkinPath = newestSkin.getAbsolutePath();
                            MyLog.d(TAG, "更新版本皮肤加载完成");
                            return;
                        }
                    }
                }
                
                // 如果没有更新版本，则加载上次使用的皮肤
                MyLog.v(TAG, "开始加载上次的皮肤文件: " + file.getName());
                loadSkin(file.getName());
                currentSkinPath = lastSkinPath;
                MyLog.d(TAG, "上次皮肤加载完成");
            } else {
                MyLog.e(TAG, "上次使用的皮肤文件不存在: " + lastSkinPath);
                // 尝试加载可用的最新皮肤
                loadNewestAvailableSkin();
            }
        } catch (Exception e) {
            MyLog.e(TAG, "加载上次使用的皮肤失败: " + e.getMessage(), e);
            // 出现异常时尝试加载可用的最新皮肤
            loadNewestAvailableSkin();
        }
    }
    
    /**
     * 加载可用的最新皮肤文件
     * <p>
     * 当上次使用的皮肤文件不存在或加载失败时，尝试加载目录中可用的最新皮肤文件
     */
    private void loadNewestAvailableSkin() {
        try {
            File skinDir = getSkinDir();
            File[] skinFiles = skinDir.listFiles((dir, filename) -> filename.startsWith("night") && filename.endsWith(".skin"));
            
            if (skinFiles != null && skinFiles.length > 0) {
                File newestSkin = findNewestSkinFile(skinFiles);
                if (newestSkin != null) {
                    MyLog.d(TAG, "尝试加载可用的最新皮肤文件: " + newestSkin.getName());
                    loadSkin(newestSkin.getName());
                    currentSkinPath = newestSkin.getAbsolutePath();
                }
            }
        } catch (Exception e) {
            MyLog.e(TAG, "加载可用的最新皮肤失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 查找最新版本的皮肤文件
     * <p>
     * 通过比较文件名中的日期来确定最新的皮肤文件
     *
     * @param skinFiles 皮肤文件数组
     * @return 返回日期最新的皮肤文件，如果无法确定则返回null
     */
    private File findNewestSkinFile(File[] skinFiles) {
        if (skinFiles == null || skinFiles.length == 0) {
            return null;
        }
        
        File newestFile = skinFiles[0];
        String newestDate = getDateFromFileName(newestFile.getName());
        
        for (int i = 1; i < skinFiles.length; i++) {
            String currentDate = getDateFromFileName(skinFiles[i].getName());
            if (currentDate != null && newestDate != null && currentDate.compareTo(newestDate) > 0) {
                newestFile = skinFiles[i];
                newestDate = currentDate;
            }
        }
        
        return newestFile;
    }

    /**
     * 重置为默认主题
     * <p>
     * 该方法会清除当前应用的所有皮肤设置，恢复到应用的默认主题。具体操作：
     * 1. 清除当前的皮肤资源
     * 2. 删除保存的皮肤路径
     * 3. 通知所有观察者主题已更改
     * <p>
     * 这个方法通常用于：
     * - 用户主动切换回默认主题
     * - 当前皮肤文件损坏需要恢复默认值
     * - 清除所有自定义主题设置
     */
    @Override
    public void restoreDefaultTheme() {
        MyLog.d(TAG, "开始执行恢复默认主题操作");
        try {
            MyLog.v(TAG, "清除当前皮肤资源");
            skinResources.setSkinResources(null, null);

            MyLog.v(TAG, "清除保存的皮肤路径");
            SPUtil.put(context, KEY, "");

            MyLog.v(TAG, "通知所有组件更新主题");
            notifySkinUpdate();

            MyLog.d(TAG, "恢复默认主题完成");
        } catch (Exception e) {
            MyLog.e(TAG, "恢复默认主题失败: " + e.getMessage(), e);
        }
    }

    /**
     * 提交皮肤项的更改
     * <p>
     * 该方法用于立即应用某个具体的皮肤项的更改。这个方法会触发
     * 指定皮肤项的立即更新，而不是等待整体皮肤切换。
     * <p>
     * 使用场景：
     * - 动态更改某个视图的皮肤属性
     * - 局部刷新皮肤效果
     * - 自定义皮肤项的即时预览
     *
     * @param skinItem 需要应用更改的皮肤项，实现了ISkinItem接口
     */
    @Override
    public void apply(ISkinItem skinItem) {
        if (skinItem != null) {
            MyLog.v(TAG, "开始应用皮肤项: " + skinItem.getClass().getSimpleName());
            try {
                skinItem.apply();
                MyLog.v(TAG, "皮肤项应用完成");
            } catch (Exception e) {
                MyLog.e(TAG, "应用皮肤项失败: " + e.getMessage(), e);
            }
        } else {
            MyLog.w(TAG, "尝试应用空的皮肤项");
        }
    }

    /**
     * 获取指定Activity的皮肤工厂实例
     * <p>
     * 该方法返回与指定Activity关联的皮肤工厂实例。皮肤工厂负责
     * 管理Activity中所有支持换肤的视图。
     * <p>
     * 注意：
     * - 返回的是弱引用持有的实例，可能为null
     * - Activity必须先调用registerSkin才能获取到对应的工厂
     *
     * @param activity 目标Activity实例
     * @return 返回与该Activity关联的SkinFactory实例，如果未注册或已被回收则返回null
     */
    @Override
    public SkinFactory getSkinFactory(Activity activity) {
        WeakReference<SkinFactory> factoryRef = skinFactories.get(activity.toString());
        return factoryRef != null ? factoryRef.get() : null;
    }

    /**
     * 检查是否已加载了自定义皮肤
     * <p>
     * 该方法用于判断当前是否正在使用自定义皮肤，而不是默认皮肤。
     * 这个状态由皮肤资源管理器维护。
     *
     * @return 如果当前使用的是自定义皮肤则返回true，否则返回false
     */
    @Override
    public boolean isHasSkin() {
        return skinResources.isHasSkin();
    }

    /**
     * 获取皮肤文件存储目录
     * <p>
     * 该方法返回用于存储皮肤文件的专用目录。如果目录不存在，
     * 会自动创建。该目录位于应用的私有文件目录下。
     * <p>
     * 目录特点：
     * - 位于应用私有存储空间，其他应用无法访问
     * - 应用卸载时自动清理
     * - 无需存储权限即可访问
     *
     * @return 返回皮肤文件存储目录的File对象
     */
    @Override
    public File getSkinDir() {
        File skinDir = new File(context.getFilesDir().getParentFile(), "skin");
        skinDir.mkdir();
        return skinDir;
    }

    /**
     * 把asset中的皮肤文件复制到内存卡中
     */
//    @Override
//    public void registerAssetSkin(String name) {
//        try {
//            InputStream open = context.getAssets().open(name);
//            registerSkin(open, name);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    /**
     * 注册Assets目录中的皮肤文件
     * <p>
     * 该方法用于将Assets目录中的皮肤文件注册到皮肤管理器。主要功能：
     * 1. 检查目标皮肤是否已存在
     * 2. 比较皮肤文件的日期，避免重复注册旧版本
     * 3. 将皮肤文件复制到应用的私有目录
     * <p>
     * 注意事项：
     * - 只处理以"night"开头且以".skin"结尾的文件
     * - 通过文件名中的日期判断版本
     * - 较新版本会覆盖旧版本
     *
     * @param name Assets目录中的皮肤文件名称
     */
    @Override
    public void registerAssetSkin(String name) {
        MyLog.d(TAG, "  注册皮肤 " + name);
        File skinDir = getSkinDir();
        File targetFile = new File(skinDir, name);
        String assetMD5 = getAssetFileMD5(context, name);
        String fileMD5 = getFileMD5(targetFile);
        if (targetFile.exists() && assetMD5 != null && assetMD5.equals(fileMD5)) {
            MyLog.d(TAG, "assets 皮肤包内容未变，跳过复制: " + name);
            return;
        }
        // 内容有变化或文件不存在，执行复制
        try {
            InputStream open = context.getAssets().open(name);
            registerSkin(open, name);
            MyLog.d(TAG, "assets 皮肤包已复制覆盖: " + name);
        } catch (Exception e) {
            MyLog.e(TAG, "注册皮肤失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从文件名中提取日期信息
     * <p>
     * 该方法使用正则表达式从文件名中提取日期字符串。日期格式为：YYYY-MM-DD
     * 例如从文件名"night-2023-12-25.skin"中提取出"2023-12-25"
     *
     * @param fileName 需要解析的文件名
     * @return 返回提取到的日期字符串，如果未找到日期则返回null
     */
    public String getDateFromFileName(String fileName) {
        Matcher matcher = DATE_PATTERN.matcher(fileName);
        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return null;
        }
    }

    /**
     * 从其他文件夹复制皮肤文件
     * <p>
     * 该方法用于将其他目录下的皮肤文件复制到应用的皮肤目录中。主要步骤：
     * 1. 获取皮肤存储目录
     * 2. 创建目标文件
     * 3. 复制文件内容
     * 4. 注册新复制的皮肤文件
     * <p>
     * 注意：
     * - 确保源文件路径有效
     * - 复制过程中会占用IO资源
     * - 复制完成后会自动注册皮肤
     *
     * @param filepath 源文件的完整路径
     * @param fileName 目标文件名
     */
    @Override
    public void otherFolderReplication(String filepath, String fileName) {
        File skinDir = getSkinDir();
        File file = new File(skinDir, fileName);
        //通过文件路径获取到文件
        try {
            FileInputStream fis = new FileInputStream(filepath);
            FileOutputStream fos = new FileOutputStream(file, false);
            byte[] data = new byte[2048];
            int nbread = 0;
            while ((nbread = fis.read(data)) > -1) {
                fos.write(data, 0, nbread);
            }
            fis.close();
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        registerFileSkin(fileName);
        //   loadSkin(fileName);
    }

    /**
     * 注册本地文件系统中的皮肤文件
     * <p>
     * 该方法用于将本地文件系统中的皮肤文件注册到皮肤管理器。
     * 与Assets中的皮肤注册不同，这个方法直接处理文件系统中的文件。
     *
     * @param fileName 要注册的皮肤文件名
     */
    @Override
    public void registerFileSkin(String fileName) {
        try {
            InputStream open = new FileInputStream(fileName);
            registerSkin(open, fileName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 通过输入流注册皮肤文件
     * <p>
     * 这是一个底层的皮肤注册方法，支持从任意输入流中读取皮肤数据并注册。
     * 主要功能：
     * 1. 创建皮肤文件
     * 2. 将输入流的内容写入文件
     * 3. 确保资源正确关闭
     * <p>
     * 注意事项：
     * - 自动处理流的关闭
     * - 使用缓冲区提高IO效率
     * - 异常会被捕获并记录
     *
     * @param is   包含皮肤数据的输入流
     * @param name 皮肤文件的目标名称
     */
    @Override
    public void registerSkin(final InputStream is, final String name) {
        MyLog.d(TAG, "  注册皮肤" + name);
        File skinDir = getSkinDir();
        File file = new File(skinDir, name);
        FileOutputStream fos = null;
        try {
            byte[] data = new byte[2048];
            int nbread = 0;
            fos = new FileOutputStream(file, false);
            while ((nbread = is.read(data)) > -1) {
                fos.write(data, 0, nbread);
            }
        } catch (Exception ex) {
        } finally {
            closeIo(is);
            closeIo(fos);
        }
    }

    /**
     * 安全关闭IO流
     * <p>
     * 该方法用于安全地关闭各种IO流，确保资源被正确释放。
     * 特点：
     * - 处理null检查
     * - 静默处理IO异常
     * - 适用于所有Closeable实现
     *
     * @param closeable 需要关闭的IO对象
     */
    private void closeIo(Closeable closeable) {
        try {
            if (closeable != null) {
                closeable.close();
            }
        } catch (IOException e) {
        }
    }

    /**
     * 获取颜色资源的十六进制字符串表示
     * <p>
     * 该方法将颜色资源ID转换为十六进制的颜色字符串（如：#FF0000）。
     * 优先从当前皮肤包中获取，如果未找到则从应用默认资源中获取。
     *
     * @param resId 颜色资源的资源ID
     * @return 返回颜色的十六进制字符串表示
     */
    @Override
    public String getColorHexString(int resId) {
        try {
            // 如果当前有皮肤资源，优先从皮肤资源中获取
            if (skinResources != null && skinResources.isHasSkin()) {
                String colorHex = skinResources.getColorHexString(resId);
                if (colorHex != null && !colorHex.isEmpty()) {
                    return colorHex;
                }
            }
            
            // 如果没有皮肤资源或获取失败，从默认资源中获取
            int color = context.getResources().getColor(resId);
            return String.format("#%06X", (0xFFFFFF & color));
        } catch (Exception e) {
            MyLog.e(TAG, "获取颜色十六进制字符串失败: " + e.getMessage());
            // 发生异常时返回默认的白色
            return "#FFFFFF";
        }
    }

    /**
     * 获取颜色资源值
     * <p>
     * 通过资源ID获取对应的颜色值。优先从当前皮肤包中获取，
     * 如果未找到则从应用默认资源中获取。
     *
     * @param resId 颜色资源的资源ID
     * @return 返回颜色的整型表示
     */
    @Override
    public int getColor(int resId) {
        return skinResources.getColor(resId);
    }

    /**
     * 通过资源名称和ID获取颜色值
     * <p>
     * 该方法支持通过资源名称和ID的组合来获取颜色。这种方式更灵活，
     * 可以处理资源ID在不同包中不一致的情况。
     *
     * @param resName 颜色资源的名称
     * @param resId   颜色资源的资源ID（用作备选）
     * @return 返回颜色的整型表示
     */
    @Override
    public int getColor(String resName, int resId) {
        return skinResources.getColor(resName, resId);
    }

    /**
     * 获取颜色状态列表
     * <p>
     * 获取支持不同状态下显示不同颜色的颜色状态列表，如按钮的不同状态颜色。
     * 优先从当前皮肤包中获取，如果未找到则从应用默认资源中获取。
     *
     * @param resId 颜色状态列表的资源ID
     * @return 返回颜色状态列表对象
     */
    @Override
    public ColorStateList getColorStateList(int resId) {
        return skinResources.getColorStateList(resId);
    }

    /**
     * 通过资源名称和ID获取颜色状态列表
     * <p>
     * 该方法支持通过资源名称和ID的组合来获取颜色状态列表。这种方式更灵活，
     * 可以处理资源ID在不同包中不一致的情况。
     *
     * @param resName 颜色状态列表的资源名称
     * @param resId   颜色状态列表的资源ID（用作备选）
     * @return 返回颜色状态列表对象
     */
    @Override
    public ColorStateList getColorStateList(String resName, int resId) {
        //   MyLog.d(TAG, "资源名字   " + resName + "  资源id   " + resId);
        return skinResources.getColorStateList(resName, resId);

    }

    /**
     * 获取Drawable资源
     * <p>
     * 通过资源ID获取对应的Drawable对象。优先从当前皮肤包中获取，
     * 如果未找到则从应用默认资源中获取。
     *
     * @param resId Drawable资源的资源ID
     * @return 返回Drawable对象
     */
    @Override
    public Drawable getDrawable(int resId) {
        //    MyLog.v(TAG, "资源id   " + resId);
        return skinResources.getDrawable(resId);
    }

    /**
     * 通过资源名称和ID获取Drawable
     * <p>
     * 该方法支持通过资源名称和ID的组合来获取Drawable。这种方式更灵活，
     * 可以处理资源ID在不同包中不一致的情况。
     *
     * @param resName Drawable资源的名称
     * @param resId   Drawable资源的资源ID（用作备选）
     * @return 返回Drawable对象
     */
    @Override
    public Drawable getDrawable(String resName, int resId) {
        return skinResources.getDrawable(resName, resId);
    }

    /**
     * 通过资源名称、类型和ID获取Drawable
     * <p>
     * 这是最灵活的Drawable获取方法，支持指定资源类型。适用于：
     * - 需要明确资源类型的场景
     * - 处理特殊类型的Drawable资源
     * - 资源ID在不同包中不一致的情况
     *
     * @param resName Drawable资源的名称
     * @param resType 资源类型（如：drawable, mipmap等）
     * @param resId   Drawable资源的资源ID（用作备选）
     * @return 返回Drawable对象
     */
    @Override
    public Drawable getDrawable(String resName, String resType, int resId) {
        //   MyLog.d(TAG, "资源类型   " + resName);
        return skinResources.getDrawable(resName, resType, resId);
    }

    /**
     * 计算文件的MD5值
     * @param file 目标文件
     * @return MD5字符串，异常时返回null
     */
    private static String getFileMD5(File file) {
        if (file == null || !file.exists()) return null;
        try (InputStream fis = new FileInputStream(file)) {
            MessageDigest md = MessageDigest.getInstance("MD5");
            DigestInputStream dis = new DigestInputStream(fis, md);
            byte[] buffer = new byte[4096];
            while (dis.read(buffer) != -1) {}
            byte[] md5Bytes = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : md5Bytes) {
                sb.append(String.format("%02x", b & 0xff));
            }
            return sb.toString();
        } catch (Exception e) {
            MyLog.e(TAG, "计算文件MD5失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 计算 assets 目录下文件的MD5值
     * @param context 上下文
     * @param assetName assets 文件名
     * @return MD5字符串，异常时返回null
     */
    private static String getAssetFileMD5(Context context, String assetName) {
        if (context == null || assetName == null) return null;
        try (InputStream is = context.getAssets().open(assetName)) {
            MessageDigest md = MessageDigest.getInstance("MD5");
            DigestInputStream dis = new DigestInputStream(is, md);
            byte[] buffer = new byte[4096];
            while (dis.read(buffer) != -1) {}
            byte[] md5Bytes = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : md5Bytes) {
                sb.append(String.format("%02x", b & 0xff));
            }
            return sb.toString();
        } catch (Exception e) {
            MyLog.e(TAG, "计算 assets 文件MD5失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 同步加载皮肤包，阻塞当前线程，确保皮肤资源在 UI 初始化前生效
     * @param name 皮肤文件名
     */
    public void loadSkinSync(String name) {
        MyLog.d(TAG, "[同步] 开始加载皮肤: " + name);
        if (TextUtils.isEmpty(name)) {
            MyLog.e(TAG, "[同步] 皮肤名称为空，加载失败");
            return;
        }
        try {
            File skinFile = new File(getSkinDir(), name);
            if (!skinFile.exists()) {
                MyLog.e(TAG, "[同步] 皮肤文件不存在: " + name);
                return;
            }
            String skinPkgPath = skinFile.getAbsolutePath();
            MyLog.d(TAG, "[同步] 皮肤路径" + skinPkgPath);
            PackageManager mPm = context.getPackageManager();
            PackageInfo mInfo = mPm.getPackageArchiveInfo(skinPkgPath, PackageManager.GET_ACTIVITIES);
            if (mInfo == null) {
                MyLog.e(TAG, "[同步] 获取皮肤包信息失败");
                return;
            }
            AssetManager assetManager;
            try {
                assetManager = AssetManager.class.newInstance();
                Method addAssetPath = assetManager.getClass().getMethod("addAssetPath", String.class);
                addAssetPath.invoke(assetManager, skinPkgPath);
            } catch (Exception e) {
                MyLog.e(TAG, "[同步] 创建AssetManager失败: " + e.getMessage());
                return;
            }
            Resources superRes = context.getResources();
            Resources skinResource;
            try {
                skinResource = new Resources(assetManager,
                        superRes.getDisplayMetrics(),
                        superRes.getConfiguration());
                // 设置包名
                skinResources.setSkinPackageName(mInfo.packageName);
            } catch (Exception e) {
                MyLog.e(TAG, "[同步] 创建Resources失败: " + e.getMessage());
                return;
            }
            skinResources.setSkinResources(skinResource);
            if (skinPkgPath != null) {
                SPUtil.put(context, KEY, skinPkgPath);
            }
            MyLog.d(TAG, "[同步] 皮肤加载完成");
        } catch (Exception e) {
            MyLog.e(TAG, "[同步] 加载皮肤失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
