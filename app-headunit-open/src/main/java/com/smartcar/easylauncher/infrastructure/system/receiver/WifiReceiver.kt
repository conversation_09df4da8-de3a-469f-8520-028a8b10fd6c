package com.smartcar.easylauncher.infrastructure.system.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.net.wifi.WifiManager
import com.jeremyliao.liveeventbus.LiveEventBus
import com.smartcar.easylauncher.core.constants.Constants
import com.smartcar.easylauncher.shared.utils.MyLog
import com.smartcar.easylauncher.shared.utils.apportutil.LogUtils
import com.smartcar.easylauncher.shared.utils.apportutil.Tag

class WifiReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        LogUtils.wtf(Tag, "onReceive: ${intent.action}")
        if (ConnectivityManager.CONNECTIVITY_ACTION == intent.action) {
            updateNetworkStatus(context)
//            val flag = WifiUtils.getNetState()
//            LiveEventBus.get<Boolean>(Constants.WIFI_CONNECT_CHANGE_EVENT).post(flag)
        }
    }

    private fun updateNetworkStatus(context: Context) {
        //WiFi
        val mConnectivityManager =
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        MyLog.v("", "执行次数")
        val info = mConnectivityManager.activeNetworkInfo
        if (info != null) {
            MyLog.v("", "getTypeName(): " + info.typeName)
            MyLog.v("", "getSubtypeName(): " + info.subtypeName)
            if (info.type == ConnectivityManager.TYPE_WIFI) {  //WiFi网络
                getWifiSignalLevelDesc(context)
            } else if (info.type == ConnectivityManager.TYPE_MOBILE) {   //移动网络
                LiveEventBus.get<Boolean>(Constants.WIFI_CONNECT_CHANGE_EVENT).post(false)
            }
        } else {   //没有网络
            MyLog.v("", "发送的时候的网络状态  " + false)
            LiveEventBus.get<Boolean>(Constants.WIFI_CONNECT_CHANGE_EVENT).post(false)
        }
    }

    /**
     * 获取当前连接的Wifi信号强度等级的中文描述
     *
     * @param context 上下文
     * @return 信号强度等级的中文描述
     */
    fun getWifiSignalLevelDesc(context: Context) {
        // 获取WifiManager实例
        val wifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
        // 获取当前连接的WifiInfo
        val wifiInfo = wifiManager.connectionInfo
        // 获取当前连接的信号强度等级
        MyLog.v("", "wifiInfo.getRssi()    " + wifiInfo.rssi)
        val level = WifiManager.calculateSignalLevel(wifiInfo.rssi, 5)
        MyLog.v("", "level    $level")
        signalLevel(level)
    }

    private fun signalLevel(level: Int) {

        // 将信号强度等级转换为中文描述
        val levelDesc: String
        when (level) {
            0 -> {
                levelDesc = "无信号"
                LiveEventBus.get<Boolean>(Constants.WIFI_CONNECT_CHANGE_EVENT).post(false)
            }

            1 -> {
                LiveEventBus.get<Boolean>(Constants.WIFI_CONNECT_CHANGE_EVENT).post(true)
                levelDesc = "极弱"
            }

            2 -> {
                LiveEventBus.get<Boolean>(Constants.WIFI_CONNECT_CHANGE_EVENT).post(true)
                levelDesc = "较弱"
            }

            3 -> {
                LiveEventBus.get<Boolean>(Constants.WIFI_CONNECT_CHANGE_EVENT).post(true)
                levelDesc = "良好"
            }

            4 -> {
                LiveEventBus.get<Boolean>(Constants.WIFI_CONNECT_CHANGE_EVENT).post(true)
                levelDesc = "强"
            }

            else -> {
                levelDesc = "未知"
                LiveEventBus.get<Boolean>(Constants.WIFI_CONNECT_CHANGE_EVENT).post(false)
            }
        }
    }
}
