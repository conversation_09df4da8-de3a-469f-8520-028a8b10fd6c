package com.smartcar.easylauncher.infrastructure.driver;

import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;

import java.io.IOException;

/**
 * USB串口通信的抽象基类
 * 实现了UsbSerialPort接口，提供USB设备串口通信的基本功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since [发布日期]
 */
abstract class CommonUsbSerialPort implements UsbSerialPort {

    /**
     * 默认读取缓冲区大小：16KB
     * 注意：较大的缓冲区可能会占用更多内存，但可以提高读取效率
     */
    public static final int DEFAULT_READ_BUFFER_SIZE = 16 * 1024;

    /**
     * 默认写入缓冲区大小：16KB
     * 注意：较大的缓冲区可能会占用更多内存，但可以提高写入效率
     */
    public static final int DEFAULT_WRITE_BUFFER_SIZE = 16 * 1024;

    /**
     * USB设备实例
     * 用于存储和操作USB设备相关信息
     */
    protected final UsbDevice mDevice;

    /**
     * USB端口号
     * 用于标识当前使用的USB端口
     */
    protected final int mPortNumber;

    /**
     * 读取缓冲区的同步锁对象
     * 用于保证多线程环境下读取操作的线程安全
     */
    protected final Object mReadBufferLock = new Object();

    /**
     * 写入缓冲区的同步锁对象
     * 用于保证多线程环境下写入操作的线程安全
     */
    protected final Object mWriteBufferLock = new Object();

    /**
     * USB设备连接实例
     * 注意：使用volatile确保多线程环境下的可见性
     */
    protected volatile UsbDeviceConnection mConnection = null;

    /**
     * 读取数据的缓冲区
     * 注意：此缓冲区的访问需要通过mReadBufferLock进行同步
     */
    protected byte[] mReadBuffer;

    /**
     * 写入数据的缓冲区
     * 注意：此缓冲区的访问需要通过mWriteBufferLock进行同步
     */
    protected byte[] mWriteBuffer;

    /**
     * 构造函数
     *
     * @param usbDevice USB设备实例
     * @param portNumber 端口号
     */
    public CommonUsbSerialPort(UsbDevice usbDevice, int portNumber) {
        this.mDevice = usbDevice;
        this.mPortNumber = portNumber;
        this.mReadBuffer = new byte[DEFAULT_READ_BUFFER_SIZE];
        this.mWriteBuffer = new byte[DEFAULT_WRITE_BUFFER_SIZE];
    }

    @Override
    public abstract void close() throws IOException;

    @Override
    public abstract boolean getCD() throws IOException;

    @Override
    public abstract boolean getCTS() throws IOException;

    @Override
    public abstract boolean getDSR() throws IOException;

    @Override
    public abstract boolean getDTR() throws IOException;

    @Override
    public abstract void setDTR(boolean dtrState) throws IOException;

    @Override
    public abstract boolean getRI() throws IOException;

    @Override
    public abstract boolean getRTS() throws IOException;

    @Override
    public abstract void setRTS(boolean rtsState) throws IOException;

    @Override
    public abstract void open(UsbDeviceConnection usbDeviceConnection) throws IOException;

    /**
     * 清除硬件缓冲区
     *
     * @param flushRead 是否清除读取缓冲区
     * @param flushWrite 是否清除写入缓冲区
     * @return 如果没有执行任何清除操作返回true，否则返回false
     * @throws IOException 清除缓冲区时可能发生的IO异常
     */
    @Override
    public boolean purgeHwBuffers(boolean flushRead, boolean flushWrite) throws IOException {
        return !flushRead && !flushWrite;
    }

    @Override
    public abstract int read(byte[] buffer, int length) throws IOException;

    @Override
    public abstract void setParameters(int baudRate, int dataBits, int stopBits, int parity)
            throws IOException;

    @Override
    public abstract int write(byte[] buffer, int length) throws IOException;

    /**
     * 获取设备信息的字符串表示
     *
     * @return 包含设备名称、设备ID和端口号的格式化字符串
     */
    @Override
    public String toString() {
        return String.format("<%s device_name=%s device_id=%s port_number=%s>",
                getClass().getSimpleName(),
                mDevice.getDeviceName(),
                Integer.valueOf(mDevice.getDeviceId()),
                Integer.valueOf(mPortNumber));
    }

    /**
     * 获取USB设备实例
     *
     * @return USB设备实例
     */
    public final UsbDevice getDevice() {
        return mDevice;
    }

    @Override
    public int getPortNumber() {
        return mPortNumber;
    }

    @Override
    public String getSerial() {
        return mConnection.getSerial();
    }

    /**
     * 设置读取缓冲区大小
     * 注意：此方法是线程安全的
     *
     * @param size 新的缓冲区大小（字节）
     */
    public final void setReadBufferSize(int size) {
        synchronized (mReadBufferLock) {
            if (size != mReadBuffer.length) {
                mReadBuffer = new byte[size];
            }
        }
    }

    /**
     * 设置写入缓冲区大小
     * 注意：此方法是线程安全的
     *
     * @param size 新的缓冲区大小（字节）
     */
    public final void setWriteBufferSize(int size) {
        synchronized (mWriteBufferLock) {
            if (size != mWriteBuffer.length) {
                mWriteBuffer = new byte[size];
            }
        }
    }
}