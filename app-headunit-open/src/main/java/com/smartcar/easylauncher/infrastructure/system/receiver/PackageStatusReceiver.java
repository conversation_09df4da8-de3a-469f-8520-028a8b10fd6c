package com.smartcar.easylauncher.infrastructure.system.receiver;


import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.core.constants.Constants;
import com.smartcar.easylauncher.infrastructure.event.scope.notice.cody.GeneralNotificationScopeBus;
import com.smartcar.easylauncher.data.model.common.GeneralNoticeModel;
import com.smartcar.easylauncher.shared.utils.MyLog;

public class PackageStatusReceiver extends BroadcastReceiver {

    private static final String TAG = PackageStatusReceiver.class.getSimpleName();
    Context context;

    public PackageStatusReceiver(Context context) {
        this.context = context;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        String packageName = "";
        // 获取广播的动作
        String action = intent.getAction();
        MyLog.v(TAG, "广播内容    " + action);
        // 判断广播动作
        switch (action) {
            case Intent.ACTION_PACKAGE_ADDED:
                // 当应用被安装时触发
                packageName = intent.getData().getSchemeSpecificPart();
                MyLog.v(TAG, packageName + "安装成功");
                // 发送一个事件通知
                GeneralNotificationScopeBus.eventBean().post(new GeneralNoticeModel(Constants.NoticeType.APP_INSTALL, packageName, true));
                break;
            case Intent.ACTION_PACKAGE_REPLACED:
                // 当应用被替换时触发
                packageName = intent.getData().getSchemeSpecificPart();
                MyLog.v(TAG, packageName + "替换成功");
                break;
            case Intent.ACTION_PACKAGE_REMOVED:
                // 当应用被卸载时触发
                packageName = intent.getData().getSchemeSpecificPart();
                MyLog.v(TAG, packageName + "卸载成功");
                // 发送一个事件通知
                GeneralNotificationScopeBus.eventBean().post(new GeneralNoticeModel(Constants.NoticeType.APP_UNINSTALL, packageName, true));
                break;
            case Intent.ACTION_BOOT_COMPLETED:
                // 当设备启动完成时触发
                MyLog.v(TAG, "设备启动成功了");
                MToast.makeTextShort("Package系统广播时间更新");
                break;

            default:
                MyLog.v(TAG, "其他事件     " + action);
        }
    }
}
