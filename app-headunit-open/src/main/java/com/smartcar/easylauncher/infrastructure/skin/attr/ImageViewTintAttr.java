package com.smartcar.easylauncher.infrastructure.skin.attr;

import android.view.View;
import android.widget.ImageView;
import androidx.core.widget.ImageViewCompat;
import android.content.res.ColorStateList;

import com.smartcar.easylauncher.infrastructure.skin.SkinManager;

public class ImageViewTintAttr extends SkinAttr {

    public ImageViewTintAttr() {
        super("tint");
    }

    public ImageViewTintAttr(String attrName, String attrType, String resName, int resId) {
        super(attrName, attrType, resName, resId);
    }

    @Override
    public void applySkin(View view) {
        if (!(view instanceof ImageView)) return;

        ImageView iv = (ImageView) view;
        if (isColor()) {
            // 使用 ImageViewCompat 设置 tint,支持所有 API 版本
            ImageViewCompat.setImageTintList(iv,
                    ColorStateList.valueOf(SkinManager.getInstance().getColor(resName, resId))
            );
        }
    }
}