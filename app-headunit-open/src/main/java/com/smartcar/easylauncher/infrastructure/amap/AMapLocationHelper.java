package com.smartcar.easylauncher.infrastructure.amap;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;

import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.infrastructure.event.scope.location.cody.LocatelnfoScopeBus;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.core.manager.ThemeHelper;
import com.smartcar.easylauncher.data.model.navigation.LocateInforModel;
import com.smartcar.easylauncher.shared.utils.location.AmapUtils;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.thread.ThreadPoolUtil;
import com.smartcar.easylauncher.shared.utils.time.TimeUnitKt;
import com.smartcar.easylauncher.shared.utils.time.TimedTasksUtilsKt;

import kotlinx.coroutines.Job;

/**
 * 范庆文 2023/1/2
 * 高德定位工具类
 *
 * <AUTHOR>
 */
public class AMapLocationHelper {
    public static final String TAG = "LocationUtil";
    private AMapLocationClient locationClient = null;
    private AMapLocationClientOption locationOption = null;
    private double mLongitude = 0;
    private double mLatitude = 0;
    private Context mContext;
    private Job job;
    private TimedTasksUtilsKt timedTasksUtilsKt = TimedTasksUtilsKt.Companion.getInstance();

    private AMapLocationHelper() {
    }

    private static class SingletonHolder {
        private static final AMapLocationHelper INSTANCE = new AMapLocationHelper();
    }

    public static AMapLocationHelper getInstance() {
        return AMapLocationHelper.SingletonHolder.INSTANCE;
    }


    public AMapLocationHelper init(Context context) {
        this.mContext = context.getApplicationContext();
        return this;
    }


    /**
     * 单次定位
     *
     * @param callback 定位结果回调
     */
    public void singleLocation(AMapLocationListener callback) {
        if (locationClient == null) {
            try {
                locationClient = new AMapLocationClient(mContext);
            } catch (Exception e) {
                e.printStackTrace();
                return;
            }
        }

        if (locationClient.isStarted()) {
            MyLog.v(TAG, "  已经开始定位,无需重复开始");
            return;
        }

        AMapLocationClientOption singleLocationOption = new AMapLocationClientOption();
        singleLocationOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);

        // 设置是否优先使用GPS进行定位
        // 只在高精度模式下有效，默认关闭
        singleLocationOption.setGpsFirst(true);

        // 设置网络请求超时时间为10000毫秒（10秒）
        // 在仅设备模式下无效，默认为30秒
        singleLocationOption.setHttpTimeOut(10000);

        // 设置定位间隔，单位为毫秒
        // 默认为2秒
        singleLocationOption.setInterval(Const.LOCATION_INTERVAL);

        // 设置是否返回逆地理地址信息
        // 默认是true
        singleLocationOption.setNeedAddress(true);

        // 设置是否为单次定位
        // 默认为false
        singleLocationOption.setOnceLocation(true);

        // 设置是否等待Wi-Fi刷新后再进行定位
        // 默认为false
        // 如果设置为true，会自动变为单次定位，持续定位时不要使用
        singleLocationOption.setOnceLocationLatest(false);

        // 设置网络请求的协议为HTTP
        // 可选HTTP或者HTTPS，默认为HTTP
        AMapLocationClientOption.setLocationProtocol(AMapLocationClientOption.AMapLocationProtocol.HTTP);

        // 设置是否使用传感器进行定位
        // 默认是false
        singleLocationOption.setSensorEnable(true);

        // 设置是否开启Wi-Fi扫描
        // 默认为true
        // 如果设置为false，会停止主动刷新，完全依赖于系统刷新，定位位置可能存在误差
        singleLocationOption.setWifiScan(true);

        // 设置是否使用缓存定位
        // 默认为true
        singleLocationOption.setLocationCacheEnable(true);

        // 设置逆地理信息的语言
        // 默认值为默认语言（根据所在地区选择语言）
        singleLocationOption.setGeoLanguage(AMapLocationClientOption.GeoLanguage.DEFAULT);

        locationClient.setLocationOption(singleLocationOption);
        locationClient.setLocationListener(callback);
        locationClient.startLocation();
    }

    /**
     * 初始化定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    public void initLocation() {
        //初始化client
        if (locationClient != null) {
            MyLog.v(TAG, "  已经初始化过,无需重复初始化");
            return;
        }
        try {
            locationClient = new AMapLocationClient(mContext);
        } catch (Exception e) {
            e.printStackTrace();
            return;
        }

        locationOption = getDefaultOption();
        //设置定位参数
        locationClient.setLocationOption(locationOption);
        // 设置定位监听
        locationClient.setLocationListener(locationListener);
        startLocation();
    }


    /**
     * 获取当前定位信息
     *
     * @return 当前定位信息，如果定位客户端未初始化，则返回null
     */
    public com.amap.api.location.AMapLocation getCurrentLocation() {
        if (locationClient == null) {
            return null;
        }
        return locationClient.getLastKnownLocation();
    }

    /**
     * 打开逆地理编码功能
     */
    public void openNeedAddress() {
        if (locationClient != null) {
            AMapLocationClientOption tempOption = new AMapLocationClientOption();
            tempOption.setNeedAddress(true);
            locationClient.setLocationOption(tempOption);
            locationClient.startLocation();
        }
    }

    /**
     * 关闭逆地理编码功能
     */
    private void closeNeedAddress() {
        if (locationClient != null) {
            AMapLocationClientOption tempOption = new AMapLocationClientOption();
            tempOption.setNeedAddress(false);
            locationClient.setLocationOption(tempOption);
            locationClient.startLocation();
        }
    }


    /**
     * 计算两个坐标点之间的距离
     *
     * @param lat1
     * @param lon1
     * @param lat2
     * @param lon2
     * @return
     */
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        // 地球半径，单位为米
        double earthRadius = 6371000;
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
                        Math.sin(dLon / 2) * Math.sin(dLon / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return earthRadius * c;
    }

    /**
     * 获取AMap定位客户端的默认配置选项
     *
     * @return 返回AMapLocationClientOption对象，包含了定位相关的配置信息
     */
    private AMapLocationClientOption getDefaultOption() {
        // 创建一个AMapLocationClientOption对象，用于设置定位相关的配置
        AMapLocationClientOption mOption = new AMapLocationClientOption();

        // 设置定位模式为高精度模式
        // 可选的模式有高精度、仅设备（Device_Sensors）、仅网络
        // 默认为高精度模式
        mOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);

        // 设置是否优先使用GPS进行定位
        // 只在高精度模式下有效，默认关闭
        mOption.setGpsFirst(true);

        // 设置网络请求超时时间为10000毫秒（10秒）
        // 在仅设备模式下无效，默认为30秒
        mOption.setHttpTimeOut(10000);

        // 设置定位间隔，单位为毫秒
        // 默认为2秒
        mOption.setInterval(Const.LOCATION_INTERVAL);

        // 设置是否返回逆地理地址信息
        // 默认是true
        mOption.setNeedAddress(true);

        // 设置是否为单次定位
        // 默认为false
        mOption.setOnceLocation(true);

        // 设置是否等待Wi-Fi刷新后再进行定位
        // 默认为false
        // 如果设置为true，会自动变为单次定位，持续定位时不要使用
        mOption.setOnceLocationLatest(false);

        // 设置网络请求的协议为HTTP
        // 可选HTTP或者HTTPS，默认为HTTP
        AMapLocationClientOption.setLocationProtocol(AMapLocationClientOption.AMapLocationProtocol.HTTP);

        // 设置是否使用传感器进行定位
        // 默认是false
        mOption.setSensorEnable(true);

        // 设置是否开启Wi-Fi扫描
        // 默认为true
        // 如果设置为false，会停止主动刷新，完全依赖于系统刷新，定位位置可能存在误差
        mOption.setWifiScan(true);

        // 设置是否使用缓存定位
        // 默认为true
        mOption.setLocationCacheEnable(true);

        // 设置逆地理信息的语言
        // 默认值为默认语言（根据所在地区选择语言）
        mOption.setGeoLanguage(AMapLocationClientOption.GeoLanguage.DEFAULT);

        // 返回配置好的AMapLocationClientOption对象
        return mOption;
    }


    /**
     * 开始定位
     *
     * <AUTHOR>
     * @since 2.8.0
     */
    private void startLocation() {
        //根据控件的选择，重新设置定位参数
        //   resetOption();
        // 设置定位参数
        locationClient.setLocationOption(locationOption);
        // 启动定位
        locationClient.startLocation();

    }

    private void inspectionTopic() {
        Handler handler = new Handler(Looper.getMainLooper());
        job = timedTasksUtilsKt.beginExecutorScan(new TimedTasksUtilsKt.TimerCallback() {
            @Override
            public void onTimeUpdate() {
                MyLog.v("主题计时器", "正在检测时间");
                ThreadPoolUtil.getThreadPoolExecutor().submit(() -> {
                    handler.post(() -> ThemeHelper.updateThemeIfNeeded(mLongitude, mLatitude));
                });
            }

            @Override
            public void onTimerEnd() {

            }
        }, 0, Const.UPDATE_THEME, TimeUnitKt.MINUTES);
    }




    /**
     * 定位监听
     */
    AMapLocationListener locationListener = new AMapLocationListener() {
        @Override
        public void onLocationChanged(com.amap.api.location.AMapLocation location) {

            if (null != location) {
                LocateInforModel locationInfo = new LocateInforModel();
                locationInfo.setErrorCode(location.getErrorCode());
                StringBuilder sb = new StringBuilder();
                //errCode等于0代表定位成功，其他的为定位失败，具体的可以参照官网定位错误码说明
                if (location.getErrorCode() == 0) {
                    // 如果开启了逆地理编码功能，并且有地址信息，就关闭逆地理编码
                    // 检查location的地址信息是否非空
                    if (location.getAddress() != null && !location.getAddress().isEmpty()) {
                        // 如果地址信息非空，则从location中获取并设置到DataManager中
                        // 设置国家
                        DataManager.setCountry(location.getCountry());
                        // 设置省份
                        DataManager.setProvince(location.getProvince());
                        // 设置城市
                        DataManager.setCity(location.getCity());
                        // 设置城市代码
                        DataManager.setCityCode(location.getCityCode());
                        // 设置详细地址
                        DataManager.setAddress(location.getAddress());
                        // 设置区域（可能是区、县等）
                        DataManager.setDistrict(location.getDistrict());
                        // 设置行政区划代码
                        DataManager.setAdCode(location.getAdCode());
                        // 设置兴趣点名称（如餐厅、景点等）
                        DataManager.setPoiName(location.getPoiName());

                        // 关闭需要地址的某些操作或界面（根据具体业务逻辑实现）
                        closeNeedAddress();
                    } else {
                        // 如果location的地址信息为空，则从DataManager中获取并设置到location中
                        // 设置国家
                        location.setCountry(DataManager.getCountry());
                        // 设置省份
                        location.setProvince(DataManager.getProvince());
                        // 设置城市
                        location.setCity(DataManager.getCity());
                        // 设置城市代码
                        location.setCityCode(DataManager.getCityCode());
                        // 设置详细地址
                        location.setAddress(DataManager.getAddress());
                        // 设置区域（可能是区、县等）
                        location.setDistrict(DataManager.getDistrict());
                        // 设置行政区划代码
                        location.setAdCode(DataManager.getAdCode());
                        // 设置兴趣点名称（如餐厅、景点等）
                        location.setPoiName(DataManager.getPoiName());
                    }
                    locationInfo.setLocationProvider(1);
                    locationInfo.setLocationType(location.getLocationType());
                    locationInfo.setLatitude(location.getLatitude());
                    locationInfo.setLongitude(location.getLongitude());
                    locationInfo.setAccuracy(location.getAccuracy());
                    locationInfo.setAltitude(location.getAltitude());
                    locationInfo.setProvider(location.getProvider());
                    locationInfo.setSpeed(location.getSpeed());
                    locationInfo.setBearing(location.getBearing());
                    locationInfo.setSatellites(location.getSatellites());
                    locationInfo.setCountry(location.getCountry());
                    locationInfo.setProvince(location.getProvince());
                    locationInfo.setCity(location.getCity());
                    locationInfo.setCityCode(location.getCityCode());
                    locationInfo.setDistrict(location.getDistrict());
                    locationInfo.setAdCode(location.getAdCode());
                    locationInfo.setAddress(location.getAddress());
                    locationInfo.setPoiName(location.getPoiName());
                    locationInfo.setTime(location.getTime());
                    //发送定位信息
                    LocatelnfoScopeBus.eventBean().post(locationInfo);
                    mLongitude = location.getLongitude();
                    mLatitude = location.getLatitude();
//                    if (timingTool == null) {
//                        inspectionTopic();
//                    }

                    sb.append("定位成功" + "\n");
                    sb.append("定位类型: ").append(location.getLocationType()).append("\n");
                    sb.append("经    度    : ").append(location.getLongitude()).append("\n");
                    sb.append("纬    度    : ").append(location.getLatitude()).append("\n");
                    sb.append("精    度    : ").append(location.getAccuracy()).append("米").append("\n");
                    sb.append("海    拔    : ").append(location.getAltitude()).append("米").append("\n");
                    sb.append("提供者    : ").append(location.getProvider()).append("\n");
                    sb.append("速    度    : ").append(location.getSpeed()).append("米/秒").append("\n");
                    sb.append("角    度    : ").append(location.getBearing()).append("\n");
                    // 获取当前提供定位服务的卫星个数
                    sb.append("星    数    : ").append(location.getSatellites()).append("\n");
                    sb.append("国    家    : ").append(location.getCountry()).append("\n");
                    sb.append("省            : ").append(location.getProvince()).append("\n");
                    sb.append("市            : ").append(location.getCity()).append("\n");
                    //  UserManager.setCity(location.getCity());
                    sb.append("城市编码 : ").append(location.getCityCode()).append("\n");
                    sb.append("区            : ").append(location.getDistrict()).append("\n");
                    sb.append("区域 码   : ").append(location.getAdCode()).append("\n");
                    sb.append("地    址    : ").append(location.getAddress()).append("\n");
                    sb.append("兴趣点    : ").append(location.getPoiName()).append("\n");
                    //定位完成的时间
                    sb.append("定位时间: ").append(AmapUtils.formatUTC(location.getTime(), "yyyy-MM-dd HH:mm:ss")).append("\n");
                } else {
                    //定位失败
                    sb.append("定位失败" + "\n");
                    sb.append("错误码:").append(location.getErrorCode()).append("\n");
                    sb.append("错误信息:").append(location.getErrorInfo()).append("\n");
                    sb.append("错误描述:").append(location.getLocationDetail()).append("\n");
                }
                sb.append("***定位质量报告***").append("\n");
                sb.append("* WIFI开关：").append(location.getLocationQualityReport().isWifiAble() ? "开启" : "关闭").append("\n");
                sb.append("* GPS状态：").append(AmapUtils.getGPSStatusString(location.getLocationQualityReport().getGPSStatus())).append("\n");
                sb.append("* GPS星数：").append(location.getLocationQualityReport().getGPSSatellites()).append("\n");
                sb.append("* 网络类型：").append(location.getLocationQualityReport().getNetworkType()).append("\n");
                sb.append("* 网络耗时：").append(location.getLocationQualityReport().getNetUseTime()).append("\n");
                sb.append("****************").append("\n");
                //定位之后的回调时间
                sb.append("回调时间: ").append(AmapUtils.formatUTC(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss")).append("\n");

                //解析定位结果，
                String result = sb.toString();
                MyLog.v(TAG, result);

            } else {
                MyLog.v(TAG, "定位失败----loc is null");
            }
        }
    };


}
