package com.smartcar.easylauncher.infrastructure.event.scope.location;


import com.smartcar.easylauncher.data.model.navigation.NavigationSettingsModel;

import cody.bus.annotation.Event;
import cody.bus.annotation.EventGroup;

/**
 * 高德功能设置导航事件
 */
@EventGroup(value = "NavigationSettingsScope", active = true)
public class NavigationSettingsEventDefine {

    @Event(value = "NavigationSettingseventBean", multiProcess = true)
    NavigationSettingsModel eventBean;

}