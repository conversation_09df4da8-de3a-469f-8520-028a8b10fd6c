package com.smartcar.easylauncher.infrastructure.system.service.weather;


import androidx.annotation.NonNull;

import com.smartcar.easylauncher.data.model.navigation.LocateInforModel;


/**
 * 天气API提供者接口
 * 所有的天气数据源都需要实现这个接口
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-03-21
 */
public interface WeatherProvider {

    /**
     * 获取提供者优先级
     * 数值越小优先级越高
     *
     * @return 优先级值
     */
    int getPriority();

    /**
     * 获取提供者名称
     *
     * @return 提供者名称
     */
    @NonNull
    String getProviderName();

    /**
     * 获取天气数据
     *
     * @param location 位置信息
     * @param callback 回调接口
     */
    void fetchWeatherData(@NonNull LocateInforModel location, @NonNull WeatherCallback callback);

    /**
     * 检查服务是否可用
     *
     * @return 服务是否可用
     */
    boolean isAvailable();

    /**
     * 获取API密钥
     *
     * @return API密钥
     */
    String getApiKey();
}