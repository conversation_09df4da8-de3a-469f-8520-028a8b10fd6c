package com.smartcar.easylauncher.infrastructure.system.receiver;


import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;

import com.smartcar.easylauncher.core.constants.Constants;
import com.smartcar.easylauncher.infrastructure.event.scope.device.cody.UsbMessageScopeBus;
import com.smartcar.easylauncher.infrastructure.event.scope.layout.cody.ScreenStatusScopeBus;
import com.smartcar.easylauncher.infrastructure.event.scope.notice.cody.GeneralNotificationScopeBus;
import com.smartcar.easylauncher.data.model.common.GeneralNoticeModel;
import com.smartcar.easylauncher.data.model.system.ScreenStatusModel;
import com.smartcar.easylauncher.data.model.system.UsbMessageModel;
import com.smartcar.easylauncher.shared.utils.MyLog;

public class SystemReceiver extends BroadcastReceiver {

    private static final String TAG = SystemReceiver.class.getSimpleName();
    Context context;

    public SystemReceiver(Context context) {
        this.context = context;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        String packageName = "";
        // 获取广播的动作
        String action = intent.getAction();
        MyLog.v(TAG, "广播内容    " + action);
        // 判断广播动作
        switch (action) {
            case UsbManager.ACTION_USB_DEVICE_DETACHED:
                // 当USB设备断开时触发
                MyLog.v(TAG, "USB设备断开了");
                UsbMessageScopeBus.eventBean().post(new UsbMessageModel(intent));
                break;

            case UsbManager.ACTION_USB_DEVICE_ATTACHED:
                // 当USB设备连接时触发
                MyLog.v(TAG, "USB设备连接了");
                UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                if (device != null) {
                    MyLog.d(TAG, "已连接的USB设备信息: " + "供应商" + device.getVendorId() + " 产品" + device.getProductId());
                    // 处理USB设备连接事件，并获取设备信息
                    // 例如：device.getVendorId() 获取供应商ID
                    //      device.getProductId() 获取产品ID
                    //      ...
                }
                UsbMessageScopeBus.eventBean().post(new UsbMessageModel(intent));
                break;
            case UsbManager.ACTION_USB_ACCESSORY_ATTACHED:
                // 当USB配件连接时触发
                MyLog.v(TAG, "USB配件连接了");
                break;
            case UsbManager.ACTION_USB_ACCESSORY_DETACHED:
                // 当USB配件断开时触发
                MyLog.v(TAG, "USB配件断开了");
                break;
            case "android.hardware.usb.action.USB_STATE":
                // 当USB状态改变时触发
                MyLog.v(TAG, "USB状态改变了");
                break;
            case Intent.ACTION_PACKAGE_ADDED:
                // 当应用被安装时触发
                packageName = intent.getData().getSchemeSpecificPart();
                MyLog.v(TAG, packageName + "安装成功");
                // 发送一个事件通知
                GeneralNotificationScopeBus.eventBean().post(new GeneralNoticeModel(Constants.NoticeType.APP_INSTALL, packageName, true));
                break;
            case Intent.ACTION_PACKAGE_REPLACED:
                // 当应用被替换时触发
                packageName = intent.getData().getSchemeSpecificPart();
                MyLog.v(TAG, packageName + "替换成功");
                break;
            case Intent.ACTION_PACKAGE_REMOVED:
                // 当应用被卸载时触发
                packageName = intent.getData().getSchemeSpecificPart();
                MyLog.v(TAG, packageName + "卸载成功");
                // 发送一个事件通知
                GeneralNotificationScopeBus.eventBean().post(new GeneralNoticeModel(Constants.NoticeType.APP_UNINSTALL, packageName, true));
                break;
            case Intent.ACTION_BOOT_COMPLETED:
                // 当设备启动完成时触发
                MyLog.v(TAG, "设备启动成功了");
                break;
            case Intent.ACTION_SCREEN_ON:
                // 当屏幕点亮时触发
                MyLog.v(TAG, "屏幕点亮了");
                ScreenStatusScopeBus.eventBean().post(new ScreenStatusModel(true));
                break;
            case Intent.ACTION_SCREEN_OFF:
                // 当屏幕熄灭时触发
                MyLog.v(TAG, "屏幕熄灭了");
                ScreenStatusScopeBus.eventBean().post(new ScreenStatusModel(true));
                break;
            case Intent.ACTION_SHUTDOWN:
                // 当设备关机时触发
                MyLog.v(TAG, "设备关机了");
                break;
            case Intent.ACTION_USER_PRESENT:
                // 当用户解锁屏幕时触发
                MyLog.v(TAG, "用户解锁屏幕了");
                break;
            case Intent.ACTION_USER_UNLOCKED:
                // 当用户解锁设备时触发
                MyLog.v(TAG, "用户解锁设备了");
                break;
            case Intent.ACTION_PACKAGE_FULLY_REMOVED:
                // 当应用完全被移除时触发
                MyLog.v(TAG, "应用完全被移除了");
                break;
            case Intent.ACTION_PACKAGE_CHANGED:
                // 当应用被改变时触发
                MyLog.v(TAG, "应用被改变了");
                break;
            case Intent.ACTION_PACKAGE_RESTARTED:
                // 当应用被重启时触发
                MyLog.v(TAG, "应用被重启了");
                break;
            case Intent.ACTION_PACKAGE_DATA_CLEARED:
                // 当应用的数据被清除时触发
                MyLog.v(TAG, "应用的数据被清除");
                break;
            case Intent.ACTION_PACKAGE_FIRST_LAUNCH:
                // 当应用第一次启动时触发
                MyLog.v(TAG, "应用第一次启动了");
                break;
            case Intent.ACTION_PACKAGE_NEEDS_VERIFICATION:
                // 当应用需要验证时触发
                MyLog.v(TAG, "应用需要验证了");
                break;
            case Intent.ACTION_PACKAGE_VERIFIED:
                // 当应用验证成功时触发
                MyLog.v(TAG, "应用验证成功了");
                break;
            case Intent.ACTION_PACKAGE_INSTALL:
                // 当应用被安装时触发
                MyLog.v(TAG, "应用被安装了");
                break;
            //电量变化
            case Intent.ACTION_BATTERY_CHANGED:
                // 当电量变化时触发
                MyLog.v(TAG, "电量变化了");
                break;
            //充电状态变化
            case Intent.ACTION_BATTERY_LOW:
                // 当电量低时触发
                MyLog.v(TAG, "电量低了");
                break;
            //充电状态变化
            case Intent.ACTION_BATTERY_OKAY:
                // 当电量充足时触发
                MyLog.v(TAG, "电量充足了");
                break;
            //充电状态变化
            case Intent.ACTION_POWER_CONNECTED:
                // 当充电时触发
                MyLog.v(TAG, "充电了");
                break;
            //充电状态变化
            case Intent.ACTION_POWER_DISCONNECTED:
                // 当拔掉充电器时触发
                MyLog.v(TAG, "拔掉充电器了");
                break;
            case Intent.ACTION_TIME_TICK:
                // 当时间改变时触发
                MyLog.v(TAG, "时间改变了");
                break;
            case Intent.ACTION_TIME_CHANGED:
                // 当时间改变时触发
                MyLog.v(TAG, "时间设置改变了");
                break;
            case Intent.ACTION_TIMEZONE_CHANGED:
                // 当时区改变时触发
                MyLog.v(TAG, "时区改变了");
                break;
            case Intent.ACTION_CLOSE_SYSTEM_DIALOGS:
                // 当关闭系统对话框时触发
                MyLog.v(TAG, "   关闭系统对话框了");
                break;
            case Intent.ACTION_DREAMING_STARTED:
                // 当进入睡眠模式时触发
                MyLog.v(TAG, "   进入睡眠模式了");
                break;
            case Intent.ACTION_CONFIGURATION_CHANGED:
                // 当配置改变时触发
                MyLog.v(TAG, "   配置改变了");
                break;
            case Intent.ACTION_LOCALE_CHANGED:
                // 当语言改变时触发
                MyLog.v(TAG, "   语言改变了");
                break;
            case "android.net.wifi.RSSI_CHANGED":
                // wifi信号强度改变时触发
                MyLog.v(TAG, "   wifi信号强度改变了");
                break;
            case "android.net.wifi.WIFI_STATE_CHANGED":
                // wifi状态改变时触发
                MyLog.v(TAG, "   wifi状态改变了");
                break;
            case "android.net.wifi.SCAN_RESULTS":
                // wifi扫描结果改变时触发
                MyLog.v(TAG, "   wifi扫描结果改变了");
                break;
            case "android.net.wifi.STATE_CHANGE":
                // wifi状态改变时触发
                MyLog.v(TAG, "   wifi状态改变了");
                break;
            case "android.net.conn.CONNECTIVITY_CHANGE":
                // 网络连接状态改变时触发
                MyLog.v(TAG, "   网络连接状态改变了");
                break;
            case "android.net.conn.CONNECTIVITY_CHANGE_ACTION":
                // 网络连接状态改变时触发
                MyLog.v(TAG, "   网络连接状态改变了");
                break;
            case Intent.ACTION_DREAMING_STOPPED:
                // 当退出睡眠模式时触发
                MyLog.v(TAG, "   退出睡眠模式了");
                break;
            default:
                MyLog.v(TAG, "其他事件     " + action);
        }
    }
}
