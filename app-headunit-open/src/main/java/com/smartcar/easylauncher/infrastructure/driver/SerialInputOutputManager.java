package com.smartcar.easylauncher.infrastructure.driver;

import android.util.Log;

import com.smartcar.easylauncher.shared.utils.MyLog;

import java.io.IOException;
import java.nio.ByteBuffer;

/**
 * SerialInputOutputManager类用于管理USB串口的输入输出操作。
 * 该类实现了Runnable接口，以便在单独的线程中运行。
 */
public class SerialInputOutputManager implements Runnable {
    /**
     * 缓冲区大小
     */
    private static final int BUFSIZ = 4096;

    /**
     * 调试标志
     */
    private static final boolean DEBUG = true;

    /**
     * 读取等待时间（毫秒）
     */
    private static final int READ_WAIT_MILLIS = 200;

    /**
     * 日志标签
     */
    private static final String TAG = SerialInputOutputManager.class.getSimpleName();

    /**
     * USB串口驱动
     */
    private final UsbSerialPort mDriver;

    /**
     * 读取缓冲区
     */
    private final ByteBuffer mReadBuffer;

    /**
     * 写入缓冲区
     */
    private final ByteBuffer mWriteBuffer;

    /**
     * 监听器，用于回调数据接收和错误处理
     */
    private Listener mListener;

    /**
     * 当前状态
     */
    private State mState;

    /**
     * 构造函数，初始化USB串口和默认监听器
     *
     * @param usbSerialPort USB串口驱动
     */
    public SerialInputOutputManager(UsbSerialPort usbSerialPort) {
        this(usbSerialPort, null);
    }

    /**
     * 构造函数，初始化USB串口和指定监听器
     *
     * @param usbSerialPort USB串口驱动
     * @param listener      监听器
     */
    public SerialInputOutputManager(UsbSerialPort usbSerialPort, Listener listener) {
        this.mReadBuffer = ByteBuffer.allocate(BUFSIZ);
        this.mWriteBuffer = ByteBuffer.allocate(BUFSIZ);
        // 初始状态为停止
        this.mState = State.STOPPED;
        this.mDriver = usbSerialPort;
        this.mListener = listener;
    }

    /**
     * 获取当前监听器
     *
     * @return 当前监听器
     */
    public synchronized Listener getListener() {
        return this.mListener;
    }

    /**
     * 设置监听器
     *
     * @param listener 新的监听器
     */
    public synchronized void setListener(Listener listener) {
        this.mListener = listener;
    }

    /**
     * 异步写入数据到写入缓冲区
     *
     * @param bArr 要写入的数据
     */
    public void writeAsync(byte[] bArr) {
        synchronized (this.mWriteBuffer) {
            // 将数据放入写入缓冲区
            this.mWriteBuffer.put(bArr);
        }
    }

    /**
     * 停止当前操作
     */
    public synchronized void stop() {
        if (getState() == State.RUNNING) {
            // 记录停止请求
            MyLog.v(TAG, "Stop requested");
            // 设置状态为停止中
            this.mState = State.STOPPING;
        }
    }

    /**
     * 获取当前状态
     *
     * @return 当前状态
     */
    private synchronized State getState() {
        return this.mState;
    }

    @Override
    public void run() {
        synchronized (this) {
            if (getState() == State.STOPPED) {
                // 状态设置为运行
                this.mState = State.RUNNING;
            } else {
                // 抛出异常，表示已经在运行
                throw new IllegalStateException("Already running.");
            }
        }
        MyLog.v(TAG, "Running .."); // 记录运行状态
        while (getState() == State.RUNNING) {
            try {
                step(); // 执行步骤
            } catch (Exception e) {
                try {
                    Log.w(TAG, "Run ending due to exception: " + e.getMessage(), e); // 记录异常信息
                    Listener listener = getListener();
                    if (listener != null) {
                        listener.onRunError(e); // 调用监听器的错误处理方法
                    }
                    synchronized (this) {
                        this.mState = State.STOPPED; // 设置状态为停止
                        MyLog.v(TAG, "Stopped."); // 记录停止状态
                        return;
                    }
                } catch (Throwable th) {
                    synchronized (this) {
                        this.mState = State.STOPPED; // 设置状态为停止
                        MyLog.v(TAG, "Stopped."); // 记录停止状态
                    }
                }
            }
        }
        MyLog.v(TAG, "Stopping mState=" + getState()); // 记录停止状态
        synchronized (this) {
            this.mState = State.STOPPED; // 设置状态为停止
            MyLog.v(TAG, "Stopped."); // 记录停止状态
        }
    }

    /**
     * 执行读取和写入操作
     *
     * @throws IOException 可能抛出IO异常
     */
    private void step() throws IOException {
        int position;
        int read = this.mDriver.read(this.mReadBuffer.array(), READ_WAIT_MILLIS); // 从驱动读取数据
        if (read > 0) {
            MyLog.v(TAG, "Read data len=" + read); // 记录读取的数据长度
            Listener listener = getListener();
            if (listener != null) {
                byte[] bArr = new byte[read];
                this.mReadBuffer.get(bArr, 0, read); // 从缓冲区获取数据
                listener.onNewData(bArr); // 调用监听器的回调方法
            }
            this.mReadBuffer.clear(); // 清空读取缓冲区
        }
        byte[] bArr2 = null;
        synchronized (this.mWriteBuffer) {
            position = this.mWriteBuffer.position(); // 获取写入缓冲区的当前位置
            if (position > 0) {
                bArr2 = new byte[position];
                this.mWriteBuffer.rewind(); // 重置缓冲区位置
                this.mWriteBuffer.get(bArr2, 0, position); // 从缓冲区获取数据
                this.mWriteBuffer.clear(); // 清空写入缓冲区
            }
        }
        if (bArr2 != null) {
            MyLog.v(TAG, "Writing data len=" + position); // 记录写入的数据长度
            this.mDriver.write(bArr2, READ_WAIT_MILLIS); // 向驱动写入数据
        }
    }

    /**
     * 定义一个名为State的枚举类，用于表示对象的状态
     */
    public enum State {
        /**
         * 表示对象已停止的状态
         */
        STOPPED,
        /**
         * 表示对象正在运行的状态
         */
        RUNNING,
        /**
         * 表示对象正在停止的状态
         */
        STOPPING
    }

    /**
     * 监听器接口，用于接收新数据和错误通知
     */
    public interface Listener {
        /**
         * 当接收到新数据时调用
         *
         * @param bArr 新接收到的数据
         */
        void onNewData(byte[] bArr);

        /**
         * 当发生运行错误时调用
         *
         * @param exc 发生的异常
         */
        void onRunError(Exception exc);
    }
}