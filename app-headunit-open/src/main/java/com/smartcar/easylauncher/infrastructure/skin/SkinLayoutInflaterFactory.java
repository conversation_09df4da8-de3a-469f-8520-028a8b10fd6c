package com.smartcar.easylauncher.infrastructure.skin;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;

import androidx.appcompat.app.AppCompatInflaterHelper;

import com.smartcar.easylauncher.infrastructure.skin.iface.OnInflaterInterceptor;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.lang.ref.WeakReference;
import java.lang.reflect.Constructor;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;

/**
 * SkinLayoutInflaterFactory类用于在自定义主题或皮肤支持中，拦截并创建视图。
 * 它实现了LayoutInflater.Factory2接口，以允许在视图创建过程中插入自定义逻辑。
 */
public class SkinLayoutInflaterFactory implements LayoutInflater.Factory2 {

    // 缓存已找到的控件构造函数签名
    /**
     * 控件构造函数签名，用于通过反射创建控件实例。
     * 包括Context和AttributeSet作为参数。
     */
    protected final Class<?>[] sConstructorSignature = new Class[]{Context.class, AttributeSet.class};

    // 缓存控件名称到构造函数的映射
    /**
     * 缓存已找到的控件名称到其对应构造函数的映射，以加速后续视图的创建。
     */
    protected final HashMap<String, Constructor<? extends View>> sConstructorMap = new HashMap<>();

    // 存储额外的Factory2实现
    /**
     * 存储其他LayoutInflater.Factory2实现，以便在创建视图时先尝试这些自定义的Factory。
     */
    protected final HashSet<LayoutInflater.Factory2> factorySet = new HashSet<>();

    // 存储视图创建拦截器
    /**
     * 存储OnInflaterInterceptor实现，用于在视图创建完成后进行额外的处理或修改。
     */
    protected final HashSet<OnInflaterInterceptor> interceptorSet = new HashSet<>();

    // 1. 添加 Context 追踪
    private final Set<WeakReference<Context>> trackedContexts = new HashSet<>();

    // 用于存储 Factory 和对应的调用栈信息
    private static class FactoryInfo {
        LayoutInflater.Factory2 factory;
        String dialogTag;  // DialogFragment 的 tag 或生成的唯一标识
        
        FactoryInfo(LayoutInflater.Factory2 factory, String dialogTag) {
            this.factory = factory;
            this.dialogTag = dialogTag;
        }
    }
    
    // 替换原有的 HashSet
    private final Set<FactoryInfo> factoryInfoSet = new HashSet<>();
    
    /**
     * 构造函数，初始化SkinLayoutInflaterFactory实例。
     */
    public SkinLayoutInflaterFactory() {
    }

    /**
     * 添加一个LayoutInflater.Factory2到集合中。
     * @param factory2 要添加的Factory2实例。
     */
    public void addFactory2(LayoutInflater.Factory2 factory2) {
        if (factory2 != null) {
            // 只对 DialogFragment 相关的 Factory 进行替换
            if (isDialogFragmentFactory(factory2)) {
                MyLog.d("SkinSupport", "添加 DialogFragment Factory，移除同类型旧实例");
                // 记录当前的 DialogFragment tag
                String dialogTag = getCurrentDialogTag();
                if (dialogTag != null) {
                    // 移除相同 tag 的旧 Factory
                    factorySet.removeIf(existing -> {
                        if (isDialogFragmentFactory(existing)) {
                            String existingTag = getDialogTagFromFactory(existing);
                            return dialogTag.equals(existingTag);
                        }
                        return false;
                    });
                    // 将新 Factory 与 tag 关联存储
                    factorySet.add(factory2);
                    // 启动定期清理
                    scheduleCleanup();
                }
            } else {
                factorySet.add(factory2);
            }
            MyLog.d("SkinSupport", "成功添加 Factory: " + factory2.getClass().getSimpleName() + 
                ", 当前 Factory 数量: " + factorySet.size());
        }
    }

    private boolean isDialogFragmentFactory(LayoutInflater.Factory2 factory) {
        boolean isDialog = factory.getClass().getName().contains("FragmentLayoutInflaterFactory");
        if (isDialog) {
            MyLog.d("SkinSupport", "检测到 DialogFragment Factory: " + factory.getClass().getName());
        }
        return isDialog;
    }

    private String getDialogTagFromFactory(LayoutInflater.Factory2 factory) {
        try {
            Field fragmentManagerField = factory.getClass().getDeclaredField("mFragmentManager");
            fragmentManagerField.setAccessible(true);
            Object fragmentManager = fragmentManagerField.get(factory);
            if (fragmentManager != null) {
                // 从 FragmentManager 中获取当前活跃的 DialogFragment
                Method getActiveFragmentsMethod = fragmentManager.getClass()
                    .getDeclaredMethod("getActiveFragments");
                getActiveFragmentsMethod.setAccessible(true);
                List<?> fragments = (List<?>) getActiveFragmentsMethod.invoke(fragmentManager);
                if (fragments != null) {
                    for (Object fragment : fragments) {
                        if (fragment != null && fragment.getClass().getName().contains("DialogFragment")) {
                            // 返回 DialogFragment 的唯一标识
                            return fragment.getClass().getName() + "@" + System.identityHashCode(fragment);
                        }
                    }
                }
            }
        } catch (Exception e) {
            MyLog.e("SkinSupport", "获取 Dialog tag 失败", e);
        }
        return null;
    }

    /**
     * 根据给定的名称和属性集，创建并返回一个View对象。
     * 首先尝试使用自定义的Factory2集合中的Factory，如果都失败，则使用AppCompatInflaterHelper或反射来创建。
     *
     * @param name 视图的全名（包括包名）
     * @param context 上下文环境
     * @param attrs 属性集
     * @return 创建的View对象，如果无法创建则返回null
     */
    @Override
    public View onCreateView(String name, Context context, AttributeSet attrs) {
        return onCreateView(null, name, context, attrs);
    }

    /**
     * 根据给定的父视图、名称和属性集，创建并返回一个View对象。
     *
     * @param parent 父视图，可能为null
     * @param name 视图的全名（包括包名）
     * @param context 上下文环境
     * @param attrs 属性集
     * @return 创建的View对象，如果无法创建则返回null
     */
    @Override
    public View onCreateView(View parent, String name, Context context, AttributeSet attrs) {
//        MyLog.d("SkinSupport", "开始创建视图: " + name +
//            ", context: " + context.getClass().getSimpleName());
        
        // 2. 检查并记录 DialogFragment 的 Context
        if (isDialogFragmentContext(context)) {
            trackDialogContext(context);
        }
        
        View view = null;
        // 遍历自定义的Factory2集合，尝试创建视图
        for (LayoutInflater.Factory2 factory2 : factorySet) {
            try {
                view = factory2.onCreateView(parent, name, context, attrs);
            } catch (Exception e) {
                MyLog.e("SkinSupport", "Factory2 创建视图失败: " + factory2.getClass().getSimpleName(), e);
            }

            if (view != null) {
                MyLog.d("SkinSupport", "Factory2 成功创建视图: " + name + 
                    ", factory: " + factory2.getClass().getSimpleName());
                disposeCreateView(context, view, name, attrs);
                return view;
            }
        }

        // 尝试使用AppCompatInflaterHelper创建视图
        try {
            view = AppCompatInflaterHelper.createView(parent, name, context, attrs);
        } catch (Throwable e) {
            e.printStackTrace();
        }

        // 如果还未创建成功，则尝试通过反射创建
        if (view == null) {
            view = createView(name, context, attrs);
        }

        // 处理图创建结果
        if (view == null) {
            Log.w("SkinSupport", "创建视图失败；视图名称是 " + name);
        } else {
            // 视图创建成功，执行后续处理
            disposeCreateView(context, view, name, attrs);
        }
        return view;
    }

    /**
     * 通过反射创建指定名称的视图实例。
     * 如果缓存中已有对应的构造函数，则直接使用；否则，尝试加载类并获取构造函数。
     *
     * @param name 视图的全名（包括包名）
     * @param context 上下文环境
     * @param attrs 属性集
     * @return 创建的View对象，如果无法创建则返回null
     */
    private View createView(String name, Context context, AttributeSet attrs) {
        Constructor<? extends View> constructor = sConstructorMap.get(name);
        if (constructor == null) {
            try {
                // 加载并缓存构造函数
                Class<? extends View> aClass = context.getClassLoader().loadClass(name).asSubclass(View.class);
                constructor = aClass.getConstructor(sConstructorSignature);
                constructor.setAccessible(true);
                sConstructorMap.put(name, constructor);
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }

        // 使用构造函数创建视图实例
        if (constructor != null) {
            try {
                return constructor.newInstance(context, attrs);
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }

        // 如果构造函数不存在或创建失败，则返回null
        return null;
    }

    /**
     * 添加一个视图创建拦截器。
     * 对于 SkinFactory 类型的拦截器，采用替换而不是累加的方式，
     * 以避免多个 SkinFactory 实例造成的资源重复或冲突。
     *
     * @param interceptor 要添加的拦截器实例
     */
    public void addOnInflaterInterceptor(OnInflaterInterceptor interceptor) {
        if (interceptor != null) {
            // 只对 SkinFactory 进行特殊处理
            if (interceptor instanceof SkinFactory) {
                MyLog.d("SkinSupport", "添加 SkinFactory 拦截器，移除旧实例");
                // 移除旧的 SkinFactory
                interceptorSet.removeIf(existing -> 
                    existing instanceof SkinFactory);
            }
            interceptorSet.add(interceptor);
            MyLog.d("SkinSupport", "成功添加拦截器: " + interceptor.getClass().getSimpleName() + 
                ", 当前拦截器数量: " + interceptorSet.size());
        }
    }

    /**
     * 移除一个视图创建拦截器。
     *
     * @param onInflaterInterceptor 要移除的拦截器实例
     */
    public void removeOnInflaterInterceptor(OnInflaterInterceptor onInflaterInterceptor) {
        if (onInflaterInterceptor != null) {
            interceptorSet.remove(onInflaterInterceptor);
        }
    }

    /**
     * 在视图创建后执行拦截器逻辑。
     * 遍历拦截器集合，对每个拦截器调用`interceptorCreateView`方法。
     * 注意：这里不对视图状态做过滤，确保所有视图都能正确收集换肤属性。
     *
     * @param context 上下文环境
     * @param view 已创建的视图实例
     * @param name 视图的全名（包括包名）
     * @param attrs 视图属性集
     */
    public void disposeCreateView(Context context, View view, String name, AttributeSet attrs) {
        if (interceptorSet != null) {
            for (OnInflaterInterceptor interceptor : interceptorSet) {
                if (interceptor instanceof SkinFactory) {
//                    MyLog.d("SkinSupport", "处理视图换肤属性: " + name +
//                        ", viewId: " + view.getId() +
//                        ", context: " + context.getClass().getSimpleName());
                }
                interceptor.interceptorCreateView(context, view, name, attrs);
            }
        }
    }

    private boolean isDialogFragmentContext(Context context) {
        // 检查是否是 DialogFragment 的 Context
        return context.getClass().getName().contains("DialogFragment");
    }

    private void trackDialogContext(Context context) {
        trackedContexts.add(new WeakReference<>(context));
        // 启动延迟清理
        scheduleCleanup();
    }

    // 3. 定期清理失效的引用和相关资源
    private void scheduleCleanup() {
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            cleanupDialogFactories();
        }, 500); // 500ms 后执行清理
    }

    private void cleanup() {
        // 清理已失效的 Context 引用
        Iterator<WeakReference<Context>> iterator = trackedContexts.iterator();
        while (iterator.hasNext()) {
            WeakReference<Context> contextRef = iterator.next();
            Context context = contextRef.get();
            if (context == null) {
                // Context 已被回收，清理相关资源
                iterator.remove();
                cleanupForContext(context);
            }
        }
    }

    private void cleanupForContext(Context context) {
        // 清理 factorySet 中相关的 Factory
        factorySet.removeIf(factory -> {
            // 检查是否是 FragmentLayoutInflaterFactory
            if (factory.getClass().getName().contains("FragmentLayoutInflaterFactory")) {
                try {
                    // 通过反射获取 mFragmentManager 字段
                    Field field = factory.getClass().getDeclaredField("mFragmentManager");
                    field.setAccessible(true);
                    Object fragmentManager = field.get(factory);
                    if (fragmentManager != null) {
                        // 获取 FragmentManager 关联的 Context
                        Field hostField = fragmentManager.getClass().getDeclaredField("mHost");
                        hostField.setAccessible(true);
                        Object fragmentHost = hostField.get(fragmentManager);
                        if (fragmentHost != null) {
                            Context fragmentContext = (Context) fragmentHost.getClass()
                                .getMethod("getContext")
                                .invoke(fragmentHost);
                            return fragmentContext == context;
                        }
                    }
                } catch (Exception e) {
                    MyLog.e("SkinLayoutInflaterFactory", "Clean factory failed", e);
                }
            }
            return false;
        });
        
        // 清理 interceptorSet 中相关的拦截器
        interceptorSet.removeIf(interceptor -> {
            if (interceptor instanceof SkinFactory) {
                try {
                    Field contextField = interceptor.getClass().getDeclaredField("context");
                    contextField.setAccessible(true);
                    Context interceptorContext = (Context) contextField.get(interceptor);
                    return interceptorContext == context;
                } catch (Exception e) {
                    MyLog.e("SkinLayoutInflaterFactory", "Clean interceptor failed", e);
                }
            }
            return false;
        });
    }

    private boolean isInterceptorBelongToContext(OnInflaterInterceptor interceptor, Context context) {
        // 根据实际情况实现判断逻辑
        return false; // 需要根据具体实现补充判断逻辑
    }

    private String getCurrentDialogTag() {
        // 获取当前调用栈
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        for (StackTraceElement element : stackTrace) {
            // 检查是否是 DialogFragment 的相关调用
            if (element.getClassName().contains("DialogFragment")) {
                // 生成唯一标识
                return element.getClassName() + "@" + element.getLineNumber();
            }
        }
        return null;
    }

    private void cleanupDialogFactories() {
        // 获取当前活跃的 DialogFragment 标识
        String currentDialogTag = getCurrentDialogTag();
        
        // 清理已经不在活跃状态的 DialogFragment 相关的 Factory
        factorySet.removeIf(factory -> {
            if (isDialogFragmentFactory(factory)) {
                String factoryTag = getDialogTagFromFactory(factory);
                return factoryTag == null || !factoryTag.equals(currentDialogTag);
            }
            return false;
        });
    }
}

