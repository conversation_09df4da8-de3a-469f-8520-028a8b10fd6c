package com.smartcar.easylauncher.infrastructure.amap;


import static com.smartcar.easylauncher.infrastructure.amap.AMapKeyType.ACTION_SEND;
import static com.smartcar.easylauncher.infrastructure.amap.AMapKeyType.ACTION_RECV;
import static com.smartcar.easylauncher.infrastructure.amap.AMapKeyType.GET_LAMP;
import static com.smartcar.easylauncher.infrastructure.amap.AMapKeyType.KEY_TYPE;

import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import com.elvishew.xlog.XLog;
import com.google.gson.Gson;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.app.App;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.core.constants.SettingsConstants;
import com.smartcar.easylauncher.infrastructure.event.scope.location.cody.NavInfoScopeBus;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.data.model.navigation.AMapLaneInfo;
import com.smartcar.easylauncher.data.model.navigation.NavInfo;
import com.smartcar.easylauncher.data.model.system.AppInfo;
import com.smartcar.easylauncher.core.manager.ThemeHelper;
import com.smartcar.easylauncher.shared.utils.AppUtils;
import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * 范庆文 2023/1/2
 * 高德地图帮助类
 */
public class AMapHelper {
    public static final String TAG = "AMapHelper";
    private static final String SOURCE_APP = "智车桌面";

    private static final int KEY_GO_HOME = 10040;
    private static final int KEY_GO_COMPANY = 10070;

    private static final int TYPE_GO_HOME = 1;
    private static final int TYPE_GO_COMPANY = 2;

    private static final int CATEGORY_HOME_OR_COMPANY = 1;

    private static AMapHelper sInstance;

    private static Context mContext = App.getContextInstance();

    private static BroadcastReceiver mReceiver;

    private AMapHelper() {
        mReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                handleIntent(intent);
            }
        };
    }

    public static synchronized AMapHelper getInstance() {
        if (sInstance == null) {
            sInstance = new AMapHelper();
        }
        registerReceiver();
        return sInstance;
    }

    public static void registerReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_SEND);
        filter.addAction(ACTION_RECV);
        mContext.registerReceiver(mReceiver, filter);
    }

    public void unregisterReceiver() {
        mContext.unregisterReceiver(mReceiver);
    }

    private void handleIntent(Intent intent) {
        if (intent == null) {
            return;
        }

        try {
            String action = intent.getAction();
            if (action == null) {
                return;
            }

            if (ACTION_SEND.equals(action)) {
                int keyType = intent.getIntExtra("KEY_TYPE", 1);
                MyLog.v("VVV高德数据      ", keyType + "");
                switch (keyType) {
                    case 10046:
                        //  goHere(intent);
                        break;
                    case 10001:
                        StringBuffer sb = new StringBuffer();
                        //errCode等于0代表定位成功，其他的为定位失败，具体的可以参照官网定位错误码说明
                        sb.append("导航引导信息" + "\n");
                        sb.append("导航类型: " + intent.getIntExtra("TYPE", 1) + "\n");
                        sb.append("当前道路名称: " + intent.getStringExtra("CUR_ROAD_NAME") + "\n");
                        sb.append("下一道路名: " + intent.getStringExtra("NEXT_ROAD_NAME") + "\n");
                        sb.append("距离最近服务区的距离: " + intent.getIntExtra("SAPA_DIST", -1) + "米" + "\n");
                        sb.append("服务区类型: " + intent.getIntExtra("SAPA_TYPE", -1) + "\n");

                        sb.append("最近的电子眼距离: " + intent.getIntExtra("CAMERA_DIST", -1) + "米" + "\n");
                        sb.append("电子眼类型: " + intent.getIntExtra("CAMERA_TYPE", -99) + "\n");
                        // 获取当前提供定位服务的卫星个数
                        sb.append("电子眼限速度: " + intent.getIntExtra("CAMERA_SPEED", -1) + "公里/小时" + "\n");
                        sb.append("下一个将要路过的电子眼编号: " + intent.getIntExtra("CAMERA_INDEX", -1) + "\n");
                        sb.append("导航转向图标: " + intent.getIntExtra("ICON", 1) + "\n");
                        sb.append("导航最新的转向图标: " + intent.getIntExtra("NEW_ICON", 1) + "\n");
                        sb.append("路径剩余距离: " + intent.getIntExtra("ROUTE_REMAIN_DIS", 0) + "米" + "\n");
                        sb.append("路径剩余时间: " + intent.getIntExtra("ROUTE_REMAIN_TIME", 0) + "秒" + "\n");
                        sb.append("当前导航段剩余距离: " + intent.getIntExtra("SEG_REMAIN_DIS", 0) + "米" + "\n");
                        sb.append("当前导航段剩余时间: " + intent.getIntExtra("SEG_REMAIN_TIME", 0) + "秒" + "\n");
                        sb.append("自车方向: " + intent.getIntExtra("CAR_DIRECTION", 0) + "度" + "\n");
                        sb.append("自车纬度: " + intent.getDoubleExtra("CAR_LATITUDE", 0) + "\n");
                        sb.append("自车经度: " + intent.getDoubleExtra("CAR_LONGITUDE", 0) + "\n");
                        sb.append("当前道路速度限制: " + intent.getIntExtra("LIMITED_SPEED", 0) + "公里/小时" + "\n");
                        sb.append("前自车所在Link: " + intent.getIntExtra("CUR_SEG_NUM", 0) + "\n");
                        sb.append("当前位置的前一个形状点号: " + intent.getIntExtra("CUR_POINT_NUM", 0) + "\n");
                        sb.append("环岛出口序号: " + intent.getIntExtra("ROUNG_ABOUT_NUM", 0) + "\n");//从0开始，只有在icon为11和12时有效，其余为无效值0
                        sb.append("环岛出口个数: " + intent.getIntExtra("ROUND_ALL_NUM", 0) + "\n");//从0开始，只有在icon为11和12时有效，其余为无效值0
                        sb.append("路径总距离: " + intent.getIntExtra("ROUTE_ALL_DIS", 0) + "米" + "\n");//从0开始，只有在icon为11和12时有效，其余为无效值0
                        sb.append("路径总时间: " + intent.getIntExtra("ROUTE_ALL_TIME", 0) + "秒" + "\n");//从0开始，只有在icon为11和12时有效，其余为无效值0
                        sb.append("当前车速: " + intent.getIntExtra("CUR_SPEED", 0) + "公里/小时" + "\n");//从0开始，只有在icon为11和12时有效，其余为无效值0
                        sb.append("红绿灯个数: " + intent.getIntExtra("TRAFFIC_LIGHT_NUM", 0) + "\n");//从0开始，只有在icon为11和12时有效，其余为无效值0
                        sb.append("服务区个数: " + intent.getIntExtra("SAPA_NUM", 0) + "\n");//从0开始，只有在icon为11和12时有效，其余为无效值0
                        sb.append("下一个服务区名称: " + intent.getStringExtra("SAPA_NAME") + "\n");//从0开始，只有在icon为11和12时有效，其余为无效值0
                        sb.append("当前道路类型: " + intent.getIntExtra("ROAD_TYPE", 0) + "\n");//从0开始，只有在icon为11和12时有效，其余为无效值0
                        sb.append("下下个路名名称: " + intent.getStringExtra("NEXT_NEXT_ROAD_NAME") + "\n");//从0开始，只有在icon为11和12时有效，其余为无效值0
                        sb.append("下下个路口转向图标: " + intent.getIntExtra("NEXT_NEXT_TURN_ICON", 0) + "\n");
                        sb.append("距离下下个路口剩余距离: " + intent.getIntExtra("NEXT_SEG_REMAIN_DIS", 0) + "\n");//距离下下个路口剩余距离，对应的值为int类型，单位：米
                        sb.append("距离下下个路口剩余时间: " + intent.getIntExtra("NEXT_SEG_REMAIN_TIME", 0) + "\n");//对应的值为int类型，单位：秒
                        sb.append("转换后的路径剩余距离（带单位）: " + intent.getStringExtra("ROUTE_REMAIN_DIS_AUTO") + "\n");//对应的值为int类型，单位：秒
                        sb.append("转换后的路径剩余时间（带单位）: " + intent.getStringExtra("ROUTE_REMAIN_TIME_AUTO") + "\n");//对应的值为int类型，单位：秒
                        sb.append("转换后距离最近服务区的距离: " + intent.getStringExtra("SAPA_DIST_AUTO") + "\n");//转换后距离最近服务区的距离，对应的值为String类型，由距离和单位组成
                        sb.append("转换后当前导航段剩余距离: " + intent.getStringExtra("SEG_REMAIN_DIS_AUTO") + "\n");//转换后当前导航段剩余距离，对应的值为String类型，由距离和单位组成
                        MyLog.v("导航引导信息 ", sb.toString());
                        NavInfoScopeBus.eventBean().post(new NavInfo(intent.getIntExtra("TYPE", 1)
                                , intent.getStringExtra("CUR_ROAD_NAME")
                                , intent.getStringExtra("NEXT_ROAD_NAME")
                                , intent.getIntExtra("SAPA_DIST", -1)
                                , intent.getIntExtra("SAPA_TYPE", -1)
                                , intent.getIntExtra("CAMERA_DIST", -1)
                                , intent.getIntExtra("CAMERA_TYPE", -99)
                                , intent.getIntExtra("CAMERA_SPEED", 0)
                                , intent.getIntExtra("CAMERA_INDEX", -1)
                                , intent.getIntExtra("ICON", 1)
                                , intent.getIntExtra("NEW_ICON", 1)
                                , intent.getIntExtra("ROUTE_REMAIN_DIS", 0)
                                , intent.getIntExtra("ROUTE_REMAIN_TIME", 0)
                                , intent.getIntExtra("SEG_REMAIN_DIS", 0)
                                , intent.getIntExtra("SEG_REMAIN_TIME", 0)
                                , intent.getIntExtra("CAR_DIRECTION", 0)
                                , intent.getDoubleExtra("CAR_LATITUDE", 0)
                                , intent.getDoubleExtra("CAR_LONGITUDE", 0)
                                , intent.getIntExtra("LIMITED_SPEED", 0)
                                , intent.getIntExtra("CUR_SEG_NUM", 0)
                                , intent.getIntExtra("CUR_POINT_NUM", 0)
                                , intent.getIntExtra("ROUNG_ABOUT_NUM", 0)
                                , intent.getIntExtra("ROUND_ALL_NUM", 0)
                                , intent.getIntExtra("ROUTE_ALL_DIS", 0)
                                , intent.getIntExtra("ROUTE_ALL_TIME", 0)
                                , intent.getIntExtra("CUR_SPEED", 0)
                                , intent.getIntExtra("TRAFFIC_LIGHT_NUM", 0)
                                , intent.getIntExtra("SAPA_NUM", 0)
                                , intent.getStringExtra("SAPA_NAME")
                                , intent.getStringExtra("ROUTE_REMAIN_DIS_AUTO")
                                , intent.getStringExtra("SEG_REMAIN_DIS_AUTO")));
                        break;
                    case 10017:
                        //EXTRA_HEADLIGHT_STATE
                        int extraHeadLightState = intent.getIntExtra("EXTRA_HEADLIGHT_STATE", 1);
                        MyLog.v("高德头灯信息      ", extraHeadLightState + "");

                        break;
                    case 10019:
                        int extraState = intent.getIntExtra("EXTRA_STATE", 1);
                        //创建一个运行状态检测的方法
                        runningState(extraState);

                        break;
                    case 13012:
                        String extraS12tate = intent.getStringExtra("EXTRA_DRIVE_WAY");
                        MyLog.v("高德车道信息      ", extraS12tate);
                        AMapLaneInfo aMapLaneInfo = new Gson().fromJson(extraS12tate, AMapLaneInfo.class);
                        NavInfoScopeBus.AmapLaneInfoeventBean().post(aMapLaneInfo);
                        break;
                    default:
                }
                int extraState = intent.getIntExtra("EXTRA_STATE", 1);
                String extraParkData = intent.getStringExtra("EXTRA_ROAD_INFO");
                String efef = intent.getStringExtra("EXTRA_HOME_AND_COMPANY");
                String address = intent.getStringExtra("ADDRESS");
                MyLog.v("VVV高德数据      ", "keyType    " + keyType + "   类型     " + extraState + "   地址 " + address + "   efef   " + efef);
                MyLog.v("高德运行状态      ", extraState + "");
            } else if (ACTION_RECV.equals(action)) {
                int keyType = intent.getIntExtra(KEY_TYPE, 1);
                int extraState = intent.getIntExtra("EXTRA_STATE", 1);

                String extraParkData = intent.getStringExtra("EXTRA_ROAD_INFO");
                String efef = intent.getStringExtra("EXTRA_HOME_AND_COMPANY");
                String address = intent.getStringExtra("ADDRESS");
                MyLog.v("VVV高德数据      ", "keyType    " + keyType + "   类型     " + extraState + "   地址 " + address + "   efef   " + efef);
                MyLog.v("高德运行状态      ", extraState + "");

                switch (keyType) {
                    case GET_LAMP:
                        int headLightState = parseHeadLightState(intent);
                        MyLog.v("大灯事件      ", headLightState + "");
                        XLog.tag(TAG).v("大灯事件      ", headLightState);
                        MToast.makeTextShort("大灯状态：" + headLightState);
                        break;
                    case AMapKeyType.ACC_ON:
                        MyLog.v("acc事件      ", "acc 打开");
                        XLog.tag(TAG).v("acc事件      ", "acc 打开");
                        MToast.makeTextShort("acc 打开");
                        break;
                    case AMapKeyType.ACC_OFF:
                        MyLog.v("acc事件      ", "acc 关闭");
                        XLog.tag(TAG).v("acc事件      ", "acc 关闭");
                        MToast.makeTextShort("acc 关闭");
                        break;
                    default:
                }
            }
        } catch (Exception e) {
            XLog.tag(TAG).e("处理Intent时发生错误: " + e.getMessage());
            MToast.makeTextLong("处理Intent时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

//    public static void moniMap() {
//        Intent intent = new Intent();
//        intent.setPackage("com.autonavi.amapauto");
//        intent.setAction("AUTONAVI_STANDARD_BROADCAST_RECV");
//        intent.putExtra("KEY_TYPE", 10076);
//        intent.putExtra("EXTRA_SLAT", 36.080119d);
//        intent.putExtra("EXTRA_SLON", 120.120945d);
//        intent.putExtra("EXTRA_SNAME", "G22");
//        intent.putExtra("EXTRA_DLAT", 36.159661d);
//        intent.putExtra("EXTRA_DLON", 120.363064d);
//        intent.putExtra("EXTRA_DNAME", "G22");
//        intent.putExtra("KEY_RECYLE_SIMUNAVI", true);
//        mContext.sendBroadcast(intent);
//    }

//    public static void moniMap() {
//        Intent intent = new Intent();
//        intent.setPackage("com.autonavi.amapauto");
//        intent.setAction("AUTONAVI_STANDARD_BROADCAST_RECV");
//        intent.putExtra("KEY_TYPE", 10076);
//        intent.putExtra("EXTRA_SLAT", 37.538276d);
//        intent.putExtra("EXTRA_SLON", 121.255855d);
//        intent.putExtra("EXTRA_SNAME", "G22");
//        intent.putExtra("EXTRA_DLAT", 37.489172d);
//        intent.putExtra("EXTRA_DLON", 121.257894d);
//        intent.putExtra("EXTRA_DNAME", "G22");
//        intent.putExtra("KEY_RECYLE_SIMUNAVI", true);
//        mContext.sendBroadcast(intent);
//    }

    public static void moniMap() {
        AppInfo appInfo = new Gson().fromJson(SettingsManager.getDefaultNavi(), AppInfo.class);
        if (appInfo == null) {
            MToast.makeTextShort("没有找到默认导航，请先设置默认导航");
            return;
        }
        try {
            Intent launchIntent = new Intent();
            launchIntent.setComponent(new ComponentName(appInfo.getPackageName(), "com.autonavi.auto.remote.fill.UsbFillActivity"));
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            mContext.startActivity(launchIntent);
        } catch (Exception e) {
        }
        Intent intent = new Intent();
        intent.setAction("AUTONAVI_STANDARD_BROADCAST_RECV");
        intent.putExtra(KEY_TYPE, 10076);
        intent.putExtra("EXTRA_SLAT", 24.496706);
        intent.putExtra("EXTRA_SLON", 118.182682);
        intent.putExtra("EXTRA_SNAME", "佰翔软件园酒店");
        intent.putExtra("EXTRA_FMIDLAT", 24.492793);
        intent.putExtra("EXTRA_FMIDLON", 118.162947);
        intent.putExtra("EXTRA_FMIDNAME", "蔡塘");
        intent.putExtra("EXTRA_SMIDLAT", 24.483256);
        intent.putExtra("EXTRA_SMIDLON", 118.148825);
        intent.putExtra("EXTRA_SMIDNAME", "太川大楼");
        intent.putExtra("EXTRA_TMIDLAT", 24.47658);
        intent.putExtra("EXTRA_TMIDLON", 118.163917);
        intent.putExtra("EXTRA_TMIDNAME", "世界山庄");
        intent.putExtra("EXTRA_DLAT", 24.453688);
        intent.putExtra("EXTRA_DLON", 118.17581);
        intent.putExtra("EXTRA_DNAME", "椰风寨");
        intent.putExtra("EXTRA_DEV", 0);
        intent.putExtra("EXTRA_M", 0);
        intent.putExtra("KEY_RECYLE_SIMUNAVI", true);
        mContext.sendBroadcast(intent);
    }

    public static void ceshi2() {
        AppInfo appInfo = new Gson().fromJson(SettingsManager.getDefaultNavi(), AppInfo.class);
        if (appInfo == null) {
            MToast.makeTextShort("没有找到默认导航，请先设置默认导航");
            return;
        }
        try {
            Intent launchIntent = new Intent();
            launchIntent.setComponent(new ComponentName(appInfo.getPackageName(), "com.autonavi.auto.remote.fill.UsbFillActivity"));
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            mContext.startActivity(launchIntent);
        } catch (Exception e) {
        }
        Intent intent = new Intent();
        intent.setAction("AUTONAVI_STANDARD_BROADCAST_RECV");
        intent.putExtra(KEY_TYPE, 10076);
        intent.putExtra("EXTRA_SLAT", 38.90497);
        intent.putExtra("EXTRA_SLON", 121.578422);
        intent.putExtra("EXTRA_SNAME", "佰翔软件园酒店");
//        intent.putExtra("EXTRA_FMIDLAT", 24.492793);
//        intent.putExtra("EXTRA_FMIDLON", 118.162947);
//        intent.putExtra("EXTRA_FMIDNAME", "蔡塘");
//        intent.putExtra("EXTRA_SMIDLAT", 24.483256);
//        intent.putExtra("EXTRA_SMIDLON", 118.148825);
//        intent.putExtra("EXTRA_SMIDNAME", "太川大楼");
//        intent.putExtra("EXTRA_TMIDLAT", 24.47658);
//        intent.putExtra("EXTRA_TMIDLON", 118.163917);
//        intent.putExtra("EXTRA_TMIDNAME", "世界山庄");
        intent.putExtra("EXTRA_DLAT", 38.90817);
        intent.putExtra("EXTRA_DLON", 121.597923);
        intent.putExtra("EXTRA_DNAME", "椰风寨");
        intent.putExtra("EXTRA_DEV", 0);
        intent.putExtra("EXTRA_M", 0);
        intent.putExtra("KEY_RECYLE_SIMUNAVI", true);
        mContext.sendBroadcast(intent);
    }

    /**
     * 高德运行状态检测
     */
    private void runningState(int extraState) {
        MyLog.v(" 高德运行状态      ", extraState + "");
        NavInfoScopeBus.eventInt().post(extraState);
        switch (extraState) {
            case 0:
                MyLog.v(" 地图状态发送      ", extraState + "   Application启动即为开始运行");
                if (SettingsManager.getDefaultHome() == SettingsConstants.HOME_MAP_LAYOUT_TYPE && SettingsManager.getNaviShowMode() == 1) {
                    minimizeNav();
                }
                break;
            case 1:
                MyLog.v(" 地图状态发送      ", extraState + "   每次创建地图完成通知");
                break;
            case 2:
                MyLog.v(" 地图状态发送      ", extraState + "   每次创建地图完成通知");
                break;
            case 3:
                MyLog.v(" 地图状态发送      ", extraState + "   进入前台，OnStart函数中调用");
                break;
            case 4:
                MyLog.v(" 地图状态发送      ", extraState + "   进入后台，OnStop函数中调用");
                break;
            case 5:
                MyLog.v(" 地图状态发送      ", extraState + "   开始算路");
                break;
            case 6:
                MyLog.v(" 地图状态发送      ", extraState + "    算路完成，成功");
                break;
            case 7:
                MyLog.v(" 地图状态发送      ", extraState + "    算路完成，失败");
                break;
            case 8:
                if (SettingsManager.getDefaultHome() == SettingsConstants.HOME_MAP_LAYOUT_TYPE && SettingsManager.getNaviShowMode() == 1) {
                    minimizeNav();
                }
                MyLog.v(" 地图状态发送      ", extraState + "   开始导航 ");
                break;
            case 9:
                MyLog.v(" 地图状态发送      ", extraState + "   结束导航 ");
                break;

            case 10:
                MyLog.v(" 地图状态发送      ", extraState + "   开始模拟导航 ");
                if (SettingsManager.getDefaultHome() == SettingsConstants.HOME_MAP_LAYOUT_TYPE && SettingsManager.getNaviShowMode() == 1) {
                    minimizeNav();
                }
                break;

            case 11:
                MyLog.v(" 地图状态发送      ", extraState + " 暂停模拟导航");
                break;

            case 12:
                MyLog.v(" 地图状态发送      ", extraState + "   停止模拟导航 ");
                break;

            case 24:
                MyLog.v(" 地图状态发送      ", extraState + "   进入巡航播报状态 ");
                break;

            case 25:
                MyLog.v(" 地图状态发送      ", extraState + "   退出巡航播报状态 ");
                break;

            case 26:
                MyLog.v(" 地图状态发送      ", extraState + "   收藏夹家信息变更通知 ");
                break;
            case 27:
                MyLog.v(" 地图状态发送      ", extraState + "   收藏夹公司信息变更通知 ");

                break;
            case 37:
                MyLog.v(" 地图状态发送      ", extraState + "    昼夜模式白天状态通知 ");
                if (SettingsManager.getThemeMode() == 3) {
                    ThemeHelper.setDaySkin();
                }
                break;
            case 38:
                MyLog.v(" 地图状态发送      ", extraState + "   昼夜模式黑夜状态通知 ");
                if (SettingsManager.getThemeMode() == 3) {
                    ThemeHelper.setNightSkin();
                }
                break;

            case 39:
                MyLog.v(" 地图状态发送      ", extraState + "   到达目的地通知 ");

                break;

            case 40:
                MyLog.v(" 地图状态发送      ", extraState + "    心跳通知");

                break;
            case 45:
                MyLog.v(" 地图状态发送      ", extraState + "   完全运行结束，退出程序 ");

                break;
            case 46:
                MyLog.v(" 地图状态发送      ", extraState + "   主界面-包括主图巡航界面和警告界面 ");

                break;
            case 47:
                MyLog.v(" 地图状态发送      ", extraState + "   子界面-除了包括主图巡航界面和警告界面的其他界面 ");

                break;
            case 48:
                MyLog.v(" 地图状态发送      ", extraState + "   GPS已定位 ");

                break;
            case 49:
                MyLog.v(" 地图状态发送      ", extraState + "   主图activity onCreate状态 ");
//                if (SettingsManager.getNaviCardDisplayMode() == 1) {
//                    minimizeNav();
//                }
                break;
            case 50:
                MyLog.v(" 地图状态发送      ", extraState + "   activity获得焦点 ");
                break;
            case 51:
                MyLog.v(" 地图状态发送      ", extraState + "   activity失去焦点 ");
                break;
            case 60:
                openAauto();
                MyLog.v(" 地图状态发送      ", extraState + "   进入【敬告界面】 ");
                break;
            case 116:
                MyLog.v(" 地图状态发送      ", extraState + "   地图图层加载完成通知（第一帧） ");
                break;
            default:
        }
    }

    /**
     * 高德返回的回家信息
     *
     * @param intent
     */
    private void goHere(Intent intent) {
        //1 =回家  2=去公司
        if (intent.getIntExtra("CATEGORY", 99) == 1) {//查询回家信息
            if (intent.getStringExtra("ADDRESS") != null && !intent.getStringExtra("ADDRESS").isEmpty()) {
                openAauto(10040, 0, 0);
            } else {
                MToast.makeTextShort("没有找到回家地址，请先设置回家地址");
                openCollection(10070, 0);
            }

        } else if (intent.getIntExtra("CATEGORY", 99) == 2) {//查询公司信息
            if (intent.getStringExtra("ADDRESS") != null && !intent.getStringExtra("ADDRESS").isEmpty()) {
                openAauto(10040, 1, 1);
            } else {
                MToast.makeTextShort("没有找到公司地址，请先设置公司地址");
                openCollection(10070, 1);
            }
        } else {
            MyLog.v("广播", "未知");
        }
    }

    /**
     * 打开导航设置页面
     *
     * @param keyType
     * @param extraType 0 打开回家设置  1= 去公司设置
     */
    private void openCollection(int keyType, int extraType) {
        Intent intent3 = new Intent();
        intent3.setAction(ACTION_RECV);
        intent3.putExtra("KEY_TYPE", keyType);
        intent3.putExtra("EXTRA_TYPE", extraType);
        try {
            mContext.sendBroadcast(intent3);
        } catch (SecurityException e) {
            // 处理异常，例如记录日志、显示错误消息等
            e.printStackTrace();
            // 可能需要在这里通知用户或采取其他恢复措施
        }
    }

    /**
     * 打开高德
     */
    public static void openAauto() {
        Intent intent = new Intent();
        intent.setAction(ACTION_RECV);
        intent.putExtra("KEY_TYPE", 10034);
        intent.putExtra("SOURCE_APP", "Third App");
        try {
            mContext.sendBroadcast(intent);
        } catch (SecurityException e) {
            // 处理异常，例如记录日志、显示错误消息等
            e.printStackTrace();
            // 可能需要在这里通知用户或采取其他恢复措施
        }
    }

    /**
     * 最小化导航
     */
    private void minimizeNav() {
        Intent intent = new Intent();
        intent.setAction(ACTION_RECV);
        intent.putExtra(KEY_TYPE, AMapKeyType.MINI_NAV);
        try {
            mContext.sendBroadcast(intent);
        } catch (SecurityException e) {
            // 处理异常，例如记录日志、显示错误消息等
            e.printStackTrace();
            // 可能需要在这里通知用户或采取其他恢复措施
        }
    }

    /**
     *
     */
    public static void openAauto(int keyType, int dest, int isStartNani) {
        AppInfo appInfo = new Gson().fromJson(SettingsManager.getDefaultNavi(), AppInfo.class);
        if (appInfo == null) {
            MToast.makeTextShort("没有找到默认导航，请先设置默认导航");
            return;
        }
        try {
            Intent launchIntent = new Intent();
            launchIntent.setComponent(new ComponentName(appInfo.getPackageName(), "com.autonavi.auto.remote.fill.UsbFillActivity"));
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            mContext.startActivity(launchIntent);
        } catch (Exception e) {
            // 捕获异常，防止崩溃
            MToast.makeTextShort("启动失败，你安装的高德可能不适配");
            e.printStackTrace();
        }

        try {
            Intent intent1 = new Intent();
            intent1.setAction("AUTONAVI_STANDARD_BROADCAST_RECV");
            intent1.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
            intent1.setClassName(appInfo.getPackageName(), "com.autonavi.amapauto.adapter.internal.AmapAutoBroadcastReceiver");// class要用原版高德的
            intent1.putExtra(KEY_TYPE, keyType);
            intent1.putExtra("DEST", dest);//必填）0 回家；1 回公司(int)
            intent1.putExtra("IS_START_NAVI", isStartNani);
            intent1.putExtra("SOURCE_APP", "智车桌面");
            intent1.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
            mContext.sendBroadcast(intent1);
        } catch (SecurityException e) {
            // 处理异常，例如记录日志、显示错误消息等
            MToast.makeTextShort("打开错误，你安装的高德可能不适配");
            e.printStackTrace();
            // 可能需要在这里通知用户或采取其他恢复措施
        }
    }

    /**
     * 快捷导航 回家或者去公司
     *
     * @param defaultNav 导航包名
     * @param keyType    10040 导航到家或者公司
     * @param extraType
     */
    public static void quickPlanning(String defaultNav, int keyType, int extraType) {
        //判断是否有导航
        AppInfo appInfo = new Gson().fromJson(defaultNav, AppInfo.class);
        if (!appInfo.getPackageName().equals(Const.GAODE_NAV) &&
                !appInfo.getPackageName().equals(Const.GONGCUN_NAV)) {
            MToast.makeTextShort("快捷导航仅适配高德车机版和高德共存版，请重新设置默认导航");
            return;
        }
        if (AppUtils.checkPackInfo(mContext, appInfo.getPackageName())) {
            Intent launchIntent = new Intent();
            launchIntent.setComponent(new ComponentName(appInfo.getPackageName(), "com.autonavi.auto.remote.fill.UsbFillActivity"));
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            mContext.startActivity(launchIntent);

            Intent intent = new Intent();
            intent.setAction(ACTION_RECV);
            intent.setClassName(appInfo.getPackageName(), "com.autonavi.amapauto.adapter.internal.AmapAutoBroadcastReceiver");
            intent.putExtra(KEY_TYPE, keyType);
            intent.putExtra("EXTRA_TYPE", extraType);
            mContext.sendBroadcast(intent);
        } else {
            MToast.makeTextShort("找不到应用，长按卡片进行导航设置");
        }
    }

    /**
     * 查询导航状态
     * 0：前后台状态查询
     * 1：导航状态查询
     * 2：导航中路线信息查询（仅2.1版本支持）
     * 前后台/导航状态查询通过10019协议返回结果
     * 路线信息查询，通过10056协议返回结果
     */
    public static void queryNaviState(int extraType) {
        Intent intent = new Intent();
        intent.setAction(ACTION_RECV);
        intent.putExtra(KEY_TYPE, AMapKeyType.NAV_STATUS);
        intent.putExtra("EXTRA_TYPE", extraType);
        mContext.sendBroadcast(intent);
    }

    /**
     * 设置高德 昼夜模式类型
     *
     * @param state 0：自动模式
     *              1：白天模式
     *              2：黑夜模式
     */
    public static void setDayNightMode(int state) {
        Intent intent = new Intent();
        intent.setAction(ACTION_RECV);
        intent.putExtra(KEY_TYPE, AMapKeyType.DAY_NIGHT_MODE);
        intent.putExtra("EXTRA_DAY_NIGHT_MODE", state);
        mContext.sendBroadcast(intent);
    }

    /**
     * 请求获取大灯状态
     */
    public static void getHeadLightState() {
        Intent intent = new Intent();
        intent.setAction(ACTION_SEND);
        intent.putExtra(KEY_TYPE, GET_LAMP);
        mContext.sendBroadcast(intent);
    }

    /**
     * 系统通知大灯状态
     * KEY_TYPE 10017
     * EXTRA_HEADLIGHT_STATE 0：关闭   1：开启
     */
    public static void setHeadLightState(int state) {
        Intent intent = new Intent();
        intent.setAction(ACTION_RECV);
        intent.putExtra(KEY_TYPE, GET_LAMP);
        intent.putExtra("EXTRA_HEADLIGHT_STATE", state);
        mContext.sendBroadcast(intent);
    }

    /**
     * 解析大灯状态
     *
     * @param intent
     * @return 0：开启  1：关闭
     */
    public static int parseHeadLightState(Intent intent) {
        int extraHeadLightState = intent.getIntExtra("EXTRA_HEADLIGHT_STATE", -1);
        switch (extraHeadLightState) {
            case 0:
                return 1;
            case 1:
                return 0;
            default:
                break;
        }
        return -1;
    }


    /**
     * acc on 通知
     * 点⽕后，第三⽅通过该接⼝发送acc on 消息
     * KEY_TYPE 10073
     */
    public static void sendAccOnMsg() {
        Intent intent = new Intent();
        intent.setAction(ACTION_RECV);
        intent.putExtra(KEY_TYPE, AMapKeyType.ACC_ON);
        mContext.sendBroadcast(intent);
    }

    /**
     * acc off 通知
     * 第三⽅发送acc off的信息给auto，auto接到通知后在规定时间内进⾏相关数据的保存
     */
    public static void sendAccOffMsg() {
        Intent intent = new Intent();
        intent.setAction(ACTION_RECV);
        intent.putExtra(KEY_TYPE, AMapKeyType.ACC_OFF);
        mContext.sendBroadcast(intent);
    }


}