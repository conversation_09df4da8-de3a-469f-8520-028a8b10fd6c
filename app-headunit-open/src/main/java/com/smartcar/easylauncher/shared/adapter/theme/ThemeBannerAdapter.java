package com.smartcar.easylauncher.shared.adapter.theme;

import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.data.model.theme.ThemeBannerModel;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.zhpan.bannerview.BaseBannerAdapter;
import com.zhpan.bannerview.BaseViewHolder;

/**
 * 主题轮播图适配器
 * 用于主题详情页面的轮播图展示
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
public class ThemeBannerAdapter extends BaseBannerAdapter<ThemeBannerModel.BannerItem> {
    
    private static final String TAG = "ThemeBannerAdapter";

    @Override
    protected void bindData(BaseViewHolder<ThemeBannerModel.BannerItem> holder, 
                          ThemeBannerModel.BannerItem item, int position, int pageSize) {
        
        ImageView bannerImage = holder.findViewById(R.id.banner_image);

        // 设置图片 - 支持默认主题资源
        if (item.getImageUrl() != null && !item.getImageUrl().isEmpty()) {
            String imageUrl = item.getImageUrl();

            // 检查是否为默认主题资源
            if (imageUrl.startsWith("resource://")) {
                // 加载默认主题资源图片
                loadDefaultThemeResource(bannerImage, imageUrl);
            } else {
                // 加载网络图片
                loadNetworkImage(bannerImage, imageUrl);
            }

        }


    }

    @Override
    public int getLayoutId(int viewType) {
        return R.layout.item_theme_banner;
    }

    /**
     * 加载默认主题资源图片
     */
    private void loadDefaultThemeResource(ImageView imageView, String resourceUrl) {
        try {
            // 提取资源ID
            String resourceIdStr = resourceUrl.replace("resource://", "");
            int resourceId = Integer.parseInt(resourceIdStr);

            MyLog.d(TAG, "加载默认主题资源图片: " + resourceId);

            // 使用Glide加载资源图片
            RequestOptions options = new RequestOptions()
                    .transform(new RoundedCorners(13)) // 圆角半径
                    .fitCenter(); // 保持比例，完整显示图片

            Glide.with(imageView.getContext())
                    .load(resourceId)
                    .apply(options)
                    .into(imageView);

        } catch (Exception e) {
            MyLog.e(TAG, "加载默认主题资源图片失败", e);
        }
    }

    /**
     * 加载网络图片
     */
    private void loadNetworkImage(ImageView imageView, String imageUrl) {
        // 如果是相对路径，添加基础URL
        if (!imageUrl.startsWith("http")) {
            imageUrl = Const.DEV_BASEURL + imageUrl;
        }

        MyLog.d(TAG, "加载主题轮播图: " + imageUrl);

        // 使用Glide加载图片，保持16:9比例不裁剪
        RequestOptions options = new RequestOptions()
                .transform(new RoundedCorners(13)) // 圆角半径
                .fitCenter(); // 保持比例，完整显示图片

        Glide.with(imageView.getContext())
                .load(imageUrl)
                .apply(options)
                .thumbnail(0.1f) // 缩略图加载，提升用户体验
                .into(imageView);
    }

}
