package com.smartcar.easylauncher.shared.widget.button;

/**
 * 快速点击检查器类，用于判断用户的点击操作是否为快速连续点击。
 * Created by Administrator on 2016/10/25.
 */
public class QuickClickChecker {
    /**
     * 定义快速点击的时间阈值（单位：毫秒）。
     * 如果两次点击的时间间隔小于等于该阈值，则认为是快速点击。
     */
    private int threshold;

    /**
     * 记录上一次点击的时间戳（单位：毫秒）。
     */
    private long lastClickTime;

    /**
     * 构造函数，初始化快速点击检查器。
     *
     * @param threshold 快速点击的时间阈值（单位：毫秒）
     */
    public QuickClickChecker(int threshold) {
        this.threshold = threshold;
        // 初始化上一次点击时间为0，表示尚未有点击操作
        this.lastClickTime = 0;
    }

    /**
     * 判断当前点击是否为快速点击。
     *
     * @return 如果当前点击与上一次点击的时间间隔小于等于阈值，则返回true，表示快速点击；否则返回false。
     */
    public boolean isQuick() {
        // 获取当前时间戳
        long currentTime = System.currentTimeMillis();
        // 计算当前点击与上一次点击的时间间隔
        long timeInterval = currentTime - lastClickTime;

        // 判断时间间隔是否小于等于阈值
        boolean isQuick = timeInterval <= threshold;

        // 更新上一次点击时间为当前时间
        lastClickTime = currentTime;

        // 返回判断结果
        return isQuick;
    }
}
