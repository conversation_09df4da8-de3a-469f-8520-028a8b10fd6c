package com.smartcar.easylauncher.shared.widget.layout.map;

import androidx.constraintlayout.widget.ConstraintSet;

/**
 * 首页地图布局接口
 * 定义了所有布局实现类需要实现的方法
 */
public interface IHomeMapLayout {
    
    /**
     * 应用布局配置
     * 实现此方法来配置特定的布局
     * 
     * @param constraintSet 约束布局配置对象，用于设置和应用约束
     */
    void applyLayout(ConstraintSet constraintSet);
    
    /**
     * 获取布局类型标识符
     * 
     * @return 布局类型的字符串标识符
     */
    String getLayoutType();
} 