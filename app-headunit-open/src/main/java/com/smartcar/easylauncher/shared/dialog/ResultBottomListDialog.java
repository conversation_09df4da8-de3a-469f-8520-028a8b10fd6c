package com.smartcar.easylauncher.shared.dialog;

import android.app.Activity;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;

import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bumptech.glide.Glide;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.TaskResultConst;
import com.smartcar.easylauncher.data.model.task.TaskOptionModel;
import com.timmy.tdialog.TDialog;
import com.timmy.tdialog.base.BindViewHolder;
import com.timmy.tdialog.base.TBaseAdapter;
import com.timmy.tdialog.list.TListDialog;
import com.timmy.tdialog.listener.OnBindViewListener;
import com.timmy.tdialog.listener.OnViewClickListener;

import java.util.List;

public class ResultBottomListDialog {

    private Context context;
    private List<TaskOptionModel> data;
    private OnItemSelectedListener listener;

    public ResultBottomListDialog(Context context) {
        this.context = context;
    }

    public ResultBottomListDialog setData(List<TaskOptionModel> data) {
        this.data = data;
        return this;
    }

    public ResultBottomListDialog setOnItemSelectedListener(OnItemSelectedListener listener) {
        this.listener = listener;
        return this;
    }

    public void show() {
        new TListDialog.Builder(((FragmentActivity) context).getSupportFragmentManager())
                .setListLayoutRes(R.layout.dialog_share_recycler, LinearLayoutManager.HORIZONTAL)
                .setLayoutManager(new GridLayoutManager(context, 4))
                .setScreenHeightAspect((Activity) context, 1f)
                .setScreenWidthAspect((Activity) context, 1f)
                .setGravity(Gravity.BOTTOM)
                .addOnClickListener(R.id.iv_grzx)
                .setOnBindViewListener(new OnBindViewListener() {
                    @Override
                    public void bindView(BindViewHolder viewHolder) {
                        viewHolder.setText(R.id.tv_title, "就执行");
                    }
                })
                .setOnViewClickListener(new OnViewClickListener() {
                    @Override
                    public void onViewClick(BindViewHolder viewHolder, View view, TDialog tDialog) {
                        switch (view.getId()) {
                            case R.id.iv_grzx:
                                tDialog.dismiss();
                                break;
                            default:
                        }
                    }
                })
                .setAdapter(new TBaseAdapter<TaskOptionModel>(R.layout.item_simple_text, data) {
                    @Override
                    protected void onBind(BindViewHolder holder, int position, TaskOptionModel s) {
                        holder.setText(R.id.tv_name, s.getName());
                        switch (s.getId()) {
                            case TaskResultConst.RESULT_ID_DELAY:
                                //   holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_yanshi);
                                Glide.with(context).load(R.drawable.ic_task_yanshi).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskResultConst.RESULT_ID_WIFI:
                                //   holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_wifi_switch);
                                Glide.with(context).load(R.drawable.ic_task_wifi_switch).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskResultConst.RESULT_ID_BLUETOOTH:
                                //   holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_ble_switch);
                                Glide.with(context).load(R.drawable.ic_task_ble_switch).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskResultConst.RESULT_ID_VOLUME:
                                //    holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_shengyin);
                                Glide.with(context).load(R.drawable.ic_task_shengyin).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskResultConst.RESULT_ID_BRIGHTNESS:
                                //   holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_liangdu);
                                Glide.with(context).load(R.drawable.ic_task_liangdu).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskResultConst.RESULT_ID_THEME:
                                //    holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_zhuti);
                                Glide.with(context).load(R.drawable.ic_task_zhuti).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskResultConst.RESULT_ID_APP:
                                //     holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_app);
                                Glide.with(context).load(R.drawable.ic_task_app).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskResultConst.RESULT_ID_NAV:
                                //    holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_kuaijiedaohagn);
                                Glide.with(context).load(R.drawable.ic_task_kuaijiedaohagn).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskResultConst.RESULT_ID_MUSIC:
                               // holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_yinyue);
                                Glide.with(context).load(R.drawable.ic_task_yinyue).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskResultConst.RESULT_ID_TIP:
                               // holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_yinyue);
                                Glide.with(context).load(R.drawable.ic_task_tip).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            default:
                        }
                    }
                })
                .setOnAdapterItemClickListener(new TBaseAdapter.OnAdapterItemClickListener<TaskOptionModel>() {
                    @Override
                    public void onItemClick(BindViewHolder holder, int position, TaskOptionModel s, TDialog tDialog) {
                        if (listener != null) {
                            listener.onItemSelected(position, s);
                        }
                        tDialog.dismiss();
                    }
                })
                .create()
                .show();
    }


    public interface OnItemSelectedListener {
        void onItemSelected(int position, TaskOptionModel item);
    }
}