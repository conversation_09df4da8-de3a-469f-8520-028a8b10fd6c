package com.smartcar.easylauncher.shared.adapter.common;


import android.view.View;

import androidx.annotation.NonNull;

import com.chad.library.adapter.base.BaseNodeAdapter;
import com.chad.library.adapter.base.entity.node.BaseNode;
import com.smartcar.easylauncher.shared.adapter.common.provider.RootNodeProvider;
import com.smartcar.easylauncher.shared.adapter.common.provider.SecondNodeProvider;
import com.smartcar.easylauncher.data.model.common.ItemNode;
import com.smartcar.easylauncher.data.model.common.RootNode;


import org.jetbrains.annotations.NotNull;

import java.util.List;

public class NodeSectionAdapter extends BaseNodeAdapter {

    public NodeSectionAdapter() {
        super();
        addFullSpanNodeProvider(new RootNodeProvider());
        addNodeProvider(new SecondNodeProvider());
    }

    @Override
    protected int getItemType(@NotNull List<? extends BaseNode> data, int position) {
        BaseNode node = data.get(position);
        if (node instanceof RootNode) {
            return 0;
        } else if (node instanceof ItemNode) {
            return 1;
        }
        return -1;
    }

    @Override
    protected void setOnItemChildClick(@NonNull View v, int position) {
        super.setOnItemChildClick(v, position);
    }
}
