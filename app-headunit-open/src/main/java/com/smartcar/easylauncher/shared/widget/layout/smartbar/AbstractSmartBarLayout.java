package com.smartcar.easylauncher.shared.widget.layout.smartbar;

import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.FragmentActivity;

import com.smartcar.easylauncher.core.constants.SettingsConstants;
import com.smartcar.easylauncher.databinding.FragmentSmartBarBinding;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.infrastructure.skin.SkinManager;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;
import com.smartcar.easylauncher.core.constants.BasicConfiguration;
import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * 智能导航栏布局抽象基类
 * 提供所有SmartBar布局实现类共享的基础功能
 * <p>
 * 🚀 重构：增强基类功能，让Layout类负责所有UI操作
 * 参考HomeMapLayoutManager的AbstractHomeMapLayout设计
 *
 * <AUTHOR>
 * @since 2024-12-18
 * @version 2.0 重构版 - 职责分离
 */
public abstract class AbstractSmartBarLayout implements ISmartBarLayout {

    private static final String TAG = "AbstractSmartBarLayout";

    // UI组件引用
    protected final FragmentSmartBarBinding binding;
    protected final FragmentActivity activity;

    // 缓存的尺寸值，避免重复计算
    protected final int iconSize;
    protected final int margin5;
    protected final int margin10;
    protected final int margin15;

    /**
     * 构造函数
     *
     * @param binding  数据绑定对象
     * @param activity Fragment所属的Activity
     */
    public AbstractSmartBarLayout(FragmentSmartBarBinding binding, FragmentActivity activity) {
        this.binding = binding;
        this.activity = activity;

        // 预计算常用尺寸，提升性能
        this.iconSize = DensityUtils.dp2px(activity, BasicConfiguration.ICON_SIZE);
        this.margin5 = DensityUtils.dp2px(activity, 5f);
        this.margin10 = DensityUtils.dp2px(activity, 10f);
        this.margin15 = DensityUtils.dp2px(activity, 15f);
    }

    /**
     * 设置容器布局参数（底部布局）
     */
    protected void setupContainerLayoutParams() {
        try {
            // 🚀 修复：使用安全的布局参数设置方法
            setViewLayoutParamsSafely(binding.fragmentSmartBar,
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);

            setViewLayoutParamsSafely(binding.clSmartbar,
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);

            MyLog.d(TAG, "容器布局参数设置完成");
        } catch (Exception e) {
            MyLog.e(TAG, "设置容器布局参数失败", e);
        }
    }

    /**
     * 设置全屏布局参数（侧边布局）
     */
    protected void setupFullScreenLayoutParams() {
        try {
            // 🚀 修复：使用安全的布局参数设置方法
            setViewLayoutParamsSafely(binding.fragmentSmartBar,
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);

            setViewLayoutParamsSafely(binding.clSmartbar,
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);

            MyLog.d(TAG, "全屏布局参数设置完成");
        } catch (Exception e) {
            MyLog.e(TAG, "设置全屏布局参数失败", e);
        }
    }

    /**
     * 🚀 新增：安全地设置View的布局参数
     * 根据父容器类型创建正确的LayoutParams
     */
    private void setViewLayoutParamsSafely(View view, int width, int height) {
        try {
            ViewGroup.LayoutParams currentParams = view.getLayoutParams();
            if (currentParams != null) {
                // 保持现有的LayoutParams类型，只修改尺寸
                currentParams.width = width;
                currentParams.height = height;
                view.setLayoutParams(currentParams);
            } else {
                // 🚀 修复：根据父容器类型创建合适的LayoutParams
                ViewGroup parent = (ViewGroup) view.getParent();
                ViewGroup.LayoutParams newParams = createLayoutParamsForParent(parent, width, height);
                view.setLayoutParams(newParams);
            }
        } catch (Exception e) {
            MyLog.e(TAG, "设置View布局参数失败: " + view.getClass().getSimpleName(), e);
        }
    }

    /**
     * 🚀 新增：根据父容器类型创建合适的LayoutParams
     */
    private ViewGroup.LayoutParams createLayoutParamsForParent(ViewGroup parent, int width, int height) {
        if (parent == null) {
            return new ViewGroup.LayoutParams(width, height);
        }

        // 根据父容器类型创建对应的LayoutParams
        if (parent instanceof androidx.constraintlayout.widget.ConstraintLayout) {
            return new androidx.constraintlayout.widget.ConstraintLayout.LayoutParams(width, height);
        } else if (parent instanceof android.widget.FrameLayout) {
            return new android.widget.FrameLayout.LayoutParams(width, height);
        } else if (parent instanceof android.widget.LinearLayout) {
            return new android.widget.LinearLayout.LayoutParams(width, height);
        } else if (parent instanceof android.widget.RelativeLayout) {
            return new android.widget.RelativeLayout.LayoutParams(width, height);
        } else {
            // 默认使用MarginLayoutParams，大多数ViewGroup都支持
            return new ViewGroup.MarginLayoutParams(width, height);
        }
    }

    // 🚀 已删除 configureBottomMoreButton() 和 configureBottomAppIcons() 方法
    // 这些方法现在在具体的布局类中实现，参考SmartBarScene.java的原有逻辑

    // 🚀 已删除 configureAppIcon() 方法
    // 该方法的功能现在在具体的布局类中直接实现

    // 🚀 重构：已移除 configureSideAppIcons 方法
    // 侧边栏的应用配置现在统一在 SmartBarSideLayout.configureSideBarApps() 中处理

    /**
     * 隐藏音乐控制组件
     * 🚀 修复：直接使用binding，不使用findViewById
     */
    protected void hideMusicControl() {
        try {
            binding.ilDibu.setVisibility(View.GONE);
            MyLog.d(TAG, "音乐控制组件已隐藏");
        } catch (Exception e) {
            MyLog.e(TAG, "隐藏音乐控制组件失败", e);
        }
    }

    /**
     * 显示音乐控制组件
     * 🚀 修复：直接使用binding，不使用findViewById
     */
    protected void showMusicControl() {
        try {
            binding.ilDibu.setVisibility(View.VISIBLE);
            MyLog.d(TAG, "音乐控制组件已显示");
        } catch (Exception e) {
            MyLog.e(TAG, "显示音乐控制组件失败", e);
        }
    }

    /**
     * 检查是否应该显示音乐控制
     */
    protected boolean shouldShowMusicControl() {
        return SettingsManager.getNavigationBarMusicShow() && supportsMusicControl();
    }

    /**
     * 🚀 新增：应用文字可见性设置
     * 每个具体的Layout类必须实现自己的文字显示逻辑
     *
     * @param wordlessMode true表示无字模式（隐藏文字），false表示显示文字
     */
    public abstract void applyTextVisibility(boolean wordlessMode);

    /**
     * 🚀 新增：应用布局特定的文字可见性（可选重写）
     * 子类可以重写此方法来实现更复杂的文字显示逻辑
     *
     * @param baseVisibility 基础可见性（View.VISIBLE 或 View.GONE）
     */
    protected void applyLayoutSpecificTextVisibility(int baseVisibility) {
        // 默认实现：简单设置所有文字的可见性
        setAllTextsVisibility(baseVisibility);
    }

    /**
     * 🚀 新增：设置所有文字的可见性（通用方法）
     *
     * @param visibility View.VISIBLE 或 View.GONE
     */
    protected void setAllTextsVisibility(int visibility) {
        try {
            // 🚀 修复：确保UI操作在主线程执行
            if (android.os.Looper.myLooper() != android.os.Looper.getMainLooper()) {
                MyLog.w(TAG, "⚠️ 检测到非主线程UI操作文字，切换到主线程执行");
                activity.runOnUiThread(() -> setAllTextsVisibility(visibility));
                return;
            }

            binding.tvMore.setVisibility(visibility);
            binding.tvOne.setVisibility(visibility);
            binding.tvTwo.setVisibility(visibility);
            binding.tvThree.setVisibility(visibility);
            binding.tvFour.setVisibility(visibility);
            binding.tvFive.setVisibility(visibility);
            binding.tvSix.setVisibility(visibility);

            MyLog.d(TAG, "所有文字可见性设置为: " + (visibility == View.VISIBLE ? "显示" : "隐藏"));
        } catch (Exception e) {
            MyLog.e(TAG, "设置所有文字可见性失败", e);
        }
    }

    /**
     * 🚀 重构：实现显示设置接口方法
     * Layout类负责处理所有UI相关的显示设置
     *
     * @param wordlessState    无字模式状态
     * @param transparentState 透明模式状态
     * @param musicControlShow 音乐控制显示状态
     */
    @Override
    public void applyDisplaySettings(boolean wordlessState, boolean transparentState, boolean musicControlShow) {
        try {
            MyLog.d(TAG, "🚀 [重构] Layout类开始处理显示设置 - 无字模式: " + wordlessState +
                    ", 透明模式: " + transparentState +
                    ", 音乐控制: " + musicControlShow);

            // 1. 处理音乐控制显示
            setupMusicControlVisibility(musicControlShow);

            // 2. 处理背景模式
            applyBackgroundMode(transparentState);
            // 3. 处理文字可见性
            applyTextVisibility(wordlessState);

            MyLog.d(TAG, "🚀 [重构] Layout类显示设置处理完成");
        } catch (Exception e) {
            MyLog.e(TAG, "Layout类处理显示设置失败", e);
        }
    }

    /**
     * 🚀 重构：设置音乐控制组件的可见性
     * Layout类负责根据自身特性和用户设置决定音乐控制的显示
     *
     * @param musicControlShow 用户设置的音乐控制显示状态
     */
    protected void setupMusicControlVisibility(boolean musicControlShow) {
        try {
            // 🚀 修复：确保UI操作在主线程执行
            if (android.os.Looper.myLooper() != android.os.Looper.getMainLooper()) {
                MyLog.w(TAG, "⚠️ 检测到非主线程UI操作，切换到主线程执行");
                activity.runOnUiThread(() -> setupMusicControlVisibility(musicControlShow));
                return;
            }

            if (supportsMusicControl() && musicControlShow) {
                // 支持音乐控制且用户设置显示
                binding.ilDibu.setVisibility(View.VISIBLE);
                MyLog.d(TAG, "🚀 [重构] 音乐控制组件设置为可见（支持音乐控制且用户设置显示）");
            } else {
                // 不支持音乐控制或用户设置隐藏
                binding.ilDibu.setVisibility(View.GONE);
                String reason = !supportsMusicControl() ? "不支持音乐控制的布局" : "用户设置隐藏";
                MyLog.d(TAG, "🚀 [重构] 音乐控制组件设置为隐藏（" + reason + "）");
            }
        } catch (Exception e) {
            MyLog.e(TAG, "设置音乐控制组件可见性失败", e);
        }
    }

    /**
     * 🚀 重构：应用背景模式的默认实现
     * 子类可以重写此方法来实现特定的背景逻辑
     *
     * @param transparentState 透明模式状态
     */
    @Override
    public void applyBackgroundMode(boolean transparentState) {
        try {
            MyLog.d(TAG, "🚀 [重构] 开始应用背景模式: " + transparentState);

            if (transparentState) {
                // 透明模式：使用透明背景
                setTransparentBackground();
            } else {
                // 非透明模式：根据布局类型设置背景
                setLayoutSpecificBackground();
            }

            MyLog.d(TAG, "🚀 [重构] 背景模式应用完成");
        } catch (Exception e) {
            MyLog.e(TAG, "应用背景模式失败", e);
        }
    }

    /**
     * 🚀 重构：设置透明背景
     */
    protected void setTransparentBackground() {
        try {
            // 🚀 修复：确保UI操作在主线程执行
            if (android.os.Looper.myLooper() != android.os.Looper.getMainLooper()) {
                MyLog.w(TAG, "⚠️ 检测到非主线程UI操作，切换到主线程执行");
                activity.runOnUiThread(this::setTransparentBackground);
                return;
            }

            binding.clSmartbar.setBackground(
                    SkinManager.getInstance()
                            .getDrawable(com.smartcar.easylauncher.R.color.transparent)
            );
            MyLog.d(TAG, "🚀 [重构] 设置为透明背景");
        } catch (Exception e) {
            MyLog.e(TAG, "设置透明背景失败", e);
        }
    }

    /**
     * 🚀 重构：设置布局特定的背景（子类可重写）
     * 默认实现使用圆角背景
     */
    protected void setLayoutSpecificBackground() {
        try {
            // 🚀 修复：确保UI操作在主线程执行
            if (android.os.Looper.myLooper() != android.os.Looper.getMainLooper()) {
                MyLog.w(TAG, "⚠️ 检测到非主线程UI操作，切换到主线程执行");
                activity.runOnUiThread(this::setLayoutSpecificBackground);
                return;
            }

            String mapLayout = SettingsManager.getMapLayout();
            MyLog.d(TAG, "🚀 [重构] 底部布局设置背景，当前地图布局: " + mapLayout);

            switch (mapLayout) {
                case SettingsConstants.DEFAULT_MAP_LAYOUT_1:
                case SettingsConstants.DEFAULT_MAP_LAYOUT_2:
                case SettingsConstants.DEFAULT_MAP_LAYOUT_5:
                case SettingsConstants.DEFAULT_MAP_LAYOUT_6:
                    // 水平布局：尝试使用专用的水平背景
                    setHorizontalBackground();
                    MyLog.d(TAG, "🚀 [重构] 底部布局使用水平背景");
                    break;

                case SettingsConstants.DEFAULT_MAP_LAYOUT_3:
                case SettingsConstants.DEFAULT_MAP_LAYOUT_4:
                    // 垂直布局：尝试使用专用的垂直背景
                    setVerticalBackground();
                    MyLog.d(TAG, "🚀 [重构] 底部布局使用垂直背景");
                    break;

                default:
                    // 默认情况：使用圆角背景
                    // 默认使用圆角背景
                    binding.clSmartbar.setBackground(
                            SkinManager.getInstance()
                                    .getDrawable(com.smartcar.easylauncher.R.drawable.rounded)
                    );
                    MyLog.d(TAG, "🚀 [重构] 底部布局使用默认背景");
                    break;
            }
            MyLog.d(TAG, "🚀 [重构] 使用默认圆角背景");
        } catch (Exception e) {
            MyLog.e(TAG, "设置布局特定背景失败", e);
        }
    }

    /**
     * 🚀 重构：设置水平布局背景
     */
    private void setHorizontalBackground() {
        try {
            // 尝试使用专用的水平背景资源
            binding.clSmartbar.setBackground(
                    SkinManager.getInstance().getDrawable(com.smartcar.easylauncher.R.drawable.bg_smart_bar_horizontal));
        } catch (Exception e) {
            // 如果专用背景不存在，使用默认圆角背景
            MyLog.w(TAG, "水平背景资源不存在，使用默认圆角背景");
            // 默认使用圆角背景
            binding.clSmartbar.setBackground(
                    SkinManager.getInstance()
                            .getDrawable(com.smartcar.easylauncher.R.drawable.rounded)
            );
        }
    }

    /**
     * 🚀 重构：设置垂直布局背景
     */
    private void setVerticalBackground() {
        try {
            // 尝试使用专用的垂直背景资源
            binding.clSmartbar.setBackground(
                    SkinManager.getInstance()
                            .getDrawable(com.smartcar.easylauncher.R.drawable.bg_smart_bar_vertical)
            );
        } catch (Exception e) {
            // 如果专用背景不存在，使用默认圆角背景
            MyLog.w(TAG, "垂直背景资源不存在，使用默认圆角背景");
            // 默认使用圆角背景
            binding.clSmartbar.setBackground(
                    SkinManager.getInstance()
                            .getDrawable(com.smartcar.easylauncher.R.drawable.rounded)
            );
        }
    }
}
