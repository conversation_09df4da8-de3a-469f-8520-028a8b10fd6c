package com.smartcar.easylauncher.shared.dialog;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.view.View;

import androidx.core.util.Consumer;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.dialog.DialogBluetoothDeviceAdapter;
import com.smartcar.easylauncher.data.model.system.BluetoothDeviceModel;
import com.smartcar.easylauncher.shared.utils.system.BluetoothUtils;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;
import com.timmy.tdialog.base.BindViewHolder;
import com.timmy.tdialog.base.TBaseAdapter;
import com.timmy.tdialog.list.TListDialog;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class BluetoothDeviceSelector {

    private final Context context;
    private List<BluetoothDeviceModel> mAllAppList;
    private String bleInfo;
    private BluetoothDeviceModel selectedInfo;

    public BluetoothDeviceSelector(Context context) {
        this.context = context;
    }

    public static void selectBle(Context context, Consumer<BluetoothDeviceModel> callback) {
        BluetoothDeviceSelector selector = new BluetoothDeviceSelector(context);
        selector.bleInfo = "";
        selector.selectedInfo = null;

        final Set<BluetoothDevice> devices = BluetoothUtils.getBondedDevices(context);
        selector.mAllAppList = new ArrayList<>();
        for (BluetoothDevice device : devices) {
            selector.mAllAppList.add(new BluetoothDeviceModel(device.getName(), device.getAddress()));
        }
        selector.showDialog(callback);
    }

    private void showDialog(Consumer<BluetoothDeviceModel> callback) {
        new TListDialog.Builder(((FragmentActivity) context).getSupportFragmentManager())
                // ...省略其他设置代码...
                .setListLayoutRes(R.layout.dialog_app_select, LinearLayoutManager.VERTICAL)
                //.setLayoutManager(new LinearLayoutManager(getActivity(), RecyclerView.VERTICAL, false))
//                .setScreenWidthAspect((Activity) context, 1)
//                .setScreenHeightAspect((Activity) context, 1)
                .setWidth(DensityUtils.dp2px(context, 460))
                .setHeight(DensityUtils.dp2px(context, 310))
                .setOnBindViewListener(bindViewHolder -> bindViewHolder.setText(R.id.tv_hint, "选择蓝牙设备"))
                .addOnClickListener(R.id.root, R.id.ll_payment, R.id.ll_xiezai, R.id.ll_guanbi).setOnViewClickListener((viewHolder, view, tDialog) -> {
                    switch (view.getId()) {
                        case R.id.ll_payment:
                            if (bleInfo == null || bleInfo.isEmpty()) {
                                MToast.makeTextShort("选择你要使用的音乐播放器");
                                return;
                            }
                            callback.accept(selectedInfo);
                            tDialog.dismiss();
                            break;

                        case R.id.ll_xiezai:
                            tDialog.dismiss();
                            break;
                        case R.id.ll_guanbi:
                            tDialog.dismiss();
                            break;
                        case R.id.root:
                            tDialog.dismiss();
                            break;
                        default:
                    }
                })
                .setAdapter(new DialogBluetoothDeviceAdapter(R.layout.device_item_app, mAllAppList) {
                    @Override
                    protected void onBind(BindViewHolder holder, int position, BluetoothDeviceModel info) {
                        holder.setText(R.id.tv_package_name, info.getName() + " ( " + info.getAddress() + " )");
                        // Glide.with(context).load(info.getLogoPath()).into((ImageView) holder.getView(R.id.grid_item_app_icon));
                        if (info.isSelect()) {
                            holder.getView(R.id.grid_item_app_delete).setVisibility(View.VISIBLE);
                        } else {
                            holder.getView(R.id.grid_item_app_delete).setVisibility(View.GONE);
                        }
                    }
                })
                .setOnAdapterItemClickListener((TBaseAdapter.OnAdapterItemClickListener<BluetoothDeviceModel>) (holder, position, info, tDialog) -> {
                    if (info.isSelect()) {
                        bleInfo = new Gson().toJson(info);
                        selectedInfo = info;
                    } else {
                        bleInfo = "";
                    }
                }).setOnDismissListener(dialog -> {
                    // Toast.makeText(HomeActivity.this, "setOnDismissListener 回调", Toast.LENGTH_SHORT).show();
                })
                // ...省略其他设置代码...
                .create()
                .show();
    }
}