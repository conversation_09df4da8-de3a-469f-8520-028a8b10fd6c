package com.smartcar.easylauncher.shared.adapter.personal;

import android.content.Context;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;

import com.bumptech.glide.Glide;
import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.data.database.entity.CarModel;
import com.smartcar.easylauncher.modules.trip.TripDetailsScene;


/**
 * <AUTHOR>
 */
public class CarListAdapter extends BaseQuickAdapter<CarModel, QuickViewHolder> {


    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable CarModel item) {
        assert item != null;
        Glide.with(getContext()).load(item.getBrandLogo()).into((ImageView) helper.getView(R.id.iv_icon));
        helper.setText(R.id.tv_time, item.getBrand() + " - " + item.getModel());

        helper.setText(R.id.tv_path, item.getLicensePlate());
        helper.setText(R.id.tv_distance, item.getTotalMileage()/1000 + "km");

        if (item.getStatus() == 0) {
            helper.setVisible(R.id.tv_size, true);
            helper.setVisible(R.id.mBtnWifiCancel, false);
        } else {
            helper.setVisible(R.id.tv_size, false);
            helper.setVisible(R.id.mBtnWifiCancel, true);
        }

        ViewCompat.setTransitionName(helper.getView(R.id.iv_icon), TripDetailsScene.VIEW_NAME_HEADER_IMAGE + item.getCarId());
        ViewCompat.setTransitionName(helper.getView(R.id.tv_path), TripDetailsScene.VIEW_NAME_HEADER_TITLE + item.getCarId());
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.car_item, viewGroup);
    }
}
