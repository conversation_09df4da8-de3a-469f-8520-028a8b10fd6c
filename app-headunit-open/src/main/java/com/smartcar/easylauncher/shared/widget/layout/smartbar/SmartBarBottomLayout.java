package com.smartcar.easylauncher.shared.widget.layout.smartbar;

import android.view.View;

import androidx.constraintlayout.widget.ConstraintSet;
import androidx.fragment.app.FragmentActivity;

import com.smartcar.easylauncher.databinding.FragmentSmartBarBinding;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * 智能导航栏底部布局实现
 * 适用于卡片布局1、卡片布局2、地图布局1、地图布局2、地图布局5、地图布局6
 * <p>
 * 布局特点：
 * - 底部导航栏
 * - 支持音乐控制组件
 * - 应用图标水平排列
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public class SmartBarBottomLayout extends AbstractSmartBarLayout {

    private static final String TAG = "SmartBarBottomLayout";

    /**
     * 构造函数
     *
     * @param binding  数据绑定对象
     * @param activity Fragment所属的Activity
     */
    public SmartBarBottomLayout(FragmentSmartBarBinding binding, FragmentActivity activity) {
        super(binding, activity);
    }

    @Override
    public void applyLayout(ConstraintSet constraintSet) {
        try {
            MyLog.d(TAG, "开始应用底部布局配置");

            // 🚀 参考SmartBarScene.java的bottomLayout()实现
            // 设置容器布局参数
            setupContainerLayoutParams();

            // 🚀 重要：先配置音乐控制组件，确保它的约束正确设置
            configureMusicControlArea(constraintSet);

            // 🚀 然后配置更多按钮，此时音乐控制组件的状态已确定
            configureBottomMoreButton(constraintSet);

            // 配置应用图标（按照原有顺序：1-6个图标）
            configureBottomAppIcons(constraintSet);

            MyLog.d(TAG, "底部布局配置完成");
        } catch (Exception e) {
            MyLog.e(TAG, "应用底部布局失败", e);
        }
    }

    /**
     * 配置音乐控制区域
     * 🚀 完全参考SmartBarScene.java中binding.ilDibu的配置
     */
    private void configureMusicControlArea(ConstraintSet constraintSet) {
        try {
            if (SettingsManager.getNavigationBarMusicShow()) {
                binding.ilDibu.setVisibility(View.VISIBLE);
            } else {
                binding.ilDibu.setVisibility(View.GONE);
            }
            // 🚀 直接使用binding，不使用findViewById
            // 完全参考原有的binding.ilDibu配置（第407-413行）
            constraintSet.clear(binding.ilDibu.getId());
            constraintSet.constrainWidth(binding.ilDibu.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.constrainHeight(binding.ilDibu.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.connect(binding.ilDibu.getId(), ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START);
            // 注释掉的约束（参考第411行）
            // constraintSet.connect(binding.ilDibu. getId(), ConstraintSet.END, binding.gridItemAppIcon.getId(), ConstraintSet.START);
            constraintSet.connect(binding.ilDibu.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM);
            constraintSet.connect(binding.ilDibu.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP);

            MyLog.d(TAG, "音乐控制区域配置完成");
        } catch (Exception e) {
            MyLog.e(TAG, "配置音乐控制区域失败", e);
        }
    }

    /**
     * 配置底部更多按钮
     * 🚀 完全参考SmartBarScene.java中的更多按钮配置（第415-422行）
     */
    private void configureBottomMoreButton(ConstraintSet constraintSet) {
        try {
            // 🚀 更多图标 - 完全参考原有实现，不使用findViewById
            constraintSet.clear(binding.gridItemAppIcon.getId());
            constraintSet.constrainWidth(binding.gridItemAppIcon.getId(), iconSize);
            constraintSet.constrainHeight(binding.gridItemAppIcon.getId(), iconSize);

            // 🚀 修复：直接使用binding.ilDibu，完全按照原有逻辑（第419行）
            constraintSet.connect(binding.gridItemAppIcon.getId(), ConstraintSet.START, binding.ilDibu.getId(), ConstraintSet.END);
            constraintSet.connect(binding.gridItemAppIcon.getId(), ConstraintSet.END, binding.ivOne.getId(), ConstraintSet.START);
            constraintSet.connect(binding.gridItemAppIcon.getId(), ConstraintSet.BOTTOM, binding.tvMore.getId(), ConstraintSet.TOP, margin5);
            constraintSet.connect(binding.gridItemAppIcon.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, margin5);

            // 🚀 更多文字 - 完全参考原有实现
            constraintSet.clear(binding.tvMore.getId());
            constraintSet.constrainWidth(binding.tvMore.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.constrainHeight(binding.tvMore.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.connect(binding.tvMore.getId(), ConstraintSet.START, binding.gridItemAppIcon.getId(), ConstraintSet.START);
            constraintSet.connect(binding.tvMore.getId(), ConstraintSet.END, binding.gridItemAppIcon.getId(), ConstraintSet.END);
            constraintSet.connect(binding.tvMore.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, margin5);
            constraintSet.connect(binding.tvMore.getId(), ConstraintSet.TOP, binding.gridItemAppIcon.getId(), ConstraintSet.BOTTOM);

            MyLog.d(TAG, "底部更多按钮配置完成");
        } catch (Exception e) {
            MyLog.e(TAG, "配置底部更多按钮失败", e);
        }
    }

    /**
     * 配置底部应用图标
     * 🚀 完全参考SmartBarScene.java中的应用图标配置
     */
    private void configureBottomAppIcons(ConstraintSet constraintSet) {
        try {
            // 🚀 第一个图标 - 完全参考原有实现
            constraintSet.clear(binding.ivOne.getId());
            constraintSet.constrainWidth(binding.ivOne.getId(), iconSize);
            constraintSet.constrainHeight(binding.ivOne.getId(), iconSize);
            constraintSet.connect(binding.ivOne.getId(), ConstraintSet.START, binding.gridItemAppIcon.getId(), ConstraintSet.END);
            constraintSet.connect(binding.ivOne.getId(), ConstraintSet.END, binding.ivTwo.getId(), ConstraintSet.START);
            constraintSet.connect(binding.ivOne.getId(), ConstraintSet.BOTTOM, binding.tvOne.getId(), ConstraintSet.TOP, margin5);
            constraintSet.connect(binding.ivOne.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, margin5);

            // 🚀 第一个文字
            constraintSet.clear(binding.tvOne.getId());
            constraintSet.constrainWidth(binding.tvOne.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.constrainHeight(binding.tvOne.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.connect(binding.tvOne.getId(), ConstraintSet.START, binding.ivOne.getId(), ConstraintSet.START);
            constraintSet.connect(binding.tvOne.getId(), ConstraintSet.END, binding.ivOne.getId(), ConstraintSet.END);
            constraintSet.connect(binding.tvOne.getId(), ConstraintSet.TOP, binding.ivOne.getId(), ConstraintSet.BOTTOM);
            constraintSet.connect(binding.tvOne.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, margin5);

            // 🚀 第二个图标
            constraintSet.clear(binding.ivTwo.getId());
            constraintSet.constrainWidth(binding.ivTwo.getId(), iconSize);
            constraintSet.constrainHeight(binding.ivTwo.getId(), iconSize);
            constraintSet.connect(binding.ivTwo.getId(), ConstraintSet.START, binding.ivOne.getId(), ConstraintSet.END);
            constraintSet.connect(binding.ivTwo.getId(), ConstraintSet.END, binding.ivThree.getId(), ConstraintSet.START);
            constraintSet.connect(binding.ivTwo.getId(), ConstraintSet.BOTTOM, binding.tvTwo.getId(), ConstraintSet.TOP, margin5);
            constraintSet.connect(binding.ivTwo.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, margin5);

            // 🚀 第二个文字
            constraintSet.clear(binding.tvTwo.getId());
            constraintSet.constrainWidth(binding.tvTwo.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.constrainHeight(binding.tvTwo.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.connect(binding.tvTwo.getId(), ConstraintSet.START, binding.ivTwo.getId(), ConstraintSet.START);
            constraintSet.connect(binding.tvTwo.getId(), ConstraintSet.END, binding.ivTwo.getId(), ConstraintSet.END);
            constraintSet.connect(binding.tvTwo.getId(), ConstraintSet.TOP, binding.ivTwo.getId(), ConstraintSet.BOTTOM);
            constraintSet.connect(binding.tvTwo.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, margin5);

            // 🚀 第三个图标
            constraintSet.clear(binding.ivThree.getId());
            constraintSet.constrainWidth(binding.ivThree.getId(), iconSize);
            constraintSet.constrainHeight(binding.ivThree.getId(), iconSize);
            constraintSet.connect(binding.ivThree.getId(), ConstraintSet.START, binding.ivTwo.getId(), ConstraintSet.END);
            constraintSet.connect(binding.ivThree.getId(), ConstraintSet.END, binding.ivFour.getId(), ConstraintSet.START);
            constraintSet.connect(binding.ivThree.getId(), ConstraintSet.BOTTOM, binding.tvThree.getId(), ConstraintSet.TOP, margin5);
            constraintSet.connect(binding.ivThree.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, margin5);

            // 🚀 第三个文字
            constraintSet.clear(binding.tvThree.getId());
            constraintSet.constrainWidth(binding.tvThree.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.constrainHeight(binding.tvThree.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.connect(binding.tvThree.getId(), ConstraintSet.START, binding.ivThree.getId(), ConstraintSet.START);
            constraintSet.connect(binding.tvThree.getId(), ConstraintSet.END, binding.ivThree.getId(), ConstraintSet.END);
            constraintSet.connect(binding.tvThree.getId(), ConstraintSet.TOP, binding.ivThree.getId(), ConstraintSet.BOTTOM);
            constraintSet.connect(binding.tvThree.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, margin5);

            // 🚀 第四个图标
            constraintSet.clear(binding.ivFour.getId());
            constraintSet.constrainWidth(binding.ivFour.getId(), iconSize);
            constraintSet.constrainHeight(binding.ivFour.getId(), iconSize);
            constraintSet.connect(binding.ivFour.getId(), ConstraintSet.START, binding.ivThree.getId(), ConstraintSet.END);
            constraintSet.connect(binding.ivFour.getId(), ConstraintSet.END, binding.ivFive.getId(), ConstraintSet.START);
            constraintSet.connect(binding.ivFour.getId(), ConstraintSet.BOTTOM, binding.tvFour.getId(), ConstraintSet.TOP, margin5);
            constraintSet.connect(binding.ivFour.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, margin5);

            // 🚀 第四个文字
            constraintSet.clear(binding.tvFour.getId());
            constraintSet.constrainWidth(binding.tvFour.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.constrainHeight(binding.tvFour.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.connect(binding.tvFour.getId(), ConstraintSet.START, binding.ivFour.getId(), ConstraintSet.START);
            constraintSet.connect(binding.tvFour.getId(), ConstraintSet.END, binding.ivFour.getId(), ConstraintSet.END);
            constraintSet.connect(binding.tvFour.getId(), ConstraintSet.TOP, binding.ivFour.getId(), ConstraintSet.BOTTOM);
            constraintSet.connect(binding.tvFour.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, margin5);

            // 🚀 第五个图标
            constraintSet.clear(binding.ivFive.getId());
            constraintSet.constrainWidth(binding.ivFive.getId(), iconSize);
            constraintSet.constrainHeight(binding.ivFive.getId(), iconSize);
            constraintSet.connect(binding.ivFive.getId(), ConstraintSet.START, binding.ivFour.getId(), ConstraintSet.END);
            constraintSet.connect(binding.ivFive.getId(), ConstraintSet.END, binding.ivPersonnalCenter.getId(), ConstraintSet.START);
            constraintSet.connect(binding.ivFive.getId(), ConstraintSet.BOTTOM, binding.tvFive.getId(), ConstraintSet.TOP, margin5);
            constraintSet.connect(binding.ivFive.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, margin5);

            // 🚀 第五个文字
            constraintSet.clear(binding.tvFive.getId());
            constraintSet.constrainWidth(binding.tvFive.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.constrainHeight(binding.tvFive.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.connect(binding.tvFive.getId(), ConstraintSet.START, binding.ivFive.getId(), ConstraintSet.START);
            constraintSet.connect(binding.tvFive.getId(), ConstraintSet.END, binding.ivFive.getId(), ConstraintSet.END);
            constraintSet.connect(binding.tvFive.getId(), ConstraintSet.TOP, binding.ivFive.getId(), ConstraintSet.BOTTOM);
            constraintSet.connect(binding.tvFive.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, margin5);

            // 🚀 第六个图标（个人中心）
            constraintSet.clear(binding.ivPersonnalCenter.getId());
            constraintSet.constrainWidth(binding.ivPersonnalCenter.getId(), iconSize);
            constraintSet.constrainHeight(binding.ivPersonnalCenter.getId(), iconSize);
            constraintSet.connect(binding.ivPersonnalCenter.getId(), ConstraintSet.START, binding.ivFive.getId(), ConstraintSet.END);
            constraintSet.connect(binding.ivPersonnalCenter.getId(), ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END);
            constraintSet.connect(binding.ivPersonnalCenter.getId(), ConstraintSet.BOTTOM, binding.tvSix.getId(), ConstraintSet.TOP, margin5);
            constraintSet.connect(binding.ivPersonnalCenter.getId(), ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, margin5);

            // 🚀 第六个文字
            constraintSet.clear(binding.tvSix.getId());
            constraintSet.constrainWidth(binding.tvSix.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.constrainHeight(binding.tvSix.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.connect(binding.tvSix.getId(), ConstraintSet.START, binding.ivPersonnalCenter.getId(), ConstraintSet.START);
            constraintSet.connect(binding.tvSix.getId(), ConstraintSet.END, binding.ivPersonnalCenter.getId(), ConstraintSet.END);
            constraintSet.connect(binding.tvSix.getId(), ConstraintSet.TOP, binding.ivPersonnalCenter.getId(), ConstraintSet.BOTTOM);
            constraintSet.connect(binding.tvSix.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, margin5);

            MyLog.d(TAG, "底部应用图标配置完成");
        } catch (Exception e) {
            MyLog.e(TAG, "配置底部应用图标失败", e);
        }
    }

    @Override
    public String getLayoutType() {
        return "bottom";
    }

    @Override
    public boolean isSidebarLayout() {
        return false;
    }

    @Override
    public boolean supportsMusicControl() {
        return true;
    }

    /**
     * 🚀 实现：底部布局的文字可见性控制
     * 底部布局的文字显示逻辑相对简单：要么全显示，要么全隐藏
     *
     * @param wordlessMode true表示无字模式（隐藏文字），false表示显示文字
     */
    @Override
    public void applyTextVisibility(boolean wordlessMode) {
        try {
            MyLog.d(TAG, "🚀 [底部布局] 开始应用文字可见性设置 - 无字模式: " + wordlessMode);

            if (wordlessMode) {
                // 显示文字模式：显示所有文字
                setAllTextsVisibility(View.VISIBLE);
                MyLog.d(TAG, "🚀 [底部布局] 显示模式：显示所有文字");
            } else {
                // 无字模式：隐藏所有文字
                setAllTextsVisibility(View.GONE);
                MyLog.d(TAG, "🚀 [底部布局] 无字模式：隐藏所有文字");
            }

            MyLog.d(TAG, "🚀 [底部布局] 文字可见性设置完成");
        } catch (Exception e) {
            MyLog.e(TAG, "底部布局应用文字可见性失败", e);
        }
    }

//    /**
//     * 🚀 重构：底部布局的背景设置
//     * 根据地图布局类型设置相应的背景
//     */
//    @Override
//    protected void setLayoutSpecificBackground() {
//        try {
//            String mapLayout = SettingsManager.getMapLayout();
//            MyLog.d(TAG, "🚀 [重构] 底部布局设置背景，当前地图布局: " + mapLayout);
//
//            switch (mapLayout) {
//                case SettingsConstants.DEFAULT_MAP_LAYOUT_1:
//                case SettingsConstants.DEFAULT_MAP_LAYOUT_2:
//                case SettingsConstants.DEFAULT_MAP_LAYOUT_5:
//                case SettingsConstants.DEFAULT_MAP_LAYOUT_6:
//                    // 水平布局：尝试使用专用的水平背景
//                    setHorizontalBackground();
//                    MyLog.d(TAG, "🚀 [重构] 底部布局使用水平背景");
//                    break;
//
//                case SettingsConstants.DEFAULT_MAP_LAYOUT_3:
//                case SettingsConstants.DEFAULT_MAP_LAYOUT_4:
//                    // 垂直布局：尝试使用专用的垂直背景
//                    setVerticalBackground();
//                    MyLog.d(TAG, "🚀 [重构] 底部布局使用垂直背景");
//                    break;
//
//                default:
//                    // 默认情况：使用圆角背景
//                    super.setLayoutSpecificBackground();
//                    MyLog.d(TAG, "🚀 [重构] 底部布局使用默认背景");
//                    break;
//            }
//        } catch (Exception e) {
//            MyLog.e(TAG, "底部布局设置背景失败", e);
//            // 降级处理：使用默认背景
//            super.setLayoutSpecificBackground();
//        }
//    }
//
//    /**
//     * 🚀 重构：设置水平布局背景
//     */
//    private void setHorizontalBackground() {
//        try {
//            // 尝试使用专用的水平背景资源
//            binding.clSmartbar.setBackground(
//                    SkinManager.getInstance().getDrawable(com.smartcar.easylauncher.R.drawable.bg_smart_bar_horizontal));
//        } catch (Exception e) {
//            // 如果专用背景不存在，使用默认圆角背景
//            MyLog.w(TAG, "水平背景资源不存在，使用默认圆角背景");
//            super.setLayoutSpecificBackground();
//        }
//    }
//
//    /**
//     * 🚀 重构：设置垂直布局背景
//     */
//    private void setVerticalBackground() {
//        try {
//            // 尝试使用专用的垂直背景资源
//            binding.clSmartbar.setBackground(
//                    com.smartcar.easylauncher.skin.SkinManager.getInstance()
//                            .getDrawable(com.smartcar.easylauncher.R.drawable.bg_smart_bar_vertical)
//            );
//        } catch (Exception e) {
//            // 如果专用背景不存在，使用默认圆角背景
//            MyLog.w(TAG, "垂直背景资源不存在，使用默认圆角背景");
//            super.setLayoutSpecificBackground();
//        }
//    }
}
