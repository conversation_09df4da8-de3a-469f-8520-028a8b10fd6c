package com.smartcar.easylauncher.shared.widget.layout.smartbar;

import android.os.Build;
import android.transition.TransitionManager;

import androidx.constraintlayout.widget.ConstraintSet;
import androidx.fragment.app.FragmentActivity;

import com.smartcar.easylauncher.databinding.FragmentSmartBarBinding;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * 智能导航栏布局管理器 - 重构版本
 * <p>
 * 🚀 重构重点：职责分离，专注于协调工作
 * 1. 作为协调者，不直接操作UI组件
 * 2. 管理布局工厂和布局切换
 * 3. 提供统一的查询接口
 * 4. 将具体的UI操作委托给Layout类处理
 * <p>
 * 功能特性：
 * - 支持底部布局和侧边布局两种模式
 * - 兼容Android API 17及以上版本
 * - 简化的布局判断逻辑
 * - 高效的约束配置
 * - 清晰的职责分工
 *
 * <AUTHOR>
 * @since 2024-12-18
 * @version 2.0 重构版 - 职责分离
 */
public class SmartBarLayoutManager {

    private static final String TAG = "SmartBarLayoutManager";

    // UI组件引用（仅用于传递给Layout类）
    private final FragmentSmartBarBinding binding;
    private final FragmentActivity activity;
    private final ConstraintSet constraintSet;

    // 🚀 重构：布局工厂，负责创建和管理Layout实例
    private final SmartBarLayoutFactory layoutFactory;

    /**
     * 构造函数
     *
     * @param binding       数据绑定对象
     * @param activity      Fragment所属的Activity
     * @param constraintSet 约束布局配置对象
     */
    public SmartBarLayoutManager(FragmentSmartBarBinding binding,
                                 FragmentActivity activity,
                                 ConstraintSet constraintSet) {
        this.binding = binding;
        this.activity = activity;
        this.constraintSet = constraintSet;

        // 🚀 重构：创建布局工厂，负责Layout实例管理
        this.layoutFactory = new SmartBarLayoutFactory(binding, activity);

        MyLog.d(TAG, "🚀 [重构] SmartBarLayoutManager 初始化完成 - 专注于协调工作");
    }

    /**
     * 🚀 重构：初始化布局配置
     * 作为协调者，委托Layout类处理具体的UI操作
     */
    public void initLayout() {
        try {
            MyLog.d(TAG, "🚀 [重构] 开始初始化布局配置 - 协调者模式");

            // 开始布局过渡动画（仅在Android 4.4及以上版本）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                TransitionManager.beginDelayedTransition(binding.clSmartbar);
            }

            // 🚀 重构：从工厂获取当前应该使用的布局实现
            ISmartBarLayout layout = layoutFactory.getCurrentLayout();

            MyLog.d(TAG, "🚀 [重构] 使用布局类型: " + layout.getLayoutType() +
                    ", 是否侧边栏: " + layout.isSidebarLayout() +
                    ", 支持音乐控制: " + layout.supportsMusicControl());

            // 🚀 重构：委托Layout类处理约束配置
            layout.applyLayout(constraintSet);

            // 应用约束配置
            constraintSet.applyTo(binding.clSmartbar);

            // 🚀 重构：委托Layout类处理初始显示设置
            boolean nameShow = SettingsManager.getNavigationBarNameShow();
            boolean transparentMode = SettingsManager.getNavigationBarTransparent();
            boolean musicControlShow = SettingsManager.getNavigationBarMusicShow();

            layout.applyDisplaySettings(nameShow, transparentMode, musicControlShow);

            MyLog.d(TAG, "🎯 [重构] 布局配置初始化完成 - 已委托Layout类处理所有UI操作");
        } catch (Exception e) {
            MyLog.e(TAG, "初始化布局配置失败", e);
        }
    }

    /**
     * 🚀 修正：切换布局
     * 当布局设置发生变化时调用此方法
     */
    public void changeLayout() {
        try {
            MyLog.d(TAG, "🚀 [修正] 开始切换布局");

            // 🚀 修正：清除布局缓存，确保使用最新的设置
            layoutFactory.clearCache();

            // 重新初始化布局
            initLayout();

            MyLog.d(TAG, "🎯 [修正] 布局切换完成");
        } catch (Exception e) {
            MyLog.e(TAG, "切换布局失败", e);
        }
    }

    /**
     * 🚀 修正：检查当前是否为侧边栏布局
     *
     * @return true表示侧边栏布局，false表示底部栏布局
     */
    public boolean isSidebarLayout() {
        try {
            return layoutFactory.isSidebarLayout();
        } catch (Exception e) {
            MyLog.e(TAG, "检查侧边栏布局状态失败", e);
            return false;
        }
    }

    /**
     * 🚀 修正：检查当前布局是否支持音乐控制
     *
     * @return true表示支持音乐控制，false表示不支持
     */
    public boolean supportsMusicControl() {
        try {
            return layoutFactory.supportsMusicControl();
        } catch (Exception e) {
            MyLog.e(TAG, "检查音乐控制支持状态失败", e);
            return false;
        }
    }

    /**
     * 🚀 修正：获取当前布局类型
     *
     * @return 布局类型标识符
     */
    public String getCurrentLayoutType() {
        try {
            return layoutFactory.getCurrentLayout().getLayoutType();
        } catch (Exception e) {
            MyLog.e(TAG, "获取当前布局类型失败", e);
            return "bottom";
        }
    }

    /**
     * 🚀 重构：应用显示设置
     * 作为协调者，完全委托给Layout类处理
     *
     * @param appName    应用名称
     * @param transparentState 透明模式状态
     * @param musicControlShow 音乐控制显示状态
     */
    public void applyDisplaySettings(boolean appName, boolean transparentState, boolean musicControlShow) {
        try {
            MyLog.d(TAG, "🚀 [重构] 协调者开始处理显示设置 - 是否显示应用名称: " + appName +
                    ", 透明模式: " + transparentState +
                    ", 音乐控制: " + musicControlShow);

            // 🚀 重构：获取当前布局实现
            ISmartBarLayout currentLayout = layoutFactory.getCurrentLayout();

            // 🚀 重构：完全委托给Layout类处理所有显示设置
            currentLayout.applyDisplaySettings(appName, transparentState, musicControlShow);

            MyLog.d(TAG, "🚀 [重构] 协调者完成显示设置 - 已委托Layout类处理");
        } catch (Exception e) {
            MyLog.e(TAG, "协调者处理显示设置失败", e);
        }
    }

    /**
     * 🚀 重构：清理资源
     * 作为协调者，负责清理工厂缓存
     */
    public void cleanup() {
        try {
            layoutFactory.clearCache();
            MyLog.d(TAG, "🚀 [重构] SmartBarLayoutManager 资源清理完成 - 协调者模式");
        } catch (Exception e) {
            MyLog.e(TAG, "清理资源失败", e);
        }
    }
}
