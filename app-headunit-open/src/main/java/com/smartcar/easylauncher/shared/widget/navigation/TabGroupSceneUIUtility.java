/*
 * Copyright (C) 2019 ByteDance Inc
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.smartcar.easylauncher.shared.widget.navigation;

import android.view.View;

import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.angcyo.tablayout.DslSelector;
import com.angcyo.tablayout.DslTabLayout;
import com.angcyo.tablayout.DslTabLayoutConfig;
import com.bytedance.scene.Scene;
import com.bytedance.scene.group.GroupScene;
import com.google.android.material.bottomnavigation.BottomNavigationView;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.functions.Function4;

/**
 * Created by JiangQi on 8/16/18.
 * 该工具类用于帮助设置和管理基于不同导航方式的场景界面（如底部导航、抽屉导航和ViewPager）。
 * 特别地，这个类提供了使用底部导航视图（通过DslTabLayout）来设置和管理场景的功能。
 */
public class TabGroupSceneUIUtility {

    /**
     * 使用底部导航视图（DslTabLayout）设置场景，不带导航项选择监听器。
     * 此方法将自动管理场景的显示与隐藏，以及根据底部导航的选择来切换场景。
     *
     * @param dslTabLayout     底部导航视图，用于用户交互
     * @param groupScene       场景组，用于管理多个场景
     * @param containerId      容器ID，用于将场景内容添加到界面上
     * @param children         子场景映射，键为菜单项ID（Integer），值为场景对象（Scene），表示底部导航的每个菜单项对应的场景
     * @param defaultSelectedItemId 默认选中的菜单项ID
     */
    public static void setupWithTabNavigationView(@NonNull final DslTabLayout dslTabLayout,
                                                  @NonNull final GroupScene groupScene,
                                                  @IdRes final int containerId,
                                                  @NonNull final LinkedHashMap<Integer, Scene> children,
                                                  final int defaultSelectedItemId) {
        // 调用重载的方法，不设置导航项选择监听器
        setupWithTabNavigationView(dslTabLayout, groupScene, containerId, children, null, defaultSelectedItemId);
    }

    /**
     * 使用底部导航视图（DslTabLayout）设置场景，并可选择性地设置导航项选择监听器。
     * 此方法将自动管理场景的显示与隐藏，以及根据底部导航的选择来切换场景。
     * 如果提供了导航项选择监听器，它将在场景切换之前被调用。
     *
     * @param dslTabLayout                     底部导航视图，用于用户交互
     * @param groupScene                       场景组，用于管理多个场景
     * @param containerId                      容器ID，用于将场景内容添加到界面上
     * @param children                         子场景映射，键为菜单项ID（Integer），值为场景对象（Scene）
     * @param onNavigationItemSelectedListener 导航项选择监听器，如果不需要可以传入null
     * @param defaultSelectedItemId            默认选中的菜单项ID
     */
    public static void setupWithTabNavigationView(@NonNull final DslTabLayout dslTabLayout,
                                                  @NonNull final GroupScene groupScene,
                                                  @IdRes final int containerId,
                                                  @NonNull final LinkedHashMap<Integer, Scene> children,
                                                  @Nullable final BottomNavigationView.OnNavigationItemSelectedListener onNavigationItemSelectedListener,
                                                  final int defaultSelectedItemId) {
        // 检查子场景映射是否为空
        if (children.size() == 0) {
            throw new IllegalArgumentException("children can't be empty");
        }

        // 初始化菜单项ID列表，这里简单地使用索引作为ID的字符串表示
        final List<String> menuIdList = new ArrayList<>();
        for (int i = 0; i < children.size(); i++) {
            menuIdList.add(String.valueOf(i));
        }
        // 配置DslTabLayout，设置选择项时的行为
        dslTabLayout.configTabLayoutConfig(new Function1<DslTabLayoutConfig, Unit>() {
            @Override
            public Unit invoke(DslTabLayoutConfig dslTabLayoutConfig) {
                dslTabLayoutConfig.setOnSelectItemView(new Function4<View, Integer, Boolean, Boolean, Boolean>() {
                    @Override
                    public Boolean invoke(View view, Integer selectedItemId, Boolean isSmooth, Boolean forceUpdate) {

                        // 设置DslTabLayout的当前项
                        dslTabLayout.setCurrentItem(selectedItemId, isSmooth, forceUpdate);

                        // 根据菜单项ID获取场景
                        String tag = String.valueOf(selectedItemId);
                        Scene scene = groupScene.findSceneByTag(tag);
                        if (scene == null) {
                            // 如果没有找到，则从子场景映射中直接获取
                            scene = children.get(selectedItemId);
                        }

                        // 管理场景的显示与隐藏
                        if (!groupScene.isAdded(scene)) {
                            // 如果场景未添加，则添加到容器中
                            groupScene.add(containerId, scene, tag);
                        } else if (!groupScene.isShowing(scene)) {
                            // 如果场景已添加但未显示，则显示场景
                            groupScene.show(scene);
                        }

                        // 隐藏其他已显示场景
                        for (int i = 0; i < menuIdList.size(); i++) {
                            Scene otherScene = groupScene.findSceneByTag(menuIdList.get(i));
                            if (otherScene != null && otherScene != scene && groupScene.isAdded(otherScene) && groupScene.isShowing(otherScene)) {
                                groupScene.hide(otherScene);
                            }
                        }

                        return true;
                    }
                });

                // 设置选择视图变化监听器，确保切换item时清除之前的文字选中状态
                dslTabLayoutConfig.setOnSelectViewChange(new Function4<View, List<? extends View>, Boolean, Boolean, Unit>() {
                    @Override
                    public Unit invoke(View view, List<? extends View> views, Boolean aBoolean, Boolean aBoolean2) {
                         DslSelector dslSelector = dslTabLayout.getDslSelector();

                        // 首先清除所有视图的选中状态，确保之前的文字选中状态被取消
                        if (views != null) {
                            for (View tabView : views) {
                                if (tabView != null && tabView != view) {
                                    // 重置非当前选中视图的状态
                                    tabView.setSelected(false);
                                }
                            }
                        }

                        // 然后更新选择器样式以反映当前选中状态
                        dslSelector.updateStyle();
                        return null;
                    }
                });

                return null;
            }
        });
        dslTabLayout.setCurrentItem(defaultSelectedItemId, true, false);
        // 初始化显示默认选中的场景
        String tag = String.valueOf(defaultSelectedItemId);
        Scene scene = groupScene.findSceneByTag(tag);
        if (scene == null) {
            scene = children.get(defaultSelectedItemId);
        }

        if (!groupScene.isAdded(scene)) {
            groupScene.add(containerId, scene, tag);
        } else if (!groupScene.isShowing(scene)) {
            groupScene.show(scene);
        }

        for (int i = 0; i < menuIdList.size(); i++) {
            Scene otherScene = groupScene.findSceneByTag(menuIdList.get(i));
            if (otherScene != null && otherScene != scene && groupScene.isAdded(otherScene) && groupScene.isShowing(otherScene)) {
                groupScene.hide(otherScene);
            }
        }
    }
}