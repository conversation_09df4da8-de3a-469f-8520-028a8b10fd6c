package com.smartcar.easylauncher.shared.adapter.setting;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.KeyMapConstants;
import com.smartcar.easylauncher.data.database.entity.KeyMapModel;

import java.util.ArrayList;
import java.util.List;

/**
 * 按键映射适配器
 * 用于在RecyclerView中显示按键映射列表
 * 
 * <AUTHOR>
 * @date 2024/07/09
 */
public class KeyMapAdapter extends RecyclerView.Adapter<KeyMapAdapter.ViewHolder> {
    
    private final Context context;
    private List<KeyMapModel> keyMaps = new ArrayList<>();
    private OnItemClickListener onItemClickListener;
    
    /**
     * 构造函数
     */
    public KeyMapAdapter(Context context) {
        this.context = context;
    }
    
    /**
     * 设置数据
     * 
     * @param keyMaps 按键映射列表
     */
    public void setData(List<KeyMapModel> keyMaps) {
        this.keyMaps = keyMaps != null ? keyMaps : new ArrayList<>();
        notifyDataSetChanged();
    }
    
    /**
     * 设置条目点击监听器
     * 
     * @param listener 监听器
     */
    public void setOnItemClickListener(OnItemClickListener listener) {
        this.onItemClickListener = listener;
    }
    
    /**
     * 条目点击监听接口
     */
    public interface OnItemClickListener {
        /**
         * 条目点击回调
         * 
         * @param keyMapModel 按键映射模型
         */
        void onItemClick(KeyMapModel keyMapModel);
    }
    
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_key_mapping, parent, false);
        return new ViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        KeyMapModel keyMap = keyMaps.get(position);
        
        // 设置按键名称
        holder.tvKeyName.setText(keyMap.getKeyName());
        
        // 根据动作类型设置描述
        StringBuilder actionDesc = new StringBuilder();
        
        switch (keyMap.getActionType()) {
            case KeyMapConstants.ActionType.BROADCAST:
                actionDesc.append("发送广播：").append(keyMap.getActionData());
                break;
                
            case KeyMapConstants.ActionType.LAUNCH_APP:
                actionDesc.append("启动应用：").append(keyMap.getActionData());
                break;
                
            case KeyMapConstants.ActionType.MEDIA_CONTROL:
                actionDesc.append("媒体控制：").append(KeyMapConstants.MediaControlType.getMediaControlTypeName(keyMap.getActionData()));
                break;
                
            case KeyMapConstants.ActionType.SYSTEM_ACTION:
                actionDesc.append("系统操作：").append(KeyMapConstants.SystemActionType.getSystemActionTypeName(keyMap.getActionData()));
                break;
                
            default:
                actionDesc.append("未知操作");
                break;
        }
        
        // 设置动作描述
        holder.tvActionDescription.setText(actionDesc.toString());
        
        // 设置启用状态图标
        if (keyMap.getEnabled() == 1) {
            holder.ivEnabled.setImageResource(R.drawable.ic_check_circle);
        } else {
            holder.ivEnabled.setImageResource(R.drawable.ic_remove_circle);
        }
        
        // 设置按键图标
        int imageResId = R.drawable.ic_key;
        switch (keyMap.getActionType()) {
            case KeyMapConstants.ActionType.BROADCAST:
                imageResId = R.drawable.ic_broadcast;
                break;
                
            case KeyMapConstants.ActionType.LAUNCH_APP:
                imageResId = R.drawable.ic_apps;
                break;
                
            case KeyMapConstants.ActionType.MEDIA_CONTROL:
                imageResId = R.drawable.ic_music_note;
                break;
                
            case KeyMapConstants.ActionType.SYSTEM_ACTION:
                imageResId = R.drawable.ic_setting;
                break;
        }
        holder.ivKeyIcon.setImageResource(imageResId);
        
        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            if (onItemClickListener != null) {
                onItemClickListener.onItemClick(keyMap);
            }
        });
    }
    
    @Override
    public int getItemCount() {
        return keyMaps.size();
    }
    
    /**
     * ViewHolder类
     */
    static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView ivKeyIcon;
        TextView tvKeyName;
        TextView tvActionDescription;
        ImageView ivEnabled;
        
        ViewHolder(View itemView) {
            super(itemView);
            ivKeyIcon = itemView.findViewById(R.id.iv_key_icon);
            tvKeyName = itemView.findViewById(R.id.tv_key_name);
            tvActionDescription = itemView.findViewById(R.id.tv_action_description);
            ivEnabled = itemView.findViewById(R.id.iv_enabled);
        }
    }
} 