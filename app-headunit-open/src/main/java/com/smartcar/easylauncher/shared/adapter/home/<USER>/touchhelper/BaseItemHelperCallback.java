package com.smartcar.easylauncher.shared.adapter.home.channel.touchhelper;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;

import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * 基础ItemTouchHelper的回调类
 *
 * <AUTHOR>
 * @since 1.1.0
 */
public abstract class BaseItemHelperCallback extends ItemTouchHelper.Callback {

    /**
     * 触摸辅助监听器
     */
    protected OnItemTouchHelperListener listener;

    /**
     * 构造函数，初始化触摸辅助监听器
     *
     * @param listener 触摸辅助监听器
     */
    public BaseItemHelperCallback(OnItemTouchHelperListener listener) {
        this.listener = listener;
    }

    @Override
    public void onSelectedChanged(@Nullable RecyclerView.ViewHolder viewHolder, int actionState) {
        // 当选中状态改变时调用
        MyLog.v("JACK8", "onSelectedChanged() called with: viewHolder = [" + viewHolder + "], actionState = [" + actionState + "]");
        if (actionState != ItemTouchHelper.ACTION_STATE_IDLE) {
            // 如果不是空闲状态
            if (viewHolder instanceof OnItemTouchViewHolder itemViewHolder) {
                // 调用onItemSelected方法
                itemViewHolder.onItemSelected(viewHolder);
            }
        }
        // 调用父类方法
        super.onSelectedChanged(viewHolder, actionState);
    }

    @Override
    public void clearView(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
        // 清除View时的回调
        MyLog.v("JACK8", "clearView() called with: recyclerView = [" + recyclerView + "], viewHolder = [" + viewHolder + "]");
        if (viewHolder instanceof OnItemTouchViewHolder itemViewHolder) {
            // 调用onItemClear方法
            itemViewHolder.onItemClear(viewHolder);
        }
        // 调用父类方法
        super.clearView(recyclerView, viewHolder);
    }

}

