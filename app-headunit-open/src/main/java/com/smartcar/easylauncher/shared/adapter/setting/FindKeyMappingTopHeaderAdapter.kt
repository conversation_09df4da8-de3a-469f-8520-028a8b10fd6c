package com.smartcar.easylauncher.shared.adapter.setting

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter4.BaseSingleItemAdapter
import com.chad.library.adapter4.fullspan.FullSpanAdapterType
import com.smartcar.easylauncher.R

/**
 * 发现按键映射页面头部适配器
 * 显示标题和按键映射数量
 * <AUTHOR>
 * @date 2024/12/18
 */
class FindKeyMappingTopHeaderAdapter : BaseSingleItemAdapter<Any, FindKeyMappingTopHeaderAdapter.VH>(),
    FullSpanAdapterType {

    companion object {
        val HEAD_VIEWTYPE = 0x10000557
    }

    private var keyMapCount: Int = 0
    private var isLoading: Boolean = true

    class VH(view: View) : RecyclerView.ViewHolder(view) {
        val tvKeyMapCount: TextView = view.findViewById(R.id.tv_key_map_count)
    }

    override fun onCreateViewHolder(context: Context, parent: ViewGroup, viewType: Int): VH {
        return VH(LayoutInflater.from(parent.context).inflate(R.layout.find_key_mapping_top_view, parent, false))
    }

    override fun onBindViewHolder(holder: VH, item: Any?) {
        updateKeyMapCountDisplay(holder)
    }

    override fun getItemViewType(position: Int, list: List<Any>): Int {
        return HEAD_VIEWTYPE
    }

    /**
     * 更新按键映射数量显示
     */
    fun updateKeyMapCount(count: Int, loading: Boolean = false) {
        this.keyMapCount = count
        this.isLoading = loading
        notifyDataSetChanged()
    }

    /**
     * 更新按键映射数量显示文本
     */
    private fun updateKeyMapCountDisplay(holder: VH) {
        val countText = when {
            isLoading -> "加载中..."
            keyMapCount == 0 -> "暂无按键映射"
            keyMapCount == 1 -> "1个按键映射"
            else -> "${keyMapCount}个按键映射"
        }
        holder.tvKeyMapCount.text = countText
    }
}
