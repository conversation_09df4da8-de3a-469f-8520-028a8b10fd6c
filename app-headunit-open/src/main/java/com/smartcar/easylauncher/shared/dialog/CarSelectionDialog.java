package com.smartcar.easylauncher.shared.dialog;

import static org.jetbrains.anko.AsyncKt.runOnUiThread;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Handler;
import android.os.Looper;
import android.view.View;

import androidx.fragment.app.FragmentManager;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.core.constants.StatusCodeModel;
import com.smartcar.easylauncher.data.model.vehicle.BrandListModel;
import com.smartcar.easylauncher.data.model.vehicle.CarSelectModel;
import com.smartcar.easylauncher.data.model.vehicle.VehicleListModel;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.tools.cityselect.callback.OnCitySelectListener;
import com.smartcar.tools.cityselect.model.CityModel;
import com.smartcar.tools.cityselect.view.CitySelectView;
import com.timmy.tdialog.TDialog;
import com.timmy.tdialog.base.BindViewHolder;
import com.timmy.tdialog.listener.OnBindViewListener;
import com.timmy.tdialog.listener.OnViewClickListener;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import rxhttp.RxHttp;

/**
 * <AUTHOR>
 */
public class CarSelectionDialog {
    private static final String TAG = "CarSelectionDialog";
    // 常量定义
    private static final int SCREEN_WIDTH_ASPECT = 1;
    private static final int SCREEN_HEIGHT_ASPECT = 1;
    private static CitySelectView citySelectView;
    //选择标记
    public static boolean isSelect = false;
    //品牌选择
    public static String brandSelect;
    private static TDialog dialog;
    private static List<CityModel> hotCitys;
    private static CarSelectModel carSelectModel = new CarSelectModel();

    public static void showGeneralDialog(FragmentManager fragmentManager, Context context, DialogClickListener listener) {
        // 参数校验
        if (fragmentManager == null || context == null || listener == null) {
            throw new IllegalArgumentException("Parameters cannot be null");
        }
        isSelect = false;
        brandSelect = "";
        hotCitys = new ArrayList<>();

        // 创建并显示对话框
        TDialog.Builder builder = new TDialog.Builder(fragmentManager)
                .setLayoutRes(R.layout.dialog_car_selection)
                .setScreenWidthAspect(context, SCREEN_WIDTH_ASPECT)
                .setScreenHeightAspect(context, SCREEN_HEIGHT_ASPECT)
                .addOnClickListener(R.id.root)
                .setCancelableOutside(false);

        // 设置监听器
        builder.setOnViewClickListener(new OnViewClickListenerImpl(listener));
        builder.setOnBindViewListener(new OnBindViewListener() {
            @Override
            public void bindView(BindViewHolder viewHolder) {
                citySelectView = viewHolder.getView(R.id.city_view);
                //设置搜索框的文案提示
                citySelectView.setSearchTips("请输入车辆名称或者拼音");
                citySelectView.setCancelText("取消");
                getBrandData(citySelectView);

                //设置城市选择之后的事件监听
                citySelectView.setOnCitySelectListener(new OnCitySelectListener() {
                    @Override
                    public void onCitySelect(CityModel cityModel) {
                        if (!isSelect) {
                            brandSelect = cityModel.getCityName();
                            getCarModelData(cityModel.getExtra().toString());
                        } else {
                          //  DataManager.setBrand(brandSelect);
                          //  DataManager.setModel(cityModel.getCityName());
                            carSelectModel.setBrand(brandSelect);
                            carSelectModel.setModel(cityModel.getCityName());
                            listener.onPositiveClick(carSelectModel);
                            dialog.dismiss();
                        }
                    }

                    @Override
                    public void onSelectCancel() {
                        if ("取消".equals(citySelectView.getCancelText())) {
                            dialog.dismiss();
                        }
                        if ("上一级".equals(citySelectView.getCancelText())) {
                            if (hotCitys.size() > 0) {
                                isSelect = false;
                                brandSelect = "";
                                citySelectView.bindData(hotCitys, null, null);
                                citySelectView.setCancelText("取消");
                            }
                        }
                    }
                });
            }
        });
        builder.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialogInterface) {
                //释放全部资源，避免内存泄漏
            }
        });
        // 显示对话框
        dialog = builder.create();
        try {
            dialog.show();
        } catch (Exception e) {
            // 异常处理逻辑
            e.printStackTrace();
        }
    }

    private static void runOnUiThread(Runnable runnable) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            runnable.run();
        } else {
            Handler handler = new Handler(Looper.getMainLooper());
            handler.post(runnable);
        }
    }

    /**
     * 弹窗点击事件监听器接口。
     */
    public interface DialogClickListener {

        /**
         * 当点击弹窗中的积极/确认按钮时调用。
         * 例如，可以用于确认操作或执行某项任务。
         */
        void onPositiveClick(CarSelectModel carSelectModel);

    }


    private static class OnViewClickListenerImpl implements OnViewClickListener {
        private final DialogClickListener listener;

        public OnViewClickListenerImpl(DialogClickListener listene) {
            this.listener = listene;
        }

        @Override
        public void onViewClick(BindViewHolder viewHolder, View view, TDialog tDialog) {
            switch (view.getId()) {
                case R.id.root:
                    // 处理点击根视图的情况，如果需要的话
                    tDialog.dismiss();
                    break;
                default:
                    // 未知视图ID的处理逻辑
            }
        }
    }

    /**
     * 通过RxHTTP方式获取品牌数据
     */
    public static void getBrandData(CitySelectView citySelectView) {
        String url = Const.BRAND_LIST;
        MyLog.v("WeatherRequest", "请求地址：" + url);
        RxHttp.get(url)
                .toObservable(BrandListModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(realTimeWeatherModel -> {
                    if (realTimeWeatherModel.getCode() == StatusCodeModel.SUCCESS) {
                        // 请求成功
                        MyLog.v(TAG, "请求成功" + realTimeWeatherModel);
                        //组装
                        for (int i = 0; i < realTimeWeatherModel.getRows().size(); i++) {
                            CityModel city = new CityModel(realTimeWeatherModel.getRows().get(i).getName(), realTimeWeatherModel.getRows().get(i).getId());
                            hotCitys.add(city);
                        }
                        citySelectView.bindData(hotCitys, null, null);
                    }
                    // 请求成功
                    MyLog.v(TAG, "请求成功" + realTimeWeatherModel);
                }, throwable -> {
                    // 请求失败

                    MyLog.v(TAG, "请求失败" + throwable.getMessage());
                });
    }

    /**
     * 通过id获取车型数据
     */
    public static void getCarModelData(String id) {
        String url = Const.CAR_MODEL + id;
        MyLog.v("WeatherRequest", "请求地址：" + url);
        RxHttp.get(url)
                .toObservable(VehicleListModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(realTimeWeatherModel -> {
                    if (realTimeWeatherModel.getCode() == StatusCodeModel.SUCCESS) {
                        isSelect = true;
                        citySelectView.setCancelText("上一级");
                        // 请求成功
                        MyLog.v(TAG, "请求成功" + realTimeWeatherModel);
                        //组装
                        List<CityModel> hotCitys = new ArrayList<>();
                        for (int i = 0; i < realTimeWeatherModel.getData().getVehicleModelList().size(); i++) {
                            CityModel city = new CityModel(realTimeWeatherModel.getData().getVehicleModelList().get(i).getName(), realTimeWeatherModel.getData().getVehicleModelList().get(i).getId());
                            hotCitys.add(city);
                        }
                        citySelectView.bindData(hotCitys, null, null);
                    }
                    // 请求成功
                    MyLog.v(TAG, "请求成功" + realTimeWeatherModel);
                }, throwable -> {
                    // 请求失败

                    MyLog.v(TAG, "请求失败" + throwable.getMessage());
                });
    }
}