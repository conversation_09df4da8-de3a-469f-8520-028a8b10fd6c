package com.smartcar.easylauncher.shared.utils;

import android.content.Context;
import android.net.ParseException;
import android.os.Build;
import android.util.Base64;


import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;
import java.util.UUID;


public class UniversalMusicUtils {
    private static UniversalMusicUtils Function;

    public UniversalMusicUtils() {
    }

    public static UniversalMusicUtils getFunction() {
        if (Function == null) {
            Function = new UniversalMusicUtils();
        }
        return Function;
    }


    public static String getUniquePsuedoID() {
        String str;
        try {
            str = "";
            for (String str2 : Build.VERSION.SDK_INT >= 21 ? Build.SUPPORTED_ABIS : new String[]{Build.CPU_ABI}) {
                str = str + str2;
            }
        } catch (Exception unused) {
            str = "";
        }
        String str3 = "35";
        if (Build.BOARD != null) {
            str3 = str3 + (Build.BOARD.length() % 10);
        }
        if (Build.BRAND != null) {
            str3 = str3 + (Build.BRAND.length() % 10);
        }
        if (str != null) {
            str3 = str3 + (str.length() % 10);
        }
        if (Build.DEVICE != null) {
            str3 = str3 + (Build.DEVICE.length() % 10);
        }
        if (Build.MANUFACTURER != null) {
            str3 = str3 + (Build.MANUFACTURER.length() % 10);
        }
        if (Build.MODEL != null) {
            str3 = str3 + (Build.MODEL.length() % 10);
        }
        if (Build.PRODUCT != null) {
            str3 = str3 + (Build.PRODUCT.length() % 10);
        }
        try {
            return new UUID((long) str3.hashCode(), (long) Build.class.getField("SERIAL").get((Object) null).toString().hashCode()).toString();
        } catch (Exception unused2) {
            return new UUID((long) str3.hashCode(), (long) ("" + Calendar.getInstance().getTimeInMillis()).hashCode()).toString();
        }
    }

    public static long dateToLong(Date date) {
        try {
            return date.getTime();
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    public static Date longToDate(long j, String str) throws ParseException {
        return stringToDate(dateToString(new Date(j), str), str);
    }

    public static Date stringToDate(String str, String str2) throws ParseException {
        try {
            return new SimpleDateFormat(str2).parse(str);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String dateToString(Date date, String str) {
        return new SimpleDateFormat(str).format(date);
    }


    public static class StreamTool {
        public static byte[] read(InputStream inputStream) throws Exception {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] bArr = new byte[1024];
            while (true) {
                int read = inputStream.read(bArr);
                if (read != -1) {
                    byteArrayOutputStream.write(bArr, 0, read);
                } else {
                    inputStream.close();
                    return byteArrayOutputStream.toByteArray();
                }
            }
        }
    }


    public static String getUrlCont(String str) throws Exception {
        HttpURLConnection httpURLConnection = (HttpURLConnection) new URL(str).openConnection();
        httpURLConnection.setConnectTimeout(5000);
        httpURLConnection.setRequestMethod("GET");
        if (httpURLConnection.getResponseCode() == 200) {
            return new String(StreamTool.read(httpURLConnection.getInputStream()), "UTF-8");
        }
        return null;
    }

//    public static String encodeBase64(String input) {
//        byte[] bytes = input.getBytes();
//        int flags = Base64.NO_WRAP | Base64.URL_SAFE;
//        return Base64.encodeToString(bytes, flags);
//    }
//
//    public static String decodeBase64(String input) {
//        byte[] decodedBytes = Base64.decode(input, Base64.NO_WRAP | Base64.URL_SAFE);
//        return new String(decodedBytes);
//    }

    public static String encodeBase64(String str) {
        if (str == null) {
            return null;
        }
        try {
            return new String(Base64.encode(str.getBytes(StandardCharsets.UTF_8), 0));
        } catch (Exception unused) {
            return null;
        }
    }

    public static String decodeBase64(String str) {
        if (str == null) {
            return null;
        }
        try {
            return new String(Base64.decode(str, 0), StandardCharsets.UTF_8);
        } catch (Exception unused) {
            return null;
        }
    }

    public static String getMD5(String str) {
        try {
            MessageDigest instance = MessageDigest.getInstance("MD5");
            instance.update(str.getBytes("UTF-8"));
            byte[] digest = instance.digest();
            StringBuffer stringBuffer = new StringBuffer();
            for (int i = 0; i < digest.length; i++) {
                if (Integer.toHexString(digest[i] & 255).length() == 1) {
                    stringBuffer.append("0");
                    stringBuffer.append(Integer.toHexString(digest[i] & 255));
                } else {
                    stringBuffer.append(Integer.toHexString(digest[i] & 255));
                }
            }
            return stringBuffer.toString();
        } catch (UnsupportedEncodingException | NoSuchAlgorithmException unused) {
            return "";
        }
    }

    public String getMusicLrc(String str, Context context) {
        //根据歌曲名字获取歌词
        // String strCont = getStrCont(getMD5(str), context);
        String strCont2 = "kugou.com";
//        if (!strCont.equals("")) {
//            return strCont;
//        }
        try {
            JSONObject jSONObject = new JSONObject(getUrlCont(getMusicId(new JSONObject(getUrlCont(getSeachApi(str, strCont2))).getJSONObject("data").getJSONArray("info").getJSONObject(0).getString("hash"), strCont2)));
            String decodeBase64 = decodeBase64(new JSONObject(getUrlCont(getMusicHash(jSONObject.getJSONArray("candidates").getJSONObject(0).getString("id"), jSONObject.getJSONArray("candidates").getJSONObject(0).getString("accesskey"), strCont2))).getString("content"));
            if (!"".equals(decodeBase64)) {
                //存储歌词
                // setStrCont(getMD5(str), decodeBase64, context);
            }
            return decodeBase64;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public String getMusicImg(String str, Context context) {
        //根据歌曲名字获取图片
        //  String strCont = getStrCont(getMD5(str + "IMG"), context);
        String strCont2 = "kugou.com";
//        if (!strCont.equals("")) {
//            return strCont;
//        }
        try {
            String replace = new JSONObject(getUrlCont(getMusicImgUrl(new JSONObject(getUrlCont(getSeachApi(str, strCont2))).getJSONObject("data").getJSONArray("info").getJSONObject(0).getString("hash"), strCont2))).getString("album_img").replace("{size}", "400");
            //存储图片
//            if (!replace.equals("")) {
//                setStrCont(getMD5(str + "IMG"), replace, context);
//            }
            return replace;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public String getSeachApi(String str, String str2) {
        return "http://mobilecdn." + str2 + "/api/v3/search/song?format=json&keyword=" + str + "&page=1&pagesize=20&showtype=1";
    }

    public String getMusicId(String str, String str2) {
        return "http://krcs." + str2 + "/search?ver=1&man=yes&client=mobi&keyword=&duration=&hash=" + str + "&album_audio_id=";
    }

    public String getMusicImgUrl(String str, String str2) {
        return "http://m." + str2 + "/app/i/getSongInfo.php?cmd=playInfo&hash=" + str;
    }

    public String getMusicHash(String str, String str2, String str3) {
        return "http://lyrics." + str3 + "/download?ver=1&client=pc&id=" + str + "&accesskey=" + str2 + "&fmt=lrc&charset=utf8";
    }


    public static String getTime(String str) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(str);
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT+08"));
        return simpleDateFormat.format(new Date(System.currentTimeMillis()));
    }


}
