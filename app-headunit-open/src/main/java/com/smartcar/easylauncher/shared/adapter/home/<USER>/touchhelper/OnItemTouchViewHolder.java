package com.smartcar.easylauncher.shared.adapter.home.channel.touchhelper;


import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.smartcar.easylauncher.core.base.BaseRecyclerViewAdapter;
import com.smartcar.easylauncher.data.model.common.Channel;


/**
 * 用于监听ItemTouchHelper相关操作的ViewHolder
 * <AUTHOR>
 * @date 2024/12/02
 */
public abstract class OnItemTouchViewHolder extends BaseRecyclerViewAdapter.BaseRecyclerViewHolder<Channel> {


    public OnItemTouchViewHolder(@NonNull View itemView) {
        super(itemView);
    }

    /**
     * 是否可拖拽
     * @return  true
     */
    public abstract boolean canDrag();


    /**
     * 选中的时候触发
     * @param viewHolder    ViewHolder
     */
    public abstract void onItemSelected(RecyclerView.ViewHolder viewHolder);

    /**
     * 拖拽释放的时候触发
     * @param viewHolder    ViewHolder
     */
    public abstract void onItemClear(RecyclerView.ViewHolder viewHolder);
}
