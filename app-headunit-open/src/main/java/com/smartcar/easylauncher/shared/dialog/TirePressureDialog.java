package com.smartcar.easylauncher.shared.dialog;

import android.content.Context;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.dialog.flexible.DialogHelper;
import com.smartcar.easylauncher.shared.dialog.flexible.FlexibleDialog;
import com.smartcar.easylauncher.shared.dialog.flexible.FlexibleDialogBuilder;

public class TirePressureDialog {

    public static void showTirePressureDialog(Context context, String title, String tirePosition,
                                              String pressure, String description, OnDialogClickListener listener) {
        showTirePressureDialog(context, title, tirePosition, pressure, description, false,
                context.getString(R.string.confirm), context.getString(R.string.cancel), true, true, listener);
    }

    public static void showTirePressureDialogWithCancel(Context context, String title, String tirePosition,
                                                        String pressure, String description, OnDialogClickListener listener) {
        showTirePressureDialog(context, title, tirePosition, pressure, description, true,
                context.getString(R.string.confirm), context.getString(R.string.cancel), true, true, listener);
    }

    public static void showTirePressureDialog(Context context, String title, String tirePosition,
                                              String pressure, String description, boolean showCancelButton,
                                              String okText, String cancelText, boolean keystroke, boolean touch,
                                              OnDialogClickListener listener) {
        if (context == null || listener == null) {
            throw new IllegalArgumentException("Context and listener cannot be null");
        }

        FlexibleDialog dialog = new FlexibleDialogBuilder(context)
                .setLayout(R.layout.dialog_tire_pressure)
                .setCancelable(keystroke)
                .setCanceledOnTouchOutside(touch)
                .setAnimation(com.smart.easy.permissions.R.style.notAnimation)
                .setWindowType(DialogHelper.WINDOW_TYPE_APPLICATION_OVERLAY)
                .addOnClickListener(R.id.okButton, R.id.cancelButton)
                .setViewBinder((view, dialogInstance) -> {
                    TextView titleCombinedTextView = view.findViewById(R.id.tv_title_combined);
                    TextView pressureCombinedTextView = view.findViewById(R.id.tv_pressure_combined);
                    TextView descriptionTextView = view.findViewById(R.id.tv_description);
                    Button okButton = view.findViewById(R.id.okButton);
                    Button cancelButton = view.findViewById(R.id.cancelButton);

                    // 标题和副标题合并
                    StringBuilder titleBuilder = new StringBuilder();
                    if (title != null && !title.isEmpty()) {
                        titleBuilder.append(title);
                    }
                    if (tirePosition != null && !tirePosition.isEmpty()) {
                        if (titleBuilder.length() > 0) titleBuilder.append(" "); // 添加空格分隔
                        titleBuilder.append(tirePosition);
                    }
                    titleCombinedTextView.setText(titleBuilder.toString());

                    // 胎压数值和单位合并
                    if (pressure != null && !pressure.isEmpty()) {
                        // 假设单位总是 bar，如果单位可能变化，需要更灵活的处理
                        pressureCombinedTextView.setText("当前胎压：" + pressure + " bar");
                        pressureCombinedTextView.setVisibility(View.VISIBLE);
                    } else {
                        pressureCombinedTextView.setVisibility(View.GONE);
                    }

                    if (description != null && !description.isEmpty()) {
                        descriptionTextView.setText(description);
                    }

                    okButton.setText(okText);
                    cancelButton.setText(cancelText);

                    if (showCancelButton) {
                        cancelButton.setVisibility(View.VISIBLE);
                    } else {
                        cancelButton.setVisibility(View.GONE);
                    }
                }).setOnViewClickListener((view, dialogInstance) -> {
                    switch (view.getId()) {
                        case R.id.okButton:
                            if (listener != null) {
                                listener.onOkButtonClick(dialogInstance);
                            }
                            dialogInstance.dismiss();
                            break;
                        case R.id.cancelButton:
                            if (listener != null) {
                                listener.onCancelButtonClick(dialogInstance);
                            }
                            dialogInstance.dismiss();
                            break;
                        default:
                            break;
                    }
                })
                .create();

        dialog.show();
    }

    public interface OnDialogClickListener {
        void onOkButtonClick(FlexibleDialog dialog);

        default void onCancelButtonClick(FlexibleDialog dialog) {
            // 默认空实现
        }
    }
}