package com.smartcar.easylauncher.shared.adapter.personal;


import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.data.model.user.PersonalBannerModel;
import com.zhpan.bannerview.BaseBannerAdapter;
import com.zhpan.bannerview.BaseViewHolder;

public class PersonalBannerAdapter extends BaseBannerAdapter<PersonalBannerModel.RowsDTO> {


    @Override
    protected void bindData(BaseViewHolder<PersonalBannerModel.RowsDTO> baseViewHolder, PersonalBannerModel.RowsDTO item, int i, int i1) {
        ImageView banner = baseViewHolder.findViewById(R.id.banner_image);
        Glide.with(baseViewHolder.itemView).load(Const.DEV_BASEURL + item.getImgPath()).into(banner);
    }

    @Override
    public int getLayoutId(int i) {
        return R.layout.item_slide_mode;
    }
}
