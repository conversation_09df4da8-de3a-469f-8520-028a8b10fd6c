package com.smartcar.easylauncher.shared.dialog;

import android.app.Activity;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;

import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bumptech.glide.Glide;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.TaskConditionConst;
import com.smartcar.easylauncher.data.model.task.TaskOptionModel;
import com.timmy.tdialog.TDialog;
import com.timmy.tdialog.base.BindViewHolder;
import com.timmy.tdialog.base.TBaseAdapter;
import com.timmy.tdialog.list.TListDialog;
import com.timmy.tdialog.listener.OnBindViewListener;
import com.timmy.tdialog.listener.OnViewClickListener;

import java.util.List;

public class OptionBottomListDialog {

    private Context context;
    private List<TaskOptionModel> data;
    private OnItemSelectedListener listener;

    public OptionBottomListDialog(Context context) {
        this.context = context;
    }

    public OptionBottomListDialog setData(List<TaskOptionModel> data) {
        this.data = data;
        return this;
    }

    public OptionBottomListDialog setOnItemSelectedListener(OnItemSelectedListener listener) {
        this.listener = listener;
        return this;
    }

    public void show() {
        new TListDialog.Builder(((FragmentActivity) context).getSupportFragmentManager())
                .setListLayoutRes(R.layout.dialog_share_recycler, LinearLayoutManager.HORIZONTAL)
                .setLayoutManager(new GridLayoutManager(context, 4))
                .setScreenHeightAspect((Activity) context, 1f)
                .setScreenWidthAspect((Activity) context, 1f)
                .setGravity(Gravity.BOTTOM)
                .addOnClickListener(R.id.iv_grzx)
                .setOnBindViewListener(new OnBindViewListener() {
                    @Override
                    public void bindView(BindViewHolder viewHolder) {
                        viewHolder.setText(R.id.tv_title, "如果");
                    }
                })
                .setOnViewClickListener(new OnViewClickListener() {
                    @Override
                    public void onViewClick(BindViewHolder viewHolder, View view, TDialog tDialog) {
                        switch (view.getId()) {
                            case R.id.iv_grzx:
                                tDialog.dismiss();
                                break;
                            default:
                        }
                    }
                })
                .setAdapter(new TBaseAdapter<TaskOptionModel>(R.layout.item_simple_text, data) {
                    @Override
                    protected void onBind(BindViewHolder holder, int position, TaskOptionModel s) {
                        holder.setText(R.id.tv_name, s.getName());
                        switch (s.getId()) {
                            case TaskConditionConst.TYPE_WIFI_SWITCH:
                                //holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_wifi);
                                Glide.with(context).load(R.drawable.ic_task_wifi).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskConditionConst.TYPE_NETWORK_STATE:
                                // holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_wangluozhuangt);
                                Glide.with(context).load(R.drawable.ic_task_wangluozhuangt).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskConditionConst.TYPE_BLUETOOTH_SWITCH:
                                // holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_ble_switch);
                                Glide.with(context).load(R.drawable.ic_task_ble_switch).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskConditionConst.TYPE_BLUETOOTH_DEVICE:
                                //  holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_ble);
                                Glide.with(context).load(R.drawable.ic_task_ble).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskConditionConst.TYPE_DEVICE_START:
                                //  holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_kaiji);
                                Glide.with(context).load(R.drawable.ic_task_kaiji).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskConditionConst.TYPE_START:
                                //  holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_qidong);
                                Glide.with(context).load(R.drawable.ic_task_qidong).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskConditionConst.TYPE_MUSIC_STATE:
                                //  holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_yinyue);
                                Glide.with(context).load(R.drawable.ic_task_yinyue).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskConditionConst.TYPE_SUN:
                                // holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_richu);
                                Glide.with(context).load(R.drawable.ic_task_richu).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskConditionConst.TYPE_SPEED:
                                //  holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_chesu);
                                Glide.with(context).load(R.drawable.ic_task_chesu).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskConditionConst.TYPE_GEO_FENCE:
                                //  holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_diliweilan);
                                Glide.with(context).load(R.drawable.ic_task_diliweilan).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskConditionConst.TYPE_TIME_RANGE:
                                // holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_shijianfanwei);
                                Glide.with(context).load(R.drawable.ic_task_shijianfanwei).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskConditionConst.TYPE_TIMING:
                                //  holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_dingshi);
                                Glide.with(context).load(R.drawable.ic_task_dingshi).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskConditionConst.TYPE_PERIOD:
                                //  holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_zhouqi);
                                Glide.with(context).load(R.drawable.ic_task_zhouqi).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            case TaskConditionConst.TYPE_SCREEN:
                                //  holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_riqi);
                                Glide.with(context).load(R.drawable.ic_task_screen).into((ImageView) holder.getView(R.id.iv_icon));
                                break;
                            default:
                        }
                    }
                })
                .setOnAdapterItemClickListener(new TBaseAdapter.OnAdapterItemClickListener<TaskOptionModel>() {
                    @Override
                    public void onItemClick(BindViewHolder holder, int position, TaskOptionModel s, TDialog tDialog) {
                        if (listener != null) {
                            listener.onItemSelected(position, s);
                        }
                        tDialog.dismiss();
                    }
                })
                .create()
                .show();
    }


    public interface OnItemSelectedListener {
        void onItemSelected(int position, TaskOptionModel item);
    }
}