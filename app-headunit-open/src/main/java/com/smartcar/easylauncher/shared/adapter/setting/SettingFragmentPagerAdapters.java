package com.smartcar.easylauncher.shared.adapter.setting;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.viewpager2.adapter.FragmentStateAdapter;

/**
 * ViewPager2的Fragment适配器
 */
public class SettingFragmentPagerAdapters extends FragmentStateAdapter {
    private final Class<? extends Fragment>[] fragmentClasses;

    public SettingFragmentPagerAdapters(@NonNull FragmentManager fragmentManager,
                                       @NonNull Lifecycle lifecycle,
                                       Class<? extends Fragment>[] fragmentClasses) {
        super(fragmentManager, lifecycle);
        this.fragmentClasses = fragmentClasses;
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        try {
            return fragmentClasses[position].newInstance();
        } catch (Exception e) {
            throw new RuntimeException("Cannot instantiate fragment", e);
        }
    }

    @Override
    public int getItemCount() {
        return fragmentClasses.length;
    }
}