package com.smartcar.easylauncher.shared.adapter.common;

import android.annotation.SuppressLint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.data.model.common.MenuModel;

import java.util.List;

public class MenuAdapter extends RecyclerView.Adapter<MenuAdapter.MenuViewHolder> {
    private List<MenuModel> menuList;
    private MenuItemClickListener itemClickListener;

    public MenuAdapter(List<MenuModel> menuList, MenuItemClickListener itemClickListener) {
        this.menuList = menuList;
        this.itemClickListener = itemClickListener;
    }

    @NonNull
    @Override
    public MenuViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_layout, parent, false);
        return new MenuViewHolder(view);
    }

    @Override
    public void onBindViewHolder(MenuViewHolder holder, @SuppressLint("RecyclerView") final int position) {

        holder.menuTextView.setText(menuList.get(position).getMenuName());
        holder.iconImageView.setImageResource(menuList.get(position).getIcon());
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (itemClickListener != null) {
                    MenuModel menuItem = menuList.get(position);
                    itemClickListener.onMenuItemClick(position, menuItem);
                }
            }
        });
    }

    public MenuModel getMenuItem(int position) {
        return menuList.get(position);
    }

    @Override
    public int getItemCount() {
        return menuList.size();
    }

    static class MenuViewHolder extends RecyclerView.ViewHolder {

        TextView menuTextView;
        ImageView iconImageView;

        MenuViewHolder(@NonNull View itemView) {
            super(itemView);
            menuTextView = itemView.findViewById(R.id.text_view);
            iconImageView = itemView.findViewById(R.id.iv_icon);
        }
    }

    public interface MenuItemClickListener {

        void onMenuItemClick(int position, MenuModel menuItem);
    }
}
