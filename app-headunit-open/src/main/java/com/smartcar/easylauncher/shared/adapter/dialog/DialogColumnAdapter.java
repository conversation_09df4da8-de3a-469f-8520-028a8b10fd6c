package com.smartcar.easylauncher.shared.adapter.dialog;

import android.annotation.SuppressLint;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.smartcar.easylauncher.data.model.system.AppInfo;
import com.timmy.tdialog.TDialog;
import com.timmy.tdialog.base.BindViewHolder;
import com.timmy.tdialog.base.TBaseAdapter;

import java.util.List;

/**
 * @param <T>
 * <AUTHOR>
 */
public class DialogColumnAdapter<T> extends TBaseAdapter<AppInfo> {

    private final int layoutRes;
    private List<AppInfo> datas;

    private TDialog dialog;
    private int choosePosition = 0;
    private OnAdapterItemClickListener adapterItemClickListener;


    public DialogColumnAdapter(int layoutRes, List<AppInfo> datas) {
        super(layoutRes, datas);
        this.layoutRes = layoutRes;
        this.datas = datas;
    }

    @NonNull
    @Override
    public BindViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new BindViewHolder(LayoutInflater.from(parent.getContext()).inflate(layoutRes, parent, false));
    }


    @Override
    protected void onBind(BindViewHolder holder, int position, AppInfo appInfo) {

    }

    @Override
    public void onBindViewHolder(final BindViewHolder holder, @SuppressLint("RecyclerView") final int position) {
        if (position < 0 || position >= datas.size()) {
            return;
        }

        AppInfo info = datas.get(position);
        // 确保每次绑定时都更新视图状态
        onBind(holder, position, info);

        // 更新视图的选中状态
        holder.itemView.setSelected(info.isSelect());

        holder.itemView.setOnClickListener(v -> {
            boolean wasSelected = info.isSelect();
            info.setSelect(!wasSelected);

            if (choosePosition != position && choosePosition != -1) {
                datas.get(choosePosition).setSelect(false);
                notifyItemChanged(choosePosition);
            }

            choosePosition = position;
            notifyItemChanged(position);

            if (adapterItemClickListener != null) {
                adapterItemClickListener.onItemClick(holder, position, datas.get(position), dialog);
            }
        });
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }

    @Override
    public void setTDialog(TDialog tDialog) {
        this.dialog = tDialog;
    }

    @Override
    public void setOnAdapterItemClickListener(OnAdapterItemClickListener listener) {
        this.adapterItemClickListener = listener;
        super.setOnAdapterItemClickListener(listener);
    }

    /**
     * 新增的 setData 方法
     */
    public void setData(List<T> newData) {
        this.datas = (List<AppInfo>) newData;
        choosePosition = -1; // 重置选择位置
        notifyDataSetChanged(); // 确保数据更新后刷新视图
    }
}
