package com.smartcar.easylauncher.shared.adapter.home.channel.touchhelper;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * 拖拽Item的HelperCallback实现
 *
 * <AUTHOR>
 */
public class DragItemHelperCallback extends BaseItemHelperCallback {

    /**
     * 构造函数，初始化DragItemHelperCallback
     *
     * @param listener 触摸辅助监听器
     */
    public DragItemHelperCallback(OnItemTouchHelperListener listener) {
        super(listener);
    }

    /**
     * 是否支持长按拖拽
     *
     * @return 不支持长按拖拽，返回false
     */
    @Override
    public boolean isLongPressDragEnabled() {
        return false;
    }

    /**
     * 是否支持滑动删除
     *
     * @return 不支持滑动删除，返回false
     */
    @Override
    public boolean isItemViewSwipeEnabled() {
        return false;
    }

    /**
     * 获取移动标志位
     *
     * @param recyclerView RecyclerView实例
     * @param holder       ViewHolder实例
     * @return 返回拖拽和滑动的标志位
     */
    @Override
    public int getMovementFlags(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder holder) {
        // 记录日志，记录调用情况
        MyLog.v("JACK8", "getMovementFlags() called with: recyclerView = [" + recyclerView + "], holder = [" + holder + "]");
        int dragFlag = 0;
        int swipeFlag = 0;
        RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
        // 根据LayoutManager类型设置拖拽方向
        if (layoutManager instanceof GridLayoutManager) {
            dragFlag = ItemTouchHelper.LEFT | ItemTouchHelper.RIGHT | ItemTouchHelper.UP | ItemTouchHelper.DOWN;
        } else if (layoutManager instanceof LinearLayoutManager) {
            dragFlag = ItemTouchHelper.UP | ItemTouchHelper.DOWN;
        }
        // 返回拖拽和滑动的标志位
        return makeMovementFlags(dragFlag, swipeFlag);
    }

    /**
     * 处理Item的移动
     *
     * @param recyclerView       RecyclerView实例
     * @param srcViewHolder      源ViewHolder
     * @param targetViewHolder   目标ViewHolder
     * @return 是否成功移动Item，成功返回true，否则返回false
     */
    @Override
    public boolean onMove(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder srcViewHolder, @NonNull RecyclerView.ViewHolder targetViewHolder) {
        // 记录日志，记录调用情况
        MyLog.v("JACK8", "onMove() called with: recyclerView = [" + recyclerView + "], srcViewHolder = [" + srcViewHolder + "], targetViewHolder = [" + targetViewHolder + "]");
        // 检查Item类型是否一致
        if (srcViewHolder.getItemViewType() != targetViewHolder.getItemViewType()) {
            return false;
        }
        // 检查ViewHolder类型是否正确
        if (!(srcViewHolder instanceof OnItemTouchViewHolder) || !(targetViewHolder instanceof OnItemTouchViewHolder)) {
            return false;
        }
        // 检查ViewHolder是否支持拖拽
        if (!((OnItemTouchViewHolder) srcViewHolder).canDrag() || !((OnItemTouchViewHolder) targetViewHolder).canDrag()) {
            return false;
        }
        // 通知监听器Item被移动，并返回true表示移动成功
        if (listener != null) {
            listener.onItemMove(srcViewHolder.getAdapterPosition(), targetViewHolder.getAdapterPosition());
            return true;
        }
        // 监听器为空，返回false表示移动失败
        return false;
    }

    /**
     * 处理Item的滑动删除（当前类未实现该功能）
     *
     * @param holder   ViewHolder实例
     * @param position 被滑动的Item位置
     */
    @Override
    public void onSwiped(@NonNull RecyclerView.ViewHolder holder, int position) {
        // 当前类未实现滑动删除功能，可按需扩展
    }
}

