# 更新系统测试指南

## 🔧 编译错误修复

### ✅ 已解决的问题
- **编译错误**: `找不到符号 showUpdateDialog(ResultsDTO)` 
- **解决方案**: 添加了 `showLegacyUpdateDialog` 方法来处理私有云更新显示

### 🔄 更新后的逻辑流程
```
1. 主要更新检查 (新API)
   ↓ 失败时
2. 备用更新检查 (私有云)
   ↓ 发现更新时
3. 显示更新对话框 (showLegacyUpdateDialog)
   ↓ 用户确认后
4. 直接下载安装包
```

## 🌐 测试环境配置

### 当前配置
- **测试服务器地址**: `http://*************:1025`
- **新API接口**: `/system/appVersion/checkUpdate`
- **完整URL**: `http://*************:1025/system/appVersion/checkUpdate`

### 测试参数
```
platform: android
currentVersionCode: 15 (当前版本)
channelType: public/byd/custom
versionType: alpha/beta/release (新增)
```

## 🧪 测试步骤

### 1. 新API测试
```bash
# 测试新接口是否正常响应
curl "http://*************:1025/system/appVersion/checkUpdate?platform=android&currentVersionCode=15&channelType=public&versionType=alpha"
```

**期望响应 (有更新)**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "platform": "android",
    "versionCode": 16,
    "versionName": "1.4.9",
    "title": "新版本发布",
    "description": "• 新增功能\n• 修复问题\n• 性能优化",
    "realDownloadUrl": "http://example.com/app.apk",
    "fileName": "SmartCar-1.4.9.apk",
    "fileSize": 25600000,
    "fileMd5": "d41d8cd98f00b204e9800998ecf8427e",
    "isForceUpdate": 0,
    "channelType": "public"
  }
}
```

**期望响应 (无更新)**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

### 2. 应用内测试

#### 测试场景1: 新API正常工作
1. 启动应用
2. 触发更新检查
3. 验证是否调用新API
4. 检查更新对话框显示
5. 测试下载功能

#### 测试场景2: 新API失败回退
1. 断开新API服务器连接
2. 触发更新检查
3. 验证是否自动回退到私有云检查
4. 检查私有云更新逻辑

#### 测试场景3: 渠道和版本类型匹配
1. 测试不同渠道版本 (public/byd)
2. 测试不同版本类型 (alpha/beta/release)
3. 验证参数传递正确
4. 确认返回对应渠道和版本类型的版本

## 📱 UI测试要点

### 新版本对话框
- [ ] 版本信息显示正确
- [ ] 文件大小格式化显示
- [ ] 下载进度正常
- [ ] 安装功能正常

### 私有云对话框 (备用)
- [ ] 兼容旧版本信息格式
- [ ] 下载功能正常
- [ ] 错误处理正确

## 🐛 常见问题排查

### 网络请求失败
```
检查点:
1. 服务器地址是否正确
2. 网络连接是否正常
3. 接口路径是否正确
4. 参数格式是否正确
```

### 解析错误
```
检查点:
1. 返回数据格式是否符合模型
2. JSON解析是否正常
3. 必填字段是否存在
```

### 下载失败
```
检查点:
1. 下载链接是否有效
2. 文件权限是否正确
3. 存储空间是否足够
```

## 📊 测试记录模板

### 测试环境
- 设备型号: ___________
- Android版本: ___________
- 应用版本: ___________
- 网络环境: ___________

### 测试结果
- [ ] 新API调用成功
- [ ] 更新检查正常
- [ ] 对话框显示正确
- [ ] 下载功能正常
- [ ] 安装功能正常
- [ ] 错误处理正确
- [ ] 备用机制正常

### 问题记录
```
问题描述:
复现步骤:
期望结果:
实际结果:
```

## 🔄 回滚方案

如果新系统出现问题，可以快速回滚:

1. **临时回滚**: 注释新API调用，直接使用私有云检查
2. **完全回滚**: 恢复蒲公英SDK (需要重新添加依赖)

## 📞 联系方式

如有问题请联系开发团队进行支持。
