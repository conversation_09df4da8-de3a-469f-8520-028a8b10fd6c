package com.smartcar.easylauncher.shared.adapter.personal;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.BaseSingleItemAdapter;
import com.chad.library.adapter4.fullspan.FullSpanAdapterType;
import com.smartcar.easylauncher.R;

import java.util.ArrayList;
import java.util.List;

/**
 * 车型选择头部适配器
 * 使用 BaseSingleItemAdapter 实现单个头部视图
 * 
 * <AUTHOR>
 * @date 2024/07/19
 */
public class CarModelHeaderAdapter extends BaseSingleItemAdapter<CarModelHeaderAdapter.CarModelHeader, CarModelHeaderAdapter.ViewHolder> implements FullSpanAdapterType {
    
    // 视图类型常量
    public static final int HEAD_VIEW_TYPE = 0x10000557;
    
    // 车型选择监听器
    private OnCarModelSelectedListener listener;
    
    // 是否折叠状态
    private boolean isCollapsed = false;
    
    /**
     * 车型选择监听器接口
     */
    public interface OnCarModelSelectedListener {
        /**
         * 当选择车型时调用
         * 
         * @param carModel 车型ID
         * @param position 位置
         */
        void onCarModelSelected(String carModel, int position);
    }
    
    /**
     * 设置车型选择监听器
     * 
     * @param listener 监听器
     */
    public void setOnCarModelSelectedListener(OnCarModelSelectedListener listener) {
        this.listener = listener;
    }
    
    /**
     * 车型头部数据类
     */
    public static class CarModelHeader {
        private List<CarModelItem> carModels;
        private int selectedPosition;
        
        public CarModelHeader(List<CarModelItem> carModels, int selectedPosition) {
            this.carModels = carModels;
            this.selectedPosition = selectedPosition;
        }
        
        public List<CarModelItem> getCarModels() {
            return carModels != null ? carModels : new ArrayList<>();
        }
        
        public int getSelectedPosition() {
            return selectedPosition;
        }
        
        public void setSelectedPosition(int selectedPosition) {
            this.selectedPosition = selectedPosition;
        }
    }
    
    /**
     * 车型数据项
     */
    public static class CarModelItem {
        private String id;
        private String name;
        private int iconResId;
        
        public CarModelItem(String id, String name, int iconResId) {
            this.id = id;
            this.name = name;
            this.iconResId = iconResId;
        }
        
        public String getId() {
            return id;
        }
        
        public String getName() {
            return name;
        }
        
        public int getIconResId() {
            return iconResId;
        }
    }
    
    /**
     * 设置折叠状态
     * 
     * @param collapsed 是否折叠
     */
    public void setCollapsed(boolean collapsed) {
        if (this.isCollapsed != collapsed) {
            this.isCollapsed = collapsed;
            notifyItemChanged(0);
        }
    }
    
    /**
     * 更新选中的车型
     * 
     * @param position 选中的位置
     */
    public void updateSelectedPosition(int position) {
        CarModelHeader header = getItem();
        if (header != null) {
            header.setSelectedPosition(position);
            notifyItemChanged(0);
        }
    }
    
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_car_model_header, parent, false);
        return new ViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, @Nullable CarModelHeader item) {
        if (item == null) return;
        
        // 设置折叠状态
        holder.headerContainer.setVisibility(isCollapsed ? View.GONE : View.VISIBLE);
        
        // 设置车型列表
        LinearLayoutManager layoutManager = new LinearLayoutManager(holder.itemView.getContext(), LinearLayoutManager.HORIZONTAL, false);
        holder.rvCarModels.setLayoutManager(layoutManager);
        
        // 创建内部适配器
        InnerCarModelAdapter adapter = new InnerCarModelAdapter(item.getCarModels(), item.getSelectedPosition());
        holder.rvCarModels.setAdapter(adapter);
        
        // 设置点击事件
        adapter.setOnCarModelClickListener((carModel, position) -> {
            if (listener != null) {
                listener.onCarModelSelected(carModel, position);
            }
        });
    }
    
    @Override
    public int getItemViewType(int position, @Nullable List<? extends Object> list) {
        return HEAD_VIEW_TYPE;
    }
    
    /**
     * 视图持有者
     */
    public static class ViewHolder extends RecyclerView.ViewHolder {
        RecyclerView rvCarModels;
        View headerContainer;
        
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            rvCarModels = itemView.findViewById(R.id.rv_car_models);
            headerContainer = itemView.findViewById(R.id.header_container);
        }
    }
    
    /**
     * 内部车型适配器
     */
    private static class InnerCarModelAdapter extends RecyclerView.Adapter<InnerCarModelAdapter.InnerViewHolder> {
        private List<CarModelItem> models;
        private int selectedPosition = 0;
        private OnCarModelClickListener listener;
        
        public interface OnCarModelClickListener {
            void onCarModelClick(String carModel, int position);
        }
        
        public void setOnCarModelClickListener(OnCarModelClickListener listener) {
            this.listener = listener;
        }
        
        public InnerCarModelAdapter(List<CarModelItem> models, int selectedPosition) {
            this.models = models;
            this.selectedPosition = selectedPosition;
        }
        
        @NonNull
        @Override
        public InnerViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_car_model_grid, parent, false);
            return new InnerViewHolder(view);
        }
        
        @Override
        public void onBindViewHolder(@NonNull InnerViewHolder holder, int position) {
            CarModelItem model = models.get(position);
            
            // 设置车型名称和图标
            holder.tvCarName.setText(model.getName());
            holder.ivCarIcon.setImageResource(model.getIconResId());
            
            // 设置选中状态
            boolean isSelected = position == selectedPosition;
            holder.selectedIndicator.setVisibility(isSelected ? View.VISIBLE : View.GONE);
            
            // 设置点击事件
            holder.itemView.setOnClickListener(v -> {
                if (position != selectedPosition && listener != null) {
                    listener.onCarModelClick(model.getId(), position);
                }
            });
        }
        
        @Override
        public int getItemCount() {
            return models != null ? models.size() : 0;
        }
        
        static class InnerViewHolder extends RecyclerView.ViewHolder {
            ImageView ivCarIcon;
            TextView tvCarName;
            View selectedIndicator;
            
            public InnerViewHolder(@NonNull View itemView) {
                super(itemView);
                ivCarIcon = itemView.findViewById(R.id.iv_car_icon);
                tvCarName = itemView.findViewById(R.id.tv_car_name);
                selectedIndicator = itemView.findViewById(R.id.selected_indicator);
            }
        }
    }
} 