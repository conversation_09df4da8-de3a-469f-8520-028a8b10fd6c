package com.smartcar.easylauncher.shared.widget.layout.map;

import android.view.View;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.fragment.app.FragmentActivity;

import com.smartcar.easylauncher.core.constants.SettingsConstants;
import com.smartcar.easylauncher.databinding.FragmentMapHomeBinding;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.shared.view.pagergridlayoutmanager.PagerGridLayoutManager;

/**
 * 首页地图布局3实现
 * 布局3配置：顶部状态栏 + 左侧导航栏 + 中间内容区域 + 底部卡片列表
 * 适用场景：横向卡片布局，导航栏位于左侧
 */
public class HomeMapLayout3 extends AbstractHomeMapLayout {

    private static final String TAG = "HomeMapLayout3";

    /**
     * 构造函数
     *
     * @param binding 数据绑定对象
     * @param activity Fragment所属的Activity
     * @param pagerLayoutManager RecyclerView的布局管理器
     */
    public HomeMapLayout3(FragmentMapHomeBinding binding,
                         FragmentActivity activity,
                         PagerGridLayoutManager pagerLayoutManager) {
        super(binding, activity, pagerLayoutManager);
    }

    @Override
    public void applyLayout(ConstraintSet constraintSet) {
        // 获取状态栏和导航栏的显示状态
        boolean statusBarVisible = SettingsManager.getStatusBarShow();
        boolean navigationBarVisible = SettingsManager.getNavigationBarShow();

        // 配置状态栏
        if (statusBarVisible) {
            configureStatusBar(constraintSet,
                    ConstraintSet.MATCH_CONSTRAINT,  // 宽度
                    dimensionCache.statusBarHeight40,  // 高度
                    0,  // 顶部边距
                    0,  // 右边距
                    ConstraintSet.MATCH_CONSTRAINT  // 对齐方式
            );
        } else {
            // 状态栏不显示时，隐藏并清除约束
            hideStatusBar(constraintSet);
        }

        // 配置导航栏（左侧垂直布局）
        if (navigationBarVisible) {
            configureNavigationBarVertical(constraintSet);
        } else {
            // 导航栏不显示时，隐藏并清除约束
            hideNavigationBar(constraintSet);
        }

        // 配置主内容区域
        configureFrameLayoutForHorizontalCards(constraintSet);

        // 配置RecyclerView（底部横向布局）
        configureRecyclerViewHorizontal(constraintSet);

        // 使用增强的方法设置RecyclerView为横向布局
        setupHorizontalRecyclerView();

    }

    @Override
    public String getLayoutType() {
        return SettingsConstants.DEFAULT_MAP_LAYOUT_3;
    }
    
    /**
     * 配置垂直导航栏
     */
    private void configureNavigationBarVertical(ConstraintSet constraintSet) {
        constraintSet.clear(binding.maiIconBar.getId());
        constraintSet.constrainWidth(binding.maiIconBar.getId(), dimensionCache.navigationBarWidth);
        constraintSet.constrainHeight(binding.maiIconBar.getId(), ConstraintSet.MATCH_CONSTRAINT);

        // 设置顶部约束
        if (SettingsManager.getStatusBarShow()) {
            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.TOP,
                    binding.mainLayoutStatusBar.getId(), ConstraintSet.BOTTOM, 0);
        } else {
            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID, ConstraintSet.TOP, dimensionCache.margin5);
        }

        constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.START,
                ConstraintSet.PARENT_ID, ConstraintSet.START, dimensionCache.margin5);
        constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.BOTTOM,
                ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, dimensionCache.margin5);

        // 设置导航栏可见性
        binding.maiIconBar.setVisibility(
                SettingsManager.getNavigationBarShow() ? View.VISIBLE : View.GONE);
    }
    
    /**
     * 配置主内容区域FrameLayout（适用于横向卡片布局）
     */
    private void configureFrameLayoutForHorizontalCards(ConstraintSet constraintSet) {
        constraintSet.clear(binding.frameLayout.getId());
        constraintSet.constrainWidth(binding.frameLayout.getId(), ConstraintSet.MATCH_CONSTRAINT);
        constraintSet.constrainHeight(binding.frameLayout.getId(), ConstraintSet.MATCH_CONSTRAINT);

        // 设置顶部约束
        if (SettingsManager.getStatusBarShow()) {
            constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.TOP,
                    binding.mainLayoutStatusBar.getId(), ConstraintSet.BOTTOM, 0);
        } else {
            constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID, ConstraintSet.TOP, dimensionCache.margin5);
        }

        // 设置水平约束 - 左侧与导航栏对齐（如果显示）
        if (SettingsManager.getNavigationBarShow()) {
            constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.START,
                    binding.maiIconBar.getId(), ConstraintSet.END, dimensionCache.margin5);
        } else {
            constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.START,
                    ConstraintSet.PARENT_ID, ConstraintSet.START, dimensionCache.margin5);
        }
        
        // 右侧与父容器对齐
        constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.END,
                ConstraintSet.PARENT_ID, ConstraintSet.END, dimensionCache.margin5);

        // 设置底部约束 - 与RecyclerView顶部对齐
        constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.BOTTOM,
                binding.rvHome.getId(), ConstraintSet.TOP, dimensionCache.margin5);
    }
    
    /**
     * 配置RecyclerView（横向布局）
     */
    private void configureRecyclerViewHorizontal(ConstraintSet constraintSet) {
        constraintSet.clear(binding.rvHome.getId());
        constraintSet.constrainWidth(binding.rvHome.getId(), ConstraintSet.MATCH_CONSTRAINT);
        constraintSet.constrainHeight(binding.rvHome.getId(), dimensionCache.mapCardHeight);

        // 设置水平约束 - 左侧与导航栏对齐（如果显示）
        if (SettingsManager.getNavigationBarShow()) {
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.START,
                    binding.maiIconBar.getId(), ConstraintSet.END, dimensionCache.getNavigationBarMargin());
        } else {
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.START,
                    ConstraintSet.PARENT_ID, ConstraintSet.START, dimensionCache.margin5);
        }
        
        // 右侧与父容器对齐
        constraintSet.connect(binding.rvHome.getId(), ConstraintSet.END,
                ConstraintSet.PARENT_ID, ConstraintSet.END, dimensionCache.margin5);
                
        // 底部与父容器对齐
        constraintSet.connect(binding.rvHome.getId(), ConstraintSet.BOTTOM,
                ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, dimensionCache.margin5);
    }
} 