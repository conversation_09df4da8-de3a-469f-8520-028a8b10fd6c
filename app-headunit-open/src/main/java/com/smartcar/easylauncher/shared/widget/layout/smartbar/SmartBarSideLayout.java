package com.smartcar.easylauncher.shared.widget.layout.smartbar;

import androidx.constraintlayout.widget.ConstraintSet;
import androidx.fragment.app.FragmentActivity;

import com.smartcar.easylauncher.databinding.FragmentSmartBarBinding;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.ui.ScreenUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 智能导航栏侧边布局实现（优化版）
 * 适用于卡片布局3、地图布局3、地图布局4
 *
 * 布局特点：
 * - 侧边导航栏
 * - 不支持音乐控制组件
 * - 应用图标垂直排列
 * - 根据屏幕类型动态显示图标数量
 *
 * 优化内容：
 * - 简化约束配置逻辑
 * - 提升性能（缓存计算结果）
 * - 提高代码稳定性
 * - 改善可维护性
 *
 * <AUTHOR>
 * @since 2024-12-18
 * @version 2.0 优化版
 */
public class SmartBarSideLayout extends AbstractSmartBarLayout {

    private static final String TAG = "SmartBarSideLayout";

    // 缓存屏幕方向，避免重复计算
    private int cachedOrientation = -1;
    private boolean orientationCacheValid = false;

    // 🚀 修复：记录最后一个可见应用的ID，用于更多按钮约束
    private int lastVisibleAppId = ConstraintSet.UNSET;

    /**
     * 构造函数
     *
     * @param binding  数据绑定对象
     * @param activity Fragment所属的Activity
     */
    public SmartBarSideLayout(FragmentSmartBarBinding binding, FragmentActivity activity) {
        super(binding, activity);
    }

    @Override
    public void applyLayout(ConstraintSet constraintSet) {
        try {
            MyLog.d(TAG, "🚀 [优化版] 开始应用侧边布局配置");

            // 设置全屏布局参数
            setupFullScreenLayoutParams();

            // 强制隐藏音乐控制组件
            hideMusicControlCompletely(constraintSet);

            // 优化版：统一配置侧边栏应用
            configureSideBarAppsOptimized(constraintSet);

            MyLog.d(TAG, "🚀 [优化版] 侧边布局配置完成");
        } catch (Exception e) {
            MyLog.e(TAG, "应用侧边布局失败", e);
        }
    }

    /**
     * 🚀 优化版：统一配置侧边栏所有应用图标和更多按钮
     * 优化内容：
     * 1. 缓存屏幕方向计算结果
     * 2. 简化约束配置逻辑
     * 3. 提高代码稳定性
     */
    private void configureSideBarAppsOptimized(ConstraintSet constraintSet) {
        try {
            MyLog.d(TAG, "🚀 [优化版] 开始配置侧边栏应用");

            // 第一步：获取屏幕方向（带缓存优化）
            int orientation = getScreenOrientationCached();

            // 第二步：根据屏幕类型设置应用可见性
            setAppVisibilityByOrientation(orientation);

            // 第三步：配置约束（优化版）
            configureSideAppConstraintsOptimized(constraintSet, orientation);

            MyLog.d(TAG, "🚀 [优化版] 侧边栏应用配置完成");
        } catch (Exception e) {
            MyLog.e(TAG, "配置侧边栏应用失败", e);
        }
    }

    /**
     * 🚀 优化版：获取屏幕方向（带缓存）
     * 避免重复计算，提升性能
     */
    private int getScreenOrientationCached() {
        if (!orientationCacheValid) {
            cachedOrientation = ScreenUtils.getScreenOrientation(
                ScreenUtils.getWindowWidth(activity),
                ScreenUtils.getWindowHeigh(activity)
            );
            orientationCacheValid = true;
            MyLog.d(TAG, "🚀 [缓存优化] 屏幕方向已缓存: " + getOrientationName(cachedOrientation));
        }
        return cachedOrientation;
    }

    /**
     * 🚀 优化版：根据屏幕方向设置应用可见性
     * 简化逻辑，提高可读性
     */
    private void setAppVisibilityByOrientation(int orientation) {
        try {
            MyLog.d(TAG, "🚀 [屏幕适配] 当前屏幕方向: " + getOrientationName(orientation));

            boolean showPersonalCenter;
            boolean showFifthApp;

            switch (orientation) {
                case ScreenUtils.ORIENTATION_PORTRAIT:
                    // 竖屏模式：显示所有按钮（7个）
                    showPersonalCenter = true;
                    showFifthApp = true;
                    MyLog.v(TAG, "🚀 [屏幕适配] 竖屏模式 - 显示7个应用");
                    break;
                case ScreenUtils.ORIENTATION_LANDSCAPE:
                    // 横屏模式：隐藏个人中心（6个）
                    showPersonalCenter = false;
                    showFifthApp = true;
                    MyLog.v(TAG, "🚀 [屏幕适配] 横屏模式 - 显示6个应用");
                    break;
                case ScreenUtils.ORIENTATION_LONG_LANDSCAPE:
                    // 带鱼屏模式：隐藏个人中心和第五个app（5个）
                    showPersonalCenter = false;
                    showFifthApp = false;
                    MyLog.v(TAG, "🚀 [屏幕适配] 带鱼屏模式 - 显示5个应用");
                    break;
                default:
                    // 默认情况按横屏处理
                    showPersonalCenter = false;
                    showFifthApp = true;
                    MyLog.w(TAG, "🚀 [屏幕适配] 未知屏幕方向，按横屏处理");
                    break;
            }

            setAppVisibility(showPersonalCenter, showFifthApp);
        } catch (Exception e) {
            MyLog.e(TAG, "根据屏幕方向设置应用可见性失败", e);
        }
    }

    /**
     * 设置应用可见性
     *
     * @param showPersonalCenter 是否显示个人中心（第六个应用）
     * @param showFifthApp       是否显示第五个应用
     */
    private void setAppVisibility(boolean showPersonalCenter, boolean showFifthApp) {
        try {
            // 设置第五个应用的可见性
            int fifthAppVisibility = showFifthApp ? android.view.View.VISIBLE : android.view.View.GONE;
            binding.ivFive.setVisibility(fifthAppVisibility);
            binding.tvFive.setVisibility(fifthAppVisibility);

            // 设置个人中心的可见性
            int personalCenterVisibility = showPersonalCenter ? android.view.View.VISIBLE : android.view.View.GONE;
            binding.ivPersonnalCenter.setVisibility(personalCenterVisibility);
            binding.tvSix.setVisibility(personalCenterVisibility);

            MyLog.d(TAG, "🚀 [可见性设置] 个人中心: " + showPersonalCenter +
                    ", 第五个应用: " + showFifthApp);
        } catch (Exception e) {
            MyLog.e(TAG, "设置应用可见性失败", e);
        }
    }

    /**
     * 获取屏幕方向名称（用于日志）
     */
    private String getOrientationName(int orientation) {
        switch (orientation) {
            case ScreenUtils.ORIENTATION_PORTRAIT:
                return "竖屏";
            case ScreenUtils.ORIENTATION_LANDSCAPE:
                return "横屏";
            case ScreenUtils.ORIENTATION_LONG_LANDSCAPE:
                return "带鱼屏";
            default:
                return "未知(" + orientation + ")";
        }
    }

    /**
     * 🚀 修复：完全隐藏音乐控制组件并清理约束
     * 解决动态切换到侧边栏时出现"等待播放"文字的问题
     */
    private void hideMusicControlCompletely(ConstraintSet constraintSet) {
        try {
            MyLog.d(TAG, "🚀 [音乐组件] 开始完全隐藏音乐控制组件");

            // 第一步：调用父类的隐藏方法
            hideMusicControl();

            // 第二步：在约束中强制设置音乐组件为GONE
            constraintSet.setVisibility(binding.ilDibu.getId(), ConstraintSet.GONE);

            // 第三步：清理音乐组件的所有约束，避免影响其他View
            constraintSet.clear(binding.ilDibu.getId());

            // 第四步：清理音乐组件内部View的约束（防止约束泄露）
            constraintSet.clear(binding.ivBgmBg.getId());
            constraintSet.clear(binding.tvSongTitle.getId());
            constraintSet.clear(binding.ivBgmZhong.getId());
            constraintSet.clear(binding.ivBgmYou.getId());

            // 第五步：在约束中强制设置音乐组件内部View为GONE
            constraintSet.setVisibility(binding.ivBgmBg.getId(), ConstraintSet.GONE);
            constraintSet.setVisibility(binding.tvSongTitle.getId(), ConstraintSet.GONE);
            constraintSet.setVisibility(binding.ivBgmZhong.getId(), ConstraintSet.GONE);
            constraintSet.setVisibility(binding.ivBgmYou.getId(), ConstraintSet.GONE);

            MyLog.d(TAG, "🚀 [音乐组件] 音乐控制组件完全隐藏完成");
        } catch (Exception e) {
            MyLog.e(TAG, "完全隐藏音乐控制组件失败", e);
            // 降级处理：至少调用基础的隐藏方法
            try {
                hideMusicControl();
            } catch (Exception fallbackE) {
                MyLog.e(TAG, "降级隐藏音乐控制组件也失败", fallbackE);
            }
        }
    }

    /**
     * 🚀 优化版：配置侧边栏应用约束
     * 优化内容：
     * 1. 简化约束配置逻辑
     * 2. 减少重复代码
     * 3. 提高代码可读性和维护性
     * 4. 优化性能
     */
    private void configureSideAppConstraintsOptimized(ConstraintSet constraintSet, int orientation) {
        try {
            MyLog.d(TAG, "🚀 [优化版] 开始配置侧边栏约束");

            // 清除所有相关View的约束，确保干净的状态
            clearAllAppConstraints(constraintSet);

            // 根据屏幕方向确定可见的应用列表
            AppVisibilityConfig config = getAppVisibilityConfig(orientation);

            // 配置可见应用的约束
            configureVisibleAppsConstraints(constraintSet, config);

            // 配置更多按钮约束
            configureMoreButtonConstraints(constraintSet);

            MyLog.d(TAG, "🚀 [优化版] 侧边栏约束配置完成");
        } catch (Exception e) {
            MyLog.e(TAG, "配置侧边栏约束失败", e);
        }
    }

    /**
     * 清除所有应用相关的约束
     */
    private void clearAllAppConstraints(ConstraintSet constraintSet) {
        // 清除所有应用图标和文字的约束
        int[] iconIds = {
            binding.ivPersonnalCenter.getId(), binding.ivFive.getId(), binding.ivFour.getId(),
            binding.ivThree.getId(), binding.ivTwo.getId(), binding.ivOne.getId()
        };
        int[] textIds = {
            binding.tvSix.getId(), binding.tvFive.getId(), binding.tvFour.getId(),
            binding.tvThree.getId(), binding.tvTwo.getId(), binding.tvOne.getId()
        };

        for (int id : iconIds) {
            constraintSet.clear(id);
        }
        for (int id : textIds) {
            constraintSet.clear(id);
        }

        // 清除更多按钮约束
        constraintSet.clear(binding.gridItemAppIcon.getId());
        constraintSet.clear(binding.tvMore.getId());
    }

    /**
     * 应用可见性配置类
     */
    private static class AppVisibilityConfig {
        final boolean showPersonalCenter;
        final boolean showFifthApp;
        final int visibleAppCount;

        AppVisibilityConfig(boolean showPersonalCenter, boolean showFifthApp) {
            this.showPersonalCenter = showPersonalCenter;
            this.showFifthApp = showFifthApp;
            this.visibleAppCount = 4 + (showFifthApp ? 1 : 0) + (showPersonalCenter ? 1 : 0) + 1; // +1 for more button
        }
    }

    /**
     * 根据屏幕方向获取应用可见性配置
     */
    private AppVisibilityConfig getAppVisibilityConfig(int orientation) {
        switch (orientation) {
            case ScreenUtils.ORIENTATION_PORTRAIT:
                return new AppVisibilityConfig(true, true); // 7个应用
            case ScreenUtils.ORIENTATION_LANDSCAPE:
                return new AppVisibilityConfig(false, true); // 6个应用
            case ScreenUtils.ORIENTATION_LONG_LANDSCAPE:
                return new AppVisibilityConfig(false, false); // 5个应用
            default:
                return new AppVisibilityConfig(false, true); // 默认6个应用
        }
    }

    /**
     * 🚀 修复：配置可见应用的约束（修复约束链问题）
     */
    private void configureVisibleAppsConstraints(ConstraintSet constraintSet, AppVisibilityConfig config) {
        // 应用数据：图标ID、文字ID、是否可见
        AppData[] apps = {
            new AppData(binding.ivPersonnalCenter.getId(), binding.tvSix.getId(), config.showPersonalCenter),
            new AppData(binding.ivFive.getId(), binding.tvFive.getId(), config.showFifthApp),
            new AppData(binding.ivFour.getId(), binding.tvFour.getId(), true),
            new AppData(binding.ivThree.getId(), binding.tvThree.getId(), true),
            new AppData(binding.ivTwo.getId(), binding.tvTwo.getId(), true),
            new AppData(binding.ivOne.getId(), binding.tvOne.getId(), true)
        };

        // 🚀 修复：收集可见的应用，建立正确的约束链
        List<AppData> visibleApps = new ArrayList<>();
        for (AppData app : apps) {
            if (app.isVisible) {
                visibleApps.add(app);
            } else {
                // 隐藏不可见的应用
                constraintSet.setVisibility(app.iconId, ConstraintSet.GONE);
                constraintSet.setVisibility(app.textId, ConstraintSet.GONE);
            }
        }

        // 🚀 修复：配置可见应用的约束链
        configureVisibleAppsChain(constraintSet, visibleApps);
    }

    /**
     * 🚀 新增：配置可见应用的约束链
     */
    private void configureVisibleAppsChain(ConstraintSet constraintSet, List<AppData> visibleApps) {
        if (visibleApps.isEmpty()) {
            MyLog.w(TAG, "没有可见的应用，跳过约束配置");
            return;
        }

        MyLog.d(TAG, "🚀 [约束修复] 配置 " + visibleApps.size() + " 个可见应用的约束链");

        // 配置每个可见应用的约束
        for (int i = 0; i < visibleApps.size(); i++) {
            AppData app = visibleApps.get(i);

            // 确定上方锚点
            int topAnchorId = (i == 0) ? ConstraintSet.PARENT_ID : visibleApps.get(i - 1).iconId;
            int topAnchorSide = (i == 0) ? ConstraintSet.TOP : ConstraintSet.BOTTOM;

            // 确定下方锚点（用于建立完整的约束链）
            int bottomAnchorId = ConstraintSet.UNSET;
            int bottomAnchorSide = ConstraintSet.UNSET;

            if (i < visibleApps.size() - 1) {
                // 不是最后一个应用，连接到下一个应用
                bottomAnchorId = visibleApps.get(i + 1).iconId;
                bottomAnchorSide = ConstraintSet.TOP;
            } else {
                // 最后一个应用，连接到更多按钮
                bottomAnchorId = binding.gridItemAppIcon.getId();
                bottomAnchorSide = ConstraintSet.TOP;
            }

            // 配置应用约束
            configureAppConstraintsFixed(constraintSet, app.iconId, app.textId,
                                       topAnchorId, topAnchorSide, bottomAnchorId, bottomAnchorSide);
        }

        // 🚀 修复：记录最后一个可见应用，用于更多按钮约束
        lastVisibleAppId = visibleApps.get(visibleApps.size() - 1).iconId;
    }

    /**
     * 应用数据类
     */
    private static class AppData {
        final int iconId;
        final int textId;
        final boolean isVisible;

        AppData(int iconId, int textId, boolean isVisible) {
            this.iconId = iconId;
            this.textId = textId;
            this.isVisible = isVisible;
        }
    }

    /**
     * 🚀 修复：配置单个应用的约束（建立完整的约束链）
     */
    private void configureAppConstraintsFixed(ConstraintSet constraintSet, int iconId, int textId,
                                            int topAnchorId, int topAnchorSide,
                                            int bottomAnchorId, int bottomAnchorSide) {
        // 配置图标约束
        constraintSet.constrainWidth(iconId, iconSize);
        constraintSet.constrainHeight(iconId, iconSize);
        constraintSet.connect(iconId, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START, margin10);
        constraintSet.connect(iconId, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END, margin10);

        // 🚀 修复：设置上方约束
        constraintSet.connect(iconId, ConstraintSet.TOP, topAnchorId, topAnchorSide);

        // 🚀 修复：设置下方约束（建立完整的约束链）
        if (bottomAnchorId != ConstraintSet.UNSET) {
            constraintSet.connect(iconId, ConstraintSet.BOTTOM, bottomAnchorId, bottomAnchorSide);
        }

        // 配置文字约束
        constraintSet.constrainWidth(textId, ConstraintSet.WRAP_CONTENT);
        constraintSet.constrainHeight(textId, ConstraintSet.WRAP_CONTENT);
        constraintSet.connect(textId, ConstraintSet.START, iconId, ConstraintSet.START);
        constraintSet.connect(textId, ConstraintSet.END, iconId, ConstraintSet.END);
        constraintSet.connect(textId, ConstraintSet.TOP, iconId, ConstraintSet.BOTTOM);

        MyLog.v(TAG, "🚀 [约束修复] 配置应用约束: iconId=" + iconId +
                ", topAnchor=" + topAnchorId + ", bottomAnchor=" + bottomAnchorId);
    }

    /**
     * 🚀 修复：配置更多按钮约束（动态连接到最后一个可见应用）
     */
    private void configureMoreButtonConstraints(ConstraintSet constraintSet) {
        // 🚀 修复：动态确定上方锚点
        int topAnchorId = (lastVisibleAppId != ConstraintSet.UNSET) ? lastVisibleAppId : ConstraintSet.PARENT_ID;
        int topAnchorSide = (lastVisibleAppId != ConstraintSet.UNSET) ? ConstraintSet.BOTTOM : ConstraintSet.TOP;

        MyLog.d(TAG, "🚀 [约束修复] 更多按钮连接到: " +
                (topAnchorId == ConstraintSet.PARENT_ID ? "PARENT" : "应用ID_" + topAnchorId));

        // 配置更多按钮图标
        constraintSet.constrainWidth(binding.gridItemAppIcon.getId(), iconSize);
        constraintSet.constrainHeight(binding.gridItemAppIcon.getId(), iconSize);
        constraintSet.connect(binding.gridItemAppIcon.getId(), ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START, margin10);
        constraintSet.connect(binding.gridItemAppIcon.getId(), ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END, margin10);
        constraintSet.connect(binding.gridItemAppIcon.getId(), ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM);

        // 🚀 修复：动态连接到最后一个可见应用
        constraintSet.connect(binding.gridItemAppIcon.getId(), ConstraintSet.TOP, topAnchorId, topAnchorSide);

        // 配置更多按钮文字
        constraintSet.constrainWidth(binding.tvMore.getId(), ConstraintSet.WRAP_CONTENT);
        constraintSet.constrainHeight(binding.tvMore.getId(), ConstraintSet.WRAP_CONTENT);
        constraintSet.connect(binding.tvMore.getId(), ConstraintSet.START, binding.gridItemAppIcon.getId(), ConstraintSet.START);
        constraintSet.connect(binding.tvMore.getId(), ConstraintSet.END, binding.gridItemAppIcon.getId(), ConstraintSet.END);
        constraintSet.connect(binding.tvMore.getId(), ConstraintSet.TOP, binding.gridItemAppIcon.getId(), ConstraintSet.BOTTOM);
    }

    /**
     * 🚀 新增：应用文字可见性（侧边布局特定实现）
     * 侧边布局需要结合屏幕类型和无字模式设置文字显示
     *
     * @param wordlessMode true表示无字模式（隐藏文字），false表示显示文字
     */
    @Override
    public void applyTextVisibility(boolean wordlessMode) {
        try {
            MyLog.d(TAG, "🚀 [侧边布局] 开始应用文字可见性设置 - 无字模式: " + wordlessMode);

            if (wordlessMode) {
                // 非无字模式：根据屏幕类型和应用可见性设置文字显示
                applyTextVisibilityByScreenType();
                MyLog.d(TAG, "🚀 [侧边布局] 显示模式：根据屏幕类型设置文字显示");
            } else {
                // 无字模式：隐藏所有文字
                hideAllTexts();
                MyLog.d(TAG, "🚀 [侧边布局] 无字模式：隐藏所有文字");
            }

            MyLog.d(TAG, "🚀 [侧边布局] 文字可见性设置完成");
        } catch (Exception e) {
            MyLog.e(TAG, "应用侧边布局文字可见性失败", e);
        }
    }

    /**
     * 🚀 新增：根据屏幕类型设置文字可见性
     */
    private void applyTextVisibilityByScreenType() {
        try {
            // 获取当前屏幕方向
            int orientation = getScreenOrientationCached();

            // 基础文字始终显示（如果不是无字模式）
            binding.tvMore.setVisibility(android.view.View.VISIBLE);
            binding.tvOne.setVisibility(android.view.View.VISIBLE);
            binding.tvTwo.setVisibility(android.view.View.VISIBLE);
            binding.tvThree.setVisibility(android.view.View.VISIBLE);
            binding.tvFour.setVisibility(android.view.View.VISIBLE);

            // 根据屏幕方向和应用可见性设置特定文字
            switch (orientation) {
                case ScreenUtils.ORIENTATION_PORTRAIT:
                    // 竖屏：显示所有文字
                    binding.tvFive.setVisibility(android.view.View.VISIBLE);
                    binding.tvSix.setVisibility(android.view.View.VISIBLE);
                    MyLog.d(TAG, "🚀 [文字管理] 竖屏模式：显示所有文字");
                    break;

                case ScreenUtils.ORIENTATION_LANDSCAPE:
                    // 横屏：显示第五个应用文字，隐藏个人中心文字
                    binding.tvFive.setVisibility(android.view.View.VISIBLE);
                    binding.tvSix.setVisibility(android.view.View.GONE);
                    MyLog.d(TAG, "🚀 [文字管理] 横屏模式：隐藏个人中心文字");
                    break;

                case ScreenUtils.ORIENTATION_LONG_LANDSCAPE:
                    // 带鱼屏：隐藏第五个应用和个人中心文字
                    binding.tvFive.setVisibility(android.view.View.GONE);
                    binding.tvSix.setVisibility(android.view.View.GONE);
                    MyLog.d(TAG, "🚀 [文字管理] 带鱼屏模式：隐藏第五个应用和个人中心文字");
                    break;

                default:
                    // 默认按横屏处理
                    binding.tvFive.setVisibility(android.view.View.VISIBLE);
                    binding.tvSix.setVisibility(android.view.View.GONE);
                    MyLog.w(TAG, "🚀 [文字管理] 未知屏幕方向，按横屏处理");
                    break;
            }
        } catch (Exception e) {
            MyLog.e(TAG, "根据屏幕类型设置文字可见性失败", e);
        }
    }

    /**
     * 🚀 新增：隐藏所有文字
     */
    private void hideAllTexts() {
        binding.tvMore.setVisibility(android.view.View.GONE);
        binding.tvOne.setVisibility(android.view.View.GONE);
        binding.tvTwo.setVisibility(android.view.View.GONE);
        binding.tvThree.setVisibility(android.view.View.GONE);
        binding.tvFour.setVisibility(android.view.View.GONE);
        binding.tvFive.setVisibility(android.view.View.GONE);
        binding.tvSix.setVisibility(android.view.View.GONE);
    }




    @Override
    public String getLayoutType() {
        return "side";
    }

    @Override
    public boolean isSidebarLayout() {
        return true;
    }

    @Override
    public boolean supportsMusicControl() {
        return false;
    }
}
