package com.smartcar.easylauncher.shared.utils;


import android.os.AsyncTask;


import com.smartcar.easylauncher.data.model.navigation.CityRestriction;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.json.JSONArray;
import org.json.JSONObject;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;


public class CarRestrictionUtil {

    // 定义一个静态方法，传入城市和车牌号，返回限号或者不限号
    public static String getLimitNumber(String city, String carNumber) {
        // 定义一个Map，存储城市对应的URL
        Map<String, String> cityUrlMap = new HashMap<>();
        cityUrlMap.put("北京", "https://www.huawei.com/cn/traffic-limitation/beijing");
        cityUrlMap.put("天津", "https://www.huawei.com/cn/traffic-limitation/tianjin");
        cityUrlMap.put("石家庄", "https://www.huawei.com/cn/traffic-limitation/shijiazhuang");
        // ...省略其他城市

        // 获取城市对应的URL
        String url = cityUrlMap.get(city);
        if (url == null) {
            return "不支持该城市";
        }

        try {
            // 发送HTTP请求，获取HTML页面
            AsyncTask<Void, Void, String> task = new AsyncTask<Void, Void, String>() {
                @Override
                protected String doInBackground(Void... params) {
                    try {
                        Document doc = Jsoup.connect(url).get();
                        // 解析HTML页面，获取限号信息
                        Elements elements = doc.select("div[class=traffic-limitation__content] > ul > li");
                        for (Element element : elements) {
                            String text = element.text();
                            if (text.contains(carNumber)) {
                                return "限号";
                            }
                        }
                        return "不限号";
                    } catch (IOException e) {
                        e.printStackTrace();
                        return "查询失败";
                    }
                }

                @Override
                protected void onPostExecute(String result) {
                    // do something with result
                }
            };
            task.execute();
            return task.get();
        } catch (Exception e) {
            e.printStackTrace();
            return "查询失败";
        }
    }

    // Define a function to get the restriction information for a given city and license plate number
    public static String getRestrictionInfo(String city, String licensePlate) {
        try {
            // Create the URL for the API endpoint
            String url = "https://api.xiaomi.com/v1/datacenter/traffic_restrictions/get_restrictions_info?city=" + city + "&license_plate=" + licensePlate;

            // Create a connection to the API endpoint
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();

            // Set the request method to GET
            connection.setRequestMethod("GET");

            // Get the response from the API endpoint
            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String inputLine;
            StringBuffer response = new StringBuffer();
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();

            // Parse the response as a JSON object
            JSONObject jsonResponse = new JSONObject(response.toString());

            // Extract the restriction information from the JSON object
            String restrictionInfo = jsonResponse.getString("restriction_info");

            // Return the restriction information
            return restrictionInfo;
        } catch (Exception e) {
            // If an error occurs, return an error message
            return "Error: " + e.getMessage();
        }
    }

    // 定义获取限号信息的方法
    public static String getXianHao(String city, String carNumber) {
        String result = "";
        try {
            // 对城市和车牌号进行URL编码
            String cityCode = URLEncoder.encode(city, "UTF-8");
            String carNumberCode = URLEncoder.encode(carNumber, "UTF-8");
            // 拼接请求URL
            String urlStr = "https://xingyun.map.qq.com/api/getXianHao.php?city=" + cityCode + "&carNumber=" + carNumberCode;
            URL url = new URL(urlStr);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                result += line;
            }
            reader.close();
            connection.disconnect();
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 返回限号信息
        MyLog.v("限号查询", result);
        return result;
    }

    // 传入城市名，对比车牌号返回限号还是不限号
    public static String getXianhao(String city, String carNumber) {

// 搜狗限号查询API接口
        String apiUrl = "http://wzcx.chinamobo.com/wzcx/wzcx/xianhao.php?city=";
        try {
            // 对城市名进行URL编码
            String encodedCity = URLEncoder.encode(city, "UTF-8");
            // 拼接API接口URL
            String fullUrl = apiUrl + encodedCity;
            // 创建URL对象
            URL url = new URL(fullUrl);
            // 创建HttpURLConnection对象
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            // 设置请求方法为GET
            connection.setRequestMethod("GET");
            // 获取响应结果
            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();
            MyLog.v("限号查询", response.toString());
            // 判断车牌号是否限号
            if (response.toString().contains(carNumber)) {
                return "限号";
            } else {
                return "不限号";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "查询失败";
        }
    }

    // 定义一个方法，传入城市和车牌号，返回限号还是不限号
    public static String getBingXianHao(String city, String carNumber) {
        try {
            // 构造请求URL
            String urlStr = "https://cn.bing.com/api/v6/traffic/getroadsidexianhao?cp=" + city;
            URL url = new URL(urlStr);

            // 发送HTTP请求
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setRequestProperty("Content-Type", "application/json");
            conn.setRequestProperty("Accept", "application/json");
            BufferedReader br = new BufferedReader(new InputStreamReader((conn.getInputStream())));

            // 解析JSON响应
            StringBuilder sb = new StringBuilder();
            String output;
            while ((output = br.readLine()) != null) {
                sb.append(output);
            }
            JSONObject jsonObject = new JSONObject(sb.toString());
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject obj = jsonArray.getJSONObject(i);
                String xianHao = obj.getString("xianhao");
                String[] xianHaoArr = xianHao.split(",");
                for (String s : xianHaoArr) {
                    if (s.equals(carNumber.substring(carNumber.length() - 1))) {
                        return "限号";
                    }
                }
            }
            return "不限号";
        } catch (Exception e) {
            e.printStackTrace();
            return "查询失败";
        }
    }

    // 定义一个方法，传入城市和车牌号，返回限号还是不限号
    public static List<CityRestriction> getcheshouyeXianHao() {
        String url = "https://www.icauto.com.cn/weizhang/x_8.html";
        Document doc = null;
        try {
            doc = Jsoup.connect(url).get();
        } catch (IOException e) {
            e.printStackTrace();
        }
        Elements elements = doc.select("table tbody tr");
        List<CityRestriction> trafficRestrictions = new ArrayList<>();
        for (Element element : elements) {
            String city = element.select("td:nth-child(1)").text();
            String localLimit = element.select("td:nth-child(2)").text();
            String nonlocalLimit = element.select("td:nth-child(2)").text();
            CityRestriction trafficRestriction = new CityRestriction(city, localLimit, nonlocalLimit);
            trafficRestrictions.add(trafficRestriction);

            String city2 = element.select("td:nth-child(4)").text();
            String localLimit2 = element.select("td:nth-child(5)").text();
            String nonlocalLimit2 = element.select("td:nth-child(5)").text();
            CityRestriction trafficRestriction2 = new CityRestriction(city2, localLimit2, nonlocalLimit2);
            trafficRestrictions.add(trafficRestriction2);
        }
        return trafficRestrictions;
    }

    public static String getRestriction(String city, String plateNumber) {
        List<CityRestriction> cityRestrictions = getcheshouyeXianHao();
        for (CityRestriction trafficRestriction : cityRestrictions) {
            if (trafficRestriction.getCity().equals(city)) {
                //判断trafficRestriction.getLocalLimit()是否是"单号"或者"双号"
                if ("单号".equals(trafficRestriction.getLocalLimit())) {
                    //获取字符串中数字的最后一位
                    String lastNum = plateNumber.replaceAll("[^0-9]", "");
                    //判断最后一位是否是奇数
                    if (Integer.parseInt(lastNum) % 2 == 1) {
                        return "今天限行";
                    } else {
                        return city + "尾号限行: " + trafficRestriction.getLocalLimit();
                    }
                } else if ("双号".equals(trafficRestriction.getLocalLimit())) {
                    //获取字符串中数字的最后一位
                    String lastNum = plateNumber.replaceAll("[^0-9]", "");
                    //判断最后一位是否是偶数
                    if (Integer.parseInt(lastNum) % 2 == 0) {
                        return "今天限行";
                    } else {
                        return city + "尾号限行: " + trafficRestriction.getLocalLimit();
                    }
                }
                //获取字符串中数字的最后一位
                String lastNum = plateNumber.replaceAll("[^0-9]", "");
                if (lastNum.length() > 0) {
                    lastNum = lastNum.substring(lastNum.length() - 1);
                }
                if (trafficRestriction.getLocalLimit().contains(lastNum)) {
                    return "今天限行";
                } else {
                    return city + "尾号限行: " + trafficRestriction.getLocalLimit();
                }

            }
        }
        return "未查询到限行信息";
    }
}