package com.smartcar.easylauncher.shared.widget.layout.smartbar;

import androidx.constraintlayout.widget.ConstraintSet;

/**
 * 智能导航栏布局接口
 * 定义了所有SmartBar布局实现类需要实现的方法
 *
 * 🚀 重构：扩展接口，让Layout类负责所有UI操作
 * 参考HomeMapLayoutManager的设计模式，提供统一的布局管理接口
 *
 * <AUTHOR>
 * @since 2024-12-18
 * @version 2.0 重构版 - 职责分离
 */
public interface ISmartBarLayout {

    /**
     * 应用布局配置
     * 实现此方法来配置特定的SmartBar布局
     *
     * @param constraintSet 约束布局配置对象，用于设置和应用约束
     */
    void applyLayout(ConstraintSet constraintSet);

    /**
     * 🚀 新增：应用显示设置
     * Layout类负责处理所有UI相关的显示设置
     *
     * @param appname    是否显示名称
     * @param transparentState 透明模式状态
     * @param musicControlShow 音乐控制显示状态
     */
    void applyDisplaySettings(boolean  appname, boolean transparentState, boolean musicControlShow);

    /**
     * 🚀 新增：应用背景模式
     * Layout类负责根据布局类型和透明模式设置背景
     *
     * @param transparentState 透明模式状态
     */
    void applyBackgroundMode(boolean transparentState);

    /**
     * 获取布局类型标识符
     *
     * @return 布局类型的字符串标识符
     */
    String getLayoutType();

    /**
     * 检查当前布局是否为侧边栏布局
     *
     * @return true表示侧边栏布局，false表示底部栏布局
     */
    boolean isSidebarLayout();

    /**
     * 检查当前布局是否支持音乐控制组件
     *
     * @return true表示支持音乐控制，false表示不支持
     */
    boolean supportsMusicControl();
}
