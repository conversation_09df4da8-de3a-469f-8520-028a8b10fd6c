package com.smartcar.easylauncher.shared.dialog.flexible;

import android.content.Context;
import android.widget.Button;
import android.widget.TextView;

import com.smartcar.easylauncher.R;

public class HintDialog {
    public static void show(Context context, String title, String content, String payment, OnDialogClickListener listener) {
        show(context, title, content, payment, "取消", true, true, listener);
    }

    public static void show(Context context, String title, String content, OnDialogClickListener listener) {
        show(context, title, content, "查看", "取消", true, true, listener);
    }

    public static void show(Context context, String title, String content, String payment, String cancel, boolean keystroke, boolean touch, OnDialogClickListener listener) {
        FlexibleDialog dialog = new FlexibleDialogBuilder(context)
                .setLayout(R.layout.dialog_hint)
                .setCancelable(keystroke)
                .setCanceledOnTouchOutside(touch)
                .setAnimation(com.smart.easy.permissions.R.style.notAnimation)
                .setWindowType(DialogHelper.WINDOW_TYPE_APPLICATION_OVERLAY)
                .addOnClickListener(R.id.paymentButton, R.id.cancelButton)
                .setViewBinder((view, dialogInstance) -> {
                    TextView titleView = view.findViewById(R.id.tv_hint);
                    TextView contentView = view.findViewById(R.id.tv_content);
                    Button paymentButton = view.findViewById(R.id.paymentButton);
                    Button cancelButton = view.findViewById(R.id.cancelButton);

                    titleView.setText(title);
                    contentView.setText(content);
                    paymentButton.setText(payment);
                    cancelButton.setText(cancel);

                }).setOnViewClickListener((view, dialog1) -> {
                    switch (view.getId()) {
                        case R.id.paymentButton:
                            if (listener != null) {
                                listener.onPaymentButtonClick(dialog1);
                            }
                            dialog1.dismiss();
                            // 关闭对话框
                            break;
                        case R.id.cancelButton:
                            if (listener != null) {
                                listener.onCancelButtonClick(dialog1);
                            }
                            dialog1.dismiss();
                            // 关闭对话框
                            break;
                        default:
                    }
                })
                .create();

        dialog.show();
    }

    public interface OnDialogClickListener {
        void onPaymentButtonClick(FlexibleDialog dialog);

        void onCancelButtonClick(FlexibleDialog dialog);
    }
}