package com.smartcar.easylauncher.shared.dialog;

import android.content.Context;
import android.os.Build;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;

import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.shared.utils.permission.OverlayPermissionUtils;
import com.smartcar.easylauncher.shared.utils.ui.HideNavBarUtil;

/**
 * 通用对话框组件
 *
 * <AUTHOR>
 * @since 2018/12/1
 * @version 1.0
 */
public class CommonDialog {
    /**
     * 应用上下文
     */
    private final Context context;

    /**
     * 对话框实例
     */
    private CustomDialog dialog;

    /**
     * 对话框类型
     * TODO: 建议将类型定义为枚举类，增加代码可读性
     */
    private final int dialogType;

    /**
     * 对话框按钮点击的确认回调接口
     */
    public interface OnConfirmClickListener {
        /**
         * 确认按钮点击时的回调方法
         */
        void onConfirm();
    }

    /**
     * 对话框按钮点击的取消回调接口
     */
    public interface OnCancelClickListener {
        /**
         * 取消按钮点击时的回调方法
         */
        void onCancel();
    }

    private OnConfirmClickListener onConfirmClickListener;
    private OnCancelClickListener onCancelClickListener;

    /**
     * 构造函数
     *
     * @param context 上下文对象
     * @param dialogType 对话框类型
     */
    public CommonDialog(Context context, int dialogType) {
        this.context = context;
        this.dialogType = dialogType;
    }

    /**
     * 关闭对话框
     */
    public void dismiss() {
        if (dialog != null) {
            dialog.dismiss();
        }
    }

    /**
     * 显示提示对话框
     *
     * @param title 对话框标题
     * @param content 对话框内容
     * @param confirmText 确认按钮文本
     * @param cancelText 取消按钮文本
     */
    public void showDialog(String title, String content, String confirmText, String cancelText) {
        // 创建自定义对话框
        dialog = new CustomDialog(context, R.style.WoDeDialog, R.layout.dialog_hint);

        // 设置对话框窗口类型
        if (OverlayPermissionUtils.isWindow(context)) {
            setDialogWindowType();
        }

        // 设置对话框基本属性
        dialog.setCancelable(false);
        HideNavBarUtil.hideBottomUIMenu(context, dialog, SettingsManager.getSystNavigationBarShow());

        // 显示对话框，处理可能的异常
        try {
            dialog.show();
        } catch (Exception e) {
            MToast.makeTextShort(context.getString(R.string.overlay_permission_required));
            return;
        }

        // 初始化对话框视图
        initDialogViews(title, content, confirmText, cancelText);
    }

    /**
     * 设置对话框窗口类型
     * 针对不同Android版本设置不同的窗口类型
     */
    private void setDialogWindowType() {
        int windowType = Build.VERSION.SDK_INT >= Build.VERSION_CODES.O
                ? WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                : WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
        dialog.getWindow().setType(windowType);
    }

    /**
     * 初始化对话框视图组件
     *
     * @param title 标题文本
     * @param content 内容文本
     * @param confirmText 确认按钮文本
     * @param cancelText 取消按钮文本
     */
    private void initDialogViews(String title, String content, String confirmText, String cancelText) {
        Button confirmButton = dialog.findViewById(R.id.paymentButton);
        Button cancelButton = dialog.findViewById(R.id.cancelButton);
        TextView titleTextView = dialog.findViewById(R.id.tv_hint);
        TextView contentTextView = dialog.findViewById(R.id.tv_content);

        // 设置文本内容
        confirmButton.setText(confirmText);
        cancelButton.setText(cancelText);
        titleTextView.setText(title);
        contentTextView.setText(content);

        // 设置按钮点击监听器
        confirmButton.setOnClickListener(v -> {
            if (onConfirmClickListener != null) {
                onConfirmClickListener.onConfirm();
            }
            dialog.dismiss();
        });

        cancelButton.setOnClickListener(v -> {
            if (onCancelClickListener != null) {
                onCancelClickListener.onCancel();
            }
            dialog.dismiss();
        });
    }

    /**
     * 设置确认按钮点击监听器
     *
     * @param listener 确认按钮监听器
     */
    public void setOnConfirmClickListener(OnConfirmClickListener listener) {
        this.onConfirmClickListener = listener;
    }

    /**
     * 设置取消按钮点击监听器
     *
     * @param listener 取消按钮监听器
     */
    public void setOnCancelClickListener(OnCancelClickListener listener) {
        this.onCancelClickListener = listener;
    }
}