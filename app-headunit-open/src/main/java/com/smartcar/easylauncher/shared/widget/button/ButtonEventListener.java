package com.smartcar.easylauncher.shared.widget.button;

/**
 * 按钮事件监听器接口，用于处理按钮的点击、展开和折叠事件。
 *
 *
 * <AUTHOR>
 * @date 2016/9/11
 */
public interface ButtonEventListener {
    /**
     * 当按钮被点击时调用此方法。
     *
     * @param index 按钮的索引值，从起始角度到结束角度进行计数，值域为1到expandButtonCount（包含）。
     *              索引值用于标识被点击的按钮在按钮组中的位置。
     */
    void onButtonClicked(int index);

    /**
     * 当按钮组处于展开状态时调用此方法。
     * 该方法通常用于处理按钮组展开后的逻辑，例如更新界面、调整布局等。
     */
    void onExpand();

    /**
     * 当按钮组处于折叠状态时调用此方法。
     * 该方法通常用于处理按钮组折叠后的逻辑，例如隐藏界面元素、恢复布局等。
     */
    void onCollapse();
}
