package com.smartcar.easylauncher.shared.utils;

import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.Looper;

import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.shared.utils.thread.ThreadPoolUtil;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 应用刷新诊断工具
 * 用于诊断和解决应用刷新一直转圈的问题
 * 
 * <AUTHOR>
 */
public class AppRefreshDiagnostic {
    private static final String TAG = "AppRefreshDiagnostic";
    
    /**
     * 诊断应用刷新问题
     * 
     * @param context 上下文
     * @return 诊断结果
     */
    public static String diagnoseRefreshIssues(Context context) {
        StringBuilder report = new StringBuilder();
        report.append("=== 应用刷新诊断报告 ===\n");
        
        // 1. 检查Context状态
        report.append("1. Context检查: ");
        if (context == null) {
            report.append("❌ Context为空\n");
        } else {
            report.append("✅ Context正常\n");
        }
        
        // 2. 检查PackageManager
        report.append("2. PackageManager检查: ");
        try {
            if (context != null) {
                PackageManager pm = context.getPackageManager();
                if (pm == null) {
                    report.append("❌ PackageManager为空\n");
                } else {
                    report.append("✅ PackageManager正常\n");
                }
            } else {
                report.append("⚠️ 无法检查（Context为空）\n");
            }
        } catch (Exception e) {
            report.append("❌ PackageManager异常: ").append(e.getMessage()).append("\n");
        }
        
        // 3. 检查线程池状态
        report.append("3. 线程池检查: ");
        try {
            ThreadPoolExecutor executor = ThreadPoolUtil.getThreadPoolExecutor();
            if (executor == null) {
                report.append("❌ 线程池为空\n");
            } else {
                report.append("✅ 线程池正常\n");
                report.append("   - 核心线程数: ").append(executor.getCorePoolSize()).append("\n");
                report.append("   - 最大线程数: ").append(executor.getMaximumPoolSize()).append("\n");
                report.append("   - 活跃线程数: ").append(executor.getActiveCount()).append("\n");
                report.append("   - 队列大小: ").append(executor.getQueue().size()).append("\n");
                report.append("   - 已完成任务数: ").append(executor.getCompletedTaskCount()).append("\n");
            }
        } catch (Exception e) {
            report.append("❌ 线程池异常: ").append(e.getMessage()).append("\n");
        }
        
        // 4. 检查数据管理器
        report.append("4. 数据管理器检查: ");
        try {
            String appListData = DataManager.getAppListData();
            if (appListData == null) {
                report.append("⚠️ 应用列表数据为空\n");
            } else if (appListData.isEmpty()) {
                report.append("⚠️ 应用列表数据为空字符串\n");
            } else {
                report.append("✅ 应用列表数据正常 (长度: ").append(appListData.length()).append(")\n");
            }
        } catch (Exception e) {
            report.append("❌ 数据管理器异常: ").append(e.getMessage()).append("\n");
        }
        
        // 5. 检查主线程状态
        report.append("5. 主线程检查: ");
        if (Looper.getMainLooper() == null) {
            report.append("❌ 主线程Looper为空\n");
        } else {
            report.append("✅ 主线程Looper正常\n");
            if (Thread.currentThread() == Looper.getMainLooper().getThread()) {
                report.append("   - 当前在主线程\n");
            } else {
                report.append("   - 当前在子线程\n");
            }
        }
        
        // 6. 检查AppInfoProvider状态
        report.append("6. AppInfoProvider检查: ");
        try {
            if (context != null) {
                AppInfoProvider provider = AppInfoProvider.getInstance(context);
                if (provider == null) {
                    report.append("❌ AppInfoProvider为空\n");
                } else {
                    report.append("✅ AppInfoProvider正常\n");
                }
            } else {
                report.append("⚠️ 无法检查（Context为空）\n");
            }
        } catch (Exception e) {
            report.append("❌ AppInfoProvider异常: ").append(e.getMessage()).append("\n");
        }
        
        // 7. 内存状态检查
        report.append("7. 内存状态检查: ");
        try {
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            report.append("✅ 内存状态正常\n");
            report.append("   - 最大内存: ").append(maxMemory / 1024 / 1024).append("MB\n");
            report.append("   - 已分配内存: ").append(totalMemory / 1024 / 1024).append("MB\n");
            report.append("   - 已使用内存: ").append(usedMemory / 1024 / 1024).append("MB\n");
            report.append("   - 可用内存: ").append(freeMemory / 1024 / 1024).append("MB\n");
            
            // 内存使用率检查
            double memoryUsageRate = (double) usedMemory / maxMemory;
            if (memoryUsageRate > 0.8) {
                report.append("   ⚠️ 内存使用率过高: ").append(String.format("%.1f%%", memoryUsageRate * 100)).append("\n");
            }
        } catch (Exception e) {
            report.append("❌ 内存检查异常: ").append(e.getMessage()).append("\n");
        }
        
        report.append("=== 诊断完成 ===\n");
        
        return report.toString();
    }
    
    /**
     * 执行诊断并输出日志
     * 
     * @param context 上下文
     */
    public static void runDiagnostic(Context context) {
        String report = diagnoseRefreshIssues(context);
        MyLog.i(TAG, report);
    }
    
    /**
     * 异步执行诊断
     * 
     * @param context 上下文
     * @param callback 诊断完成回调
     */
    public static void runDiagnosticAsync(Context context, DiagnosticCallback callback) {
        ThreadPoolUtil.getThreadPoolExecutor().execute(() -> {
            try {
                String report = diagnoseRefreshIssues(context);
                
                // 在主线程回调结果
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (callback != null) {
                        callback.onDiagnosticComplete(report);
                    }
                });
            } catch (Exception e) {
                MyLog.e(TAG, "异步诊断失败: " + e.getMessage());
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (callback != null) {
                        callback.onDiagnosticError(e);
                    }
                });
            }
        });
    }
    
    /**
     * 诊断回调接口
     */
    public interface DiagnosticCallback {
        /**
         * 诊断完成
         * 
         * @param report 诊断报告
         */
        void onDiagnosticComplete(String report);
        
        /**
         * 诊断出错
         * 
         * @param error 错误信息
         */
        void onDiagnosticError(Exception error);
    }
}
