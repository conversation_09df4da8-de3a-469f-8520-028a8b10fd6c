package com.smartcar.easylauncher.shared.utils;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonPrimitive;
import com.google.gson.reflect.TypeToken;
import com.smartcar.easylauncher.data.model.common.KeyValuePair;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 键值对工具类，用于在JSON字符串和键值对列表之间进行转换
 *
 * <AUTHOR>
 * @date 2024/07/14
 */
public class KeyValueUtils {
    private static final Gson gson = new Gson();

    /**
     * 将JSON字符串转换为键值对列表
     *
     * @param jsonStr JSON字符串
     * @return 键值对列表
     */
    public static List<KeyValuePair> jsonToKeyValuePairs(String jsonStr) {
        List<KeyValuePair> pairs = new ArrayList<>();
        
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return pairs;
        }
        
        try {
            JsonObject jsonObject = JsonParser.parseString(jsonStr).getAsJsonObject();
            
            for (Map.Entry<String, JsonElement> entry : jsonObject.entrySet()) {
                String key = entry.getKey();
                JsonElement element = entry.getValue();
                
                if (element.isJsonPrimitive()) {
                    JsonPrimitive primitive = element.getAsJsonPrimitive();
                    KeyValuePair pair;
                    
                    if (primitive.isString()) {
                        pair = new KeyValuePair(key, primitive.getAsString(), KeyValuePair.ValueType.STRING);
                    } else if (primitive.isNumber()) {
                        String valueStr = primitive.getAsString();
                        if (valueStr.contains(".")) {
                            // 浮点数
                            try {
                                Float.parseFloat(valueStr);
                                pair = new KeyValuePair(key, valueStr, KeyValuePair.ValueType.FLOAT);
                            } catch (NumberFormatException e) {
                                pair = new KeyValuePair(key, valueStr, KeyValuePair.ValueType.DOUBLE);
                            }
                        } else {
                            // 整数
                            try {
                                Integer.parseInt(valueStr);
                                pair = new KeyValuePair(key, valueStr, KeyValuePair.ValueType.INTEGER);
                            } catch (NumberFormatException e) {
                                pair = new KeyValuePair(key, valueStr, KeyValuePair.ValueType.LONG);
                            }
                        }
                    } else if (primitive.isBoolean()) {
                        pair = new KeyValuePair(key, Boolean.toString(primitive.getAsBoolean()), KeyValuePair.ValueType.BOOLEAN);
                    } else {
                        // 默认作为字符串
                        pair = new KeyValuePair(key, primitive.getAsString(), KeyValuePair.ValueType.STRING);
                    }
                    
                    pairs.add(pair);
                }
            }
        } catch (Exception e) {
            MyLog.e("KeyValueUtils", "Error parsing JSON: " + e.getMessage());
            // 解析失败时返回空列表
        }
        
        return pairs;
    }
    
    /**
     * 将键值对列表转换为JSON字符串
     *
     * @param pairs 键值对列表
     * @return JSON字符串
     */
    public static String keyValuePairsToJson(List<KeyValuePair> pairs) {
        if (pairs == null || pairs.isEmpty()) {
            return "{}";
        }
        
        Map<String, Object> map = new LinkedHashMap<>();
        
        for (KeyValuePair pair : pairs) {
            // 跳过键为空的键值对
            if (pair.getKey() == null || pair.getKey().trim().isEmpty()) {
                continue;
            }
            
            // 确保键是唯一的，如果有重复键，后面的会覆盖前面的
            map.put(pair.getKey(), pair.getTypedValue());
        }
        
        return gson.toJson(map);
    }
    
    /**
     * 将键值对列表转换为Map对象
     *
     * @param pairs 键值对列表
     * @return Map对象
     */
    public static Map<String, Object> keyValuePairsToMap(List<KeyValuePair> pairs) {
        Map<String, Object> map = new LinkedHashMap<>();
        
        if (pairs == null || pairs.isEmpty()) {
            return map;
        }
        
        for (KeyValuePair pair : pairs) {
            // 跳过键为空的键值对
            if (pair.getKey() == null || pair.getKey().trim().isEmpty()) {
                continue;
            }
            
            // 确保键是唯一的，如果有重复键，后面的会覆盖前面的
            map.put(pair.getKey(), pair.getTypedValue());
        }
        
        return map;
    }
    
    /**
     * 判断JSON字符串是否有效
     *
     * @param jsonStr JSON字符串
     * @return 是否有效
     */
    public static boolean isValidJson(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return false;
        }
        
        try {
            gson.fromJson(jsonStr, Object.class);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 将JSON字符串格式化为易读的格式
     *
     * @param jsonStr JSON字符串
     * @return 格式化后的JSON字符串
     */
    public static String formatJson(String jsonStr) {
        if (!isValidJson(jsonStr)) {
            return jsonStr;
        }
        
        try {
            Type type = new TypeToken<Object>(){}.getType();
            Object obj = gson.fromJson(jsonStr, type);
            return gson.toJson(obj);
        } catch (Exception e) {
            return jsonStr;
        }
    }
} 