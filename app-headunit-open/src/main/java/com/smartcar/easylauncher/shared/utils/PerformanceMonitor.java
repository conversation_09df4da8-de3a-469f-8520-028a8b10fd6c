package com.smartcar.easylauncher.shared.utils;

import android.os.SystemClock;

import java.util.HashMap;
import java.util.Map;

/**
 * 性能监控工具类
 * 用于监控设备信息加载的性能，特别是在低端设备上的表现
 * 
 * <AUTHOR>
 */
public class PerformanceMonitor {
    private static final String TAG = "PerformanceMonitor";
    private static PerformanceMonitor instance;
    
    private final Map<String, Long> startTimes = new HashMap<>();
    private final Map<String, Long> durations = new HashMap<>();
    
    private PerformanceMonitor() {
        // 私有构造函数
    }
    
    public static synchronized PerformanceMonitor getInstance() {
        if (instance == null) {
            instance = new PerformanceMonitor();
        }
        return instance;
    }
    
    /**
     * 开始计时
     * @param tag 标识符
     */
    public void startTiming(String tag) {
        startTimes.put(tag, SystemClock.elapsedRealtime());
        MyLog.d(TAG, "开始计时: " + tag);
    }
    
    /**
     * 结束计时并记录
     * @param tag 标识符
     * @return 耗时（毫秒）
     */
    public long endTiming(String tag) {
        Long startTime = startTimes.get(tag);
        if (startTime == null) {
            MyLog.w(TAG, "未找到开始时间: " + tag);
            return -1;
        }
        
        long duration = SystemClock.elapsedRealtime() - startTime;
        durations.put(tag, duration);
        startTimes.remove(tag);
        
        MyLog.d(TAG, "结束计时: " + tag + " 耗时: " + duration + "ms");
        return duration;
    }
    
    /**
     * 获取耗时
     * @param tag 标识符
     * @return 耗时（毫秒），如果不存在返回-1
     */
    public long getDuration(String tag) {
        Long duration = durations.get(tag);
        return duration != null ? duration : -1;
    }
    
    /**
     * 打印性能报告
     */
    public void printReport() {
        MyLog.i(TAG, "=== 性能报告 ===");
        for (Map.Entry<String, Long> entry : durations.entrySet()) {
            String tag = entry.getKey();
            long duration = entry.getValue();
            String level = getPerformanceLevel(duration);
            MyLog.i(TAG, String.format("%s: %dms [%s]", tag, duration, level));
        }
        MyLog.i(TAG, "===============");
    }
    
    /**
     * 获取性能等级
     * @param duration 耗时（毫秒）
     * @return 性能等级描述
     */
    private String getPerformanceLevel(long duration) {
        if (duration < 100) {
            return "优秀";
        } else if (duration < 300) {
            return "良好";
        } else if (duration < 500) {
            return "一般";
        } else if (duration < 1000) {
            return "较慢";
        } else {
            return "很慢";
        }
    }
    
    /**
     * 清除所有记录
     */
    public void clear() {
        startTimes.clear();
        durations.clear();
    }
    
    /**
     * 记录内存使用情况
     */
    public void logMemoryUsage(String tag) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();

        MyLog.d(TAG, String.format("%s - 内存使用: %dKB/%dKB (最大: %dKB)",
            tag,
            usedMemory / 1024,
            totalMemory / 1024,
            maxMemory / 1024));
    }

    /**
     * 获取所有性能数据
     * @return 包含所有计时数据的Map
     */
    public Map<String, Long> getAllDurations() {
        return new HashMap<>(durations);
    }
}