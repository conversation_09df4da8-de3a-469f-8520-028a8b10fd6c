package com.smartcar.easylauncher.shared.widget.layout.map;

import android.view.View;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.fragment.app.FragmentActivity;

import com.smartcar.easylauncher.core.constants.SettingsConstants;
import com.smartcar.easylauncher.databinding.FragmentMapHomeBinding;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.shared.view.pagergridlayoutmanager.PagerGridLayoutManager;

/**
 * 首页地图布局4实现
 * 布局4配置：顶部状态栏 + 左侧导航栏 + 中间内容区域 + 右侧卡片列表
 * 适用场景：卡片列表位于右侧的布局
 */
public class HomeMapLayout4 extends AbstractHomeMapLayout {

    /**
     * 构造函数
     *
     * @param binding 数据绑定对象
     * @param activity Fragment所属的Activity
     * @param pagerLayoutManager RecyclerView的布局管理器
     */
    public HomeMapLayout4(FragmentMapHomeBinding binding,
                        FragmentActivity activity,
                        PagerGridLayoutManager pagerLayoutManager) {
        super(binding, activity, pagerLayoutManager);
    }

    @Override
    public void applyLayout(ConstraintSet constraintSet) {
        // 获取状态栏和导航栏的显示状态
        boolean statusBarVisible = SettingsManager.getStatusBarShow();
        boolean navigationBarVisible = SettingsManager.getNavigationBarShow();

        // 配置状态栏（顶部位置）
        if (statusBarVisible) {
            configureStatusBar(constraintSet,
                    ConstraintSet.MATCH_CONSTRAINT,  // 宽度
                    dimensionCache.statusBarHeight30,  // 高度
                    dimensionCache.margin5,  // 顶部边距
                    0,  // 右边距
                    ConstraintSet.MATCH_CONSTRAINT  // 水平约束类型
            );
        } else {
            // 状态栏不显示时，隐藏并清除约束
            hideStatusBar(constraintSet);
        }

        // 配置导航栏（左侧位置）
        if (navigationBarVisible) {
            configureNavigationBarLeft(constraintSet);
        } else {
            // 导航栏不显示时，隐藏并清除约束
            hideNavigationBar(constraintSet);
        }

        // 配置RecyclerView（右侧卡片列表）
        configureRecyclerViewRight(constraintSet, statusBarVisible);

        // 配置主内容区域（中间位置）
        configureFrameLayoutCenter(constraintSet, statusBarVisible, navigationBarVisible);

        // 设置RecyclerView为垂直布局
        setupVerticalRecyclerView();
    }

    @Override
    public String getLayoutType() {
        return SettingsConstants.DEFAULT_MAP_LAYOUT_4;
    }
    
    /**
     * 配置左侧导航栏
     */
    private void configureNavigationBarLeft(ConstraintSet constraintSet) {
        constraintSet.clear(binding.maiIconBar.getId());
        constraintSet.constrainWidth(binding.maiIconBar.getId(), dimensionCache.navigationBarWidth);
        constraintSet.constrainHeight(binding.maiIconBar.getId(), ConstraintSet.MATCH_CONSTRAINT);

        // 左侧导航栏位置
        constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.START,
                ConstraintSet.PARENT_ID, ConstraintSet.START, dimensionCache.margin5);
        
        // 顶部约束 - 根据状态栏显示状态
        if (SettingsManager.getStatusBarShow()) {
            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.TOP,
                    binding.mainLayoutStatusBar.getId(), ConstraintSet.BOTTOM, dimensionCache.margin5);
        } else {
            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID, ConstraintSet.TOP, dimensionCache.margin5);
        }
        
        // 底部约束
        constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.BOTTOM,
                ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, dimensionCache.margin5);

        // 设置导航栏可见性
        binding.maiIconBar.setVisibility(View.VISIBLE);
    }
    
    /**
     * 配置右侧RecyclerView
     */
    private void configureRecyclerViewRight(ConstraintSet constraintSet, boolean statusBarVisible) {
        constraintSet.clear(binding.rvHome.getId());
        constraintSet.constrainWidth(binding.rvHome.getId(), dimensionCache.mapCardWidth);
        constraintSet.constrainHeight(binding.rvHome.getId(), ConstraintSet.MATCH_CONSTRAINT);

        // 右侧位置
        constraintSet.connect(binding.rvHome.getId(), ConstraintSet.END,
                ConstraintSet.PARENT_ID, ConstraintSet.END, dimensionCache.margin5);
        
        // 顶部约束 - 根据状态栏显示状态
        if (statusBarVisible) {
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.TOP,
                    binding.mainLayoutStatusBar.getId(), ConstraintSet.BOTTOM, dimensionCache.margin5);
        } else {
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID, ConstraintSet.TOP, dimensionCache.margin5);
        }
        
        // 底部约束
        constraintSet.connect(binding.rvHome.getId(), ConstraintSet.BOTTOM,
                ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, dimensionCache.margin5);
    }
    
    /**
     * 配置中间内容区域
     */
    private void configureFrameLayoutCenter(ConstraintSet constraintSet, boolean statusBarVisible, boolean navigationBarVisible) {
        constraintSet.clear(binding.frameLayout.getId());
        constraintSet.constrainWidth(binding.frameLayout.getId(), ConstraintSet.MATCH_CONSTRAINT);
        constraintSet.constrainHeight(binding.frameLayout.getId(), ConstraintSet.MATCH_CONSTRAINT);

        // 顶部约束 - 根据状态栏显示状态
        if (statusBarVisible) {
            constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.TOP,
                    binding.mainLayoutStatusBar.getId(), ConstraintSet.BOTTOM, dimensionCache.margin5);
        } else {
            constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID, ConstraintSet.TOP, dimensionCache.margin5);
        }
        
        // 左侧约束 - 根据导航栏显示状态
        if (navigationBarVisible) {
            constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.START,
                    binding.maiIconBar.getId(), ConstraintSet.END, dimensionCache.margin5);
        } else {
            constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.START,
                    ConstraintSet.PARENT_ID, ConstraintSet.START, dimensionCache.margin5);
        }
        
        // 右侧约束 - 连接到RecyclerView
        constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.END,
                binding.rvHome.getId(), ConstraintSet.START, dimensionCache.margin5);
        
        // 底部约束
        constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.BOTTOM,
                ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, dimensionCache.margin5);
    }
} 