package com.smartcar.easylauncher.shared.widget;

import android.animation.ArgbEvaluator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;

import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;

/**
 * 自定义引导流程进度指示器
 * 通过自定义View实现，避免多次重复设置背景资源造成的性能浪费
 * 支持动画过渡，横屏/竖屏自适应
 *
 * <AUTHOR>
 */
public class OnboardingProgressIndicator extends View {
    private static final String TAG = "OnboardingProgress";
    
    // 默认参数
    private static final int DEFAULT_DOT_COUNT = 4;
    private static final int DEFAULT_DOT_SIZE_DP = 8;
    private static final int DEFAULT_DOT_SPACING_DP = 12;
    private static final int DEFAULT_ANIMATION_DURATION = 300;
    
    // 平板模式下的尺寸
    private static final int TABLET_DOT_SIZE_DP = 10;
    private static final int TABLET_DOT_SPACING_DP = 16;
    
    // 配置参数
    private int dotCount;
    private float dotSize;
    private float dotSpacing;
    private int animationDuration;
    private int currentStep = 0; // 当前步骤，从0开始计数
    
    // 颜色
    private int activeColor;
    private int inactiveColor;
    
    // 画笔
    private Paint dotPaint;
    
    // 当前动画中的指示器位置
    private float animatedPosition;
    
    // 值动画
    private ValueAnimator positionAnimator;
    
    // 是否是平板模式
    private boolean isTablet;
    
    public OnboardingProgressIndicator(Context context) {
        this(context, null);
    }
    
    public OnboardingProgressIndicator(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }
    
    public OnboardingProgressIndicator(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }
    
    private void init(Context context, AttributeSet attrs) {
        // 判断是否是平板
        isTablet = isTabletDevice(context);
        
        // 获取默认颜色
        activeColor = ContextCompat.getColor(context, R.color.onboarding_progress_active);
        inactiveColor = ContextCompat.getColor(context, R.color.onboarding_progress_inactive);
        
        // 根据设备类型设置默认尺寸
        float defaultDotSize = isTablet ? 
                DensityUtils.dp2px(context, TABLET_DOT_SIZE_DP) : 
                DensityUtils.dp2px(context, DEFAULT_DOT_SIZE_DP);
        float defaultDotSpacing = isTablet ? 
                DensityUtils.dp2px(context, TABLET_DOT_SPACING_DP) : 
                DensityUtils.dp2px(context, DEFAULT_DOT_SPACING_DP);
        
        // 解析自定义属性
        if (attrs != null) {
            TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.OnboardingProgressIndicator);
            dotCount = ta.getInt(R.styleable.OnboardingProgressIndicator_dotCount, DEFAULT_DOT_COUNT);
            dotSize = ta.getDimension(R.styleable.OnboardingProgressIndicator_dotSize, defaultDotSize);
            dotSpacing = ta.getDimension(R.styleable.OnboardingProgressIndicator_dotSpacing, defaultDotSpacing);
            activeColor = ta.getColor(R.styleable.OnboardingProgressIndicator_activeColor, activeColor);
            inactiveColor = ta.getColor(R.styleable.OnboardingProgressIndicator_inactiveColor, inactiveColor);
            animationDuration = ta.getInt(R.styleable.OnboardingProgressIndicator_animationDuration, 
                    DEFAULT_ANIMATION_DURATION);
            ta.recycle();
        } else {
            dotCount = DEFAULT_DOT_COUNT;
            dotSize = defaultDotSize;
            dotSpacing = defaultDotSpacing;
            animationDuration = DEFAULT_ANIMATION_DURATION;
        }
        
        // 初始化画笔
        dotPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        dotPaint.setStyle(Paint.Style.FILL);
        
        // 设置动画位置
        animatedPosition = 0;
    }
    
    /**
     * 判断当前设备是否是平板
     */
    private boolean isTabletDevice(Context context) {
        return (context.getResources().getConfiguration().screenLayout
                & Configuration.SCREENLAYOUT_SIZE_MASK) >= Configuration.SCREENLAYOUT_SIZE_LARGE;
    }
    
    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        // 计算所需总宽度 = 所有点的宽度 + 点之间的间距
        int desiredWidth = (int) (dotCount * dotSize + (dotCount - 1) * dotSpacing);
        // 计算所需高度 = 点的直径
        int desiredHeight = (int) dotSize;
        
        // 加上padding
        desiredWidth += getPaddingLeft() + getPaddingRight();
        desiredHeight += getPaddingTop() + getPaddingBottom();
        
        // 解析宽高MeasureSpec
        int width = resolveSize(desiredWidth, widthMeasureSpec);
        int height = resolveSize(desiredHeight, heightMeasureSpec);
        
        // 设置测量尺寸
        setMeasuredDimension(width, height);
    }
    
    @Override
    protected void onDraw(@NonNull Canvas canvas) {
        super.onDraw(canvas);
        
        if (dotCount <= 0) {
            return;
        }
        
        // 计算可用绘制区域
        float availableWidth = getWidth() - getPaddingLeft() - getPaddingRight();
        float availableHeight = getHeight() - getPaddingTop() - getPaddingBottom();
        
        // 重新计算点大小，确保适应可用宽度
        float adjustedDotSize = Math.min(dotSize, availableHeight);
        float adjustedSpacing = (dotCount > 1) ? 
                Math.min(dotSpacing, (availableWidth - dotCount * adjustedDotSize) / (dotCount - 1)) : 0;
        
        // 计算起始X坐标（居中）
        float totalWidth = dotCount * adjustedDotSize + (dotCount - 1) * adjustedSpacing;
        float startX = getPaddingLeft() + (availableWidth - totalWidth) / 2;
        float centerY = getPaddingTop() + availableHeight / 2;
        
        // 绘制所有点
        for (int i = 0; i < dotCount; i++) {
            float dotCenterX = startX + i * (adjustedDotSize + adjustedSpacing) + adjustedDotSize / 2;
            
            // 如果是当前激活的点或者动画中
            if (i == currentStep) {
                // 完全激活
                dotPaint.setColor(activeColor);
            } else if (i > Math.floor(animatedPosition) && i <= Math.ceil(animatedPosition)) {
                // 处于激活动画中，计算过渡颜色
                float fraction = animatedPosition - (float) Math.floor(animatedPosition);
                dotPaint.setColor(blendColors(inactiveColor, activeColor, fraction));
            } else if (i < Math.floor(animatedPosition) && i >= Math.ceil(animatedPosition - 1)) {
                // 处于取消激活的动画中
                float fraction = 1 - (animatedPosition - (float) Math.floor(animatedPosition));
                dotPaint.setColor(blendColors(activeColor, inactiveColor, fraction));
            } else {
                // 非激活状态
                dotPaint.setColor(inactiveColor);
            }
            
            // 绘制点
            canvas.drawCircle(dotCenterX, centerY, adjustedDotSize / 2, dotPaint);
        }
    }
    
    /**
     * 混合两个颜色
     */
    @ColorInt
    private int blendColors(@ColorInt int color1, @ColorInt int color2, float ratio) {
        return (int) new ArgbEvaluator().evaluate(ratio, color1, color2);
    }
    
    /**
     * 设置当前步骤，从1开始计数（与原代码保持一致）
     */
    public void setCurrentStep(int step) {
        if (step < 1 || step > dotCount) {
            MyLog.e(TAG, "步骤值无效: " + step + "，有效范围: 1-" + dotCount);
            return;
        }
        
        // 转换为0开始的索引
        int targetStep = step - 1;
        
        // 如果没有变化，直接返回
        if (targetStep == currentStep) {
            return;
        }
        
        // 取消正在运行的动画
        if (positionAnimator != null && positionAnimator.isRunning()) {
            positionAnimator.cancel();
        }
        
        // 创建并启动新动画
        positionAnimator = ValueAnimator.ofFloat(animatedPosition, targetStep);
        positionAnimator.setDuration(animationDuration);
        positionAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
        positionAnimator.addUpdateListener(animation -> {
            animatedPosition = (float) animation.getAnimatedValue();
            invalidate();
        });
        positionAnimator.start();
        
        // 更新当前步骤
        currentStep = targetStep;
    }
    
    /**
     * 设置点的数量
     */
    public void setDotCount(int count) {
        if (count <= 0) {
            return;
        }
        this.dotCount = count;
        requestLayout();
        invalidate();
    }
    
    /**
     * 设置活跃点的颜色
     */
    public void setActiveColor(@ColorInt int color) {
        this.activeColor = color;
        invalidate();
    }
    
    /**
     * 设置非活跃点的颜色
     */
    public void setInactiveColor(@ColorInt int color) {
        this.inactiveColor = color;
        invalidate();
    }
    
    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        // 释放资源
        if (positionAnimator != null && positionAnimator.isRunning()) {
            positionAnimator.cancel();
            positionAnimator = null;
        }
    }
    
    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        // 配置变化时重新判断设备类型
        boolean wasTablet = isTablet;
        isTablet = isTabletDevice(getContext());
        
        // 如果设备类型变化了，更新尺寸
        if (wasTablet != isTablet) {
            float newDotSize = isTablet ? 
                    DensityUtils.dp2px(getContext(), TABLET_DOT_SIZE_DP) : 
                    DensityUtils.dp2px(getContext(), DEFAULT_DOT_SIZE_DP);
            float newDotSpacing = isTablet ? 
                    DensityUtils.dp2px(getContext(), TABLET_DOT_SPACING_DP) : 
                    DensityUtils.dp2px(getContext(), DEFAULT_DOT_SPACING_DP);
            
            this.dotSize = newDotSize;
            this.dotSpacing = newDotSpacing;
            
            requestLayout();
            invalidate();
        }
    }
} 