package com.smartcar.easylauncher.shared.widget.layout.map;

import android.os.Build;
import android.transition.TransitionManager;
import android.util.Log;

import androidx.constraintlayout.widget.ConstraintSet;
import androidx.fragment.app.FragmentActivity;

import com.smartcar.easylauncher.databinding.FragmentMapHomeBinding;
import com.smartcar.easylauncher.core.manager.MapManager;
import com.smartcar.easylauncher.core.manager.SceneMapManagerKt;
import com.smartcar.easylauncher.shared.view.pagergridlayoutmanager.PagerGridLayoutManager;

/**
 * 首页地图布局管理器
 * 负责管理HomeMapScene的各种布局配置，提供灵活、高性能的布局切换功能
 * <p>
 * 功能特性：
 * - 支持6种不同的布局模式
 * - 兼容Android API 17及以上版本
 * - 优化的约束布局配置
 * - 缓存机制提升性能
 *
 * <AUTHOR> Team
 * @version 2.0
 */
public class HomeMapLayoutManager {

    private static final String TAG = "HomeMapLayoutManager";

    // UI组件引用
    private final FragmentMapHomeBinding binding;
    private final FragmentActivity activity;
    private final ConstraintSet constraintSet;
    private final PagerGridLayoutManager layoutManager;
    
    // 布局工厂
    private final HomeMapLayoutFactory layoutFactory;

    /**
     * 构造函数
     *
     * @param binding       数据绑定对象
     * @param activity      Fragment所属的Activity
     * @param layoutManager RecyclerView的布局管理器
     * @param constraintSet 约束布局配置对象
     */
    public HomeMapLayoutManager(FragmentMapHomeBinding binding,
                                FragmentActivity activity,
                                PagerGridLayoutManager layoutManager,
                                ConstraintSet constraintSet) {
        this.binding = binding;
        this.activity = activity;
        this.constraintSet = constraintSet;
        this.layoutManager = layoutManager;
        
        // 创建布局工厂
        this.layoutFactory = new HomeMapLayoutFactory(binding, activity, layoutManager);
    }

    /**
     * 初始化布局配置
     * 根据布局类型应用相应的布局配置
     *
     * @param layoutType 布局类型，参考SettingsConstants中的布局常量
     */
    public void initLayout(String layoutType) {
        // 开始布局过渡动画（仅在Android 4.4及以上版本）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            TransitionManager.beginDelayedTransition(binding.homeMapOne);
        }

        // 从工厂获取对应的布局实现
        IHomeMapLayout layout = layoutFactory.getLayout(layoutType);
        
        // 应用布局配置
        layout.applyLayout(constraintSet);
        
        // 应用约束配置
        constraintSet.applyTo(binding.homeMapOne);
        
        // 刷新地图显示
        MapManager.getInstance(activity).refreshMap(binding.frameLayout);

        // 同时刷新新版 SceneMapManagerKt 的地图位置
        refreshSceneMapManager();
    }
    

    
    /**
     * 刷新SceneMapManagerKt的地图位置
     * 通过重新设置地图容器来触发位置更新
     */
    private void refreshSceneMapManager() {
        try {
            SceneMapManagerKt sceneMapManager = SceneMapManagerKt.Companion.getInstance(activity);
            // 对于 HomeMapScene，使用固定的 Scene ID
            String homeMapSceneId = "HomeMapScene";
            sceneMapManager.setSceneMapContainer(homeMapSceneId, binding.frameLayout);

            // 如果当前是活跃的导航 Scene，触发地图显示状态检查
            // 这会重新计算地图位置并更新显示
            sceneMapManager.setSceneShouldShowMap(homeMapSceneId, true);
        } catch (Exception e) {
            // 如果 SceneMapManagerKt 未初始化或出现异常，记录但不影响主流程
            Log.w(TAG, "更新 SceneMapManagerKt 地图位置时出现异常: " + e.getMessage());
        }
    }
}