package com.smartcar.easylauncher.shared.adapter.common.provider;

import com.chad.library.adapter.base.entity.node.BaseNode;
import com.chad.library.adapter.base.provider.BaseNodeProvider;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.data.model.common.ItemNode;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class SecondNodeProvider extends BaseNodeProvider {

    @Override
    public int getItemViewType() {
        return 1;
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_section_content;
    }

    @Override
    public void convert(@NotNull BaseViewHolder helper, @Nullable BaseNode data) {
        if (data == null) {
            return;
        }
        ItemNode itemNode = (ItemNode) data;
        helper.setText(R.id.tv_name, itemNode.getTitle()+"");
        helper.setText(R.id.tv_path, itemNode.getDate()+"");
        helper.setText(R.id.tv_size, itemNode.getSpent() + "¥");

        switch (itemNode.getType()) {
            case Const.FUEL_TYPE_GAS:
                helper.setImageResource(R.id.iv_icon, R.drawable.icon_refuel);
                break;
            case Const.FUEL_TYPE_REPAIR:
                helper.setImageResource(R.id.iv_icon, R.drawable.icon_upkeep);
                break;
            case Const.FUEL_TYPE_INSURANCE:
                helper.setImageResource(R.id.iv_icon, R.drawable.icon_auto);
                break;
            default:
                helper.setImageResource(R.id.iv_icon, R.drawable.icon_refuel);
        }
    }


}
