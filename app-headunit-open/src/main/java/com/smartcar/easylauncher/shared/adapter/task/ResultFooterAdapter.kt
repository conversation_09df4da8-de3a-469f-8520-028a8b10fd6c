package com.smartcar.easylauncher.shared.adapter.task

import android.content.Context
import android.view.ViewGroup
import com.chad.library.adapter4.BaseSingleItemAdapter
import com.chad.library.adapter4.viewholder.QuickViewHolder
import com.smartcar.easylauncher.R

/**
 * @Description: 任务列表底部适配器
 * @author: qwfan
 */
class ResultFooterAdapter(
    private var isData: Boolean
) : BaseSingleItemAdapter<Any, QuickViewHolder>() {

    override fun onCreateViewHolder(context: Context, parent: ViewGroup, viewType: Int): QuickViewHolder {
        return QuickViewHolder(R.layout.new_task_item_tail, parent)
    }

    override fun onBindViewHolder(holder: QuickViewHolder, item: Any?) {
        holder.setImageResource(R.id.grid_item_app_icon, R.drawable.ic_task_add)
        if (isData) {
            holder.setText(R.id.grid_item_number, "然后")
        }else {
            holder.setText(R.id.grid_item_number, "添加")
        }
    }
    fun updateDataStatus(data: Boolean) {
        isData = data
        notifyDataSetChanged()
    }
}