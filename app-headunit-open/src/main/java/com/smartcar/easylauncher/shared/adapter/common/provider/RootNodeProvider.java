package com.smartcar.easylauncher.shared.adapter.common.provider;

import android.view.View;

import com.chad.library.adapter.base.entity.node.BaseNode;
import com.chad.library.adapter.base.provider.BaseNodeProvider;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.data.model.common.RootNode;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class RootNodeProvider extends BaseNodeProvider {

    @Override
    public int getItemViewType() {
        return 0;
    }

    @Override
    public int getLayoutId() {
        return R.layout.def_section_head;
    }

    @Override
    public void convert(@NotNull BaseViewHolder helper, @Nullable BaseNode data) {
        RootNode entity = (RootNode) data;
        if (entity.isExpanded()) {
            helper.setImageResource(R.id.iv_jump, R.drawable.icon_down);
        } else {
            helper.setImageResource(R.id.iv_jump, R.drawable.icon_up);
        }
        helper.setText(R.id.tv_time, entity.getTitle());
    }

    @Override
    public void onClick(@NotNull BaseViewHolder helper, @NotNull View view, BaseNode data, int position) {
        RootNode entity = (RootNode) data;
        getAdapter().expandOrCollapse(position);
        if (entity.isExpanded()) {
            helper.setImageResource(R.id.iv_jump, R.drawable.icon_down);
        } else {
            helper.setImageResource(R.id.iv_jump, R.drawable.icon_up);
        }
    }
}
