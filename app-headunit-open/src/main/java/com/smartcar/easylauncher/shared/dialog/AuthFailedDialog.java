package com.smartcar.easylauncher.shared.dialog;

import android.app.Activity;
import android.content.Intent;
import android.provider.Settings;
import android.view.KeyEvent;
import android.view.View;
import android.widget.TextView;
import androidx.fragment.app.FragmentManager;
import androidx.annotation.NonNull;
import java.lang.ref.WeakReference;

import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.timmy.tdialog.TDialog;
import com.timmy.tdialog.base.BindViewHolder;
import com.timmy.tdialog.listener.OnBindViewListener;
import com.timmy.tdialog.listener.OnViewClickListener;

/**
 * 设备认证失败对话框
 */
public class AuthFailedDialog {
    private static volatile AuthFailedDialog instance;
    private static final float SCREEN_WIDTH_ASPECT = 1f;
    private static final float SCREEN_HEIGHT_ASPECT = 1f;

    private WeakReference<TDialog> dialogRef;
    private WeakReference<Activity> activityRef;

    private AuthFailedDialog() {
        // 私有构造方法
    }

    public static AuthFailedDialog getInstance() {
        if (instance == null) {
            synchronized (AuthFailedDialog.class) {
                if (instance == null) {
                    instance = new AuthFailedDialog();
                }
            }
        }
        return instance;
    }

    /**
     * 关闭设备认证失败对话框
     */
    public void dismiss() {
        if (dialogRef != null && dialogRef.get() != null) {
            TDialog dialog = dialogRef.get();
            if (dialog.isVisible()) {
                dialog.dismiss();
            }
        }
        // 清理引用
        dialogRef = null;
        activityRef = null;
    }

    /**
     * 显示设备认证失败对话框
     *
     * @param activity 当前Activity
     * @param fragmentManager FragmentManager实例
     * @param content 对话框显示的内容
     */
    public void show(@NonNull Activity activity, @NonNull FragmentManager fragmentManager, @NonNull String content) {
        if (dialogRef != null && dialogRef.get() != null && dialogRef.get().isVisible()) {
            return; // 如果对话框已经显示，则不重复显示
        }

        activityRef = new WeakReference<>(activity);

        TDialog dialog = new TDialog.Builder(fragmentManager)
                .setLayoutRes(R.layout.dialog_ordinary)
                .setScreenWidthAspect(activity, SCREEN_WIDTH_ASPECT)
                .setScreenHeightAspect(activity, SCREEN_HEIGHT_ASPECT)
                .addOnClickListener(R.id.tv_cancel, R.id.tv_confirm)
                .setCancelableOutside(false)
                .setOnBindViewListener(new OnBindViewListener() {
                    @Override
                    public void bindView(BindViewHolder viewHolder) {
                        if (activityRef != null && activityRef.get() != null) {
                            TextView contentView = viewHolder.getView(R.id.tv_upgrade_content);
                            contentView.setText(content);
                        }
                    }
                })
                .setOnViewClickListener(new OnViewClickListener() {
                    @Override
                    public void onViewClick(BindViewHolder viewHolder, View view, TDialog tDialog) {
                        if (activityRef != null && activityRef.get() != null) {
                            Activity activity = activityRef.get();
                            switch (view.getId()) {
                                case R.id.tv_cancel:
                                    exitApp(activity);
                                    dismiss();
                                    break;
                                case R.id.tv_confirm:
                                    openSystemSettings(activity);
                                    exitApp(activity);
                                    dismiss();
                                    break;
                            }
                        }
                    }
                })
                .setOnKeyListener((dialog1, keyCode, event) -> keyCode == KeyEvent.KEYCODE_BACK)
                .create();

        dialogRef = new WeakReference<>(dialog);

        try {
            dialog.show(fragmentManager);
        } catch (Exception e) {
            e.printStackTrace();
            dismiss(); // 发生异常时清理资源
        }
    }

    /**
     * 打开系统设置
     */
    private void openSystemSettings(Activity activity) {
        try {
            Intent intent = new Intent(Settings.ACTION_SETTINGS);
            activity.startActivity(intent);
        } catch (Exception e) {
            MToast.makeTextShort("打开系统设置失败");
            e.printStackTrace();
        }
    }

    /**
     * 退出应用
     */
    private void exitApp(Activity activity) {
        activity.moveTaskToBack(true);
        activity.finish();
        System.exit(0);
    }
} 