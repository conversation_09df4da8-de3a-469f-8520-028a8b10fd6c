package com.smartcar.easylauncher.shared.dialog.flexible;

import android.content.Context;
import android.view.View;
import android.view.WindowManager;
import androidx.annotation.LayoutRes;
import androidx.annotation.StyleRes;

import com.smartcar.easylauncher.R;

/**
 * 对话框控制器
 * 用于管理对话框的参数和视图
 */
public class DialogController {
    private final Context context;
    public final DialogParams params;

    public DialogController(Context context, DialogParams params) {
        this.context = context;
        this.params = params;
    }

    public void apply(FlexibleDialog dialog) {
        // 设置对话框的布局、动画、位置等
        dialog.setTheme(params.themeResId)
                .setLayout(params.layoutResId)
                .setGravity(params.gravity)
                .setWidth(params.width)
                .setHeight(params.height)
                .setAnimation(params.animation)
                .setCancelable(params.cancelable)
                .setCanceledOnTouchOutside(params.canceledOnTouchOutside)
                .setDimAmount(params.dimAmount)
                .setWindowType(params.windowType);

        // 初始化视图
        View contentView = dialog.getContentView();
        if (params.viewBinder != null) {
            params.viewBinder.bindView(contentView, dialog);
        }

        // 设置点击事件监听
        if (params.onViewClickListener != null && params.ids != null) {
            for (int id : params.ids) {
                View view = contentView.findViewById(id);
                if (view != null) {
                    view.setOnClickListener(v -> params.onViewClickListener.onViewClick(v, dialog));
                }
            }
        }
    }

    public static class DialogParams {
        @StyleRes
        int themeResId = R.style.WoDeDialog;
        @LayoutRes
        int layoutResId;
        int gravity = android.view.Gravity.CENTER;
        int width = WindowManager.LayoutParams.WRAP_CONTENT;
        int height = WindowManager.LayoutParams.WRAP_CONTENT;
        int animation = R.style.animate_dialog;
        boolean cancelable = true;
        boolean canceledOnTouchOutside = true;
        float dimAmount = 0.5f;
        int windowType = WindowManager.LayoutParams.TYPE_APPLICATION;
        ViewBinder viewBinder;
        OnDialogEventListener onDialogEventListener;
        OnViewClickListener onViewClickListener;
        int[] ids;

        public interface ViewBinder {
            void bindView(View view, FlexibleDialog dialog);
        }

        public interface OnDialogEventListener {
            void onDialogOpened(FlexibleDialog dialog);
            void onDialogClosed(FlexibleDialog dialog);
        }

        public interface OnViewClickListener {
            void onViewClick(View view, FlexibleDialog dialog);
        }
    }
}