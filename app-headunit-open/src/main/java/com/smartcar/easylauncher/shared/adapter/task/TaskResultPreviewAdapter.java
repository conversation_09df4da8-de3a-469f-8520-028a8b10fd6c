package com.smartcar.easylauncher.shared.adapter.task;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.TaskResultConst;
import com.smartcar.easylauncher.data.model.task.ResultModel;
import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * 任务结果预览适配器
 * 用于在推荐任务预览页面展示执行结果
 * 
 * <AUTHOR>
 */
public class TaskResultPreviewAdapter extends BaseQuickAdapter<ResultModel, QuickViewHolder> {
    private static final String TAG = "TaskResultPreviewAdapter";

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int position, @Nullable ResultModel item) {
        MyLog.d(TAG, "onBindViewHolder: position=" + position);

        if (item == null) {
            MyLog.w(TAG, "onBindViewHolder: item is null at position " + position);
            return;
        }

        MyLog.d(TAG, "onBindViewHolder: 绑定结果项 - ID=" + item.getId() +
                ", 名称=" + item.getName() +
                ", 类型=" + item.getType() +
                ", 执行=" + item.getExecute() +
                ", 延时=" + item.getDelayTime());

        // 设置执行顺序标记
        helper.setText(R.id.tv_step_number, String.valueOf(position + 1));
        setStepNumberBackground(helper, item.getId(), position);
        MyLog.d(TAG, "onBindViewHolder: 设置执行顺序=" + (position + 1));

        // 设置结果图标
        setResultIcon(helper, item.getId());

        // 设置结果名称
        String displayName = getResultDisplayName(item);
        MyLog.d(TAG, "onBindViewHolder: 显示名称=" + displayName);
        helper.setText(R.id.tv_result_name, displayName);

        // 设置结果描述
        String description = getResultDescription(item);
        MyLog.d(TAG, "onBindViewHolder: 描述=" + description);
        helper.setText(R.id.tv_result_description, description);

        MyLog.d(TAG, "onBindViewHolder: 结果项绑定完成 position=" + position);
    }

    /**
     * 设置执行顺序标记背景
     */
    private void setStepNumberBackground(@NonNull QuickViewHolder helper, int resultId, int position) {
        int backgroundRes;

        switch (resultId) {
            case TaskResultConst.RESULT_ID_DELAY:
                // 延时使用橙色
                backgroundRes = R.drawable.step_number_bg_orange;
                break;
            case TaskResultConst.RESULT_ID_APP:
                // APP启动使用蓝色
                backgroundRes = R.drawable.step_number_bg_blue;
                break;
            default:
                // 其他使用绿色
                backgroundRes = R.drawable.step_number_bg;
                break;
        }

        helper.setBackgroundResource(R.id.tv_step_number, backgroundRes);
    }

    /**
     * 设置结果图标
     */
    private void setResultIcon(QuickViewHolder helper, int resultId) {
        int iconRes = R.drawable.ic_task_app; // 默认图标

        switch (resultId) {
            case TaskResultConst.RESULT_ID_DELAY:
                iconRes = R.drawable.ic_task_yanshi;
                break;
            case TaskResultConst.RESULT_ID_WIFI:
                iconRes = R.drawable.ic_task_wifi;
                break;
            case TaskResultConst.RESULT_ID_BLUETOOTH:
                iconRes = R.drawable.ic_task_ble;
                break;
            case TaskResultConst.RESULT_ID_VOLUME:
                iconRes = R.drawable.ic_task_shengyin;
                break;
            case TaskResultConst.RESULT_ID_BRIGHTNESS:
                iconRes = R.drawable.ic_task_liangdu;
                break;
            case TaskResultConst.RESULT_ID_THEME:
                iconRes = R.drawable.ic_task_zhuti;
                break;
            case TaskResultConst.RESULT_ID_APP:
                iconRes = R.drawable.ic_task_app;
                break;
            case TaskResultConst.RESULT_ID_NAV:
                iconRes = R.drawable.ic_task_kuaijiedaohagn;
                break;
            case TaskResultConst.RESULT_ID_MUSIC:
                iconRes = R.drawable.ic_task_yinyue;
                break;
            case TaskResultConst.RESULT_ID_TIP:
                iconRes = R.drawable.ic_task_tip;
                break;
        }

        helper.setImageResource(R.id.iv_result_icon, iconRes);
    }
    
    /**
     * 获取结果显示名称
     */
    private String getResultDisplayName(ResultModel result) {
        // 优先使用设置的名称
        if (result.getName() != null && !result.getName().isEmpty()) {
            return result.getName();
        }

        // 根据ID返回默认名称
        switch (result.getId()) {
            case TaskResultConst.RESULT_ID_DELAY:
                int delayTime = result.getDelayTime();
                if (delayTime > 0) {
                    return "延时" + delayTime + "秒";
                } else {
                    return "延时等待";
                }
            case TaskResultConst.RESULT_ID_WIFI:
                return "WiFi控制";
            case TaskResultConst.RESULT_ID_BLUETOOTH:
                return "蓝牙控制";
            case TaskResultConst.RESULT_ID_VOLUME:
                return "音量调节";
            case TaskResultConst.RESULT_ID_BRIGHTNESS:
                return "亮度调节";
            case TaskResultConst.RESULT_ID_THEME:
                return "主题切换";
            case TaskResultConst.RESULT_ID_APP:
                if (result.getAppInfo() != null && result.getAppInfo().getAppName() != null) {
                    return result.getAppInfo().getAppName();
                } else if (result.getExecute() != null && !result.getExecute().isEmpty()) {
                    return result.getExecute();
                } else {
                    return "启动应用";
                }
            case TaskResultConst.RESULT_ID_NAV:
                return "导航功能";
            case TaskResultConst.RESULT_ID_MUSIC:
                return "音乐控制";
            case TaskResultConst.RESULT_ID_TIP:
                return "提示信息";
            default:
                return "未知操作";
        }
    }
    
    /**
     * 获取结果描述
     */
    private String getResultDescription(ResultModel result) {
        switch (result.getId()) {
            case TaskResultConst.RESULT_ID_DELAY:
                int delayTime = result.getDelayTime();
                if (delayTime > 0) {
                    return "等待" + delayTime + "秒后继续执行";
                } else {
                    return "延时等待执行";
                }

            case TaskResultConst.RESULT_ID_WIFI:
                return result.isSwitchState() ? "开启WiFi" : "关闭WiFi";

            case TaskResultConst.RESULT_ID_BLUETOOTH:
                return result.isSwitchState() ? "开启蓝牙" : "关闭蓝牙";

            case TaskResultConst.RESULT_ID_VOLUME:
                int volume = result.getVolume();
                int volumeType = result.getVolumeType();
                if (volumeType == 0) {
                    return "设置音量为" + volume + "%";
                } else if (volumeType == 1) {
                    return "音量增加" + volume + "%";
                } else {
                    return "音量减少" + volume + "%";
                }

            case TaskResultConst.RESULT_ID_BRIGHTNESS:
                int brightness = result.getBrightness();
                return "设置屏幕亮度为" + brightness + "%";

            case TaskResultConst.RESULT_ID_THEME:
                return "切换系统主题";

            case TaskResultConst.RESULT_ID_APP:
                if (result.getAppInfo() != null && result.getAppInfo().getAppName() != null) {
                    return "启动应用：" + result.getAppInfo().getAppName();
                } else if (result.getExecute() != null && !result.getExecute().isEmpty()) {
                    return "启动应用：" + result.getExecute();
                } else {
                    return "启动指定应用";
                }

            case TaskResultConst.RESULT_ID_NAV:
                int navigation = result.getNavigation();
                switch (navigation) {
                    case 0: return "启动导航到公司";
                    case 1: return "启动导航回家";
                    default: return "启动导航功能";
                }

            case TaskResultConst.RESULT_ID_MUSIC:
                int musicEvent = result.getMusicEvent();
                switch (musicEvent) {
                    case 0: return "暂停音乐播放";
                    case 1: return "开始音乐播放";
                    case 2: return "播放上一首";
                    case 3: return "播放下一首";
                    default: return "控制音乐播放";
                }

            case TaskResultConst.RESULT_ID_TIP:
                if (result.getExecute() != null && !result.getExecute().isEmpty()) {
                    return "显示提示：" + result.getExecute();
                } else {
                    return "显示提示信息";
                }

            default:
                return "执行指定操作";
        }
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int viewType) {
        return new QuickViewHolder(R.layout.item_task_result_preview, viewGroup);
    }
}
