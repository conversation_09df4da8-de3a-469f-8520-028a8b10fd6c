package com.smartcar.easylauncher.shared.dialog;


import android.annotation.SuppressLint;
import android.content.Context;
import android.view.KeyEvent;
import android.widget.TextView;

import androidx.fragment.app.FragmentManager;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.maning.mndialoglibrary.MToast;

import com.smartcar.easylauncher.BuildConfig;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.core.constants.StatusCodeModel;
import com.smartcar.easylauncher.data.model.network.LanZouDownloadLinkV2Model;
import com.smartcar.easylauncher.data.model.system.UpdateVersionModel;
import com.smartcar.easylauncher.data.model.system.NewUpdateResponse;
import com.smartcar.easylauncher.data.model.system.NewVersionInfo;
import com.smartcar.easylauncher.shared.utils.system.DeviceUtil;
import com.smartcar.easylauncher.shared.utils.download.DownloadConfig;
import com.smartcar.easylauncher.shared.utils.download.DownloadUtil;
import com.smartcar.easylauncher.shared.utils.file.FileUtils;

import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.view.progressbar.CircularProgressBar;
import com.timmy.tdialog.TDialog;

import java.lang.ref.WeakReference;


import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.schedulers.Schedulers;
import rxhttp.RxHttp;

/**
 * <AUTHOR>
 * @date 2024/07/04
 */
public class UpdateChecker {
    private final WeakReference<Context> mContextRef;
    private final WeakReference<FragmentManager> fragmentManagerRef;
    private CircularProgressBar mCircularProgressBar;
    private TextView tvSize, tvTime;
    private String updatePath;
    private final UpdateListener listener;
    private final boolean disableBackButton;
    private DownloadUtil downloadUtil;


    public interface UpdateListener {
        /**
         * 更新成功
         */
        void onUpdateSuccess();

        /**
         * 没有更新
         */
        void onNoUpdate();

        /**
         * 更新失败
         * @param e 错误信息
         */
        void onUpdateFailed(Exception e);
    }

    private UpdateChecker(Context context, FragmentManager fragmentManager, UpdateListener listener, boolean disableBackButton) {
        // 使用应用上下文避免Activity内存泄漏
        this.mContextRef = new WeakReference<>(context.getApplicationContext());
        this.fragmentManagerRef = new WeakReference<>(fragmentManager);
        this.listener = listener;
        this.disableBackButton = disableBackButton;
    }

    public static class UpdateCheckerBuilder {
        private Context mContext;
        private FragmentManager fragmentManager;
        private UpdateListener listener;
        private boolean disableBackButton; // Add this field

        public UpdateCheckerBuilder withDisableBackButton(boolean disableBackButton) {
            this.disableBackButton = disableBackButton;
            return this;
        }

        public UpdateCheckerBuilder withContext(Context context) {
            this.mContext = context;
            return this;
        }

        public UpdateCheckerBuilder withFragmentManager(FragmentManager fragmentManager) {
            this.fragmentManager = fragmentManager;
            return this;
        }

        public UpdateCheckerBuilder withUpdateListener(UpdateListener listener) {
            this.listener = listener;
            return this;
        }

        public UpdateChecker build() {
            return new UpdateChecker(mContext, fragmentManager, listener, disableBackButton);
        }

    }

    /**
     * 获取Context对象，如果引用已被回收则返回null
     */
    private Context getContext() {
        return mContextRef != null ? mContextRef.get() : null;
    }

    /**
     * 获取FragmentManager对象，如果引用已被回收则返回null
     */
    private FragmentManager getFragmentManager() {
        return fragmentManagerRef != null ? fragmentManagerRef.get() : null;
    }

    public void checkForUpdates() {
        // 使用新的更新检查接口
        checkForUpdatesWithNewApi();
    }

    public void checkForUpdatesFromCloud() {
        //私有云自动更新检测
        updateDetection();
    }

    /**
     * 使用新API检查更新
     */
    @SuppressLint("CheckResult")
    private void checkForUpdatesWithNewApi() {
        // 获取渠道类型和版本类型
        String channelType = getChannelType();
        String versionType = getVersionType();

        MyLog.v("UpdateChecker", "开始使用新API检查更新 - 渠道: " + channelType + ", 版本类型: " + versionType + ", 当前版本号: " + BuildConfig.VERSION_CODE);

        RxHttp.get(Const.NEW_UPDATE_CHECK)
                .add("platform", "android")
                .add("currentVersionCode", BuildConfig.VERSION_CODE)
                .add("channelType", channelType)
                .add("versionType", versionType)
                .toObservable(NewUpdateResponse.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(response -> {
                    MyLog.v("UpdateChecker", "新API更新检查响应: " + new Gson().toJson(response));
                    handleNewApiResponse(response);
                }, throwable -> {
                    MyLog.e("UpdateChecker", "新API检查更新失败: " + throwable.getMessage());
                    if (listener != null) {
                        listener.onUpdateFailed(new Exception(throwable));
                    }
                    // 失败时回退到私有云检查
                    updateDetection();
                });
    }

    /**
     * 处理新API响应
     */
    private void handleNewApiResponse(NewUpdateResponse response) {
        if (!response.isSuccess()) {
            MyLog.w("UpdateChecker", "新API响应失败: " + response.getMsg());
            if (listener != null) {
                listener.onUpdateFailed(new Exception(response.getMsg()));
            }
            // 失败时回退到私有云检查
            updateDetection();
            return;
        }

        if (response.noUpdate()) {
            MyLog.v("UpdateChecker", "没有可用更新");
            if (listener != null) {
                listener.onNoUpdate();
            }
            return;
        }

        if (response.hasUpdate()) {
            MyLog.v("UpdateChecker", "发现新版本: " + response.getData().getVersionName());
            if (listener != null) {
                listener.onUpdateSuccess();
            }
            showNewUpdateDialog(response.getData());
        }
    }

    /**
     * 获取渠道类型
     */
    private String getChannelType() {
        switch (BuildConfig.CHANNEL_CODE) {
            case 0:
                return "public";
            case 1:
                return "byd";
            default:
                return "custom";
        }
    }

    /**
     * 获取版本类型
     */
    private String getVersionType() {
        // 从BuildConfig获取版本类型，与config.gradle中的配置保持一致
        return BuildConfig.VERSION_TYPE;
    }

    /**
     * 显示新版本更新对话框
     */
    private void showNewUpdateDialog(NewVersionInfo versionInfo) {
        Context context = getContext();
        FragmentManager fragmentManager = getFragmentManager();

        if (context == null || fragmentManager == null) {
            MyLog.v("UpdateChecker", "Context或FragmentManager已被回收，无法显示更新对话框");
            return;
        }

        new TDialog.Builder(fragmentManager)
                .setLayoutRes(R.layout.dialog_version_upgrde_strong)
                .setScreenWidthAspect(context, 1f)
                .setScreenHeightAspect(context, 1f)
                .addOnClickListener(R.id.cpb_test_progress)
                .setCancelableOutside(false)
                .setOnBindViewListener(bindViewHolder -> {
                    bindViewHolder.setText(R.id.tv_upgrade_content, versionInfo.getDescription());
                    bindViewHolder.setText(R.id.tv_current_version, "当前版本号: " + DeviceUtil.getVersionName(context));
                    bindViewHolder.setText(R.id.tv_renew_version, "更新版本号: " + versionInfo.getVersionName());
                    bindViewHolder.setText(R.id.tv_size, "大小: " + versionInfo.getFormattedFileSize());
                    bindViewHolder.setText(R.id.tv_time, "发布时间: " + (versionInfo.getUrlExpireTime() != null ? versionInfo.getUrlExpireTime() : "未知"));
                    mCircularProgressBar = bindViewHolder.getView(R.id.cpb_test_progress);
                })
                .setOnKeyListener((dialog, keyCode, event) -> {
                    if (disableBackButton && keyCode == KeyEvent.KEYCODE_BACK) {
                        return true;
                    }
                    return false;
                })
                .setOnViewClickListener((viewHolder, view, tDialog) -> {
                    Context ctx = getContext();
                    if (ctx == null) {
                        tDialog.dismiss();
                        return;
                    }

                    switch (view.getId()) {
                        case R.id.cpb_test_progress:
                            if ("使用".equals(mCircularProgressBar.getTipText().toString()) && updatePath != null && !updatePath.isEmpty()) {
                                FileUtils.startInstall(ctx, updatePath);
                                return;
                            }
                            if (mCircularProgressBar.isInProgress()) {
                                mCircularProgressBar.cancelDownload();
                                if (downloadUtil != null) {
                                    downloadUtil.cancelDownload();
                                }
                            } else {
                                mCircularProgressBar.doStartProgress();
                                // 直接使用新接口返回的下载链接
                                doDownload(versionInfo.getRealDownloadUrl(), versionInfo.getFileName());
                            }
                            break;
                    }
                }).setOnDismissListener(dialog -> {
                    MyLog.v("UpdateChecker", "对话框消失");
                    if (downloadUtil != null) {
                        downloadUtil.cancelDownload();
                    }
                })
                .create()
                .show();
    }
    /**
     * 显示私有云版本更新对话框（兼容旧接口）
     */
    private void showLegacyUpdateDialog(UpdateVersionModel.ResultsDTO appBean) {
        Context context = getContext();
        FragmentManager fragmentManager = getFragmentManager();

        if (context == null || fragmentManager == null) {
            MyLog.v("UpdateChecker", "Context或FragmentManager已被回收，无法显示更新对话框");
            return;
        }

        new TDialog.Builder(fragmentManager)
                .setLayoutRes(R.layout.dialog_version_upgrde_strong)
                .setScreenWidthAspect(context, 1f)
                .setScreenHeightAspect(context, 1f)
                .addOnClickListener(R.id.cpb_test_progress)
                .setCancelableOutside(false)
                .setOnBindViewListener(bindViewHolder -> {
                    bindViewHolder.setText(R.id.tv_upgrade_content, appBean.getVersionDesc());
                    bindViewHolder.setText(R.id.tv_current_version, "当前版本号: " + DeviceUtil.getVersionName(context));
                    bindViewHolder.setText(R.id.tv_renew_version, "更新版本号: " + appBean.getVersionName());
                    bindViewHolder.setText(R.id.tv_size, "大小: " + (appBean.getVersionSize() != null ? appBean.getVersionSize() : "未知"));
                    bindViewHolder.setText(R.id.tv_time, "发布时间: " + (appBean.getVersionTime() != null ? appBean.getVersionTime() : "未知"));
                    mCircularProgressBar = bindViewHolder.getView(R.id.cpb_test_progress);
                })
                .setOnKeyListener((dialog, keyCode, event) -> {
                    if (disableBackButton && keyCode == KeyEvent.KEYCODE_BACK) {
                        return true;
                    }
                    return false;
                })
                .setOnViewClickListener((viewHolder, view, tDialog) -> {
                    Context ctx = getContext();
                    if (ctx == null) {
                        tDialog.dismiss();
                        return;
                    }

                    switch (view.getId()) {
                        case R.id.cpb_test_progress:
                            if ("使用".equals(mCircularProgressBar.getTipText().toString()) && updatePath != null && !updatePath.isEmpty()) {
                                FileUtils.startInstall(ctx, updatePath);
                                return;
                            }
                            if (mCircularProgressBar.isInProgress()) {
                                mCircularProgressBar.cancelDownload();
                                if (downloadUtil != null) {
                                    downloadUtil.cancelDownload();
                                }
                            } else {
                                mCircularProgressBar.doStartProgress();
                                // 私有云接口直接下载
                                doDownload(appBean.getVersionUrl(), appBean.getVersionName());
                            }
                            break;
                    }
                }).setOnDismissListener(dialog -> {
                    MyLog.v("UpdateChecker", "对话框消失");
                    if (downloadUtil != null) {
                        downloadUtil.cancelDownload();
                    }
                })
                .create()
                .show();
    }








    /**
     * 下载
     *
     * @param downloadUrl   下载地址
     * @param appname       应用名称
     */
    private void doDownload(String downloadUrl, String appname) {
        downloadUtil = new DownloadUtil.Builder()
                .withContext(getContext())
                .withFileExtension(DownloadConfig.APK_SUFFIX)
                .withFolderName(DownloadConfig.UPDATE_FOLDER)
                .build();
        downloadUtil.doDownload(downloadUrl, appname, new DownloadUtil.DownloadListener() {
            @Override
            public void onDownloadSuccess(String path) {
                updatePath = path;
                mCircularProgressBar.setTipsFinish("使用");
                mCircularProgressBar.doFinishProgress();
                MyLog.v("下载完成", "路径地址：  nDownLoadPath=" + path);
            }

            @Override
            public void onDownloadFailed(Throwable throwable) {
                MToast.makeTextShort("下载失败，请联系管理员");
                mCircularProgressBar.setTipsFinish("失败");
                updatePath = "";
            }

            @Override
            public void onProgressUpdate(int progress) {
                mCircularProgressBar.setProgress(progress, 100);
            }

            @Override
            public void onCancelDownload() {

            }
        });

    }

    @SuppressLint("CheckResult")
    private void updateDetection() {
        RxHttp.get(Const.LC_BASEURL + Const.AV_VERSION)
                .add("order", "-" + Const.AV_KEY_VERSION_INDEX)
                .toObservable(UpdateVersionModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(students -> {
                    //请求成功
                    MyLog.v("UpdateChecker", "私有云更新信息" + new Gson().toJson(students) + "");
                    if (students.getResults().size() > 0) {
                        String toJson = new Gson().toJson(students);
                        MyLog.v("UpdateChecker", "检测到版本数量 " + students.getResults().size());
                        for (UpdateVersionModel.ResultsDTO resultsDTO : students.getResults()) {
                            //如果版本类型相同，且状态正常，且渠道号相同
                            if (resultsDTO.getVersionType().equals(BuildConfig.VERSION_TYPE) && resultsDTO.getVersionState() == 1
                                    && resultsDTO.getChannelCode() == BuildConfig.CHANNEL_CODE) {
                                //如果版本号大于当前版本号
                                if (resultsDTO.getVersionCode() > DeviceUtil.getVersionCode(getContext())) {
                                    showLegacyUpdateDialog(resultsDTO);
                                    return;
                                }
                            }

                        }
                     //   MToast.makeTextShort("没有发现新版本");
                    } else {
                   //     MToast.makeTextShort("没有发现新版本");
                    }

                }, throwable -> {
                    //请求失败
                    MToast.makeTextShort("网络连接异常,请检查网络");
                    MyLog.v("内测版本更新检测", "失败");

                });
    }



    /**
     * 释放资源，避免内存泄漏
     * 在不需要使用UpdateChecker时调用此方法
     */
    public void release() {
        if (downloadUtil != null) {
            downloadUtil.cancelDownload();
            downloadUtil = null;
        }
    }
}