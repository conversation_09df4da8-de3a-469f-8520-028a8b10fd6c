package com.smartcar.easylauncher.shared.adapter.task;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.TaskConditionConst;
import com.smartcar.easylauncher.core.constants.TaskResultConst;
import com.smartcar.easylauncher.data.model.task.RecommendTaskModel;

/**
 * 推荐任务适配器
 * 用于发现页面展示推荐任务
 *
 * <AUTHOR>
 */
public class RecommendTaskAdapter extends BaseQuickAdapter<RecommendTaskModel, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int position, @Nullable RecommendTaskModel item) {
        if (item == null) return;

        // 设置任务名称
        helper.setText(R.id.tv_task_name, item.getTaskName());

        // 设置任务描述
        helper.setText(R.id.tv_task_description, item.getShortDescription());

        // 设置任务分类
        helper.setText(R.id.tv_task_category, item.getCategory());

        // 设置评分
        helper.setText(R.id.tv_task_rating, item.getRatingStars());

        // 设置使用人数 - 优化显示格式
        String usageText = formatUsageCount(item.getUsageCount());
        helper.setText(R.id.tv_usage_count, usageText);

        // 设置热门标签
        View hotTag = helper.getView(R.id.tv_hot_tag);
        if (item.isHot()) {
            hotTag.setVisibility(View.VISIBLE);
            helper.setText(R.id.tv_hot_tag, "热门");
        } else if (item.isNew()) {
            hotTag.setVisibility(View.VISIBLE);
            helper.setText(R.id.tv_hot_tag, "新品");
        } else {
            hotTag.setVisibility(View.GONE);
        }


        // 设置条件图标
        if (item.getConditions() != null && !item.getConditions().isEmpty()) {
            int conditionId = item.getConditions().get(0).getId();
            setConditionIcon(helper, conditionId);
        }

        // 设置结果图标
        if (item.getResults() != null && !item.getResults().isEmpty()) {
            int resultId = item.getResults().get(0).getId();
            setResultIcon(helper, resultId);
        }

        // 设置可重复标识
        TextView repeatableText = helper.getView(R.id.tv_repeatable);
        if (item.isRepeatable()) {
            repeatableText.setText("可重复");
            repeatableText.setVisibility(View.VISIBLE);
        } else {
            repeatableText.setText("单次");
            repeatableText.setVisibility(View.VISIBLE);
        }

        // 添加点击动画效果
//        helper.itemView.setOnTouchListener((v, event) -> {
//            switch (event.getAction()) {
//                case android.view.MotionEvent.ACTION_DOWN:
//                    v.animate().scaleX(0.95f).scaleY(0.95f).setDuration(100).start();
//                    break;
//                case android.view.MotionEvent.ACTION_UP:
//                case android.view.MotionEvent.ACTION_CANCEL:
//                    v.animate().scaleX(1.0f).scaleY(1.0f).setDuration(100).start();
//                    break;
//            }
//            return false;
//        });
    }

    /**
     * 格式化使用人数显示
     */
    private String formatUsageCount(int count) {
        if (count >= 10000) {
            return String.format("%.1fw人使用", count / 10000.0);
        } else if (count >= 1000) {
            return String.format("%.1fk人使用", count / 1000.0);
        } else {
            return count + "人使用";
        }
    }
    
    /**
     * 设置条件图标
     */
    private void setConditionIcon(QuickViewHolder helper, int conditionId) {
        int iconRes = R.drawable.ic_task_qidong; // 默认图标

        switch (conditionId) {
            case TaskConditionConst.TYPE_WIFI_SWITCH:
            case TaskConditionConst.TYPE_NETWORK_STATE:
                iconRes = R.drawable.ic_task_wifi;
                break;
            case TaskConditionConst.TYPE_BLUETOOTH_SWITCH:
            case TaskConditionConst.TYPE_BLUETOOTH_DEVICE:
                iconRes = R.drawable.ic_task_ble;
                break;
            case TaskConditionConst.TYPE_DEVICE_START:
                iconRes = R.drawable.ic_task_kaiji;
                break;
            case TaskConditionConst.TYPE_START:
                iconRes = R.drawable.ic_task_qidong;
                break;
            case TaskConditionConst.TYPE_MUSIC_STATE:
                iconRes = R.drawable.ic_task_yinyue;
                break;
            case TaskConditionConst.TYPE_SUN:
                iconRes = R.drawable.ic_task_richu;
                break;
            case TaskConditionConst.TYPE_SPEED:
                iconRes = R.drawable.ic_task_chesu;
                break;
            case TaskConditionConst.TYPE_GEO_FENCE:
                iconRes = R.drawable.ic_task_diliweilan;
                break;
            case TaskConditionConst.TYPE_TIME_RANGE:
            case TaskConditionConst.TYPE_TIMING:
            case TaskConditionConst.TYPE_PERIOD:
                iconRes = R.drawable.ic_task_shijianfanwei;
                break;
            case TaskConditionConst.TYPE_SCREEN:
                iconRes = R.drawable.ic_task_screen;
                break;
        }

        helper.setImageResource(R.id.iv_condition, iconRes);
    }
    
    /**
     * 设置结果图标
     */
    private void setResultIcon(QuickViewHolder helper, int resultId) {
        int iconRes = R.drawable.ic_task_app; // 默认图标

        switch (resultId) {
            case TaskResultConst.RESULT_ID_DELAY:
                iconRes = R.drawable.ic_task_yanshi;
                break;
            case TaskResultConst.RESULT_ID_WIFI:
                iconRes = R.drawable.ic_task_wifi;
                break;
            case TaskResultConst.RESULT_ID_BLUETOOTH:
                iconRes = R.drawable.ic_task_ble;
                break;
            case TaskResultConst.RESULT_ID_VOLUME:
                iconRes = R.drawable.ic_task_shengyin;
                break;
            case TaskResultConst.RESULT_ID_BRIGHTNESS:
                iconRes = R.drawable.ic_task_liangdu;
                break;
            case TaskResultConst.RESULT_ID_THEME:
                iconRes = R.drawable.ic_task_zhuti;
                break;
            case TaskResultConst.RESULT_ID_APP:
                iconRes = R.drawable.ic_task_app;
                break;
            case TaskResultConst.RESULT_ID_NAV:
                iconRes = R.drawable.ic_task_kuaijiedaohagn;
                break;
            case TaskResultConst.RESULT_ID_MUSIC:
                iconRes = R.drawable.ic_task_yinyue;
                break;
            case TaskResultConst.RESULT_ID_TIP:
                iconRes = R.drawable.ic_task_tip;
                break;
        }

        helper.setImageResource(R.id.iv_result, iconRes);
    }


    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int viewType) {
        return new QuickViewHolder(R.layout.recommend_task_item, viewGroup);
    }
}
