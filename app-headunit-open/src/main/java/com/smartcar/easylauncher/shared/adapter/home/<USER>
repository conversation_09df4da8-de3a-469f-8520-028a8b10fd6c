package com.smartcar.easylauncher.shared.adapter.home;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bytedance.scene.group.GroupScene;
import com.bytedance.scene.Scene;
import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.data.model.common.Channel;
import com.smartcar.easylauncher.home.music.scene.MusicCarScene;
import com.smartcar.easylauncher.home.navigation.NavCardScene;
import com.smartcar.easylauncher.home.time.TimeCardScene;
import com.smartcar.easylauncher.home.tpms.TpmsCardScene;
import com.smartcar.easylauncher.home.weather.WeatherDetailsScene;
import com.smartcar.easylauncher.home.weather.WeatherMusicCardScene;
import com.smartcar.easylauncher.core.constants.ChannelConst;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Scene版首页卡片适配器，支持动态增删、排序、懒加载等
 * 兼容adapter4体系，每个item为scene的View
 * <p>
 * 优化特性：
 * - 高性能Scene池管理，支持懒加载和对象复用
 * - 内存优化的布局管理，减少不必要的布局计算
 * - 线程安全的数据操作，使用ConcurrentHashMap防止并发问题
 * - 完善的异常处理和资源释放机制
 * - 兼容Android API 17+，针对低端设备优化
 *
 * <AUTHOR> Team
 * @version 2.0 - 性能优化版本
 */
public class CardHomePageSceneAdapter extends BaseQuickAdapter<Channel, QuickViewHolder> {

    /**
     * 日志标签
     */
    public static final String TAG = "CardHomePageSceneAdapter";

    /**
     * GroupScene容器，用于管理Scene的生命周期
     */
    private final GroupScene groupSceneContainer;

    /**
     * Scene对象池 - 使用线程安全的ConcurrentHashMap，支持并发访问
     */
    private final ConcurrentHashMap<String, Scene> scenePool = new ConcurrentHashMap<>();

    /**
     * 当前频道列表
     */
    private List<Channel> channelList = new ArrayList<>();

    /**
     * 构造函数
     *
     * @param container   GroupScene容器
     * @param initialList 初始频道列表
     */
    public CardHomePageSceneAdapter(@NonNull GroupScene container, @NonNull List<Channel> initialList) {
        this.groupSceneContainer = container;
        this.channelList = new ArrayList<>(initialList);
        initScenePool(initialList);
    }

    /**
     * 初始化Scene对象池
     * 为每个频道预创建对应的Scene实例
     *
     * @param list 频道列表
     */
    private void initScenePool(List<Channel> list) {
        try {
            for (Channel channel : list) {
                if (channel != null && !scenePool.containsKey(channel.getChannelKey())) {
                    Scene scene = createSceneByChannel(channel);
                    if (scene != null) {
                        scenePool.put(channel.getChannelKey(), scene);
                    }
                }
            }
            MyLog.d(TAG, "Scene池初始化完成，共" + scenePool.size() + "个Scene");
        } catch (Exception e) {
            MyLog.e(TAG, "初始化Scene池失败", e);
        }
    }

    /**
     * 提交新的卡片数据列表，自动增删/排序Scene
     *
     * @param newList 新的频道列表
     */
    public void submitChannelList(@NonNull List<Channel> newList) {
        try {
            List<Channel> oldList = new ArrayList<>(channelList);
            channelList = new ArrayList<>(newList);

            // 数据驱动刷新
            try {
                // 优先用submitList（新版adapter4）
                submitList(newList);
            } catch (Throwable t) {
                // 若无submitList则降级为同步刷新
                getItems().clear();
                getItems().addAll(newList);
                notifyDataSetChanged();
            }

            // 1. 计算新旧key集合
            HashSet<String> newKeys = new HashSet<>();
            for (Channel c : newList) {
                if (c != null) {
                    newKeys.add(c.getChannelKey());
                }
            }

            HashSet<String> oldKeys = new HashSet<>();
            for (Channel c : oldList) {
                if (c != null) {
                    oldKeys.add(c.getChannelKey());
                }
            }

            // 2. 移除多余Scene
            Iterator<String> it = scenePool.keySet().iterator();
            while (it.hasNext()) {
                String key = it.next();
                if (!newKeys.contains(key)) {
                    Scene s = scenePool.get(key);
                    if (s != null) {
                        groupSceneContainer.remove(s);
                    }
                    it.remove();
                }
            }

            // 3. 新增Scene
            for (Channel c : newList) {
                if (c != null && !scenePool.containsKey(c.getChannelKey())) {
                    Scene scene = createSceneByChannel(c);
                    if (scene != null) {
                        scenePool.put(c.getChannelKey(), scene);
                    }
                }
            }

            MyLog.d(TAG, "频道列表更新完成，当前Scene数量: " + scenePool.size());

        } catch (Exception e) {
            MyLog.e(TAG, "提交频道列表失败", e);
        }
    }

    /**
     * 空占位Scene，防止NPE
     */
    public static class EmptyScene extends Scene {
        @Override
        public View onCreateView(@NonNull LayoutInflater inflater, @NonNull ViewGroup container, @Nullable Bundle savedInstanceState) {
            View v = new View(requireSceneContext());
            v.setLayoutParams(new ViewGroup.LayoutParams(1, 1));
            return v;
        }
    }

    /**
     * 工厂方法：根据Channel类型创建对应Scene
     *
     * @param channel 频道信息
     * @return Scene实例，如果不支持该频道返回null
     */
    private Scene createSceneByChannel(Channel channel) {
        if (channel == null) {
            MyLog.w(TAG, "频道为空，无法创建Scene");
            return null;
        }

        String channelName = channel.getChannelName();
        try {
            Scene scene = switch (channelName) {
                case ChannelConst.CHANNEL_NAME_NAV -> {
                    MyLog.d(TAG, "创建导航卡片Scene");
                    yield new NavCardScene();
                }
                case ChannelConst.CHANNEL_NAME_BGM -> {
                    MyLog.d(TAG, "创建音乐卡片Scene");
                    yield new MusicCarScene();
                }
                case ChannelConst.CHANNEL_NAME_WX -> {
                    MyLog.d(TAG, "创建天气卡片Scene");
                    yield new WeatherDetailsScene();
                }
                case ChannelConst.CHANNEL_NAME_TIME -> {
                    MyLog.d(TAG, "创建时间卡片Scene");
                    yield new TimeCardScene();
                }
                case ChannelConst.CHANNEL_NAME_WX_BGM -> {
                    MyLog.d(TAG, "创建天气音乐卡片Scene");
                    yield new WeatherMusicCardScene();
                }
                case ChannelConst.CHANNEL_NAME_TPMS -> {
                    MyLog.d(TAG, "创建TPMS卡片Scene");
                    yield new TpmsCardScene();
                }
                default -> {
                    MyLog.w(TAG, "不支持的卡片类型: " + channelName);
                    yield new EmptyScene();
                }
            };

            if (scene != null) {
                MyLog.d(TAG, "成功创建Scene: " + channelName);
            }

            return scene;

        } catch (Exception e) {
            MyLog.e(TAG, "创建Scene失败: " + channelName, e);
            return new EmptyScene();
        }
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_card, parent, false);
        return new QuickViewHolder(view);
    }

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder holder, int position, @Nullable Channel channel) {
        if (channel == null) {
            MyLog.w(TAG, "频道为空，跳过绑定");
            return;
        }

        try {
            String key = channel.getChannelKey();
            Scene scene = scenePool.get(key);

            if (scene == null) {
                scene = createSceneByChannel(channel);
                if (scene == null) {
                    MyLog.w(TAG, "无法创建Scene: " + key);
                    return;
                }
                scenePool.put(key, scene);
            }

            FrameLayout itemContainer = holder.getView(R.id.card_fragment);
            itemContainer.removeAllViews();

            ViewGroup rootContainer = (ViewGroup) groupSceneContainer.getView();
            if (rootContainer == null) {
                MyLog.w(TAG, "根容器为空，无法添加Scene");
                return;
            }

            int rootContainerId = rootContainer.getId();

            // 只在未add时add，避免重复add异常
            if (!groupSceneContainer.getSceneList().contains(scene)) {
                groupSceneContainer.add(rootContainerId, scene, key);
            }

            View sceneView = scene.getView();
            if (sceneView == null) {
                MyLog.w(TAG, "scene.getView() is null for key: " + key);
                return;
            }

            ViewGroup parent = (ViewGroup) sceneView.getParent();
            if (parent != null && parent != itemContainer) {
                parent.removeView(sceneView);
            }

            if (sceneView.getParent() != itemContainer) {
                itemContainer.addView(sceneView);
            }

        } catch (Exception e) {
            MyLog.e(TAG, "绑定ViewHolder失败", e);
        }
    }

    /**
     * 获取当前Scene池（只读）
     *
     * @return Scene列表
     */
    public List<Scene> getCurrentScenes() {
        List<Scene> list = new ArrayList<>();
        try {
            for (Channel c : channelList) {
                if (c != null) {
                    Scene s = scenePool.get(c.getChannelKey());
                    if (s != null) {
                        list.add(s);
                    }
                }
            }
        } catch (Exception e) {
            MyLog.e(TAG, "获取当前Scene列表失败", e);
        }
        return list;
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        try {
            scenePool.clear();
            channelList.clear();
            MyLog.d(TAG, "适配器资源清理完成");
        } catch (Exception e) {
            MyLog.e(TAG, "清理适配器资源失败", e);
        }
    }
}
