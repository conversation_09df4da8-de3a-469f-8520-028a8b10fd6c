package com.smartcar.easylauncher.shared.adapter.common;

import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.data.model.common.KeyValuePair;

import java.util.ArrayList;
import java.util.List;

/**
 * 键值对适配器，用于管理和显示键值对列表
 *
 * <AUTHOR>
 * @date 2024/07/14
 */
public class KeyValueAdapter extends RecyclerView.Adapter<KeyValueAdapter.ViewHolder> {
    
    private final Context context;
    private final List<KeyValuePair> pairs;
    private final OnKeyValueChangeListener listener;
    
    /**
     * 键值对变化监听器
     */
    public interface OnKeyValueChangeListener {
        void onKeyValueChanged();
        void onKeyValueDeleted(int position);
    }
    
    public KeyValueAdapter(Context context, List<KeyValuePair> pairs, OnKeyValueChangeListener listener) {
        this.context = context;
        this.pairs = pairs;
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_key_value_pair, parent, false);
        return new ViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        KeyValuePair pair = pairs.get(position);
        
        // 设置键名
        holder.etKey.setText(pair.getKey());
        
        // 设置值类型下拉框
        String[] typeNames = new String[KeyValuePair.ValueType.values().length];
        for (int i = 0; i < KeyValuePair.ValueType.values().length; i++) {
            typeNames[i] = KeyValuePair.ValueType.values()[i].getDisplayName();
        }
        
        ArrayAdapter<String> spinnerAdapter = new ArrayAdapter<>(
                context, android.R.layout.simple_spinner_item, typeNames);
        spinnerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        holder.spinnerType.setAdapter(spinnerAdapter);
        
        // 设置选中的值类型
        for (int i = 0; i < KeyValuePair.ValueType.values().length; i++) {
            if (KeyValuePair.ValueType.values()[i] == pair.getValueType()) {
                holder.spinnerType.setSelection(i);
                break;
            }
        }
        
        // 设置值
        holder.etValue.setText(pair.getValue());
        
        // 监听键名变化
        holder.etKey.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }
            
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                int adapterPosition = holder.getAdapterPosition();
                if (adapterPosition != RecyclerView.NO_POSITION) {
                    pairs.get(adapterPosition).setKey(s.toString());
                    if (listener != null) {
                        listener.onKeyValueChanged();
                    }
                }
            }
            
            @Override
            public void afterTextChanged(Editable s) {
            }
        });
        
        // 监听值变化
        holder.etValue.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }
            
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                int adapterPosition = holder.getAdapterPosition();
                if (adapterPosition != RecyclerView.NO_POSITION) {
                    pairs.get(adapterPosition).setValue(s.toString());
                    
                    // 验证值是否与类型匹配
                    validateValue(holder, pairs.get(adapterPosition));
                    
                    if (listener != null) {
                        listener.onKeyValueChanged();
                    }
                }
            }
            
            @Override
            public void afterTextChanged(Editable s) {
            }
        });
        
        // 监听值类型变化
        holder.spinnerType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int typePosition, long id) {
                int adapterPosition = holder.getAdapterPosition();
                if (adapterPosition != RecyclerView.NO_POSITION) {
                    KeyValuePair.ValueType newType = KeyValuePair.ValueType.values()[typePosition];
                    pairs.get(adapterPosition).setValueType(newType);
                    
                    // 验证值是否与类型匹配
                    validateValue(holder, pairs.get(adapterPosition));
                    
                    if (listener != null) {
                        listener.onKeyValueChanged();
                    }
                }
            }
            
            @Override
            public void onNothingSelected(AdapterView<?> parent) {
            }
        });
        
        // 设置删除按钮点击事件
        holder.btnDelete.setOnClickListener(v -> {
            int adapterPosition = holder.getAdapterPosition();
            if (adapterPosition != RecyclerView.NO_POSITION) {
                if (listener != null) {
                    listener.onKeyValueDeleted(adapterPosition);
                }
            }
        });
        
        // 初始验证
        validateValue(holder, pair);
    }
    
    /**
     * 验证值是否与类型匹配
     *
     * @param holder ViewHolder
     * @param pair   键值对
     */
    private void validateValue(ViewHolder holder, KeyValuePair pair) {
        if (!pair.isValueValid()) {
            holder.tvError.setVisibility(View.VISIBLE);
            holder.tvError.setText(pair.getErrorMessage());
        } else {
            holder.tvError.setVisibility(View.GONE);
        }
    }
    
    @Override
    public int getItemCount() {
        return pairs.size();
    }
    
    /**
     * 添加一个新的键值对
     */
    public void addKeyValuePair() {
        pairs.add(new KeyValuePair());
        notifyItemInserted(pairs.size() - 1);
        if (listener != null) {
            listener.onKeyValueChanged();
        }
    }
    
    /**
     * 删除指定位置的键值对
     *
     * @param position 位置
     */
    public void removeKeyValuePair(int position) {
        if (position >= 0 && position < pairs.size()) {
            pairs.remove(position);
            notifyItemRemoved(position);
            notifyItemRangeChanged(position, pairs.size() - position);
            if (listener != null) {
                listener.onKeyValueChanged();
            }
        }
    }
    
    /**
     * 获取所有键值对
     *
     * @return 键值对列表
     */
    public List<KeyValuePair> getKeyValuePairs() {
        return new ArrayList<>(pairs);
    }
    
    /**
     * 设置新的键值对列表
     *
     * @param newPairs 新的键值对列表
     */
    public void setKeyValuePairs(List<KeyValuePair> newPairs) {
        pairs.clear();
        if (newPairs != null) {
            pairs.addAll(newPairs);
        }
        notifyDataSetChanged();
        if (listener != null) {
            listener.onKeyValueChanged();
        }
    }
    
    /**
     * 检查所有键值对是否有效
     *
     * @return 是否所有键值对都有效
     */
    public boolean validateAllPairs() {
        for (KeyValuePair pair : pairs) {
            if (!pair.isValueValid()) {
                MToast.makeTextShort("存在无效的参数值，请检查");
                return false;
            }
            if (pair.getKey() == null || pair.getKey().trim().isEmpty()) {
                MToast.makeTextShort("键名不能为空");
                return false;
            }
        }
        return true;
    }
    
    /**
     * ViewHolder 类
     */
    static class ViewHolder extends RecyclerView.ViewHolder {
        EditText etKey;
        Spinner spinnerType;
        EditText etValue;
        ImageButton btnDelete;
        TextView tvError;
        
        ViewHolder(View itemView) {
            super(itemView);
            etKey = itemView.findViewById(R.id.et_key);
            spinnerType = itemView.findViewById(R.id.spinner_type);
            etValue = itemView.findViewById(R.id.et_value);
            btnDelete = itemView.findViewById(R.id.btn_delete);
            tvError = itemView.findViewById(R.id.tv_error);
        }
    }
} 