package com.smartcar.easylauncher.shared.adapter.common

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.smartcar.easylauncher.shared.utils.apportutil.FileUtils

import com.smartcar.easylauncher.R
import com.smartcar.easylauncher.model.FastTransferInfoModel
import com.smartcar.easylauncher.infrastructure.skin.SkinManager

/**
 * <AUTHOR>
 * @date 2022/6/20
 * @email <EMAIL>
 * @desc
 */
class FilesAdapter constructor(val mList: MutableList<FastTransferInfoModel> = mutableListOf()) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private val TAG = "FilesAdapter"
    private lateinit var mContext: Context

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        mContext = parent.context
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            -1 -> EmptyViewHolder(inflater.inflate(R.layout.empty_view, parent, false))
            else -> FileViewHolder(inflater.inflate(R.layout.item_file, parent, false))
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is FileViewHolder) {
            val infoModel = mList[position]
            // 显示文件名称，如果是APK则显示版本号
            holder.mTvName.text = if (infoModel.type == 1) {
                infoModel.name + "(v" + infoModel.version + ")"
            } else {
                infoModel.name
            }
            
            // 设置操作按钮文本
            holder.mOpen.text = when (infoModel.type) {
                1 -> "安装"  // APK文件
                2 -> "使用"  // 皮肤文件
                else -> "打开" // 其他文件
            }
            
            // 显示文件大小和路径
            holder.mTvSize.text = infoModel.size
            holder.mTvPath.text = infoModel.path
            
            // 设置文件图标
            when (infoModel.type) {
                1 -> holder.ivIcon.setImageDrawable(infoModel.icon) // APK文件使用应用图标
                2 -> holder.ivIcon.setImageResource(R.drawable.icon_skin) // 皮肤文件
                else -> holder.ivIcon.setImageResource(FileUtils.getIconByPath(infoModel.path)) // 其他文件
            }
            
            // 设置操作按钮点击事件
            holder.mOpen.setOnClickListener {
                if (infoModel.type == 2) {
                    // 皮肤文件处理
                    SkinManager.getInstance().otherFolderReplication(infoModel.path, infoModel.name)
                    SkinManager.getInstance().loadSkin(infoModel.name)
                } else {
                    // APK或其他文件处理
                    FileUtils.openFileorAPk(mContext, infoModel)
                }
            }
            
            // 设置删除按钮点击事件
            holder.mDelete.setOnClickListener { FileUtils.delete(infoModel.path) }

            // 显示文件时间
            holder.mTvTime.text = infoModel.timeStr
        }
    }


    override fun getItemCount(): Int {
        return if (mList.size > 0) mList.size else 1
    }

    internal inner class EmptyViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    internal inner class FileViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        var mTvName: TextView
        var mTvSize: TextView
        var mTvTime: TextView
        var mOpen: TextView
        var mDelete: TextView
        var mTvPath: TextView
        var ivIcon: ImageView

        init {
            mTvName = view.findViewById(R.id.tv_name) as TextView
            mTvSize = view.findViewById(R.id.tv_size) as TextView
            mTvTime = view.findViewById(R.id.tv_time)
            mOpen = view.findViewById(R.id.tv_open) as TextView
            mTvPath = view.findViewById(R.id.tv_path) as TextView
            mDelete = view.findViewById(R.id.tv_delete) as TextView
            ivIcon = view.findViewById(R.id.iv_icon) as ImageView
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (mList.size == 0) {
            -1
        } else {
            0
        }
    }
}