package com.smartcar.easylauncher.shared.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Build;
import android.text.method.LinkMovementMethod;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.FragmentManager;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.hjq.shape.view.ShapeCheckBox;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.blurview.BVConstraintLayout;
import com.smartcar.easylauncher.BuildConfig;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.infrastructure.amap.LocationService;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.core.constants.StatusCodeModel;
import com.smartcar.easylauncher.infrastructure.event.scope.notice.cody.GeneralNotificationScopeBus;
import com.smartcar.easylauncher.core.manager.AuthorityManager;
import com.smartcar.easylauncher.core.manager.UserManager;
import com.smartcar.easylauncher.data.model.common.GeneralNoticeModel;
import com.smartcar.easylauncher.data.model.navigation.LocateInforModel;
import com.smartcar.easylauncher.data.model.user.CheckQrCodeStatusModel;
import com.smartcar.easylauncher.data.model.user.CreateQrcodeModel;
import com.smartcar.easylauncher.data.model.user.LoginRecordModel;
import com.smartcar.easylauncher.data.model.user.UserModel;
import com.smartcar.easylauncher.shared.utils.time.CountdownTimer;
import com.smartcar.easylauncher.shared.utils.system.DeviceIdUtil;
import com.smartcar.easylauncher.shared.utils.ui.HyperlinkText;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.view.RoundCornerImageView;
import com.timmy.tdialog.TDialog;

import cn.bfy.dualsim.DeviceIdentifierUtils;
import cn.bfy.dualsim.TelephonyManagement;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import rxhttp.RxHttp;
import rxhttp.wrapper.cache.CacheMode;

import com.smartcar.easylauncher.core.constants.Constants;

/**
 * <AUTHOR>
 * @date 2024/07/04
 */
public class LoginDialog {
    private static final String TAG = "LoginDialog";
    private TDialog mDialog;
    private TextView tvHint;
    private TextView tvAgreement;
    private Button button;
    private RoundCornerImageView imageView;
    private final Context mContext;
    private BVConstraintLayout elPerson;
    private CountdownTimer timer;
    private final FragmentManager fragmentManager;
    private ShapeCheckBox landscapeIosBar;

    private OnLoginSuccessListener onLoginSuccessListener;

    public void setOnLoginSuccessListener(OnLoginSuccessListener onLoginSuccessListener) {
        this.onLoginSuccessListener = onLoginSuccessListener;
    }


    /**
     * 登录成功回调
     * @param fragmentManager   当前fragmentManager
     * @param context   当前上下文
     */
    private LoginDialog(FragmentManager fragmentManager, Context context) {
        this.fragmentManager = fragmentManager;
        this.mContext = context.getApplicationContext();
    }

    /**
     *  创建登录弹窗
     * @param fragmentManager   当前fragmentManager
     * @param context   当前上下文
     * @return  登录弹窗
     */
    public static LoginDialog createDialog(FragmentManager fragmentManager, Context context) {
        return new LoginDialog(fragmentManager, context);
    }

    public void showDialog() {
        if (mDialog != null && mDialog.isShowing()) {
            return; // Prevent multiple dialogs
        }

        mDialog = new TDialog.Builder(fragmentManager)
                .setLayoutRes(R.layout.dialog_login)
                .setScreenWidthAspect(mContext, 1f)
                .setScreenHeightAspect(mContext, 1f)
                .addOnClickListener(R.id.bt_concur, R.id.iv_grzx)
                .setCancelableOutside(true)
                .setOnBindViewListener(bindViewHolder -> {
                    tvHint = bindViewHolder.getView(R.id.tv_hint);
                    tvAgreement = bindViewHolder.getView(R.id.tv_agreement);
                    button = bindViewHolder.getView(R.id.bt_concur);
                    imageView = bindViewHolder.getView(R.id.iv_five);
                    elPerson = bindViewHolder.getView(R.id.el_person);
                    landscapeIosBar = bindViewHolder.getView(R.id.landscape_ios_bar);
                    ConstraintLayout constraintLayout = bindViewHolder.getView(R.id.cl_layou);
                    final float radius = 5f;

                    elPerson.setupWith(constraintLayout).setBlurRadius(radius);
                    tvAgreement.setText(HyperlinkText.setHyperlinkText(mContext, mContext.getString(R.string.login_user_and_privacy)));
                    tvAgreement.setMovementMethod(LinkMovementMethod.getInstance());
                })
                .setOnViewClickListener((viewHolder, view, tDialog) -> handleViewClick(view))
                .setOnDismissListener(dialog -> {
                    if (timer != null) {
                        timer.stop();
                    }
                    mDialog = null;
                })
                .create().show();
    }

    private void handleViewClick(View view) {
        switch (view.getId()) {
            case R.id.bt_concur:
                tvHint.setText("正在生成二维码，请稍后...");
                tvAgreement.setText("请使用微信扫一扫进行扫码授权登录");
                button.setVisibility(View.GONE);
                landscapeIosBar.setVisibility(View.GONE);
                createQrCode();
                break;
            case R.id.iv_grzx:
                mDialog.dismiss();
                mDialog = null;
                break;
            default:
                break;
        }
    }

    @SuppressLint("CheckResult")
    private void createQrCode() {
        RxHttp.get(Const.CREATEQRCODE)
                .setCacheMode(CacheMode.ONLY_NETWORK) //当前请求先读取缓存，失败后，再请求网络
                .toObservable(CreateQrcodeModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(this::handleQrCodeResponse, this::handleNetworkError);
    }


    private void handleQrCodeResponse(CreateQrcodeModel createQrcodeModel) {
        MyLog.v(TAG, "生成二维码" + new Gson().toJson(createQrcodeModel));
        if (createQrcodeModel.getCode() == StatusCodeModel.SUCCESS) {
            loadImage(createQrcodeModel.getQrcode());
            startCountdown(createQrcodeModel.getScene());
        } else {
            handleQrCodeFailure();
        }
    }

    private void handleNetworkError(Throwable throwable) {
        MyLog.v(TAG, "生成二维码失败" + throwable.getMessage());
        updateUI("网络连接异常", "重新获取二维码");
        showToast("网络连接异常,请检查网络");
    }

    private void loadImage(String qrcode) {
        Glide.with(mContext).load(Const.SHOWQRCODE + qrcode).into(imageView);
        elPerson.setVisibility(View.GONE);
        tvHint.setVisibility(View.GONE);
    }

    private void startCountdown(String scene) {

        timer = new CountdownTimer.Builder()
                .setTotalTime(60000)
                .setRefreshInterval(1000)
                .setOnTick(() -> checkQrCodeStatus(scene))
                .setOnFinish(this::handleCountdownFinish)
                .build();
        timer.start();
    }

    private void handleCountdownFinish() {
        updateUI("二维码已失效，请重新获取", "重新获取二维码");
        MyLog.v(TAG, "倒计时结束");
    }

    private void handleQrCodeFailure() {
        updateUI("获取二维码失败", "重新获取二维码");
    }


    private void updateUI(String hintMessage, String buttonText) {
        elPerson.setVisibility(View.VISIBLE);
        tvHint.setVisibility(View.VISIBLE);
        tvHint.setText(hintMessage);
        if (buttonText.isEmpty()) {
            button.setVisibility(View.GONE);
        } else {
            button.setVisibility(View.VISIBLE);
            button.setText(buttonText);
        }

    }

    private void showToast(String message) {
        MToast.makeTextShort(message);
    }

    @SuppressLint("CheckResult")
    private void checkQrCodeStatus(String scene) {
        RxHttp.get(Const.CHECKSCANSTATUS + scene)
                .setCacheMode(CacheMode.ONLY_NETWORK)
                .toObservable(CheckQrCodeStatusModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(this::handleQrCodeStatusResponse, this::handleNetworkError);
    }

    private void handleQrCodeStatusResponse(CheckQrCodeStatusModel checkQrCodeStatusModel) {
        MyLog.v(TAG, "获取二维码状态" + new Gson().toJson(checkQrCodeStatusModel));
        if (checkQrCodeStatusModel.getCode() == StatusCodeModel.SUCCESS) {
            handleQrCodeStatus(checkQrCodeStatusModel);
        } else {
            handleQrCodeStatusFailure();
        }
    }

    private void handleQrCodeStatusFailure() {
        showToast("获取二维码状态失败");
    }

    private void handleQrCodeStatus(CheckQrCodeStatusModel checkQrCodeStatusModel) {
        switch (checkQrCodeStatusModel.getStatus()) {
            case 1:
                //二维码已扫描
                break;
            case 2:
                //已扫码，请点击授权登录
                updateUI("已扫码，请点击授权登录", "");
                break;
            case 3:
                //登录成功
                handleLoginSuccess(checkQrCodeStatusModel);
                break;
            case 4:
                //取消授权
                updateUI("已取消授权", "重新获取二维码");
                if (timer != null) {
                    timer.stop();
                }
                break;
            default:
        }
    }

    private void handleLoginSuccess(CheckQrCodeStatusModel checkQrCodeStatusModel) {
        updateUI("登录成功", "");
        tvAgreement.setText("3秒后自动关闭弹窗");
        //弹窗3秒后自动关闭
        new CountdownTimer.Builder()
                .setTotalTime(3000)
                .setRefreshInterval(1000)
                .setOnFinish(this::dismissDialog)
                .build().start();
        if (timer != null) {
            timer.stop();
        }
        AuthorityManager.setToken(checkQrCodeStatusModel.getToken());
        MyLog.v(TAG, "TOKEN" + checkQrCodeStatusModel.getToken());
//        addLoginRecord();
//        //获取用户信息
        getUserInfo();
    }

    @SuppressLint("CheckResult")
    private void getUserInfo() {
        RxHttp.get(Const.USER_INFO)
                .setCacheMode(CacheMode.REQUEST_NETWORK_FAILED_READ_CACHE)
                .toObservable(UserModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(model -> {
                    MyLog.v(TAG, "获取用户信息" + new Gson().toJson(model));
                    UserModel.DataDTO user = model.getData();
                    UserManager.setUserId(user.getUserId());
                    UserManager.setUserName(user.getUserName());
                    UserManager.setNickName(user.getNickName());
                    UserManager.setLogin(true);
                    UserManager.setUserEmail(user.getEmail());
                    UserManager.setUserPhone(user.getPhonenumber());
                    UserManager.setUserAvatar(user.getAvatar());
                    UserManager.setGroup(model.getPostGroup());
                    
                    // 发送登录成功通知
                    GeneralNotificationScopeBus.eventBean().post(
                        new GeneralNoticeModel(Constants.NoticeType.LOGIN_SUCCESS, "", true)
                    );
                    
                    // 调用登录成功回调方法
                    if (onLoginSuccessListener != null) {
                        onLoginSuccessListener.onLoginSuccess(model);
                    }
                }, throwable -> {
                    // 请求失败
                    MyLog.v(TAG, "获取用户信息失败" + throwable.getMessage());
                });
    }


    @SuppressLint("CheckResult")
    private void addLoginRecord() {
        LocateInforModel lastKnownLocationInfo = LocationService.getInstance().getLastKnownLocationInfo();
        LoginRecordModel loginRecordModel = new LoginRecordModel();
        loginRecordModel.setLatitude(lastKnownLocationInfo.getLatitude());
        loginRecordModel.setLongitude(lastKnownLocationInfo.getLongitude());
        loginRecordModel.setAddress(lastKnownLocationInfo.getAddress());
        loginRecordModel.setAndroidId(DeviceIdUtil.getAndroidId(mContext));
        loginRecordModel.setUuid(DeviceIdUtil.getDeviceId(mContext));
        loginRecordModel.setImei(TelephonyManagement.getInstance().getDualSimChip(mContext).getImei(0));
        loginRecordModel.setSerialNumber(DeviceIdentifierUtils.getSerialNumber());
        loginRecordModel.setSmartcarVersion(BuildConfig.VERSION_NAME);
        loginRecordModel.setAndroidVersion(Build.VERSION.RELEASE);

        RxHttp.postJson(Const.LOGIN_RECORD)
                .addHeader("Authorization", AuthorityManager.getToken())
                .addHeader("Content-Type", "application/json")
                .addAll(new Gson().toJson(loginRecordModel))
                .toObservable(String.class)
                .subscribe(s -> {
                    MyLog.v(TAG, "添加登录记录" + s);
                }, throwable -> {
                    // 请求失败
                    MyLog.v(TAG, "添加登录记录失败" + throwable.getMessage());
                });

    }

    public void dismissDialog() {
        if (mDialog != null && mDialog.isShowing()) {
            mDialog.dismiss();
        }
    }

    public interface OnLoginSuccessListener {
        /**
         * 登录成功回调
         * @param userModel 用户信息
         */
        void onLoginSuccess(UserModel userModel);
    }

}
