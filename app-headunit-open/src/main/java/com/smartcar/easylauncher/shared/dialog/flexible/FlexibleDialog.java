package com.smartcar.easylauncher.shared.dialog.flexible;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.os.Build;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;

import com.smartcar.easylauncher.core.base.BaseActivity;
import com.smartcar.easylauncher.core.manager.SettingsManager;

/**
 * 通用对话框基类
 */
public class FlexibleDialog {
    protected Context context;
    protected Dialog dialog;
    protected View contentView;
    protected DialogController controller;

    public FlexibleDialog(@NonNull Context context, DialogController controller) {
        this.context = context;
        this.controller = controller;
        initDialog();
    }

    private void initDialog() {
        try {
            // 优先使用 Activity 上下文，如果没有则使用顶部 Activity 的上下文
            Context dialogContext = context instanceof Activity ?
                    context : BaseActivity.getCurrentActivity();

            if (dialogContext == null) {
                Log.w("FlexibleDialog", "未找到有效的Activity上下文");
                return;
            }
            context = dialogContext;


            dialog = new Dialog(context, controller.params.themeResId);
            adjustFullScreen(dialog.getWindow());
            contentView = LayoutInflater.from(context).inflate(controller.params.layoutResId, null);
            dialog.setContentView(contentView);
            controller.apply(this);

            dialog.setOnShowListener(dialogInterface -> {
                if (controller.params.onDialogEventListener != null) {
                    controller.params.onDialogEventListener.onDialogOpened(this);
                }
            });

            dialog.setOnDismissListener(dialogInterface -> {
                if (controller.params.onDialogEventListener != null) {
                    controller.params.onDialogEventListener.onDialogClosed(this);
                }
            });

        } catch (Exception e) {
            Log.e("FlexibleDialog", "初始化对话框失败: " + e.getMessage());
        }
    }

    public FlexibleDialog show() {
        if (dialog != null && !dialog.isShowing()) {
            dialog.show();
        }
        return this;
    }

    public void dismiss() {
        if (dialog != null && dialog.isShowing()) {
            dialog.dismiss();
        }
    }

    public boolean isShowing() {
        return dialog != null && dialog.isShowing();
    }

    public View getContentView() {
        return contentView;
    }

    // 设置方法
    public FlexibleDialog setTheme(int themeResId) {
        dialog.getWindow().setWindowAnimations(themeResId);
        return this;
    }

    public FlexibleDialog setLayout(int layoutResId) {
        contentView = LayoutInflater.from(context).inflate(layoutResId, null);
        dialog.setContentView(contentView);
        return this;
    }

    public FlexibleDialog setGravity(int gravity) {
        dialog.getWindow().setGravity(gravity);
        return this;
    }

    public FlexibleDialog setWidth(int width) {
        dialog.getWindow().setLayout(width, dialog.getWindow().getAttributes().height);
        return this;
    }

    public FlexibleDialog setHeight(int height) {
        dialog.getWindow().setLayout(dialog.getWindow().getAttributes().width, height);
        return this;
    }

    public FlexibleDialog setAnimation(int animation) {
        dialog.getWindow().setWindowAnimations(animation);
        return this;
    }

    public FlexibleDialog setCancelable(boolean cancelable) {
        dialog.setCancelable(cancelable);
        return this;
    }

    public FlexibleDialog setCanceledOnTouchOutside(boolean canceled) {
        dialog.setCanceledOnTouchOutside(canceled);
        return this;
    }

    public FlexibleDialog setDimAmount(float dimAmount) {
        dialog.getWindow().setDimAmount(dimAmount);
        return this;
    }

    public FlexibleDialog setWindowType(int windowType) {
        dialog.getWindow().setType(windowType);
        return this;
    }

    /**
     * 隐藏挖孔屏的位置  全屏显示弹窗
     * setSystemUiVisibility()方法会改变系统UI的可见性，包括导航栏和状态栏。当你调用这个方法时，
     * 如果之前隐藏的导航栏的可见性发生了改变，那么导航栏可能会短暂地显示出来，然后再次隐藏，这就造成了闪烁的效果。
     * 如果你想避免这种情况，你可以在调用setSystemUiVisibility()方法时，同时传入SYSTEM_UI_FLAG_HIDE_NAVIGATION标志，这样可以保持导航栏的隐藏状态
     *
     * @param window
     */
    public void adjustFullScreen(Window window) {
        if (window == null) {
            return;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
            window.setAttributes(lp);
            final View decorView = window.getDecorView();
            if (SettingsManager.getSystNavigationBarShow()) {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                );
            } else {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION);
            }

        }
    }
}