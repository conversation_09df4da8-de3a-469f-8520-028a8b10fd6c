package com.smartcar.easylauncher.shared.dialog;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.ImageView;

import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.dialog.DialogGridAdapter;
import com.smartcar.easylauncher.infrastructure.interfaces.AppSelectionCallback;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.data.model.system.AppInfo;
import com.smartcar.easylauncher.shared.utils.AppInfoProvider;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;
import com.smartcar.easylauncher.shared.view.RefreshCircleView;
import com.timmy.tdialog.base.BindViewHolder;
import com.timmy.tdialog.base.TBaseAdapter;
import com.timmy.tdialog.list.TListDialog;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 横向应用选择
 * <AUTHOR>
 */
public class HorizontalAppSelector {

    private final Context context;
    private List<AppInfo> mAllAppList;
    private String appinfo;
    private AppInfo selectedInfo;
    private DialogGridAdapter<AppInfo> adapter;
    private RefreshCircleView refreshCircleView;

    public HorizontalAppSelector(Context context) {
        this.context = context;
    }

    private void initAdapter(List<AppInfo> appList) {
        adapter = new DialogGridAdapter<>(R.layout.select_item_app, appList) {
            @Override
            protected void onBind(BindViewHolder holder, int position, AppInfo info) {
                holder.setText(R.id.grid_item_app_name, info.getAppName());
                Glide.with(context).load(info.getLogoPath()).into((ImageView) holder.getView(R.id.grid_item_app_icon));
                holder.getView(R.id.grid_item_app_delete).setVisibility(info.isSelect() ? View.VISIBLE : View.GONE);
            }
        };
        adapter.setOnAdapterItemClickListener((TBaseAdapter.OnAdapterItemClickListener<AppInfo>) (bindViewHolder, i, info, tDialog) -> {
            if (info.isSelect()) {
                appinfo = new Gson().toJson(info);
                selectedInfo = info;
            } else {
                selectedInfo = null;
                appinfo = "";
            }
        });
    }

    public static void selectApp(Context context, int position, int type, AppSelectionCallback callback, boolean excludeHomeApp) {
        HorizontalAppSelector selector = new HorizontalAppSelector(context);
        selector.appinfo = "";
        selector.selectedInfo = null;
        if (DataManager.getAppListData().isEmpty()) {
            AppInfoProvider mProvider = AppInfoProvider.getInstance(context);
            mProvider.queryAppInfo().thenAccept(appInfos -> {
                selector.mAllAppList = selector.filterAppList(appInfos, excludeHomeApp);
                selector.initAdapter(selector.mAllAppList);
                selector.showDialog(position, type, callback);
            });
        } else {
            selector.mAllAppList = new Gson().fromJson(DataManager.getAppListData(), new TypeToken<List<AppInfo>>() {}.getType());
            selector.mAllAppList = selector.filterAppList(selector.mAllAppList, excludeHomeApp);
            selector.initAdapter(selector.mAllAppList);
            selector.showDialog(position, type, callback);
        }
    }

    private List<AppInfo> filterAppList(List<AppInfo> appList, boolean excludeHomeApp) {
        if (excludeHomeApp) {
            return appList.stream()
                    .filter(appInfo -> !"主页".equals(appInfo.getAppName()) && appInfo.getState() == 0)
                    .collect(Collectors.toList());
        }
        return appList;
    }

    private void showDialog(int position, int type, AppSelectionCallback callback) {
        new TListDialog.Builder(((FragmentActivity) context).getSupportFragmentManager())
                .setListLayoutRes(R.layout.dialog_app_select, LinearLayoutManager.HORIZONTAL)
                .setLayoutManager(new GridLayoutManager(context, 5))
                .setWidth(DensityUtils.dp2px(context, 460))
                .setHeight(DensityUtils.dp2px(context, 310))
                .setCancelOutside(false)
                .setOnBindViewListener(bindViewHolder -> {
                    bindViewHolder.getView(R.id.ll_xiezai).setVisibility(type == 0 ? View.GONE : View.VISIBLE);
                    refreshCircleView = bindViewHolder.getView(R.id.iv_refresh);
                })
                .setAdapter(adapter)
                .addOnClickListener(R.id.root, R.id.ll_payment, R.id.ll_xiezai, R.id.ll_guanbi, R.id.iv_refresh)
                .setOnViewClickListener((viewHolder, view, tDialog) -> {
                    switch (view.getId()) {
                        case R.id.ll_payment:
                            if (appinfo == null || appinfo.isEmpty()) {
                                MToast.makeTextShort("选择你要添加的应用");
                                return;
                            }
                            callback.onAppSelected(selectedInfo);
                            tDialog.dismiss();
                            break;
                        case R.id.ll_xiezai:
                            callback.onAppUninstalled(position);
                            tDialog.dismiss();
                            break;
                        case R.id.ll_guanbi:
                            tDialog.dismiss();
                            break;
                        case R.id.iv_refresh:
                            refreshCircleView.startAnimation();
                            refreshData();
                            break;
                        case R.id.root:
                         //   tDialog.dismiss();
                            break;
                        default:
                    }
                })
                .create()
                .show();
    }

    private void refreshData() {
        AppInfoProvider mProvider = AppInfoProvider.getInstance(context);
        mProvider.refreshAppInfo().thenAccept(appInfos -> {
            mAllAppList = appInfos;
            refreshCircleView.stopAnimation();
            new Handler(Looper.getMainLooper()).post(() -> adapter.setData(mAllAppList));
        });
    }

}