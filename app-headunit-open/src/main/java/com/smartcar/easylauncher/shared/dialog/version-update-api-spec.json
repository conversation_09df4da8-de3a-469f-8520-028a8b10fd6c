{"apiSpec": {"title": "Android版本更新API规范", "version": "1.0.0", "baseUrl": "http://your-domain.com/dev-api", "description": "Android应用版本更新接口，支持自动故障转移、多链接管理和多渠道分发"}, "endpoints": {"checkUpdate": {"path": "/system/appVersion/checkUpdate", "method": "GET", "description": "检查版本更新，核心接口，支持自动故障转移和多渠道分发", "parameters": {"platform": {"type": "string", "required": true, "enum": ["android", "ios"], "description": "平台类型"}, "currentVersionCode": {"type": "integer", "required": true, "description": "当前版本代码"}, "channelType": {"type": "string", "required": false, "enum": ["public", "byd", "custom"], "description": "渠道类型，不指定时使用默认逻辑"}, "versionType": {"type": "string", "required": false, "enum": ["release", "beta", "alpha"], "description": "版本类型，用于版本匹配规则控制"}}, "responses": {"hasUpdate": {"code": 200, "data": {"id": "integer", "platform": "string", "versionCode": "integer", "versionName": "string", "title": "string", "description": "string", "realDownloadUrl": "string", "fileName": "string", "fileSize": "long", "fileMd5": "string", "isForceUpdate": "integer", "minSupportVersion": "integer", "urlExpireTime": "string", "currentActiveIndex": "integer", "backupUrls": "string", "backupPasswords": "string", "backupDiskTypes": "string", "versionType": "string", "channelType": "string", "channelName": "string"}}, "noUpdate": {"code": 200, "data": null}}}, "healthCheck": {"path": "/system/appVersion/healthCheck/{id}", "method": "POST", "description": "检查版本链接健康状态", "parameters": {"id": {"type": "integer", "required": true, "location": "path", "description": "版本ID"}}, "responses": {"success": {"code": 200, "data": {"linkStatus": "array", "issues": "array", "checkTime": "string", "activeIndex": "integer"}}}}, "switchToBackup": {"path": "/system/appVersion/switchToBackup/{id}", "method": "POST", "description": "切换到备用链接", "parameters": {"id": {"type": "integer", "required": true, "location": "path", "description": "版本ID"}, "backupIndex": {"type": "integer", "required": true, "location": "query", "description": "备用链接索引"}}, "responses": {"success": {"code": 200, "msg": "切换成功"}}}}, "dataModels": {"VersionInfo": {"id": {"type": "integer", "description": "版本ID"}, "platform": {"type": "string", "enum": ["android", "ios"], "description": "平台类型"}, "versionCode": {"type": "integer", "description": "版本代码，用于版本比较"}, "versionName": {"type": "string", "description": "版本名称，如1.0.0"}, "title": {"type": "string", "description": "更新标题"}, "description": {"type": "string", "description": "更新描述，支持换行"}, "realDownloadUrl": {"type": "string", "description": "真实下载链接（已解析的直链）"}, "fileName": {"type": "string", "description": "文件名"}, "fileSize": {"type": "long", "description": "文件大小（字节）"}, "fileMd5": {"type": "string", "description": "文件MD5校验值"}, "isForceUpdate": {"type": "integer", "enum": [0, 1], "description": "是否强制更新，0=否，1=是"}, "minSupportVersion": {"type": "integer", "description": "最低支持版本"}, "urlExpireTime": {"type": "string", "format": "datetime", "description": "下载链接过期时间"}, "currentActiveIndex": {"type": "integer", "description": "当前激活的链接索引，0=主链接，1+=备用链接"}, "backupUrls": {"type": "string", "format": "json-array", "description": "备用链接JSON数组"}, "backupPasswords": {"type": "string", "format": "json-array", "description": "备用链接密码JSON数组"}, "backupDiskTypes": {"type": "string", "format": "json-array", "description": "备用链接网盘类型JSON数组"}}, "HealthCheckResult": {"linkStatus": {"type": "array", "items": {"index": "integer", "type": "string", "status": "string", "expireTime": "string", "parseStatus": "integer"}, "description": "链接状态列表"}, "issues": {"type": "array", "items": "string", "description": "发现的问题列表"}, "checkTime": {"type": "string", "format": "datetime", "description": "检查时间"}, "activeIndex": {"type": "integer", "description": "当前激活的链接索引"}}}, "businessLogic": {"autoFailover": {"description": "自动故障转移机制", "workflow": ["Android调用checkUpdate接口", "后端检查当前激活链接状态", "如果链接有效，直接返回", "如果链接过期，尝试刷新当前链接", "如果刷新失败，切换到备用链接", "返回可用的下载链接"]}, "versionComparison": {"description": "版本比较逻辑", "rules": ["versionCode大于currentVersionCode表示有新版本", "currentVersionCode小于minSupportVersion表示强制更新", "isForceUpdate=1表示强制更新"]}, "linkManagement": {"description": "链接管理机制", "features": ["主链接自动刷新", "备用链接自动切换", "链接健康检查", "过期时间管理"]}}, "errorCodes": {"200": "操作成功", "400": "参数错误", "404": "版本不存在", "500": "系统错误"}, "examples": {"checkUpdateRequest": {"url": "/system/appVersion/checkUpdate?platform=android&currentVersionCode=99", "method": "GET"}, "checkUpdateResponse": {"hasUpdate": {"code": 200, "msg": "操作成功", "data": {"id": 1, "platform": "android", "versionCode": 100, "versionName": "1.0.0", "title": "新版本发布", "description": "• 新增功能\\n• 修复问题\\n• 性能优化", "realDownloadUrl": "https://developer-oss.lanrar.com/file/xxx", "fileName": "SmartCar-2.0.3.apk", "fileSize": 25600000, "fileMd5": "d41d8cd98f00b204e9800998ecf8427e", "isForceUpdate": 0, "minSupportVersion": 95, "urlExpireTime": "2025-08-02T18:00:00", "currentActiveIndex": 0}}, "noUpdate": {"code": 200, "msg": "操作成功", "data": null}}}, "integrationGuide": {"androidImplementation": {"dependencies": ["Retrofit for HTTP requests", "<PERSON><PERSON> for JSON parsing", "OkHttp for networking"], "keyClasses": ["VersionApi - API接口定义", "VersionInfo - 版本信息数据类", "UpdateManager - 更新管理器", "DownloadManager - 下载管理器"], "workflow": ["应用启动时调用checkUpdate", "可选择指定channelType参数", "解析返回的版本信息", "根据isForceUpdate决定更新策略", "使用realDownloadUrl下载文件", "验证文件MD5", "安装更新"]}, "versionTypes": {"release": {"name": "正式版", "description": "稳定的生产版本，面向最终用户发布"}, "beta": {"name": "测试版", "description": "功能基本完成的测试版本，用于公开测试"}, "alpha": {"name": "内测版", "description": "内部测试版本，功能可能不完整"}}, "channelTypes": {"public": {"name": "公版", "description": "通用版本，适用于所有用户的标准版本"}, "byd": {"name": "比亚迪定制版", "description": "包含比亚迪特定功能的定制版本"}, "custom": {"name": "定制版", "description": "根据特殊需求定制的版本"}}, "channelStrategy": {"description": "渠道匹配策略", "rules": ["优先返回指定渠道的版本", "指定渠道无版本时自动回退到公版", "不指定渠道时使用原有逻辑"]}, "versionTypeMatching": {"description": "版本类型匹配规则", "rules": {"release": {"canReceive": ["release"], "description": "正式版用户只能接收正式版更新"}, "beta": {"canReceive": ["beta", "release"], "description": "测试版用户可接收测试版和正式版更新"}, "alpha": {"canReceive": ["alpha", "beta", "release"], "description": "内测版用户可接收所有类型的版本更新"}}, "priority": ["优先返回相同版本类型的版本", "在相同匹配度下，返回版本代码最高的版本"]}}}