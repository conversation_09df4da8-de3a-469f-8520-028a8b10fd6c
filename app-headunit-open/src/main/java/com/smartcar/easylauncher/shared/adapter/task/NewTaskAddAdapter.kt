package com.smartcar.easylauncher.shared.adapter.task

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.chad.library.adapter4.BaseQuickAdapter
import com.smartcar.easylauncher.R
import com.smartcar.easylauncher.core.constants.TaskConditionConst
import com.smartcar.easylauncher.databinding.ItemHeaderAndFooterBinding
import com.smartcar.easylauncher.data.model.task.ConditionModel

/**
 * https://github.com/CymChad/BaseRecyclerViewAdapterHelper
 */
class NewTaskAddAdapter : BaseQuickAdapter<ConditionModel, NewTaskAddAdapter.VH> {
    constructor(list: List<ConditionModel>) : super(list)

    constructor() : super(emptyList())

    class VH(var binding: ItemHeaderAndFooterBinding) : RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(context: Context, parent: ViewGroup, viewType: Int): VH {
        val binding =
            ItemHeaderAndFooterBinding.inflate(LayoutInflater.from(context), parent, false)
        return VH(binding)
    }

    override fun onBindViewHolder(holder: VH, position: Int, item: ConditionModel?) {
        holder.binding.gridItemAppName.text = item?.name
        holder.binding.gridItemNumber.text = item?.execute
        when (item?.id) {
            TaskConditionConst.TYPE_WIFI_SWITCH -> {
                Glide.with(context).load(R.drawable.ic_task_wifi)
                    .into(holder.binding.gridItemAppIcon)
                // holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_wifi)
            }

            TaskConditionConst.TYPE_NETWORK_STATE -> {
                Glide.with(context).load(R.drawable.ic_task_wangluozhuangt)
                    .into(holder.binding.gridItemAppIcon)
                // holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_wangluozhuangt)
            }

            TaskConditionConst.TYPE_BLUETOOTH_SWITCH -> {
                Glide.with(context).load(R.drawable.ic_task_ble)
                    .into(holder.binding.gridItemAppIcon)
                // holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_ble)
            }

            TaskConditionConst.TYPE_BLUETOOTH_DEVICE -> {
                Glide.with(context).load(R.drawable.ic_task_ble)
                    .into(holder.binding.gridItemAppIcon)
                // holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_ble)
            }

            TaskConditionConst.TYPE_DEVICE_START -> {
                Glide.with(context).load(R.drawable.ic_task_kaiji)
                    .into(holder.binding.gridItemAppIcon)
                //  holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_kaiji)
            }

            TaskConditionConst.TYPE_START -> {
                Glide.with(context).load(R.drawable.ic_task_qidong)
                    .into(holder.binding.gridItemAppIcon)
                //  holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_qidong)
            }

            TaskConditionConst.TYPE_MUSIC_STATE -> {
                Glide.with(context).load(R.drawable.ic_task_yinyue)
                    .into(holder.binding.gridItemAppIcon)
                //  holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_yinyue)
            }

            TaskConditionConst.TYPE_SUN -> {
                Glide.with(context).load(R.drawable.ic_task_richu)
                    .into(holder.binding.gridItemAppIcon)
                //  holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_richu)
            }

            TaskConditionConst.TYPE_SPEED -> {
                Glide.with(context).load(R.drawable.ic_task_chesu)
                    .into(holder.binding.gridItemAppIcon)
                //   holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_chesu)
            }

            TaskConditionConst.TYPE_GEO_FENCE -> {
                Glide.with(context).load(R.drawable.ic_task_diliweilan)
                    .into(holder.binding.gridItemAppIcon)
                //   holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_diliweilan)
            }

            TaskConditionConst.TYPE_TIME_RANGE -> {
                Glide.with(context).load(R.drawable.ic_task_shijianfanwei)
                    .into(holder.binding.gridItemAppIcon)
                //  holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_shijianfanwei)
            }

            TaskConditionConst.TYPE_TIMING -> {
                Glide.with(context).load(R.drawable.ic_task_dingshi)
                    .into(holder.binding.gridItemAppIcon)
                //      holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_dingshi)
            }

            TaskConditionConst.TYPE_PERIOD -> {
                Glide.with(context).load(R.drawable.ic_task_zhouqi)
                    .into(holder.binding.gridItemAppIcon)
                // holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_zhouqi)
            }

            TaskConditionConst.TYPE_SCREEN -> {
                Glide.with(context).load(R.drawable.ic_task_screen)
                    .into(holder.binding.gridItemAppIcon)
                //  holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_tianqi)
            }

            TaskConditionConst.TYPE_WAKE_UP -> {
                Glide.with(context).load(R.drawable.ic_task_kaiji)
                    .into(holder.binding.gridItemAppIcon)
                // 使用开机图标作为设备唤醒的图标
            }

            else -> {}
        }
    }
}