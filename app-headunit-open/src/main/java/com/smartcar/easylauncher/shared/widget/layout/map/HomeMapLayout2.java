package com.smartcar.easylauncher.shared.widget.layout.map;

import androidx.constraintlayout.widget.ConstraintSet;
import androidx.fragment.app.FragmentActivity;

import com.smartcar.easylauncher.core.constants.SettingsConstants;
import com.smartcar.easylauncher.databinding.FragmentMapHomeBinding;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.shared.view.pagergridlayoutmanager.PagerGridLayoutManager;

/**
 * 首页地图布局2实现
 * 布局2配置：顶部状态栏 + 左侧卡片列表 + 中间内容区域 + 底部导航栏
 * 适用场景：状态栏横跨整个顶部的布局
 */
public class HomeMapLayout2 extends AbstractHomeMapLayout {

    /**
     * 构造函数
     *
     * @param binding 数据绑定对象
     * @param activity Fragment所属的Activity
     * @param pagerLayoutManager RecyclerView的布局管理器
     */
    public HomeMapLayout2(FragmentMapHomeBinding binding,
                        FragmentActivity activity,
                        PagerGridLayoutManager pagerLayoutManager) {
        super(binding, activity, pagerLayoutManager);
    }

    @Override
    public void applyLayout(ConstraintSet constraintSet) {
        // 获取状态栏和导航栏的显示状态
        boolean statusBarVisible = SettingsManager.getStatusBarShow();
        boolean navigationBarVisible = SettingsManager.getNavigationBarShow();

        // 配置状态栏（横跨整个顶部）
        if (statusBarVisible) {
            configureStatusBar(constraintSet,
                    ConstraintSet.MATCH_CONSTRAINT,  // 宽度
                    dimensionCache.statusBarHeight40,  // 高度
                    0,  // 顶部边距
                    0,  // 右边距
                    ConstraintSet.MATCH_CONSTRAINT  // 对齐方式
            );
        } else {
            // 状态栏不显示时，隐藏并清除约束
            hideStatusBar(constraintSet);
        }

        // 配置RecyclerView - 根据状态栏显示状态调整顶部约束
        configureRecyclerViewLayout2(constraintSet, statusBarVisible, navigationBarVisible);

        // 配置主内容区域 - 根据状态栏和导航栏显示状态调整约束
        configureFrameLayoutLayout2(constraintSet, statusBarVisible, navigationBarVisible);

        // 配置底部导航栏
        if (navigationBarVisible) {
            configureNavigationBar(constraintSet,
                    0,  // 宽度
                    dimensionCache.getNavigationBarHeight(),  // 高度 - 动态获取
                    ConstraintSet.PARENT_ID,  // 左侧约束目标
                    ConstraintSet.PARENT_ID  // 右侧约束目标
            );
        } else {
            // 导航栏不显示时，隐藏并清除约束
            hideNavigationBar(constraintSet);
        }

        // 设置RecyclerView为垂直布局
        setupVerticalRecyclerView();
    }

    @Override
    public String getLayoutType() {
        return SettingsConstants.DEFAULT_MAP_LAYOUT_2;
    }
    
    /**
     * 配置布局2专用的RecyclerView
     * 根据状态栏和导航栏的显示状态动态调整约束
     *
     * @param statusBarVisible 状态栏是否可见
     * @param navigationBarVisible 导航栏是否可见
     */
    private void configureRecyclerViewLayout2(ConstraintSet constraintSet, boolean statusBarVisible, boolean navigationBarVisible) {
        // 清除RecyclerView之前的约束
        constraintSet.clear(binding.rvHome.getId());

        // 设置RecyclerView的宽度和高度约束
        constraintSet.constrainWidth(binding.rvHome.getId(), dimensionCache.mapCardWidth);
        constraintSet.constrainHeight(binding.rvHome.getId(), ConstraintSet.MATCH_CONSTRAINT);

        // 设置顶部约束 - 根据状态栏显示状态
        if (statusBarVisible) {
            // 状态栏显示时，RecyclerView顶部与状态栏底部对齐
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.TOP,
                    binding.mainLayoutStatusBar.getId(), ConstraintSet.BOTTOM, 0);
        } else {
            // 状态栏不显示时，RecyclerView顶部与父容器顶部对齐
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID, ConstraintSet.TOP, dimensionCache.margin5);
        }

        // 设置左侧约束
        constraintSet.connect(binding.rvHome.getId(), ConstraintSet.START,
                ConstraintSet.PARENT_ID, ConstraintSet.START, 0);

        // 设置底部约束 - 根据导航栏显示状态
        if (navigationBarVisible) {
            // 导航栏显示时，RecyclerView底部与导航栏顶部对齐
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.BOTTOM,
                    binding.maiIconBar.getId(), ConstraintSet.TOP, dimensionCache.margin5);
        } else {
            // 导航栏不显示时，RecyclerView底部与父容器底部对齐
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, dimensionCache.margin5);
        }
    }

    /**
     * 配置布局2专用的FrameLayout
     * 根据状态栏和导航栏的显示状态动态调整约束
     *
     * @param statusBarVisible 状态栏是否可见
     * @param navigationBarVisible 导航栏是否可见
     */
    private void configureFrameLayoutLayout2(ConstraintSet constraintSet, boolean statusBarVisible, boolean navigationBarVisible) {
        constraintSet.clear(binding.frameLayout.getId());
        constraintSet.constrainWidth(binding.frameLayout.getId(), ConstraintSet.MATCH_CONSTRAINT);
        constraintSet.constrainHeight(binding.frameLayout.getId(), ConstraintSet.MATCH_CONSTRAINT);

        // 设置顶部约束 - 根据状态栏显示状态
        if (statusBarVisible) {
            // 状态栏显示时，FrameLayout顶部与状态栏底部对齐
            constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.TOP,
                    binding.mainLayoutStatusBar.getId(), ConstraintSet.BOTTOM, 0);
        } else {
            // 状态栏不显示时，FrameLayout顶部与父容器顶部对齐
            constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID, ConstraintSet.TOP, dimensionCache.margin5);
        }

        // 设置水平约束
        constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.START,
                binding.rvHome.getId(), ConstraintSet.END, dimensionCache.margin5);
        constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.END,
                ConstraintSet.PARENT_ID, ConstraintSet.END, dimensionCache.margin5);

        // 设置底部约束 - 根据导航栏显示状态
        if (navigationBarVisible) {
            // 导航栏显示时，FrameLayout底部与导航栏顶部对齐
            constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.BOTTOM,
                    binding.maiIconBar.getId(), ConstraintSet.TOP, dimensionCache.margin5);
        } else {
            // 导航栏不显示时，FrameLayout底部与父容器底部对齐
            constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, dimensionCache.margin5);
        }
    }
} 