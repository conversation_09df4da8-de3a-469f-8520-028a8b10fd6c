package com.smartcar.easylauncher.shared.adapter.theme;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.core.view.ViewCompat;

import com.bumptech.glide.Glide;
import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.data.model.theme.api.NewThemeInfo;
import com.smartcar.easylauncher.modules.theme.ThemeDetailScene;
import com.smartcar.easylauncher.shared.view.LabelView;

/**
 * 新主题API适配器
 * 支持NewThemeInfo数据模型
 *
 * <AUTHOR>
 */
public class NewFindThemeAdapter extends BaseQuickAdapter<NewThemeInfo, QuickViewHolder> {

    /**
     * 构造函数
     */
    public NewFindThemeAdapter() {
        super();
    }

    /**
     * 根据标签更新背景颜色
     */
    private void updateBackground(LabelView view, String label) {
        if (label == null || label.trim().isEmpty()) {
            view.setVisibility(View.GONE);
            return;
        }

        int color;
        switch (label) {
            case "热门":
            case "hot":
                color = ContextCompat.getColor(getContext(), R.color.colorHot);
                break;
            case "最新":
            case "new":
                color = ContextCompat.getColor(getContext(), R.color.colorNew);
                break;
            case "精选":
            case "choice":
                color = ContextCompat.getColor(getContext(), R.color.colorChoice);
                break;
            case "推荐":
            case "recommend":
                color = ContextCompat.getColor(getContext(), R.color.colorRecommend);
                break;
            case "更新":
            case "update":
                color = ContextCompat.getColor(getContext(), R.color.colorUpdate);
                break;
            case "限免":
            case "free":
                color = ContextCompat.getColor(getContext(), R.color.colorFree);
                break;
            case "节日":
            case "festival":
                color = ContextCompat.getColor(getContext(), R.color.colorFestival);
                break;
            default:
                color = ContextCompat.getColor(getContext(), R.color.colorDefault);
                break;
        }
        view.setBgColor(color);
    }

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable NewThemeInfo item) {
        assert item != null;
        
        // 显示标签
        if (item.getLabel() != null && !item.getLabel().trim().isEmpty()) {
            helper.getView(R.id.lv_heat).setVisibility(View.VISIBLE);
            LabelView view = (LabelView) helper.getView(R.id.lv_heat);
            view.setText(item.getLabel());
            updateBackground(view, item.getLabel());
        } else {
            helper.getView(R.id.lv_heat).setVisibility(View.GONE);
        }

        // 设置主题名称
        helper.setText(R.id.grid_item_app_name, item.getThemeName());
        
        // 设置下载量
        helper.setText(R.id.grid_item_number, formatDownloadCount(
                item.getDownloadCount() != null ? item.getDownloadCount().intValue() : 0));

        // 加载主题封面图片
        loadThemeImage(item, (ImageView) helper.getView(R.id.grid_item_app_icon));
        
        // 暂时隐藏使用状态标签（新API暂无此信息）
        helper.getView(R.id.lv_default).setVisibility(View.GONE);

        // 设置共享元素动画的transitionName
        ViewCompat.setTransitionName(helper.getView(R.id.grid_item_app_icon),
                ThemeDetailScene.VIEW_NAME_THEME_IMAGE + item.getId());
        ViewCompat.setTransitionName(helper.getView(R.id.grid_item_app_name),
                ThemeDetailScene.VIEW_NAME_THEME_TITLE + item.getId());
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.find_theme_item, viewGroup);
    }

    /**
     * 格式化下载量显示
     */
    private String formatDownloadCount(int count) {
        if (count < 1000) {
            return String.valueOf(count);
        } else if (count < 10000) {
            return String.format("%.1fK", count / 1000.0);
        } else if (count < 100000) {
            return String.format("%.0fK", count / 1000.0);
        } else if (count < 1000000) {
            return String.format("%.0fK", count / 1000.0);
        } else {
            return String.format("%.1fM", count / 1000000.0);
        }
    }

    /**
     * 安全地加载主题图片
     */
    private void loadThemeImage(NewThemeInfo item, ImageView imageView) {
        try {
            String imageUrl = item.getCoverImage();
            if (imageUrl != null && !imageUrl.trim().isEmpty()) {
                // 优化的图片加载配置
                Glide.with(getContext())
                        .load(imageUrl)
                        .centerCrop() // 居中裁剪，保持宽高比
                        .thumbnail(0.1f) // 先加载缩略图提升体验
                       // .placeholder(R.drawable.ic_theme_default) // 占位图
                    //    .error(R.drawable.ic_theme_default) // 错误图
                        .into(imageView);
            } else {
                // 使用默认图片
             //   imageView.setImageResource(R.drawable.ic_theme_default);
            }
        } catch (Exception e) {
            // 任何异常都使用默认图片
            e.printStackTrace();
        //    imageView.setImageResource(R.drawable.ic_theme_default);
        }
    }
}
