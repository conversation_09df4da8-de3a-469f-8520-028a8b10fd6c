package com.smartcar.easylauncher.shared.dialog;

import android.content.Context;
import android.content.DialogInterface;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.fragment.app.FragmentManager;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;
import com.timmy.tdialog.TDialog;
import com.timmy.tdialog.base.BindViewHolder;
import com.timmy.tdialog.listener.OnBindViewListener;
import com.timmy.tdialog.listener.OnViewClickListener;

/**
 * <AUTHOR>
 */
public class GeneralDialog {
    // 常量定义
    private static final String DEFAULT_TITLE = "提示";
    private static final String DEFAULT_WARNING_TITLE = "警告";
    private static final String DEFAULT_INFO_TITLE = "提示";
    private static final int SCREEN_WIDTH_ASPECT = 1;
    private static final int SCREEN_HEIGHT_ASPECT = 1;


    // 通用对话框（使用默认标题和按钮文本）
    public static void showGeneralDialog(FragmentManager fragmentManager, Context context, String content, DialogClickListener listener) {
        showGeneralDialog(fragmentManager, context, DEFAULT_TITLE, content, context.getString(R.string.confirm), context.getString(R.string.cancel), listener);
    }

    // 警告对话框
    public static void showWarningDialog(FragmentManager fragmentManager, Context context, String content, DialogClickListener listener) {
        showGeneralDialog(fragmentManager, context, DEFAULT_WARNING_TITLE, content, context.getString(R.string.confirm), context.getString(R.string.cancel), listener);
    }

    // 确认对话框
    public static void showConfirmDialog(FragmentManager fragmentManager, Context context, String content, DialogClickListener listener) {
        showGeneralDialog(fragmentManager, context, "确认", content, "是", "否", listener);
    }

    /**
     * @param fragmentManager
     * @param context
     * @param content
     * @param listener
     */
    public static void showInfoDialog(FragmentManager fragmentManager, Context context, String content, DialogClickListener listener) {
        showGeneralDialog(fragmentManager, context, DEFAULT_INFO_TITLE, content, context.getString(R.string.confirm), null, listener);
    }

    public static void showGeneralDialog(FragmentManager fragmentManager, Context context, String title, String content, String positiveButtonText, String negativeButtonText, DialogClickListener listener) {
        // 参数校验
        if (fragmentManager == null || context == null || listener == null) {
            throw new IllegalArgumentException("Parameters cannot be null");
        }

        // 创建并显示对话框
        TDialog.Builder builder = new TDialog.Builder(fragmentManager)
                .setLayoutRes(R.layout.dialog_hint)
//            .setScreenWidthAspect(context, SCREEN_WIDTH_ASPECT)
//            .setScreenHeightAspect(context, SCREEN_HEIGHT_ASPECT)
                .setWidth(DensityUtils.dp2px(context, 360))
                .addOnClickListener(R.id.paymentButton, R.id.cancelButton)
                .setCancelableOutside(true);

        // 设置监听器
        builder.setOnViewClickListener(new OnViewClickListenerImpl(listener));
        builder.setOnBindViewListener(new OnBindViewListenerImpl(title, content, positiveButtonText, negativeButtonText));
        builder.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialogInterface) {
                //释放全部资源，避免内存泄漏
            }
        });


        // 显示对话框
        TDialog dialog = builder.create();
        try {
            dialog.show(fragmentManager);
        } catch (Exception e) {
            // 异常处理逻辑
            e.printStackTrace();
        }
    }

    /**
     * 弹窗点击事件监听器接口。
     */
    public interface DialogClickListener {

        /**
         * 当点击弹窗中的积极/确认按钮时调用。
         * 例如，可以用于确认操作或执行某项任务。
         */
        void onPositiveClick();

        /**
         * 当点击弹窗中的消极/取消按钮时调用。
         * 例如，可以用于取消操作或关闭弹窗。
         */
        void onNegativeClick();
    }


    private static class OnViewClickListenerImpl implements OnViewClickListener {
        private final DialogClickListener listener;

        public OnViewClickListenerImpl(DialogClickListener listene) {
            this.listener = listene;
        }

        @Override
        public void onViewClick(BindViewHolder viewHolder, View view, TDialog tDialog) {
            switch (view.getId()) {
                case R.id.paymentButton:
                    listener.onPositiveClick();
                    tDialog.dismiss();
                    break;
                case R.id.cancelButton:
                    listener.onNegativeClick();
                    tDialog.dismiss();
                    break;
                default:
                    // 未知视图ID的处理逻辑
            }
        }
    }

    private static class OnBindViewListenerImpl implements OnBindViewListener {
        private final String title;
        private final String content;
        private final String okText;
        private final String cancelText;

        public OnBindViewListenerImpl(String title, String content, String okText, String cancelText) {
            this.title = title;
            this.content = content;
            this.okText = okText;
            this.cancelText = cancelText;
        }

        @Override
        public void bindView(BindViewHolder viewHolder) {
            // 使用驼峰命名法，并且使变量名更具描述性
            TextView hintTextView = viewHolder.getView(R.id.tv_hint);
            TextView contentTextView = viewHolder.getView(R.id.tv_content);
            Button paymentButton = viewHolder.getView(R.id.paymentButton);
            Button cancelButton = viewHolder.getView(R.id.cancelButton);

            // 设置文本时，确保传递的参数与变量名相对应
            hintTextView.setText(title);
            contentTextView.setText(content);
            paymentButton.setText(okText); // 假设ok是一个字符串变量，这里命名为okText以增加清晰度

            // 设置取消按钮的文本，并根据文本是否为null来决定是否隐藏按钮
            if (cancelText != null) {
                cancelButton.setText(cancelText);
            } else {
                cancelButton.setVisibility(View.GONE); // 如果cancelText为null，则隐藏取消按钮
            }
        }
    }

}