package com.smartcar.easylauncher.shared.adapter.setting;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.KeyMapConstants;
import com.smartcar.easylauncher.data.model.system.RecommendKeyMapModel;

/**
 * 推荐按键映射适配器
 * 用于发现页面展示推荐按键映射
 *
 * <AUTHOR>
 * @date 2024/12/18
 */
public class RecommendKeyMapAdapter extends BaseQuickAdapter<RecommendKeyMapModel, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int position, @Nullable RecommendKeyMapModel item) {
        if (item == null) return;

        // 设置按键映射名称
        helper.setText(R.id.tv_key_map_name, item.getKeyMapName());

        // 设置按键映射描述
        helper.setText(R.id.tv_key_map_description, item.getShortDescription());

        // 设置按键映射分类
        helper.setText(R.id.tv_key_map_category, item.getCategory());

        // 设置评分
        helper.setText(R.id.tv_key_map_rating, item.getRatingStars());

        // 设置使用人数 - 优化显示格式
        String usageText = formatUsageCount(item.getUsageCount());
        helper.setText(R.id.tv_usage_count, usageText);

        // 设置热门标签
        View hotTag = helper.getView(R.id.tv_hot_tag);
        if (item.isHot()) {
            hotTag.setVisibility(View.VISIBLE);
            helper.setText(R.id.tv_hot_tag, "热门");
        } else if (item.isNew()) {
            hotTag.setVisibility(View.VISIBLE);
            helper.setText(R.id.tv_hot_tag, "新品");
        } else {
            hotTag.setVisibility(View.GONE);
        }

        // 设置按键名称
        helper.setText(R.id.tv_key_name, item.getKeyName());

        // 设置动作类型图标
        setActionTypeIcon(helper, item.getActionType());

        // 设置动作描述
        String actionDesc = getActionDescription(item);
        helper.setText(R.id.tv_action_description, actionDesc);
    }

    /**
     * 格式化使用人数显示
     */
    private String formatUsageCount(int count) {
        if (count >= 10000) {
            return String.format("%.1fw人使用", count / 10000.0);
        } else if (count >= 1000) {
            return String.format("%.1fk人使用", count / 1000.0);
        } else {
            return count + "人使用";
        }
    }

    /**
     * 设置动作类型图标
     */
    private void setActionTypeIcon(QuickViewHolder helper, int actionType) {
        int iconRes = R.drawable.ic_key; // 默认图标

        switch (actionType) {
            case KeyMapConstants.ActionType.BROADCAST:
                iconRes = R.drawable.ic_broadcast;
                break;
                
            case KeyMapConstants.ActionType.LAUNCH_APP:
                iconRes = R.drawable.ic_apps;
                break;
                
            case KeyMapConstants.ActionType.MEDIA_CONTROL:
                iconRes = R.drawable.ic_music_note;
                break;
                
            case KeyMapConstants.ActionType.SYSTEM_ACTION:
                iconRes = R.drawable.ic_setting;
                break;
        }

        helper.setImageResource(R.id.iv_action_type, iconRes);
    }

    /**
     * 获取动作描述
     */
    private String getActionDescription(RecommendKeyMapModel item) {
        StringBuilder desc = new StringBuilder();
        
        // 添加动作类型描述
        desc.append(KeyMapConstants.ActionType.getActionTypeName(item.getActionType()));
        
        // 根据动作类型添加具体描述
        switch (item.getActionType()) {
            case KeyMapConstants.ActionType.MEDIA_CONTROL:
                String mediaDesc = KeyMapConstants.MediaControlType.getMediaControlTypeName(item.getActionData());
                desc.append(" - ").append(mediaDesc);
                break;
                
            case KeyMapConstants.ActionType.SYSTEM_ACTION:
                String systemDesc = KeyMapConstants.SystemActionType.getSystemActionTypeName(item.getActionData());
                desc.append(" - ").append(systemDesc);
                break;
                
            case KeyMapConstants.ActionType.LAUNCH_APP:
                desc.append(" - ").append(getAppNameFromPackage(item.getActionData()));
                break;
                
            case KeyMapConstants.ActionType.BROADCAST:
                desc.append(" - 自定义功能");
                break;
        }
        
        return desc.toString();
    }

    /**
     * 从包名获取应用名称（简化版本）
     */
    private String getAppNameFromPackage(String packageName) {
        if (packageName == null) return "未知应用";
        
        // 简单的包名到应用名映射
        switch (packageName) {
            case "com.netease.cloudmusic":
                return "网易云音乐";
            case "com.autonavi.minimap":
                return "高德地图";
            case "com.android.dialer":
                return "电话";
            case "com.tencent.mm":
                return "微信";
            case "com.alibaba.android.rimet":
                return "钉钉";
            default:
                // 从包名中提取最后一部分作为应用名
                String[] parts = packageName.split("\\.");
                if (parts.length > 0) {
                    return parts[parts.length - 1];
                }
                return "应用";
        }
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int viewType) {
        return new QuickViewHolder(R.layout.recommend_key_map_item, viewGroup);
    }
}
