package com.smartcar.easylauncher.shared.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.ConnectivityManager.NetworkCallback;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.NetworkInfo.State;
import android.net.NetworkRequest.Builder;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.text.TextUtils;
import android.telephony.TelephonyManager;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresPermission;

import com.smartcar.easylauncher.app.App;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.URL;
import java.net.UnknownHostException;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 网络工具类，提供网络状态检测和网络操作功能
 * <p>
 * 该工具类提供以下主要功能：
 * 1. 网络连接状态检测（WiFi、移动网络、以太网等）
 * 2. 网络类型判断（2G/3G/4G/WiFi/以太网）
 * 3. IP地址获取（支持IPv4和IPv6）
 * 4. WiFi开关控制
 * 5. 网络状态变化监听
 * </p>
 *
 * 注意事项：
 * 1. 部分方法需要特定的Android权限
 * 2. 网络状态检测可能存在延迟
 * 3. 部分方法在Android高版本中可能需要适配
 *
 * <AUTHOR> Team
 * @version 2.0.0
 */
public final class NetworkUtils {

    /**
     * 日志标签
     */
    private static final String TAG = "NetworkUtils";

    /**
     * 网络状态回调对象，用于监听网络状态变化
     * 注意：此变量必须在注册和注销时正确管理，避免内存泄漏
     */
    private static NetworkCallback mNetworkCallback;

    /**
     * 网络连接超时时间（毫秒）
     */
    private static final int CONNECTION_TIMEOUT = 3000;

    /**
     * 网络读取超时时间（毫秒）
     */
    private static final int READ_TIMEOUT = 5000;

    /**
     * 用于检测网络连接的URL列表
     */
    private static final String[] CHECK_URLS = {
            "https://www.baidu.com",
            "https://www.qq.com",
            "https://www.taobao.com"
    };

    /**
     * 私有构造方法，防止实例化
     * 工具类不应该被实例化，所有方法都是静态方法
     *
     * @throws UnsupportedOperationException 当尝试实例化此类时抛出
     */
    private NetworkUtils() {
        throw new UnsupportedOperationException("无法初始化！");
    }

    /**
     * 检查当前网络是否连接
     * <p>
     * 该方法通过获取系统网络信息来判断当前是否有可用的网络连接
     * </p>
     *
     * @return true表示网络已连接，false表示网络未连接
     * @throws SecurityException 如果应用没有ACCESS_NETWORK_STATE权限
     */
    @RequiresPermission("android.permission.ACCESS_NETWORK_STATE")
    public static boolean isConnected() {
        NetworkInfo networkInfo = getNetworkInfo();
        return networkInfo != null && networkInfo.isConnected();
    }

    /**
     * 获取当前活动的网络信息
     * <p>
     * 该方法通过ConnectivityManager获取当前活动的网络连接信息
     * </p>
     *
     * @return NetworkInfo对象，如果没有网络连接则返回null
     * @throws SecurityException 如果应用没有ACCESS_NETWORK_STATE权限
     */
    @RequiresPermission("android.permission.ACCESS_NETWORK_STATE")
    private static NetworkInfo getNetworkInfo() {
        ConnectivityManager connectivityManager = (ConnectivityManager) App.getContextInstance().getSystemService(Context.CONNECTIVITY_SERVICE);
        return connectivityManager == null ? null : connectivityManager.getActiveNetworkInfo();
    }

    /**
     * 检查是否正在使用移动数据网络
     * <p>
     * 该方法检查当前是否通过移动数据网络连接到互联网
     * </p>
     *
     * @return true表示正在使用移动数据网络，false表示未使用移动数据网络
     * @throws SecurityException 如果应用没有ACCESS_NETWORK_STATE权限
     */
    @RequiresPermission("android.permission.ACCESS_NETWORK_STATE")
    public static boolean useMobileNetwork() {
        NetworkInfo networkInfo = getNetworkInfo();
        return networkInfo != null && networkInfo.isConnected() && networkInfo.getType() == 0;
    }

    /**
     * 检查是否正在使用4G网络
     * <p>
     * 该方法通过检查网络子类型来判断是否使用4G网络连接
     * </p>
     *
     * @return true表示正在使用4G网络，false表示未使用4G网络
     * @throws SecurityException 如果应用没有ACCESS_NETWORK_STATE权限
     */
    @RequiresPermission("android.permission.ACCESS_NETWORK_STATE")
    public static boolean use4G() {
        NetworkInfo networkInfo = getNetworkInfo();
        return networkInfo != null && networkInfo.isConnected() && networkInfo.getSubtype() == 13;
    }

    /**
     * 检查WiFi是否已连接
     * <p>
     * 该方法检查当前是否通过WiFi连接到网络
     * </p>
     *
     * @return true表示WiFi已连接，false表示WiFi未连接
     * @throws SecurityException 如果应用没有ACCESS_NETWORK_STATE权限
     */
    @RequiresPermission("android.permission.ACCESS_NETWORK_STATE")
    public static boolean isWifiConnected() {
        ConnectivityManager cm = (ConnectivityManager) App.getContextInstance().getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm == null) {
            return false;
        } else {
            NetworkInfo ni = cm.getActiveNetworkInfo();
            return ni != null && ni.getType() == 1;
        }
    }

//    public static boolean isAvailablePingByIp(String ipAddress) {
//        if (StringUtils.isSpace(ipAddress)) {
//            return false;
//        } else {
//            CommandResult result = ShellUtils.execCmd(String.format("ping -c 1 %s", ipAddress), false);
//            return result.result == 0;
//        }
//    }

    /**
     * 获取设备的IP地址
     * <p>
     * 该方法可以获取设备的IPv4或IPv6地址。通过遍历所有网络接口来获取第一个非回环地址。
     * </p>
     *
     * 实现特点：
     * 1. 支持IPv4和IPv6地址获取
     * 2. 自动过滤回环地址
     * 3. 优先返回活动网络接口的地址
     *
     * @param useIpv4 true表示获取IPv4地址，false表示获取IPv6地址
     * @return 返回IP地址字符串，如果获取失败则返回空字符串
     */
    public static String getIPAddress(boolean useIpv4) {
        try {
            Enumeration<NetworkInterface> nis = NetworkInterface.getNetworkInterfaces();
            LinkedList adds = new LinkedList();

            label64:
            while(true) {
                NetworkInterface ni;
                do {
                    do {
                        if (!nis.hasMoreElements()) {
                            Iterator var9 = adds.iterator();

                            while(var9.hasNext()) {
                                InetAddress add = (InetAddress)var9.next();
                                if (!add.isLoopbackAddress()) {
                                    String hostAddress = add.getHostAddress();
                                    boolean isIpv4 = hostAddress.indexOf(58) < 0;
                                    if (useIpv4) {
                                        if (isIpv4) {
                                            return hostAddress;
                                        }
                                    } else if (!isIpv4) {
                                        int index = hostAddress.indexOf(37);
                                        return index < 0 ? hostAddress.toUpperCase() : hostAddress.substring(0, index).toUpperCase();
                                    }
                                }
                            }
                            break label64;
                        }

                        ni = (NetworkInterface)nis.nextElement();
                    } while(!ni.isUp());
                } while(ni.isLoopback());

                Enumeration addresses = ni.getInetAddresses();

                while(addresses.hasMoreElements()) {
                    adds.addFirst(addresses.nextElement());
                }
            }
        } catch (SocketException var8) {
            var8.printStackTrace();
        }

        return "";
    }

    /**
     * 设置WiFi开关状态
     * <p>
     * 该方法用于控制设备的WiFi功能的开启和关闭
     * </p>
     *
     * @param enabled true表示开启WiFi，false表示关闭WiFi
     * @throws SecurityException 如果应用没有CHANGE_WIFI_STATE权限
     */
    @RequiresPermission("android.permission.CHANGE_WIFI_STATE")
    public static void setWifiEnabled(boolean enabled) {
        WifiManager manager = (WifiManager)  App.getContextInstance().getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        if (manager != null) {
            if (enabled != manager.isWifiEnabled()) {
                manager.setWifiEnabled(enabled);
            }
        }
    }

    /**
     * 注册网络状态监听
     * <p>
     * 该方法用于注册网络状态变化的回调监听。仅支持Android 7.0（API 24）及以上版本。
     * </p>
     *
     * 注意事项：
     * 1. 需要在不再需要监听时调用unRegisterNetwork方法注销监听
     * 2. 避免重复注册导致的内存泄漏
     *
     * @param callback 网络状态回调接口，不能为null
     * @throws SecurityException 如果应用没有CHANGE_NETWORK_STATE权限
     */
    @RequiresPermission("android.permission.CHANGE_NETWORK_STATE")
    public static void registerNetwork(@NonNull final  NetworkUtils.ArcNetworkCallback callback) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            if (mNetworkCallback == null) {
                mNetworkCallback = new NetworkCallback() {
                    @Override
                    public void onAvailable(Network network) {
                        if (callback != null) {
                            callback.isConnected(true);
                        }

                    }

                    @Override
                    public void onLost(Network network) {
                        if (callback != null) {
                            callback.isConnected(false);
                        }

                    }
                };
            }

            ConnectivityManager connectivityManager = (ConnectivityManager)  App.getContextInstance().getSystemService(Context.CONNECTIVITY_SERVICE);
            if (connectivityManager != null) {
                connectivityManager.requestNetwork((new Builder()).build(), mNetworkCallback);
            }
        }

    }

    /**
     * 注销网络状态监听
     * <p>
     * 该方法用于注销之前注册的网络状态监听回调。应在不再需要监听网络状态时调用此方法。
     * </p>
     */
    public static void unRegisterNetwork() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && mNetworkCallback != null) {
            ConnectivityManager connectivityManager = (ConnectivityManager)  App.getContextInstance().getSystemService(Context.CONNECTIVITY_SERVICE);
            connectivityManager.unregisterNetworkCallback(mNetworkCallback);
            mNetworkCallback = null;
        }

    }

    /**
     * 获取当前网络连接类型
     * <p>
     * 该方法用于判断当前网络连接的具体类型，包括：
     * - 以太网
     * - WiFi
     * - 2G/3G/4G移动网络
     * </p>
     *
     * 实现特点：
     * 1. 优先判断是否为以太网连接
     * 2. 其次判断是否为WiFi连接
     * 3. 最后判断移动网络类型（2G/3G/4G）
     * 4. 支持特殊的移动网络制式判断（如TD-SCDMA等）
     *
     * @return 返回NetworkType枚举值，表示具体的网络类型
     * @throws SecurityException 如果应用没有ACCESS_NETWORK_STATE权限
     */
    @RequiresPermission("android.permission.ACCESS_NETWORK_STATE")
    public static   NetworkUtils.NetworkType getNetworkType() {
        if (isEthernet()) {
            return   NetworkUtils.NetworkType.NETWORK_ETHERNET;
        } else {
            NetworkInfo info = getActiveNetworkInfo();
            if (info != null && info.isAvailable()) {
                if (info.getType() == 1) {
                    return   NetworkUtils.NetworkType.NETWORK_WIFI;
                } else if (info.getType() == 0) {
                    switch(info.getSubtype()) {
                        case 1:
                        case 2:
                        case 4:
                        case 7:
                        case 11:
                        case 16:
                            return   NetworkUtils.NetworkType.NETWORK_2G;
                        case 3:
                        case 5:
                        case 6:
                        case 8:
                        case 9:
                        case 10:
                        case 12:
                        case 14:
                        case 15:
                        case 17:
                            return   NetworkUtils.NetworkType.NETWORK_3G;
                        case 13:
                        case 18:
                            return   NetworkUtils.NetworkType.NETWORK_4G;
                        default:
                            String subtypeName = info.getSubtypeName();
                            return !"TD-SCDMA".equalsIgnoreCase(subtypeName) && !"WCDMA".equalsIgnoreCase(subtypeName) && !"CDMA2000".equalsIgnoreCase(subtypeName) ?   NetworkUtils.NetworkType.NETWORK_UNKNOWN :   NetworkUtils.NetworkType.NETWORK_3G;
                    }
                } else {
                    return   NetworkUtils.NetworkType.NETWORK_UNKNOWN;
                }
            } else {
                return   NetworkUtils.NetworkType.NETWORK_NO;
            }
        }
    }

    /**
     * 检查是否为以太网连接
     * <p>
     * 该方法通过检查网络连接的类型和状态来判断是否使用以太网连接
     * </p>
     *
     * 注意事项：
     * 1. 需要系统支持以太网连接
     * 2. 同时检查连接状态是否为已连接或正在连接
     *
     * @return true表示当前使用以太网连接，false表示未使用以太网连接
     * @throws SecurityException 如果应用没有ACCESS_NETWORK_STATE权限
     */
    @RequiresPermission("android.permission.ACCESS_NETWORK_STATE")
    private static boolean isEthernet() {
        ConnectivityManager cm = (ConnectivityManager)  App.getContextInstance().getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm == null) {
            return false;
        } else {
            NetworkInfo info = cm.getNetworkInfo(9);
            if (info == null) {
                return false;
            } else {
                State state = info.getState();
                if (null == state) {
                    return false;
                } else {
                    return state == State.CONNECTED || state == State.CONNECTING;
                }
            }
        }
    }

    /**
     * 获取当前活动的网络连接信息
     * <p>
     * 该方法通过ConnectivityManager获取当前活动的网络连接详细信息
     * </p>
     *
     * @return NetworkInfo对象，包含当前活动网络的详细信息；如果没有活动的网络连接则返回null
     * @throws SecurityException 如果应用没有ACCESS_NETWORK_STATE权限
     */
    @RequiresPermission("android.permission.ACCESS_NETWORK_STATE")
    private static NetworkInfo getActiveNetworkInfo() {
        ConnectivityManager cm = (ConnectivityManager) App.getContextInstance().getSystemService(Context.CONNECTIVITY_SERVICE);
        return cm == null ? null : cm.getActiveNetworkInfo();
    }

    /**
     * 通过DNS解析检查网络是否可用
     * <p>
     * 该方法通过尝试解析域名来检查网络连接是否正常。如果未指定域名，默认使用www.baidu.com
     * </p>
     *
     * 注意事项：
     * 1. 该方法会产生实际的网络请求
     * 2. 建议在后台线程中调用
     * 3. 可能受DNS服务器状态影响
     *
     * @param domain 要解析的域名，如果为空则使用默认域名
     * @return true表示网络可用，false表示网络不可用
     * @throws SecurityException 如果应用没有INTERNET权限
     */
    @RequiresPermission("android.permission.INTERNET")
    public static boolean isAvailableByDns(String domain) {
        String realDomain = TextUtils.isEmpty(domain) ? "www.baidu.com" : domain;

        try {
            InetAddress inetAddress = InetAddress.getByName(realDomain);
            return inetAddress != null;
        } catch (UnknownHostException var4) {
            var4.printStackTrace();
            return false;
        }
    }

    /**
     * 获取指定域名的IP地址
     * <p>
     * 该方法通过DNS解析获取指定域名对应的IP地址
     * </p>
     *
     * 注意事项：
     * 1. 该方法会产生实际的网络请求
     * 2. 建议在后台线程中调用
     * 3. 可能受DNS服务器状态影响
     *
     * @param domain 要解析的域名
     * @return 返回域名对应的IP地址，如果解析失败则返回空字符串
     * @throws SecurityException 如果应用没有INTERNET权限
     */
    @RequiresPermission("android.permission.INTERNET")
    public static String getDomainAddress(String domain) {
        try {
            InetAddress inetAddress = InetAddress.getByName(domain);
            return inetAddress.getHostAddress();
        } catch (UnknownHostException var3) {
            var3.printStackTrace();
            return "";
        }
    }

    /**
     * 检测设备的网络连接状态
     * <p>
     * 通过Android系统API获取设备的网络连接状态，包括WiFi和移动网络
     * </p>
     *
     * @param context Android上下文对象，用于获取系统服务
     * @return true表示网络已连接且可用，false表示网络未连接或不可用
     * @throws NullPointerException 如果context参数为null
     */
    public static boolean isNetworkConnected(Context context) {
        if (context == null) {
            return false;
        }
        ConnectivityManager mConnectivityManager = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo mNetworkInfo = mConnectivityManager.getActiveNetworkInfo();
        if (mNetworkInfo != null) {
            return mNetworkInfo.isAvailable();
        } else {
            return false;
        }
    }

    /**
     * 通过Ping方式检测网络是否可用（版本1）
     * <p>
     * 该方法通过执行系统ping命令来检测网络连通性
     * </p>
     *
     * 注意事项：
     * 1. 该方法依赖系统ping命令，在某些定制ROM中可能不可用
     * 2. 执行系统命令可能存在安全风险
     * 3. 网络延迟可能导致检测时间较长
     *
     * @return true表示网络可用，false表示网络不可用
     */
    public static boolean isInternetAvailableV1() {
        MyLog.v(TAG, "isInternetAvailable    " + "正在检测网络是否可用  " + System.currentTimeMillis());
        try {
            Process process = Runtime.getRuntime().exec("/system/bin/ping -c 1 www.baidu.com");
            int exitValue = process.waitFor();
            MyLog.v(TAG, "isInternetAvailable    " + exitValue + "  检测完成  " + System.currentTimeMillis());
            return (exitValue == 0);
        } catch (Exception e) {
            e.printStackTrace();
            MyLog.v(TAG, "isInternetAvailable    " + e.getMessage());
        }
        MyLog.v(TAG, "isInternetAvailable    " + "检测完成不可用  " + System.currentTimeMillis());
        return false;
    }

    /**
     * 通过HTTP请求方式检测网络是否可用（版本2）
     * <p>
     * 该方法通过尝试建立HTTP连接来检测网络可用性
     * </p>
     *
     * 注意事项：
     * 1. 该方法会产生实际的网络请求，可能会消耗流量
     * 2. 目标服务器可能存在访问限制或防火墙拦截
     * 3. 建议在非UI线程中调用此方法
     * 4. 超时时间设置为3秒，可能需要根据实际网络环境调整
     *
     * @return true表示网络可用，false表示网络不可用
     */
    public static boolean isInternetAvailableV2() {
        MyLog.v(TAG, "isInternetAvailable    " + "正在检测网络是否可用  " + System.currentTimeMillis());
        try {
            URL url = new URL("http://www.baidu.com");
            HttpURLConnection urlConnect = (HttpURLConnection) url.openConnection();
            urlConnect.setConnectTimeout(3000);
            urlConnect.connect();
            return true;
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 通过多URL轮询方式检测网络是否可用（版本3）
     * <p>
     * 该方法通过轮询多个可靠的网站来检测网络可用性，提供更可靠的检测结果
     * </p>
     *
     * 实现特点：
     * 1. 使用OkHttp客户端，支持现代化的HTTP特性
     * 2. 多个备选URL，提高检测成功率
     * 3. 设置了连接和读取超时，避免长时间等待
     * 4. 记录检测耗时，便于性能分析
     *
     * 注意事项：
     * 1. 该方法会产生多次网络请求，消耗相对较多流量
     * 2. 建议在非UI线程中调用此方法
     * 3. 需要注意OkHttpClient实例的生命周期管理
     * 4. 可能需要根据网络环境调整超时时间
     *
     * @return true表示网络可用，false表示网络不可用
     */
    public static boolean isInternetAvailableWithTimeoutV3() {
        long startTime = System.currentTimeMillis();
        MyLog.v(TAG, "isInternetAvailable    " + "正在检测网络是否可用  " + startTime);

        String[] urls = {"https://www.baidu.com/", "https://www.jd.com/", "https://www.tencent.com/zh-cn/"};

        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(15, TimeUnit.SECONDS)
                .readTimeout(15, TimeUnit.SECONDS)
                .build();

        for (String urlStr : urls) {
            try {
                Request request = new Request.Builder().url(urlStr).build();
                Response response = client.newCall(request).execute();
                if (response.isSuccessful()) {
                    long endTime = System.currentTimeMillis();
                    long duration = endTime - startTime;
                    MyLog.v(TAG, "isInternetAvailable    " + "检测完成可用，耗时：" + duration + "毫秒");
                    return true;
                }
            } catch (IOException e) {
                MyLog.v(TAG, "isInternetAvailable    " + e.getMessage());
            }
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        MyLog.v(TAG, "isInternetAvailable    " + "检测完成不可用，耗时：" + duration + "毫秒");
        return false;
    }

//    @RequiresPermission("android.permission.INTERNET")
//    public static boolean isAvailableByPing(String ip) {
//        String realIp = TextUtils.isEmpty(ip) ? "*********" : ip;
//        CommandResult result = ShellUtils.execCmd(String.format("ping -c 1 %s", realIp), false);
//        return result.result == 0;
//    }

    /**
     * 获取当前活跃的网络类型
     * @param context 上下文
     * @return 网络类型
     */
    public static NetworkType getActiveNetworkType(Context context) {
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm == null) {
            return NetworkUtils.NetworkType.NETWORK_NO;
        }
        
        NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
        if (activeNetwork == null || !activeNetwork.isConnected()) {
            return NetworkUtils.NetworkType.NETWORK_NO;
        }
        
        // 替换fromAndroidNetworkType调用，使用直接的类型判断
        int type = activeNetwork.getType();
        if (type == ConnectivityManager.TYPE_WIFI) {
            return NetworkUtils.NetworkType.NETWORK_WIFI;
        } else if (type == ConnectivityManager.TYPE_MOBILE) {
            // 根据子类型判断具体的移动网络类型
            int subType = activeNetwork.getSubtype();
            switch (subType) {
                case TelephonyManager.NETWORK_TYPE_LTE:
                case TelephonyManager.NETWORK_TYPE_IWLAN:
                    return NetworkUtils.NetworkType.NETWORK_4G;
                case TelephonyManager.NETWORK_TYPE_UMTS:
                case TelephonyManager.NETWORK_TYPE_EVDO_0:
                case TelephonyManager.NETWORK_TYPE_EVDO_A:
                case TelephonyManager.NETWORK_TYPE_HSDPA:
                case TelephonyManager.NETWORK_TYPE_HSUPA:
                case TelephonyManager.NETWORK_TYPE_HSPA:
                case TelephonyManager.NETWORK_TYPE_EVDO_B:
                case TelephonyManager.NETWORK_TYPE_EHRPD:
                case TelephonyManager.NETWORK_TYPE_HSPAP:
                    return NetworkUtils.NetworkType.NETWORK_3G;
                case TelephonyManager.NETWORK_TYPE_GPRS:
                case TelephonyManager.NETWORK_TYPE_EDGE:
                case TelephonyManager.NETWORK_TYPE_CDMA:
                case TelephonyManager.NETWORK_TYPE_1xRTT:
                case TelephonyManager.NETWORK_TYPE_IDEN:
                    return NetworkUtils.NetworkType.NETWORK_2G;
                default:
                    return NetworkUtils.NetworkType.NETWORK_UNKNOWN;
            }
        } else if (type == 9) { // ConnectivityManager.TYPE_ETHERNET
            return NetworkUtils.NetworkType.NETWORK_ETHERNET;
        } else {
            return NetworkUtils.NetworkType.NETWORK_UNKNOWN;
        }
    }

    /**
     * 检查网络是否可用（能够连接互联网）
     * 使用连接超时方式
     * @param context 上下文
     * @return 网络是否可用
     */
    public static boolean isInternetAvailableWithTimeout(Context context) {
        // 首先检查网络连接状态
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm == null) {
            MyLog.d(TAG, "ConnectivityManager为空，网络不可用");
            return false;
        }

        // 使用新API检查网络功能（Android 6.0及以上）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Network network = cm.getActiveNetwork();
            if (network == null) {
                MyLog.d(TAG, "当前没有活动网络，网络不可用");
                return false;
            }

            NetworkCapabilities capabilities = cm.getNetworkCapabilities(network);
            if (capabilities == null) {
                MyLog.d(TAG, "无法获取网络功能，网络不可用");
                return false;
            }

            // 检查是否有互联网访问能力
            boolean hasInternet = capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                    && capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);

            if (!hasInternet) {
                MyLog.d(TAG, "网络不具备互联网访问能力");
                return false;
            }

            MyLog.d(TAG, "网络具备互联网访问能力，尝试连接测试");
        } else {
            // 旧API检查网络连接
            NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            if (activeNetwork == null || !activeNetwork.isConnected()) {
                MyLog.d(TAG, "网络未连接，网络不可用");
                return false;
            }

            MyLog.d(TAG, "网络已连接，尝试连接测试");
        }

        // 尝试实际连接测试
        return testInternetConnection();
    }

    /**
     * 测试互联网连接
     * 尝试连接多个URL，任一成功即视为网络可用
     * @return 是否能连接互联网
     */
    private static boolean testInternetConnection() {
        for (String url : CHECK_URLS) {
            if (pingUrl(url)) {
                MyLog.d(TAG, "成功连接到 " + url + "，网络可用");
                return true;
            }
        }

        MyLog.d(TAG, "无法连接到任何测试URL，网络不可用");
        return false;
    }

    /**
     * 使用HTTP连接测试URL
     * @param urlString 要测试的URL
     * @return 是否能成功连接
     */
    private static boolean pingUrl(String urlString) {
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(CONNECTION_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            connection.setRequestMethod("HEAD");
            int responseCode = connection.getResponseCode();
            return (200 <= responseCode && responseCode <= 399);
        } catch (IOException e) {
            MyLog.d(TAG, "连接 " + urlString + " 失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 异步检查网络可用性
     * @param context 上下文
     * @param callback 回调接口
     */
    public static void checkInternetAvailabilityAsync(final Context context, final NetworkAvailabilityCallback callback) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                final boolean isAvailable = isInternetAvailableWithTimeout(context);
                final NetworkType networkType = getActiveNetworkType(context);

                // 在主线程中回调结果
                android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());
                mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        if (callback != null) {
                            callback.onResult(isAvailable, networkType);
                        }
                    }
                });
            }
        }).start();
    }

    /**
     * 网络可用性检测回调接口
     */
    public interface NetworkAvailabilityCallback {
        /**
         * 网络检测结果回调
         * @param isAvailable 网络是否可用
         * @param networkType 网络类型
         */
        void onResult(boolean isAvailable, NetworkType networkType);
    }

    /**
     * 定义了一个名为 NetworkType 的枚举类，用于表示网络类型
     */
    public static enum NetworkType {
        /**
         * 定义一个枚举值表示以太网网络类型
         */
        NETWORK_ETHERNET,

        /**
         * 定义一个枚举值表示 Wi-Fi 网络类型
         */
        NETWORK_WIFI,

        /**
         * 定义一个枚举值表示 4G 网络类型
         */
        NETWORK_4G,

        /**
         * 定义一个枚举值表示 3G 网络类型
         */
        NETWORK_3G,

        /**
         * 定义一个枚举值表示 2G 网络类型
         */
        NETWORK_2G,

        /**
         * 定义一个枚举值表示未知的网络类型
         */
        NETWORK_UNKNOWN,

        /**
         * 定义一个枚举值表示没有网络连接
         */
        NETWORK_NO;

        private NetworkType() {
        }
    }


    /**
     * 网络状态回调接口
     * <p>
     * 该接口用于监听网络连接状态的变化
     * </p>
     */
    public interface ArcNetworkCallback {
        /**
         * 网络连接状态回调方法
         * 
         * @param var1 true表示网络已连接，false表示网络已断开
         */
        void isConnected(boolean var1);
    }
}
