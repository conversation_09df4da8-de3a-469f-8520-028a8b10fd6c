package com.smartcar.easylauncher.shared.dialog;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.ImageView;

import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.dialog.DialogColumnAdapter;
import com.smartcar.easylauncher.infrastructure.interfaces.AppSelectionCallback;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.data.model.system.AppInfo;
import com.smartcar.easylauncher.shared.utils.AppInfoProvider;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;
import com.smartcar.easylauncher.shared.view.RefreshCircleView;
import com.timmy.tdialog.base.BindViewHolder;
import com.timmy.tdialog.base.TBaseAdapter;
import com.timmy.tdialog.list.TListDialog;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 竖向应用选择器
 *
 * <AUTHOR>
 */
public class VerticalAppSelector {

    private final Context context;
    private List<AppInfo> mAllAppList;
    private String appinfo;
    private AppInfo selectedInfo;
    private DialogColumnAdapter<AppInfo> adapter;
    private RefreshCircleView refreshCircleView;

    public VerticalAppSelector(Context context) {
        this.context = context;
    }

    private void initAdapter(List<AppInfo> appList) {
        adapter = new DialogColumnAdapter<>(R.layout.select_v_item_app, appList) {
            @Override
            protected void onBind(BindViewHolder holder, int position, AppInfo info) {
                holder.setText(R.id.tv_package_name, info.getAppName() + " ( " + info.getPackageName() + " )");
                Glide.with(context).load(info.getLogoPath()).into((ImageView) holder.getView(R.id.grid_item_app_icon));
                if (info.isSelect()) {
                    holder.getView(R.id.grid_item_app_delete).setVisibility(View.VISIBLE);
                } else {
                    holder.getView(R.id.grid_item_app_delete).setVisibility(View.GONE);
                }
            }
        };
        adapter.setOnAdapterItemClickListener((TBaseAdapter.OnAdapterItemClickListener<AppInfo>) (bindViewHolder, i, info, tDialog) -> {
            if (info.isSelect()) {
                appinfo = new Gson().toJson(info);
                selectedInfo = info;
            } else {
                appinfo = "";
            }
        });
    }

    public static void selectApp(Context context, int position, int type, AppSelectionCallback callback) {
        VerticalAppSelector selector = new VerticalAppSelector(context);
        selector.appinfo = "";
        selector.selectedInfo = null;
        if (DataManager.getAppListData().isEmpty()) {
            AppInfoProvider mProvider = AppInfoProvider.getInstance(context);
            mProvider.queryAppInfo().thenAccept(appInfos -> {
                selector.mAllAppList = appInfos;
                selector.initAdapter(selector.mAllAppList);
                selector.showDialog(position, type, callback);
            });
        } else {
            selector.mAllAppList = new Gson().fromJson(DataManager.getAppListData(), new TypeToken<List<AppInfo>>() {
            }.getType());
            //过滤掉已经被隐藏起来的应用
            selector.mAllAppList = selector.mAllAppList.stream().filter(appInfo -> appInfo.getState() == 0).collect(Collectors.toList());
            selector.initAdapter(selector.mAllAppList);
            selector.showDialog(position, type, callback);
        }
    }

    private void showDialog(int position, int type, AppSelectionCallback callback) {
        new TListDialog.Builder(((FragmentActivity) context).getSupportFragmentManager())
                // ...省略其他设置代码...
                .setListLayoutRes(R.layout.dialog_app_select, LinearLayoutManager.VERTICAL)
                //.setLayoutManager(new LinearLayoutManager(getActivity(), RecyclerView.VERTICAL, false))
                .setWidth(DensityUtils.dp2px(context, 460))
                .setHeight(DensityUtils.dp2px(context, 310))
                .setOnBindViewListener(bindViewHolder -> {
                    if (type == 0) {
                        bindViewHolder.getView(R.id.ll_xiezai).setVisibility(View.GONE);
                    } else {
                        bindViewHolder.getView(R.id.ll_xiezai).setVisibility(View.VISIBLE);
                    }
                    refreshCircleView = bindViewHolder.getView(R.id.iv_refresh);
                })
                .addOnClickListener(R.id.root, R.id.ll_payment, R.id.ll_xiezai, R.id.ll_guanbi, R.id.iv_refresh)
                .setOnViewClickListener((viewHolder, view, tDialog) -> {
                    switch (view.getId()) {
                        case R.id.ll_payment:
                            if (appinfo == null || appinfo.isEmpty()) {
                                MToast.makeTextShort("选择你要使用的音乐播放器");
                                return;
                            }
                            callback.onAppSelected(selectedInfo);
                            tDialog.dismiss();
                            break;

                        case R.id.ll_xiezai:
                            callback.onAppUninstalled(position);
                            tDialog.dismiss();
                            break;
                        case R.id.ll_guanbi:
                            tDialog.dismiss();
                            break;
                        case R.id.root:
                            tDialog.dismiss();
                            break;
                        case R.id.iv_refresh:
                            refreshCircleView.startAnimation();
                            refreshData();
                            break;
                        default:
                    }
                })
                .setAdapter(adapter)
                .setOnDismissListener(dialog -> {
                    // Toast.makeText(HomeActivity.this, "setOnDismissListener 回调", Toast.LENGTH_SHORT).show();
                })
                // ...省略其他设置代码...
                .create()
                .show();
    }

    private void refreshData() {
        AppInfoProvider mProvider = AppInfoProvider.getInstance(context);
        mProvider.refreshAppInfo().thenAccept(appInfos -> {
            mAllAppList = appInfos;
            refreshCircleView.stopAnimation();
            new Handler(Looper.getMainLooper()).post(() -> adapter.setData(mAllAppList));
        });
    }

}