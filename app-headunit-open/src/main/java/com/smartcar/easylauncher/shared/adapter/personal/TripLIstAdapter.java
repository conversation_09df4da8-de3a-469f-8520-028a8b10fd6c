package com.smartcar.easylauncher.shared.adapter.personal;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.data.database.entity.TripModel;
import com.smartcar.easylauncher.modules.trip.TripDetailsScene;
import com.smartcar.easylauncher.shared.utils.location.DistanceConverter;
import com.smartcar.easylauncher.shared.utils.time.TimeTools;


/**
 * <AUTHOR>
 */
public class TripLIstAdapter extends BaseQuickAdapter<TripModel, QuickViewHolder> {


    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable TripModel item) {
        assert item != null;
        helper.setText(R.id.tv_time, TimeTools.getDateToString(item.getStartTime(), TimeTools.DATE_TIME_FORMAT_MINUTE)
                + " - " + TimeTools.getDateToString(item.getEndTime(), TimeTools.DATE_TIME_FORMAT_MINUTE));
        helper.setText(R.id.tv_size, DistanceConverter.formatDistance(getContext(), item.getTotalDistance()));
        helper.setText(R.id.tv_path, "总时长：" + TimeTools.displayDuration2(getContext(), item.getTotalTime()));
        helper.setText(R.id.tv_distance, "运动时长：" + TimeTools.displayDuration2(getContext(),item. getActiveTime()));

        ViewCompat.setTransitionName(helper.getView(R.id.iv_icon), TripDetailsScene.VIEW_NAME_HEADER_IMAGE + item.getId());
        ViewCompat.setTransitionName(helper.getView(R.id.tv_time), TripDetailsScene.VIEW_NAME_HEADER_TITLE + item.getId());
        ViewCompat.setTransitionName(helper.getView(R.id.tv_size), TripDetailsScene.TOTAL_DISTANCE_NUM + item.getId());
        ViewCompat.setTransitionName(helper.getView(R.id.tv_path), TripDetailsScene.TOTAL_TIME_NUM + item.getId());
        ViewCompat.setTransitionName(helper.getView(R.id.tv_distance), TripDetailsScene.DURATION_NUM + item.getId());
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.trip_item, viewGroup);
    }
}
