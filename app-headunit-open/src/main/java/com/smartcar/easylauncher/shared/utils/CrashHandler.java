package com.smartcar.easylauncher.shared.utils;

// 导入所需的Android和Java库

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;

import androidx.annotation.NonNull;

import com.smartcar.easylauncher.modules.other.CrashActivity;
import com.smartcar.easylauncher.shared.utils.system.DesktopUtils;

// 定义CrashHandler类，它实现了Thread.UncaughtExceptionHandler接口，用于处理未捕获的异常
public final class CrashHandler implements Thread.UncaughtExceptionHandler {

    // 定义Crash文件名常量
    private static final String CRASH_FILE_NAME = "crash_file";
    // 定义Crash时间记录键名常量
    private static final String KEY_CRASH_TIME = "key_crash_time";

    // 提供一个公共的静态方法，用于注册Crash监听
    public static void register(Application application) {
        Thread.setDefaultUncaughtExceptionHandler(new CrashHandler(application));
    }

    // 定义Application和Thread.UncaughtExceptionHandler类型的私有成员变量
    private final Application mApplication;
    private final Thread.UncaughtExceptionHandler mNextHandler;

    // 私有构造函数，用于初始化成员变量
    private CrashHandler(Application application) {
        mApplication = application;
        mNextHandler = Thread.getDefaultUncaughtExceptionHandler();
        // 如果已经注册过Crash监听，就抛出异常
        if (getClass().getName().equals(mNextHandler.getClass().getName())) {
            throw new IllegalStateException("are you ok?");
        }
    }

    // 重写uncaughtException方法，处理未捕获的异常
    @SuppressLint("ApplySharedPref")
    @Override
    public void uncaughtException(@NonNull Thread thread, @NonNull Throwable throwable) {
        // 获取SharedPreferences对象，用于存储和读取Crash信息
        SharedPreferences sharedPreferences = mApplication.getSharedPreferences(CRASH_FILE_NAME, Context.MODE_PRIVATE);
        // 获取当前时间
        long currentCrashTime = System.currentTimeMillis();
        // 读取上次Crash的时间
        long lastCrashTime = sharedPreferences.getLong(KEY_CRASH_TIME, 0);
        // 记录当前Crash的时间
        sharedPreferences.edit().putLong(KEY_CRASH_TIME, currentCrashTime).commit();

        // 判断是否为致命异常，如果上次Crash的时间距离当前Crash小于5分钟，那么判定为致命异常
        boolean deadlyCrash = currentCrashTime - lastCrashTime < 1000 * 60 * 5;

        // 如果是调试模式或者是内测模式，就启动CrashActivity来显示Crash信息
        if (AppConfig.isDebug() || AppConfig.isAlpha()) {
            CrashActivity.start(mApplication, throwable);
        } else {
            // 如果不是致命的异常，就自动重启应用
            if (!deadlyCrash) {
                //   RestartActivity.start(mApplication);
                // 如果是致命的异常，就自动重启桌面
                DesktopUtils.restartApp4(mApplication);
            }
        }

        // 如果下一个异常处理器不是系统的异常处理器，就调用它的uncaughtException方法
        if (mNextHandler != null && !mNextHandler.getClass().getName().startsWith("com.android.internal.os")) {
            mNextHandler.uncaughtException(thread, throwable);
        }

        // 杀死当前进程
        android.os.Process.killProcess(android.os.Process.myPid());
        // 退出程序
        System.exit(10);
    }
}
