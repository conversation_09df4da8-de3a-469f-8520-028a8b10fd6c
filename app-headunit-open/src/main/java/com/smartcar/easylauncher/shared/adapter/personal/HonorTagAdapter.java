package com.smartcar.easylauncher.shared.adapter.personal;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.data.model.user.HonorTag;

/**
 * 荣誉标签适配器
 * <AUTHOR>
 */
public class HonorTagAdapter extends BaseQuickAdapter<HonorTag, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder holder, int position, @Nullable HonorTag tag) {
        if (tag == null) return;
        
        TextView tagView = (TextView) holder.getView(R.id.tv_tag);
        tagView.setText(tag.getText());
        
        // 设置图标
        tagView.setCompoundDrawablesWithIntrinsicBounds(
                tag.getHonorType().getIconRes(),
                0,
                0,
                0
        );
        
        // 设置渐变背景 - 修改设置圆角的方式
        GradientDrawable gradient = new GradientDrawable(
                GradientDrawable.Orientation.TL_BR,
                new int[]{tag.getHonorType().getStartColor(), tag.getHonorType().getEndColor()}
        );
        
        // 使用固定的圆角大小，而不是依赖view的高度
        float cornerRadius = getContext().getResources().getDimension(R.dimen.honor_tag_corner_radius);
        gradient.setCornerRadius(cornerRadius);
        tagView.setBackground(gradient);
        
        // 设置图标大小
        Drawable[] drawables = tagView.getCompoundDrawables();
        if (drawables[0] != null) {
            int iconSize = getContext().getResources().getDimensionPixelSize(R.dimen.honor_tag_icon_size);
            drawables[0].setBounds(0, 0, iconSize, iconSize);
            tagView.setCompoundDrawables(drawables[0], null, null, null);
        }
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup parent, int viewType) {
        return new QuickViewHolder(
                LayoutInflater.from(context).inflate(R.layout.item_honor_tag, parent, false)
        );
    }
} 