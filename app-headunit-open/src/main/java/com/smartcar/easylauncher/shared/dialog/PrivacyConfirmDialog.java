package com.smartcar.easylauncher.shared.dialog;

import android.app.Activity;
import android.view.KeyEvent;
import android.view.View;
import androidx.fragment.app.FragmentManager;
import androidx.annotation.NonNull;
import java.lang.ref.WeakReference;
import android.os.Handler;
import android.os.Looper;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.manager.AuthorityManager;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;
import com.timmy.tdialog.TDialog;
import com.timmy.tdialog.base.BindViewHolder;
import com.timmy.tdialog.listener.OnViewClickListener;

/**
 * 隐私协议再次确认弹窗
 */
public class PrivacyConfirmDialog {
    private static final String TAG = "PrivacyConfirmDialog";
    private static volatile PrivacyConfirmDialog instance;
    
    // 添加静态标志以全局控制弹窗显示状态
    private static boolean isConfirmDialogShowing = false;

    private WeakReference<TDialog> dialogRef;
    private WeakReference<Activity> activityRef;
    private boolean isShowing = false; // 实例级别的状态标记

    private PrivacyConfirmDialog() {
        // 私有构造方法
    }

    public static PrivacyConfirmDialog getInstance() {
        if (instance == null) {
            synchronized (PrivacyConfirmDialog.class) {
                if (instance == null) {
                    instance = new PrivacyConfirmDialog();
                }
            }
        }
        return instance;
    }

    /**
     * 检查确认弹窗是否正在显示（全局状态）
     * @return true表示全局有确认弹窗正在显示
     */
    public static boolean isDialogShowing() {
        return isConfirmDialogShowing;
    }
    
    /**
     * 检查确认弹窗是否正在显示（实例状态）
     * @return true表示弹窗正在显示，false表示弹窗未显示
     */
    public boolean isShowing() {
        return isShowing && dialogRef != null && dialogRef.get() != null && dialogRef.get().isVisible();
    }

    /**
     * 关闭隐私协议再次确认弹窗
     */
    public void dismiss() {
        MyLog.v(TAG, "dismiss: 开始关闭确认弹窗");
        
        // 先重置状态，避免在dismiss过程中再次尝试显示弹窗
        boolean wasShowing = isShowing;
        isShowing = false;
        isConfirmDialogShowing = false;
        
        if (dialogRef != null && dialogRef.get() != null) {
            TDialog dialog = dialogRef.get();
            if (dialog.isVisible()) {
                dialog.dismiss();
                MyLog.v(TAG, "dismiss: 确认弹窗已关闭");
            }
        }
        
        // 清理引用
        dialogRef = null;
        activityRef = null;
        
        // 发布延迟消息以确保状态完全重置
        if (wasShowing) {
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                MyLog.v(TAG, "dismiss: 确认弹窗状态已完全重置");
                isConfirmDialogShowing = false;
            }, 100);
        }
    }

    /**
     * 隐私协议再次确认弹窗回调接口
     */
    public interface PrivacyConfirmCallback {
        /**
         * 用户重新查看隐私协议
         */
        void onReviewPrivacy();

        /**
         * 用户退出应用
         */
        void onExitApp();
    }

    /**
     * 显示隐私协议再次确认弹窗
     *
     * @param activity Activity实例
     * @param fragmentManager FragmentManager实例
     * @param callback 回调接口
     */
    public void show(@NonNull Activity activity, @NonNull FragmentManager fragmentManager, @NonNull PrivacyConfirmCallback callback) {
        // 检查全局状态，如果已有弹窗在显示，则不显示
        if (isConfirmDialogShowing) {
            MyLog.v(TAG, "show: 全局已有确认弹窗在显示，不再显示新弹窗");
            return;
        }
        
        // 检查实例状态，如果已有弹窗在显示，则不显示
        if (isShowing()) {
            MyLog.v(TAG, "show: 实例已有确认弹窗在显示，不再显示新弹窗");
            return;
        }

        MyLog.v(TAG, "show: 开始显示确认弹窗");
        // 设置全局状态为显示中
        isConfirmDialogShowing = true;
        
        activityRef = new WeakReference<>(activity);

        TDialog dialog = new TDialog.Builder(fragmentManager)
                .setLayoutRes(R.layout.dialog_version_upgrde)
                .setWidth(DensityUtils.dp2px(activityRef.get(), 360))
                .setHeight(DensityUtils.dp2px(activityRef.get(), 160))
                .addOnClickListener(R.id.tv_cancel, R.id.tv_confirm)
                .setCancelableOutside(false)
                .setOnViewClickListener(new OnViewClickListener() {
                    @Override
                    public void onViewClick(BindViewHolder viewHolder, View view, TDialog tDialog) {
                        switch (view.getId()) {
                            case R.id.tv_cancel:
                                AuthorityManager.setUseLicense(false);
                                callback.onExitApp();
                                dismiss();
                                break;
                            case R.id.tv_confirm:
                                callback.onReviewPrivacy();
                                dismiss();
                                break;
                        }
                    }
                })
                .setOnKeyListener((dialog1, keyCode, event) -> {
                    // 禁用返回键
                    return keyCode == KeyEvent.KEYCODE_BACK;
                })
                .create();

        dialogRef = new WeakReference<>(dialog);

        try {
            isShowing = true; // 标记实例状态为显示中
            dialog.show(fragmentManager);
            MyLog.v(TAG, "show: 确认弹窗显示成功");
        } catch (Exception e) {
            MyLog.e(TAG, "show: 确认弹窗显示失败: " + e.getMessage());
            isShowing = false; // 发生异常时重置实例状态
            isConfirmDialogShowing = false; // 发生异常时重置全局状态
            dismiss(); // 发生异常时清理资源
        }
    }

    /**
     * 重置所有弹窗状态，通常在应用启动时调用
     */
    public static void resetDialogState() {
        isConfirmDialogShowing = false;
        MyLog.v(TAG, "resetDialogState: 重置确认弹窗全局状态");
        
        if (instance != null) {
            instance.dismiss();
            instance = null;
        }
    }
} 