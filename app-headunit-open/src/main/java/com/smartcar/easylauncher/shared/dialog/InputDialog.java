package com.smartcar.easylauncher.shared.dialog;

import android.app.Activity;
import android.view.View;
import android.widget.EditText;

import androidx.fragment.app.FragmentManager;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;
import com.timmy.tdialog.TDialog;
import com.timmy.tdialog.base.BindViewHolder;
import com.timmy.tdialog.listener.OnBindViewListener;
import com.timmy.tdialog.listener.OnViewClickListener;

public class InputDialog {

    public interface OnEditTextChangeListener {
        void onEditTextChanged(String text);
    }

    public static void showDialog(Activity activity, FragmentManager supportFragmentManager, OnEditTextChangeListener listener) {
        showDialog(activity, supportFragmentManager, "", listener);
    }

    public static void showDialog(Activity activity, FragmentManager supportFragmentManager, String text, OnEditTextChangeListener listener) {
        new TDialog.Builder(supportFragmentManager)
                .setLayoutRes(R.layout.dialog_input)
                .setWidth(DensityUtils.dp2px(activity, 360))
                .setHeight(DensityUtils.dp2px(activity, 220))
                .addOnClickListener( R.id.cl_root, R.id.start_button, R.id.end_button)
                .setCancelableOutside(true)
                .setOnBindViewListener(new OnBindViewListener() {
                    @Override
                    public void bindView(BindViewHolder bindViewHolder) {
                        EditText editText = bindViewHolder.getView(R.id.et_mobile);
                        if (!text.isEmpty()) {
                            editText.setText(text);
                        }
                    }
                })
                .setOnViewClickListener(new OnViewClickListener() {
                    @Override
                    public void onViewClick(BindViewHolder viewHolder, View view, TDialog tDialog) {
                        switch (view.getId()) {
                            case R.id.start_button:
                                EditText editText = viewHolder.getView(R.id.et_mobile);
                                String text = editText.getText().toString();
                                listener.onEditTextChanged(text); // 调用接口方法
                                tDialog.dismiss();
                                break;
                            case R.id.end_button:
                                tDialog.dismiss();
                                break;
                            default:
                                break;
                        }
                    }
                }).create().show();
    }
}
