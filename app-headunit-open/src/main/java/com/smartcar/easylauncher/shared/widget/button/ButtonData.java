package com.smartcar.easylauncher.shared.widget.button;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;

/**
 * 按钮数据类，用于存储按钮的各类属性，如是否是主按钮、是否使用图标、按钮文本、图标、图标内边距和背景颜色等。
 * <p>
 *
 * <AUTHOR>
 * @date 2016/9/9
 */
public class ButtonData implements Cloneable {
    /**
     * 默认背景颜色为白色
     */
    private static final int DEFAULT_BACKGROUND_COLOR = Color.WHITE;

    /**
     * 是否是主按钮（当按钮全部折叠时，看到的按钮）
     */
    private boolean isMainButton = false;
    /**
     * 是否使用图标资源（true表示使用图标资源，false表示使用字符串资源）
     */
    private boolean iconButton;

    /**
     * 要在按钮中心显示的字符串数组，texts[i] 将显示在第 i 行
     */
    private String[] texts;
    /**
     * 要在按钮中心显示的图标
     */
    private Drawable icon;
    /**
     * 按钮中图标Drawable的内边距（以dp为单位）
     */
    private float iconPaddingDp;
    /**
     * 按钮的背景颜色
     */
    private int backgroundColor = DEFAULT_BACKGROUND_COLOR;

    /**
     * 克隆当前对象，并返回克隆后的对象。
     *
     * @return 克隆后的 ButtonData 对象
     * @throws CloneNotSupportedException 如果对象不支持克隆，则抛出此异常
     */
    @NonNull
    @Override
    protected Object clone() throws CloneNotSupportedException {
        ButtonData buttonData = (ButtonData) super.clone();
        buttonData.setIsIconButton(this.iconButton);
        buttonData.setBackgroundColor(this.backgroundColor);
        buttonData.setIsMainButton(this.isMainButton);
        buttonData.setIcon(this.icon);
        buttonData.setIconPaddingDp(this.iconPaddingDp);
        buttonData.setTexts(this.texts);
        return buttonData;
    }

    /**
     * 构建一个文本按钮。
     *
     * @param text 按钮上要显示的文本数组
     * @return 构建的 ButtonData 对象
     */
    public static ButtonData buildTextButton(String... text) {
        ButtonData buttonData = new ButtonData(false);
        buttonData.iconButton = false;
        buttonData.setText(text);
        return buttonData;
    }

    /**
     * 构建一个图标按钮。
     *
     * @param context       上下文对象，用于获取资源
     * @param iconResId     图标的资源ID
     * @param iconPaddingDp 图标Drawable的内边距（以dp为单位）
     * @return 构建的 ButtonData 对象
     */
    public static ButtonData buildIconButton(Context context, int iconResId, float iconPaddingDp) {
        ButtonData buttonData = new ButtonData(true);
        buttonData.iconButton = true;
        buttonData.iconPaddingDp = iconPaddingDp;
        buttonData.setIconResId(context, iconResId);
        return buttonData;
    }

    /**
     * 构造函数，用于初始化是否使用图标按钮。
     *
     * @param iconButton 是否使用图标按钮
     */
    private ButtonData(boolean iconButton) {
        this.iconButton = iconButton;
    }

    /**
     * 设置是否是主按钮。
     *
     * @param isMainButton 是否是主按钮
     */
    public void setIsMainButton(boolean isMainButton) {
        this.isMainButton = isMainButton;
    }

    /**
     * 获取是否是主按钮。
     *
     * @return 是否是主按钮
     */
    public boolean isMainButton() {
        return isMainButton;
    }

    /**
     * 设置是否是图标按钮。
     *
     * @param isIconButton 是否是图标按钮
     */
    public void setIsIconButton(boolean isIconButton) {
        this.iconButton = isIconButton;
    }

    /**
     * 获取按钮的文本数组。
     *
     * @return 按钮的文本数组
     */
    public String[] getTexts() {
        return texts;
    }

    /**
     * 设置按钮的文本数组。
     *
     * @param texts 按钮的文本数组
     */
    public void setTexts(String[] texts) {
        this.texts = texts;
    }

    /**
     * 设置按钮的文本。
     *
     * @param text 按钮的文本数组
     */
    public void setText(String... text) {
        this.texts = new String[text.length];
        for (int i = 0, length = text.length; i < length; i++) {
            this.texts[i] = text[i];
        }
    }

    /**
     * 设置按钮的图标。
     *
     * @param icon 按钮的图标
     */
    public void setIcon(Drawable icon) {
        this.icon = icon;
    }

    /**
     * 获取按钮的图标。
     *
     * @return 按钮的图标
     */
    public Drawable getIcon() {
        return this.icon;
    }

    /**
     * 根据资源ID设置按钮的图标。
     *
     * @param context   上下文对象，用于获取资源
     * @param iconResId 图标的资源ID
     */
    public void setIconResId(Context context, int iconResId) {
        // 使用ResourcesCompat.getDrawable()来兼容不同版本的API
        this.icon = ResourcesCompat.getDrawable(context.getResources(), iconResId, null);
    }

    /**
     * 获取是否是图标按钮。
     *
     * @return 是否是图标按钮
     */
    public boolean isIconButton() {
        return iconButton;
    }

    /**
     * 获取按钮图标Drawable的内边距（以dp为单位）。
     *
     * @return 按钮图标Drawable的内边距（以dp为单位）
     */
    public float getIconPaddingDp() {
        return iconPaddingDp;
    }

    /**
     * 设置按钮图标Drawable的内边距（以dp为单位）。
     *
     * @param padding 按钮图标Drawable的内边距（以dp为单位）
     */
    public void setIconPaddingDp(float padding) {
        this.iconPaddingDp = padding;
    }

    /**
     * 获取按钮的背景颜色。
     *
     * @return 按钮的背景颜色
     */
    public int getBackgroundColor() {
        return backgroundColor;
    }

    /**
     * 设置按钮的背景颜色。
     *
     * @param backgroundColor 按钮的背景颜色
     */
    public void setBackgroundColor(int backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    /**
     * 根据资源ID设置按钮的背景颜色。
     *
     * @param context           上下文对象，用于获取资源
     * @param backgroundColorId 背景颜色的资源ID
     */
    public void setBackgroundColorId(Context context, int backgroundColorId) {
        // 使用ContextCompat.getColor()来兼容不同版本的API
        this.backgroundColor = ContextCompat.getColor(context, backgroundColorId);
    }
}
