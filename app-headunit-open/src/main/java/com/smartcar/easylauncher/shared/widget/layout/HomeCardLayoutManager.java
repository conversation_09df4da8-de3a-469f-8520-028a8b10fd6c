package com.smartcar.easylauncher.shared.widget.layout;

import android.os.Build;
import android.transition.TransitionManager;
import android.view.View;

import androidx.constraintlayout.widget.ConstraintSet;
import androidx.fragment.app.FragmentActivity;

import com.smartcar.easylauncher.core.constants.SettingsConstants;
import com.smartcar.easylauncher.databinding.FragmentCardHomeBinding;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;
import com.smartcar.easylauncher.shared.view.pagergridlayoutmanager.PagerGridLayoutManager;

/**
 * 首页卡片布局管理器
 * 负责管理HomeCardScene的各种布局配置，提供灵活、高性能的布局切换功能
 * <p>
 * 功能特性：
 * - 支持3种不同的卡片布局模式
 * - 兼容Android API 17及以上版本
 * - 优化的约束布局配置
 * - 缓存机制提升性能
 * - 详细的异常处理和日志记录
 *
 * <AUTHOR> Team
 * @version 1.0
 */
public class HomeCardLayoutManager {

    private static final String TAG = "HomeCardLayoutManager";

    // 布局配置缓存，避免重复计算
    private final LayoutDimensionCache dimensionCache;

    // 布局辅助类
    private final HomeCardLayoutHelper layoutHelper;

    // UI组件引用
    private final FragmentCardHomeBinding binding;
    private final FragmentActivity activity;
    private final ConstraintSet constraintSet;

    /**
     * 构造函数
     *
     * @param binding       数据绑定对象
     * @param activity      Fragment所属的Activity
     * @param layoutManager RecyclerView的布局管理器
     * @param constraintSet 约束布局配置对象
     */
    public HomeCardLayoutManager(FragmentCardHomeBinding binding,
                                 FragmentActivity activity,
                                 PagerGridLayoutManager layoutManager,
                                 ConstraintSet constraintSet) {
        this.binding = binding;
        this.activity = activity;
        this.constraintSet = constraintSet;
        this.dimensionCache = new LayoutDimensionCache(activity);
        this.layoutHelper = new HomeCardLayoutHelper(binding, layoutManager, constraintSet, dimensionCache);
    }

    /**
     * 初始化布局配置
     * 根据布局类型应用相应的布局配置
     *
     * @param layoutType 布局类型，参考SettingsConstants中的布局常量
     */
    public void initLayout(String layoutType) {
        try {
            MyLog.d(TAG, "开始初始化卡片布局: " + layoutType);

            // 开始布局过渡动画（仅在Android 4.4及以上版本）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                TransitionManager.beginDelayedTransition(binding.mainLayout);
            }

            // 根据布局类型选择相应的配置策略
            switch (layoutType) {
                // 卡片布局1：左侧状态栏 + 右侧卡片列表 + 底部导航栏
                case SettingsConstants.DEFAULT_CARD_LAYOUT_1:
                    applyCardLayout1Configuration();
                    break;
                // 卡片布局2：顶部状态栏 + 中间卡片列表 + 底部导航栏
                case SettingsConstants.DEFAULT_CARD_LAYOUT_2:
                    applyCardLayout2Configuration();
                    break;
                // 卡片布局3：顶部状态栏 + 左侧导航栏 + 右侧卡片列表
                case SettingsConstants.DEFAULT_CARD_LAYOUT_3:
                    applyCardLayout3Configuration();
                    break;
                default:
                    // 默认使用卡片布局1
                    MyLog.w(TAG, "未知布局类型，使用默认布局: " + layoutType);
                    applyCardLayout1Configuration();
                    break;
            }

            // 应用约束配置
            constraintSet.applyTo(binding.mainLayout);
            MyLog.d(TAG, "卡片布局配置完成: " + layoutType);

        } catch (Exception e) {
            MyLog.e(TAG, "初始化布局配置失败: " + layoutType, e);
        }
    }

    /**
     * 卡片布局1配置：左侧状态栏 + 右侧卡片列表 + 底部导航栏
     * 适用场景：传统的左右分栏布局，状态栏位于左侧
     */
    private void applyCardLayout1Configuration() {
        try {
            // 配置状态栏（左侧垂直布局）
            configureStatusBarVertical();

            // 配置RecyclerView（右侧区域）
            configureRecyclerViewRight();

            // 配置底部导航栏
            configureNavigationBarBottom();

            MyLog.d(TAG, "应用卡片布局1配置完成");

        } catch (Exception e) {
            MyLog.e(TAG, "应用卡片布局1配置失败", e);
        }
    }

    /**
     * 卡片布局2配置：顶部状态栏 + 中间卡片列表 + 底部导航栏
     * 适用场景：状态栏横跨整个顶部的布局
     */
    private void applyCardLayout2Configuration() {
        try {
            // 配置状态栏（横跨整个顶部）
            configureStatusBarHorizontal();

            // 配置RecyclerView（中间区域）
            configureRecyclerViewCenter();

            // 配置底部导航栏
            configureNavigationBarBottom2();

            MyLog.d(TAG, "应用卡片布局2配置完成");

        } catch (Exception e) {
            MyLog.e(TAG, "应用卡片布局2配置失败", e);
        }
    }

    /**
     * 卡片布局3配置：顶部状态栏 + 左侧导航栏 + 右侧卡片列表
     * 适用场景：导航栏位于左侧的布局
     */
    private void applyCardLayout3Configuration() {
        try {
            // 配置状态栏（横跨整个顶部）
            configureStatusBarHorizontal();

            // 配置导航栏（左侧垂直布局）
            configureNavigationBarVertical();

            // 配置RecyclerView（右侧区域）
            configureRecyclerViewRightWithNavigation();

            MyLog.d(TAG, "应用卡片布局3配置完成");

        } catch (Exception e) {
            MyLog.e(TAG, "应用卡片布局3配置失败", e);
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 配置垂直状态栏（用于布局1）
     */
    private void configureStatusBarVertical() {
        try {
            constraintSet.clear(binding.mainLayoutStatusBar.getId());
            constraintSet.constrainWidth(binding.mainLayoutStatusBar.getId(),
                    DensityUtils.dp2px(activity, 40));
            constraintSet.constrainHeight(binding.mainLayoutStatusBar.getId(),
                    ConstraintSet.MATCH_CONSTRAINT);
            constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID, ConstraintSet.TOP);
            constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.START,
                    ConstraintSet.PARENT_ID, ConstraintSet.START);
            constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM);

            // 设置状态栏可见性
            binding.mainLayoutStatusBar.setVisibility(
                    SettingsManager.getStatusBarShow() ? View.VISIBLE : View.GONE);

        } catch (Exception e) {
            MyLog.e(TAG, "配置垂直状态栏失败", e);
        }
    }

    /**
     * 配置水平状态栏（用于布局2和3）
     */
    private void configureStatusBarHorizontal() {
        try {
            // 清除约束集对mainLayoutStatusBar的约束
            constraintSet.clear(binding.mainLayoutStatusBar.getId());
            // 设置mainLayoutStatusBar的宽度为匹配约束
            constraintSet.constrainWidth(binding.mainLayoutStatusBar.getId(),
                    ConstraintSet.MATCH_CONSTRAINT);
            // 设置mainLayoutStatusBar的高度为30dp
            constraintSet.constrainHeight(binding.mainLayoutStatusBar.getId(),
                    DensityUtils.dp2px(activity, 30));
            // 将mainLayoutStatusBar的顶部与父布局顶部对齐
            constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID, ConstraintSet.TOP);
            // 将mainLayoutStatusBar的右侧与父布局右侧对齐
            constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.END,
                    ConstraintSet.PARENT_ID, ConstraintSet.END);
            // 将mainLayoutStatusBar的左侧与父布局左侧对齐
            constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.START,
                    ConstraintSet.PARENT_ID, ConstraintSet.START);

            // 设置状态栏可见性
            // 根据设置管理器中的设置决定状态栏的可见性
            binding.mainLayoutStatusBar.setVisibility(
                    SettingsManager.getStatusBarShow() ? View.VISIBLE : View.GONE);

        } catch (Exception e) {
            // 捕获异常并记录错误日志
            MyLog.e(TAG, "配置水平状态栏失败", e);
        }
    }


    /**
     * 配置RecyclerView右侧布局（用于布局1）
     */
    private void configureRecyclerViewRight() {
        try {
            constraintSet.clear(binding.rvHome.getId());
            constraintSet.constrainWidth(binding.rvHome.getId(), ConstraintSet.MATCH_CONSTRAINT);
            constraintSet.constrainHeight(binding.rvHome.getId(), ConstraintSet.MATCH_CONSTRAINT);

            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID, ConstraintSet.TOP, DensityUtils.dp2px(activity, 3));
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.START,
                    binding.mainLayoutStatusBar.getId(), ConstraintSet.END);
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.END,
                    ConstraintSet.PARENT_ID, ConstraintSet.END, DensityUtils.dp2px(activity, 3));
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.BOTTOM,
                    binding.maiIconBar.getId(), ConstraintSet.TOP);

        } catch (Exception e) {
            MyLog.e(TAG, "配置RecyclerView右侧布局失败", e);
        }
    }

    /**
     * 配置RecyclerView中间布局（用于布局2）
     */
    private void configureRecyclerViewCenter() {
        try {
            constraintSet.clear(binding.rvHome.getId());
            constraintSet.constrainWidth(binding.rvHome.getId(), ConstraintSet.MATCH_CONSTRAINT);
            constraintSet.constrainHeight(binding.rvHome.getId(),
                    DensityUtils.dp2px(activity, 0));

            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.TOP,
                    binding.mainLayoutStatusBar.getId(), ConstraintSet.BOTTOM);
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.END,
                    ConstraintSet.PARENT_ID, ConstraintSet.END);
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.START,
                    ConstraintSet.PARENT_ID, ConstraintSet.START);
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.BOTTOM,
                    binding.maiIconBar.getId(), ConstraintSet.TOP);

        } catch (Exception e) {
            MyLog.e(TAG, "配置RecyclerView中间布局失败", e);
        }
    }

    /**
     * 配置RecyclerView右侧布局（用于布局3，带导航栏）
     */
    private void configureRecyclerViewRightWithNavigation() {
        try {
            constraintSet.clear(binding.rvHome.getId());
            constraintSet.constrainWidth(binding.rvHome.getId(), ConstraintSet.MATCH_CONSTRAINT);
            constraintSet.constrainHeight(binding.rvHome.getId(),
                    DensityUtils.dp2px(activity, 0));

            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.TOP,
                    binding.mainLayoutStatusBar.getId(), ConstraintSet.BOTTOM);
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.END,
                    ConstraintSet.PARENT_ID, ConstraintSet.END);
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.START,
                    binding.maiIconBar.getId(), ConstraintSet.END);
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM);

        } catch (Exception e) {
            MyLog.e(TAG, "配置RecyclerView右侧布局（带导航栏）失败", e);
        }
    }

    /**
     * 配置底部导航栏（用于布局1和2）
     */
    private void configureNavigationBarBottom() {
        try {
            // 清空约束条件
            constraintSet.clear(binding.maiIconBar.getId());
            // 设置导航栏高度为自适应内容
            constraintSet.constrainHeight(binding.maiIconBar.getId(), ConstraintSet.WRAP_CONTENT);
            // 设置导航栏宽度为匹配约束
            constraintSet.constrainWidth(binding.maiIconBar.getId(), ConstraintSet.MATCH_CONSTRAINT);


            // 将导航栏左边连接到主布局状态栏右边
            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.START,
                    binding.mainLayoutStatusBar.getId(), ConstraintSet.END);
            // 将导航栏右边连接到父布局的右边
            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.END,
                    ConstraintSet.PARENT_ID, ConstraintSet.END);
            // 将导航栏底部连接到父布局的底部，并设置间距为5dp
            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, DensityUtils.dp2px(activity, 5));

            // 设置导航栏可见性
            // 如果设置管理器中获取到导航栏显示状态为true，则显示导航栏，否则隐藏导航栏
            binding.maiIconBar.setVisibility(
                    SettingsManager.getNavigationBarShow() ? View.VISIBLE : View.GONE);


        } catch (Exception e) {
            // 记录错误日志
            MyLog.e(TAG, "配置底部导航栏失败", e);
        }
    }

    private void configureNavigationBarBottom2() {
        try {
            // 清空约束条件
            constraintSet.clear(binding.maiIconBar.getId());
            // 设置导航栏高度为自适应内容
            constraintSet.constrainHeight(binding.maiIconBar.getId(), ConstraintSet.WRAP_CONTENT);
            // 设置导航栏宽度为匹配约束
            constraintSet.constrainWidth(binding.maiIconBar.getId(), ConstraintSet.MATCH_CONSTRAINT);


            // 将导航栏左边连接到主布局状态栏右边
            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.START,
                    ConstraintSet.PARENT_ID, ConstraintSet.START);
            // 将导航栏右边连接到父布局的右边
            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.END,
                    ConstraintSet.PARENT_ID, ConstraintSet.END);
            // 将导航栏底部连接到父布局的底部，并设置间距为5dp
            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, DensityUtils.dp2px(activity, 5));

            // 设置导航栏可见性
            // 如果设置管理器中获取到导航栏显示状态为true，则显示导航栏，否则隐藏导航栏
            binding.maiIconBar.setVisibility(
                    SettingsManager.getNavigationBarShow() ? View.VISIBLE : View.GONE);


        } catch (Exception e) {
            // 记录错误日志
            MyLog.e(TAG, "配置底部导航栏失败", e);
        }
    }

    /**
     * 配置垂直导航栏（用于布局3）
     */
    private void configureNavigationBarVertical() {
        try {
            constraintSet.clear(binding.maiIconBar.getId());
            constraintSet.constrainWidth(binding.maiIconBar.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.constrainHeight(binding.maiIconBar.getId(), ConstraintSet.WRAP_CONTENT);

            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.TOP,
                    binding.mainLayoutStatusBar.getId(), ConstraintSet.BOTTOM,
                    DensityUtils.dp2px(activity, SettingsManager.getStatusBarShow() ? 0 : 5));
            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.START,
                    ConstraintSet.PARENT_ID, ConstraintSet.START);
            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, DensityUtils.dp2px(activity, 5));

            // 设置导航栏可见性
            binding.maiIconBar.setVisibility(
                    SettingsManager.getNavigationBarShow() ? View.VISIBLE : View.GONE);

        } catch (Exception e) {
            MyLog.e(TAG, "配置垂直导航栏失败", e);
        }
    }
}
