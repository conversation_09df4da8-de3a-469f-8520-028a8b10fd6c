package com.smartcar.easylauncher.shared.widget.layout.map;

import androidx.fragment.app.FragmentActivity;

import com.smartcar.easylauncher.core.constants.SettingsConstants;
import com.smartcar.easylauncher.databinding.FragmentMapHomeBinding;
import com.smartcar.easylauncher.shared.view.pagergridlayoutmanager.PagerGridLayoutManager;

import java.util.HashMap;
import java.util.Map;

/**
 * 首页地图布局工厂类
 * 负责创建和缓存不同的布局实现
 */
public class HomeMapLayoutFactory {

    // 布局缓存，避免重复创建相同的布局对象
    private final Map<String, IHomeMapLayout> layoutCache = new HashMap<>();
    
    // UI组件引用
    private final FragmentMapHomeBinding binding;
    private final FragmentActivity activity;
    private final PagerGridLayoutManager pagerLayoutManager;

    /**
     * 构造函数
     *
     * @param binding 数据绑定对象
     * @param activity Fragment所属的Activity
     * @param pagerLayoutManager RecyclerView的布局管理器
     */
    public HomeMapLayoutFactory(FragmentMapHomeBinding binding,
                               FragmentActivity activity,
                               PagerGridLayoutManager pagerLayoutManager) {
        this.binding = binding;
        this.activity = activity;
        this.pagerLayoutManager = pagerLayoutManager;
    }

    /**
     * 获取指定类型的布局实现
     * 如果布局已经创建过，则从缓存中获取，否则创建新的布局实现
     *
     * @param layoutType 布局类型，参考SettingsConstants中的布局常量
     * @return 对应类型的布局实现
     */
    public IHomeMapLayout getLayout(String layoutType) {
        // 如果缓存中已有该布局，直接返回
        if (layoutCache.containsKey(layoutType)) {
            return layoutCache.get(layoutType);
        }

        // 根据布局类型创建相应的布局实现
        IHomeMapLayout layout;
        switch (layoutType) {
            case SettingsConstants.DEFAULT_MAP_LAYOUT_1:
                layout = new HomeMapLayout1(binding, activity, pagerLayoutManager);
                break;
            case SettingsConstants.DEFAULT_MAP_LAYOUT_2:
                layout = new HomeMapLayout2(binding, activity, pagerLayoutManager);
                break;
            case SettingsConstants.DEFAULT_MAP_LAYOUT_3:
                layout = new HomeMapLayout3(binding, activity, pagerLayoutManager);
                break;
            case SettingsConstants.DEFAULT_MAP_LAYOUT_4:
                layout = new HomeMapLayout4(binding, activity, pagerLayoutManager);
                break;
            case SettingsConstants.DEFAULT_MAP_LAYOUT_5:
                layout = new HomeMapLayout5(binding, activity, pagerLayoutManager);
                break;
            // 可以继续添加其他布局类型的实现
            // case SettingsConstants.DEFAULT_MAP_LAYOUT_6:
            //     layout = new HomeMapLayout6(binding, activity, pagerLayoutManager);
            //     break;
            default:
                // 默认使用布局1
                layout = new HomeMapLayout1(binding, activity, pagerLayoutManager);
                break;
        }

        // 将新创建的布局添加到缓存中
        layoutCache.put(layoutType, layout);
        return layout;
    }

    /**
     * 清除布局缓存
     * 在需要释放内存或重新创建布局时调用
     */
    public void clearCache() {
        layoutCache.clear();
    }
} 