package com.smartcar.easylauncher.shared.widget.button;

import android.content.Context;
import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Looper;
import android.renderscript.Allocation;
import android.renderscript.Element;
import android.renderscript.RenderScript;
import android.renderscript.ScriptIntrinsicBlur;

import androidx.annotation.WorkerThread;

/**
 * 模糊效果处理类，用于对图片进行模糊处理。
 *
 * <AUTHOR>
 * @date 2016/10/21
 */
public class Blur {
    /**
     * 定义缩放比例常量
     */
    private static final float SCALE = 0.4F;

    /**
     * 模糊半径
     */
    private float radius;

    /**
     * 模糊处理线程
     */
    private Thread blurThread;
    /**
     * 应用上下文
     */
    private Context context;
    /**
     * 输入位图
     */
    private Bitmap inBitmap;
    /**
     * 回调接口，用于处理模糊完成后的位图
     */
    private Callback callback;

    /**
     * 构造函数，初始化模糊处理线程。
     */
    public Blur() {
        initThread();
    }

    /**
     * 初始化模糊处理线程，该线程负责执行模糊处理，并在完成后通过回调接口返回结果。
     */
    private void initThread() {
        blurThread = new Thread(new Runnable() {
            @Override
            public void run() {
                // 在子线程中执行模糊处理
                final Bitmap blurred = getBlurBitmap(context, inBitmap, radius);
                // 切换回主线程，调用回调接口
                Handler handler = new Handler(Looper.getMainLooper());
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        // 如果回调接口不为空，则调用onBlurred方法
                        if (callback != null) {
                            callback.onBlurred(blurred);
                        }
                    }
                });
            }
        });
    }

    /**
     * 设置模糊处理的参数，包括回调接口、应用上下文、输入位图和模糊半径。
     *
     * @param callback 回调接口，用于处理模糊完成后的位图
     * @param context 应用上下文
     * @param inBitmap 输入位图
     * @param radius 模糊半径
     */
    public void setParams(Callback callback, Context context, Bitmap inBitmap, float radius) {
        this.callback = callback;
        this.context = context;
        this.inBitmap = inBitmap;
        this.radius = radius;
    }

    /**
     * 执行模糊处理。注意，这里应该使用start()方法启动线程，而不是run()方法。
     * 但由于原代码设计如此，且直接调用run()方法在这里是为了简化示例，
     * 实际使用时应考虑使用更合适的线程管理方式（如ExecutorService）。
     */
    public void execute() {
        // 注意：这里直接调用run()方法并不符合线程启动的常规做法，
        // 但为了与原代码保持一致，并简化示例，这里保留原样。
        // 在实际项目中，应使用start()方法启动线程，或在合适的线程池中提交任务。
        blurThread.run();
    }

    /**
     * 在子线程中执行模糊处理，并返回模糊后的位图。
     * 该方法应在线程中调用，因为它执行了耗时的渲染脚本操作。
     *
     * @param context 应用上下文
     * @param inBitmap 输入位图
     * @param radius 模糊半径
     * @return 模糊后的位图
     */
    @WorkerThread
    private Bitmap getBlurBitmap(Context context, Bitmap inBitmap, float radius) {
        // 检查上下文和输入位图是否为空
        if (context == null || inBitmap == null) {
            throw new IllegalArgumentException("在执行execute()方法前，必须先调用setParams()方法设置参数！");
        }

        // 根据缩放比例计算模糊处理后的位图尺寸
        int width = Math.round(inBitmap.getWidth() * SCALE);
        int height = Math.round(inBitmap.getHeight() * SCALE);

        // 创建缩放后的输入位图
        Bitmap in = Bitmap.createScaledBitmap(inBitmap, width, height, false);
        // 创建输出位图，用于存储模糊处理后的结果
        Bitmap out = Bitmap.createBitmap(in);

        // 创建RenderScript上下文和执行模糊效果的脚本
        RenderScript rs = RenderScript.create(context);
        ScriptIntrinsicBlur blurScript = ScriptIntrinsicBlur.create(rs, Element.U8_4(rs));

        // 为输入和输出位图创建Allocation对象
        Allocation allocationIn = Allocation.createFromBitmap(rs, in);
        Allocation allocationOut = Allocation.createFromBitmap(rs, out);

        // 设置模糊半径，并执行模糊处理
        blurScript.setRadius(radius);
        blurScript.setInput(allocationIn);
        blurScript.forEach(allocationOut);
        // 将模糊处理后的结果复制回输出位图
        allocationOut.copyTo(out);

        // 销毁Allocation对象和脚本，释放资源
        allocationIn.destroy();
        allocationOut.destroy();
        blurScript.destroy();
        rs.destroy();

        // 返回模糊处理后的位图
        return out;
    }

    /**
     * 回调接口，用于处理模糊完成后的位图。
     */
    public interface Callback {
        /**
         * 当模糊处理完成后，调用此方法，并传入模糊后的位图。
         *
         * @param blurredBitmap 模糊后的位图
         */
        void onBlurred(Bitmap blurredBitmap);
    }
}
