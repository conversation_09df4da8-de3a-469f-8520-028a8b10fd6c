package com.smartcar.easylauncher.shared.adapter.personal;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.data.model.common.NoticeDataModel;


/**
 * <AUTHOR>
 */
public class NoticeListAdapter extends BaseQuickAdapter<NoticeDataModel.RowsDTO, QuickViewHolder> {


    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable NoticeDataModel.RowsDTO item) {
        assert item != null;
        helper.setText(R.id.tv_title, item.getNoticeTitle());
        helper.setText(R.id.tv_time, item.getCreateTime());


    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.notice_item, viewGroup);
    }
}
