package com.smartcar.easylauncher.shared.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.smartcar.easylauncher.core.manager.SettingsManager;

/**
 * Created by guoluxiang on 2016/6/28.
 * 修改时间：2016/6/28 14:51
 */

public class CustomDialog extends Dialog {

    private int layout;

    public CustomDialog(Context context, int theme, int layout) {
        super(context, theme);
        this.layout = layout;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //顺序不能颠倒,否则出现导航栏无法适配问题
        //hideNavigationBar();
        adjustFullScreen(getWindow());
        setContentView(layout);
    }

    public void hideNavigationBar() {
        int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_IMMERSIVE
                | View.SYSTEM_UI_FLAG_FULLSCREEN;
        this.getWindow().getDecorView().setSystemUiVisibility(uiOptions);
    }

    /**
     * 隐藏挖孔屏的位置  全屏显示弹窗
     * setSystemUiVisibility()方法会改变系统UI的可见性，包括导航栏和状态栏。当你调用这个方法时，
     * 如果之前隐藏的导航栏的可见性发生了改变，那么导航栏可能会短暂地显示出来，然后再次隐藏，这就造成了闪烁的效果。
     * 如果你想避免这种情况，你可以在调用setSystemUiVisibility()方法时，同时传入SYSTEM_UI_FLAG_HIDE_NAVIGATION标志，这样可以保持导航栏的隐藏状态
     *
     * @param window
     */
    public void adjustFullScreen(Window window) {
        if (window == null) {
            return;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
            window.setAttributes(lp);
            final View decorView = window.getDecorView();
            if (SettingsManager.getSystNavigationBarShow()) {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                );
            } else {
                decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION);
            }

        }
    }

}
