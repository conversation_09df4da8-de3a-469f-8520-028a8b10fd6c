package com.smartcar.easylauncher.shared.widget.layout.map;

import android.view.View;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.fragment.app.FragmentActivity;

import com.smartcar.easylauncher.core.constants.SettingsConstants;
import com.smartcar.easylauncher.databinding.FragmentMapHomeBinding;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.shared.view.pagergridlayoutmanager.PagerGridLayoutManager;

/**
 * 首页地图布局5实现
 * 布局5配置：顶部状态栏 + 中间内容区域 + 下方卡片列表 + 底部导航栏
 * 适用场景：全屏内容区域，底部为卡片和导航栏
 */
public class HomeMapLayout5 extends AbstractHomeMapLayout {

    /**
     * 构造函数
     *
     * @param binding 数据绑定对象
     * @param activity Fragment所属的Activity
     * @param pagerLayoutManager RecyclerView的布局管理器
     */
    public HomeMapLayout5(FragmentMapHomeBinding binding,
                        FragmentActivity activity,
                        PagerGridLayoutManager pagerLayoutManager) {
        super(binding, activity, pagerLayoutManager);
    }

    @Override
    public void applyLayout(ConstraintSet constraintSet) {
        // 获取状态栏和导航栏的显示状态
        boolean statusBarVisible = SettingsManager.getStatusBarShow();
        boolean navigationBarVisible = SettingsManager.getNavigationBarShow();

        // 配置状态栏（顶部位置）
        if (statusBarVisible) {
            configureStatusBar(constraintSet,
                    ConstraintSet.MATCH_CONSTRAINT,  // 宽度
                    dimensionCache.statusBarHeight30,  // 高度
                    dimensionCache.margin5,  // 顶部边距
                    0,  // 右边距
                    ConstraintSet.MATCH_CONSTRAINT  // 水平约束类型
            );
        } else {
            // 状态栏不显示时，隐藏并清除约束
            hideStatusBar(constraintSet);
        }

        // 配置RecyclerView（下方卡片列表）
        configureRecyclerViewBottom(constraintSet, navigationBarVisible);

        // 配置主内容区域（中间位置）
        configureFrameLayoutForVertical(constraintSet, statusBarVisible);

        // 配置底部导航栏
        if (navigationBarVisible) {
            configureNavigationBarBottom(constraintSet);
        } else {
            // 导航栏不显示时，隐藏并清除约束
            hideNavigationBar(constraintSet);
        }

        // 设置RecyclerView为水平布局
        setupHorizontalRecyclerView();
    }

    @Override
    public String getLayoutType() {
        return SettingsConstants.DEFAULT_MAP_LAYOUT_5;
    }
    
    /**
     * 配置下方RecyclerView
     */
    private void configureRecyclerViewBottom(ConstraintSet constraintSet, boolean navigationBarVisible) {
        constraintSet.clear(binding.rvHome.getId());
        constraintSet.constrainWidth(binding.rvHome.getId(), ConstraintSet.MATCH_CONSTRAINT);
        constraintSet.constrainHeight(binding.rvHome.getId(), dimensionCache.mapCardHeight);

        // 水平约束 - 横跨整个宽度
        constraintSet.connect(binding.rvHome.getId(), ConstraintSet.START,
                ConstraintSet.PARENT_ID, ConstraintSet.START, dimensionCache.margin5);
        constraintSet.connect(binding.rvHome.getId(), ConstraintSet.END,
                ConstraintSet.PARENT_ID, ConstraintSet.END, dimensionCache.margin5);
        
        // 底部约束 - 根据导航栏显示状态
        if (navigationBarVisible) {
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.BOTTOM,
                    binding.maiIconBar.getId(), ConstraintSet.TOP);
        } else {
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM);
        }
    }
    
    /**
     * 配置中间内容区域
     */
    private void configureFrameLayoutForVertical(ConstraintSet constraintSet, boolean statusBarVisible) {
        constraintSet.clear(binding.frameLayout.getId());
        constraintSet.constrainWidth(binding.frameLayout.getId(), ConstraintSet.MATCH_CONSTRAINT);
        constraintSet.constrainHeight(binding.frameLayout.getId(), ConstraintSet.MATCH_CONSTRAINT);

        // 水平约束 - 横跨整个宽度
        constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.START,
                ConstraintSet.PARENT_ID, ConstraintSet.START, dimensionCache.margin5);
        constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.END,
                ConstraintSet.PARENT_ID, ConstraintSet.END, dimensionCache.margin5);
        
        // 顶部约束 - 根据状态栏显示状态
        if (statusBarVisible) {
            constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.TOP,
                    binding.mainLayoutStatusBar.getId(), ConstraintSet.BOTTOM, dimensionCache.margin5);
        } else {
            constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID, ConstraintSet.TOP, dimensionCache.margin5);
        }
        
        // 底部约束 - 连接到RecyclerView
        constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.BOTTOM,
                binding.rvHome.getId(), ConstraintSet.TOP, dimensionCache.margin5);
    }
    
    /**
     * 配置底部导航栏
     */
    private void configureNavigationBarBottom(ConstraintSet constraintSet) {
        constraintSet.clear(binding.maiIconBar.getId());
        constraintSet.constrainWidth(binding.maiIconBar.getId(), ConstraintSet.MATCH_CONSTRAINT);
        constraintSet.constrainHeight(binding.maiIconBar.getId(), dimensionCache.getNavigationBarHeight());  // 动态获取

        // 水平约束 - 横跨整个宽度
        constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.START,
                ConstraintSet.PARENT_ID, ConstraintSet.START, dimensionCache.margin5);
        constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.END,
                ConstraintSet.PARENT_ID, ConstraintSet.END, dimensionCache.margin5);
        
        // 底部约束
        constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.BOTTOM,
                ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, dimensionCache.margin5);

        // 设置导航栏可见性
        binding.maiIconBar.setVisibility(View.VISIBLE);
    }
} 