# versionType字段更新说明

## 📋 更新概述

根据最新的API文档，新增了`versionType`查询字段，用于更精确的版本匹配控制。

## 🔄 API变更详情

### 新增参数
```json
{
  "versionType": {
    "type": "string",
    "required": false,
    "enum": ["release", "beta", "alpha"],
    "description": "版本类型，用于版本匹配规则控制"
  }
}
```

### 完整API调用示例
```
GET /system/appVersion/checkUpdate
参数:
- platform: android
- currentVersionCode: 15
- channelType: public
- versionType: alpha (新增)
```

## 🔧 代码实现

### 1. 新增获取版本类型方法
```java
/**
 * 获取版本类型
 */
private String getVersionType() {
    // 从BuildConfig获取版本类型，与config.gradle中的配置保持一致
    return BuildConfig.VERSION_TYPE;
}
```

### 2. 更新API调用
```java
RxHttp.get(Const.NEW_UPDATE_CHECK)
        .add("platform", "android")
        .add("currentVersionCode", BuildConfig.VERSION_CODE)
        .add("channelType", channelType)
        .add("versionType", versionType)  // 新增参数
        .toObservable(NewUpdateResponse.class)
```

### 3. 增强日志输出
```java
MyLog.v("UpdateChecker", "开始使用新API检查更新 - 渠道: " + channelType + 
        ", 版本类型: " + versionType + ", 当前版本号: " + BuildConfig.VERSION_CODE);
```

## 🎯 版本匹配逻辑

### 版本类型说明
- **alpha**: 内测版 - 功能可能不完整，仅供内部测试
- **beta**: 测试版 - 功能基本完成，用于公开测试
- **release**: 正式版 - 稳定的生产版本，面向最终用户

### 匹配规则
1. **精确匹配**: 优先返回相同版本类型的更新
2. **智能回退**: 如果指定版本类型无更新，可能回退到其他版本类型
3. **渠道优先**: 在版本类型匹配的基础上，再进行渠道匹配

## 🧪 测试场景

### 场景1: alpha版本检查
```bash
curl "http://192.168.31.96:1025/system/appVersion/checkUpdate?platform=android&currentVersionCode=15&channelType=public&versionType=alpha"
```

### 场景2: beta版本检查
```bash
curl "http://192.168.31.96:1025/system/appVersion/checkUpdate?platform=android&currentVersionCode=15&channelType=public&versionType=beta"
```

### 场景3: release版本检查
```bash
curl "http://192.168.31.96:1025/system/appVersion/checkUpdate?platform=android&currentVersionCode=15&channelType=public&versionType=release"
```

## 📊 配置映射

### config.gradle配置
```gradle
// 打包版本类型：alpha(内测版)、beta(公测版)、release(正式版)
versionType = 'alpha'
```

### BuildConfig映射
```java
buildConfigField "String", "VERSION_TYPE", "\"${versionType}\""
```

### API参数传递
```java
private String getVersionType() {
    return BuildConfig.VERSION_TYPE; // "alpha", "beta", 或 "release"
}
```

## 🔍 调试信息

### 日志输出示例
```
UpdateChecker: 开始使用新API检查更新 - 渠道: public, 版本类型: alpha, 当前版本号: 15
UpdateChecker: 新API更新检查响应: {"code":200,"data":{"versionType":"alpha",...}}
```

### 网络请求监控
可以通过网络抓包工具查看实际发送的请求参数：
```
GET /system/appVersion/checkUpdate?platform=android&currentVersionCode=15&channelType=public&versionType=alpha
```

## ⚠️ 注意事项

### 1. 向后兼容性
- `versionType`参数为可选参数
- 不传递此参数时，服务端应使用默认逻辑
- 确保旧版本客户端仍能正常工作

### 2. 参数验证
- 客户端应确保传递有效的版本类型值
- 服务端应验证参数的有效性
- 无效参数时应有合理的错误处理

### 3. 测试覆盖
- 测试所有版本类型组合
- 验证参数传递的正确性
- 确认匹配逻辑的准确性

## 🚀 部署建议

### 1. 分阶段部署
1. **第一阶段**: 服务端支持新参数，保持向后兼容
2. **第二阶段**: 客户端开始传递新参数
3. **第三阶段**: 验证新逻辑的正确性

### 2. 监控指标
- API调用成功率
- 版本匹配准确率
- 用户更新行为分析

### 3. 回滚方案
如果新参数导致问题，可以：
- 临时移除客户端的versionType参数传递
- 服务端忽略versionType参数
- 恢复到原有的匹配逻辑

## 📈 预期效果

### 1. 更精确的版本控制
- 内测用户只收到alpha版本更新
- 公测用户只收到beta版本更新
- 正式用户只收到release版本更新

### 2. 更好的用户体验
- 减少不必要的版本推送
- 提高更新的相关性
- 降低版本冲突的可能性

### 3. 更灵活的发布策略
- 支持多版本并行发布
- 便于A/B测试
- 简化版本管理流程

## 📞 技术支持

如在实施过程中遇到问题，请及时反馈：
- 参数传递异常
- 版本匹配错误
- 性能影响评估
