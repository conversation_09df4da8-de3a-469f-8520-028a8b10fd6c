/*
 * Copyright (C) 2019 ByteDance Inc
 *
 * 许可协议：根据Apache许可证2.0版（简称“许可证”）授权，
 * 除非符合许可证要求，否则您不得使用此文件。
 * 您可以在以下网址获得许可证的副本：
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * 除非适用法律要求或书面同意，否则根据许可证分发的软件
 * 将按“现状”分发，不提供任何明示或暗示的保证或条件。
 * 有关权限和许可证下限制的具体语言，请参阅许可证。
 */
package com.smartcar.easylauncher.shared.widget.navigation;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.angcyo.tablayout.DslTabLayout;
import com.bytedance.scene.Scene;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.databinding.SceneTablayoutViewBinding;
import com.smartcar.easylauncher.infrastructure.interfaces.SkinChangeListener;
import com.smartcar.easylauncher.data.model.theme.SkinModel;
import com.smartcar.easylauncher.infrastructure.skin.SkinManager;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * 底部导航视图场景基类，用于创建具有底部导航功能的场景。
 * 该类继承自GroupScene，允许管理多个子场景并通过底部导航视图进行切换。
 * <p>
 * Created by JiangQi on 8/24/18.
 */
public abstract class TabNavigationViewScene extends BaseScene implements SkinChangeListener {

    // 底部导航视图控件
    private SceneTablayoutViewBinding binding;

    /**
     * 创建并返回场景的视图。
     * 该方法用于加载底部导航视图场景的布局文件。
     *
     * @param inflater           布局填充器，用于填充布局文件
     * @param container          父视图组，如果场景不需要它作为自己的父视图，则可以为null
     * @param savedInstanceState 如果场景之前保存了状态，则在此处恢复它
     * @return 场景的视图
     */
    @NonNull
    @Override
    public final ViewGroup onCreateNewView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = SceneTablayoutViewBinding.inflate(getLayoutInflater());
        return binding.getRoot();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        registerSkinChangeListener();
    }

    @Override
    public void onSkinChange(SkinModel messageEvent) {
        MyLog.v("TabNavigationViewScene  ", "主题切换了");
        if (binding == null) return;
        // 更新底部导航视图的样式（例如，背景颜色、指示器等
        binding.rlParent.setBackgroundColor(SkinManager.getInstance().getColor(R.color.second_sheets_bg_color));
        binding.tabLayout.getTabIndicator().setIndicatorDrawable(SkinManager.getInstance().getDrawable(R.drawable.indicator_bottom_line));
        binding.tabLayout.invalidate();
        if (binding.tabLayout.getTabLayoutConfig() != null) {
            binding.tabLayout.getTabLayoutConfig().setTabSelectColor(SkinManager.getInstance().getColor(R.color.tab_select_color));
            binding.tabLayout.getTabLayoutConfig().setTabDeselectColor(SkinManager.getInstance().getColor(R.color.tab_deselect_color));
            binding.tabLayout.getDslSelector().updateStyle();
        }

    }

    /**
     * 当场景的视图被创建后调用。
     * 在这里初始化底部导航视图，并设置窗口插入监听器（如果Android版本支持）。
     *
     * @param view               场景的视图
     * @param savedInstanceState 如果场景之前保存了状态，则在此处恢复它
     */
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        // 初始化底部导航视图
        binding.rlParent.setBackgroundColor(SkinManager.getInstance().getColor(R.color.second_sheets_bg_color));
        binding.tabLayout.getTabIndicator().setIndicatorDrawable(SkinManager.getInstance().getDrawable(R.drawable.indicator_bottom_line));
        binding.tabLayout.invalidate();
        assert binding.tabLayout.getTabLayoutConfig() != null;
        binding.tabLayout.getTabLayoutConfig().setTabSelectColor(SkinManager.getInstance().getColor(R.color.tab_select_color));
        binding.tabLayout.getTabLayoutConfig().setTabDeselectColor(SkinManager.getInstance().getColor(R.color.tab_deselect_color));
        binding.tabLayout.getDslSelector().updateStyle();

        binding.tvTitle.setText(getTitleName());
        binding.btBack.setOnClickListener(v -> getNavigationScene(TabNavigationViewScene.this).pop());
    }

    /**
     * 当场景的活动（Activity）被创建后调用。
     * 在这里，通过调用getMenuResId()获取菜单资源ID，并将其充气到底部导航视图中。
     * 然后，使用GroupSceneUIUtility设置底部导航视图与场景之间的交互。
     *
     * @param savedInstanceState 如果场景之前保存了状态，则在此处恢复它
     */
    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        List<String> navigationTitles = getNavigationTitles();
        if (navigationTitles == null || navigationTitles.isEmpty()) {
            return; // 如果列表为空，则直接返回
        }

        // 仅在循环外部创建一次LayoutInflater实例
        LayoutInflater inflater = LayoutInflater.from(requireActivity());
        DslTabLayout tabLayout = binding.tabLayout;
        int tabItemLayoutId = getTabItemLayout();
        for (int i = 0; i < navigationTitles.size(); i++) {
            // 使用一个临时的ViewHolder来避免在循环中多次调用findViewById
            ViewHolder viewHolder = new ViewHolder(inflater.inflate(tabItemLayoutId, tabLayout, false));
            viewHolder.textView.setText(navigationTitles.get(i));
            // 将视图添加到TabLayout中
            tabLayout.addView(viewHolder.itemView);
        }
        // 设置底部导航视图与场景之间的交互
        TabGroupSceneUIUtility.setupWithTabNavigationView(tabLayout, this, R.id.fl_content, getSceneMap(), getDefaultItemIndex());
    }

    /**
     * 获取默认打开的 item 索引
     */
    protected int getDefaultItemIndex() {
        return 0;
    }

    /**
     * 获取 tab item 布局
     */
    @LayoutRes
    protected abstract int getTabItemLayout();

    /**
     * 获取tab 标题名称
     */
    protected abstract List<String> getNavigationTitles();

    /**
     * 获取页面标题
     */
    protected abstract String getTitleName();

    /**
     * 获取场景映射。
     * 该方法是一个抽象方法，需要由子类实现。
     * 子类应返回一个LinkedHashMap，映射底部导航菜单项ID到对应的Scene实例。
     *
     * @return 场景映射，键为菜单项ID，值为对应的Scene实例
     */
    @NonNull
    protected abstract LinkedHashMap<Integer, Scene> getSceneMap();

    /**
     * 当场景的视图被销毁时调用。
     * 在这里清理TabLayout中的Tab视图，防止重复添加。
     */
    @Override
    public void onDestroyView() {
        super.onDestroyView();

        // 清理TabLayout中的所有Tab视图，防止重复添加
        if (binding != null) {
            binding.tabLayout.removeAllViews();
        }
    }

    /**
     * 获取底部导航视图控件。
     * 该方法用于外部访问底部导航视图控件。
     *
     * @return 底部导航视图控件
     */
    public DslTabLayout getTabLayoutNavigationView() {
        return binding.tabLayout;
    }

    static class ViewHolder {
        TextView textView;
        View itemView;

        ViewHolder(View itemView) {
            this.itemView = itemView;
            this.textView = itemView.findViewById(R.id.text_view);
        }
    }
}

