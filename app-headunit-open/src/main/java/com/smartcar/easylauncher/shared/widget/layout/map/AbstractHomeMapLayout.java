package com.smartcar.easylauncher.shared.widget.layout.map;

import android.util.Log;
import android.view.View;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.fragment.app.FragmentActivity;

import com.smartcar.easylauncher.databinding.FragmentMapHomeBinding;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.shared.view.pagergridlayoutmanager.PagerGridLayoutManager;
import com.smartcar.easylauncher.shared.widget.layout.LayoutDimensionCache;

/**
 * 首页地图布局抽象基类
 * 提供所有布局实现类共享的基础功能
 */
public abstract class AbstractHomeMapLayout implements IHomeMapLayout {

    private static final String TAG = "AbstractHomeMapLayout";

    // 布局配置缓存
    protected final LayoutDimensionCache dimensionCache;
    
    // UI组件引用
    protected final FragmentMapHomeBinding binding;
    protected final FragmentActivity activity;
    protected final PagerGridLayoutManager pagerLayoutManager;
    
    /**
     * 构造函数
     *
     * @param binding 数据绑定对象
     * @param activity Fragment所属的Activity
     * @param pagerLayoutManager RecyclerView的布局管理器
     */
    public AbstractHomeMapLayout(FragmentMapHomeBinding binding,
                                FragmentActivity activity,
                                PagerGridLayoutManager pagerLayoutManager) {
        this.binding = binding;
        this.activity = activity;
        this.pagerLayoutManager = pagerLayoutManager;
        this.dimensionCache = new LayoutDimensionCache(activity);
    }
    
    /**
     * 隐藏状态栏并清除其约束
     * 
     * @param constraintSet 约束布局配置对象
     */
    protected void hideStatusBar(ConstraintSet constraintSet) {
        // 设置状态栏不可见
        binding.mainLayoutStatusBar.setVisibility(View.GONE);

        // 清除状态栏的所有约束
        constraintSet.clear(binding.mainLayoutStatusBar.getId());

        // 设置状态栏高度为0，确保不占用空间
        constraintSet.constrainHeight(binding.mainLayoutStatusBar.getId(), 0);
        constraintSet.constrainWidth(binding.mainLayoutStatusBar.getId(), 0);
    }

    /**
     * 隐藏导航栏并清除其约束
     * 
     * @param constraintSet 约束布局配置对象
     */
    protected void hideNavigationBar(ConstraintSet constraintSet) {
        // 设置导航栏不可见
        binding.maiIconBar.setVisibility(View.GONE);

        // 清除导航栏的所有约束
        constraintSet.clear(binding.maiIconBar.getId());

        // 设置导航栏高度为0，确保不占用空间
        constraintSet.constrainHeight(binding.maiIconBar.getId(), 0);
        constraintSet.constrainWidth(binding.maiIconBar.getId(), 0);
    }
    
    /**
     * 配置状态栏的通用方法
     *
     * @param constraintSet 约束布局配置对象
     * @param width 宽度约束
     * @param height 高度值
     * @param topMargin 顶部边距
     * @param endMargin 右边距
     * @param horizontalConstraint 水平约束类型
     */
    protected void configureStatusBar(ConstraintSet constraintSet, int width, int height, 
                                    int topMargin, int endMargin, int horizontalConstraint) {
        constraintSet.clear(binding.mainLayoutStatusBar.getId());
        constraintSet.constrainWidth(binding.mainLayoutStatusBar.getId(), width);
        constraintSet.constrainHeight(binding.mainLayoutStatusBar.getId(), height);
        constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.TOP,
                ConstraintSet.PARENT_ID, ConstraintSet.TOP, topMargin);

        if (horizontalConstraint == ConstraintSet.MATCH_CONSTRAINT) {
            // 横跨整个宽度
            constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.START,
                    ConstraintSet.PARENT_ID, ConstraintSet.START);
            constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.END,
                    ConstraintSet.PARENT_ID, ConstraintSet.END);
        } else {
            // 右对齐
            constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.END,
                    ConstraintSet.PARENT_ID, ConstraintSet.END, endMargin);
        }

        // 设置状态栏可见性
        binding.mainLayoutStatusBar.setVisibility(
                SettingsManager.getStatusBarShow() ? View.VISIBLE : View.GONE);
    }
    
    /**
     * 配置导航栏的通用方法
     *
     * @param constraintSet 约束布局配置对象
     * @param width 宽度约束
     * @param height 高度约束
     * @param startTarget 左侧约束目标
     * @param endTarget 右侧约束目标
     */
    protected void configureNavigationBar(ConstraintSet constraintSet, int width, int height, 
                                        int startTarget, int endTarget) {
        constraintSet.clear(binding.maiIconBar.getId());
        constraintSet.constrainWidth(binding.maiIconBar.getId(), width);
        constraintSet.constrainHeight(binding.maiIconBar.getId(), height);

        constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.START,
                startTarget, startTarget == ConstraintSet.PARENT_ID ? ConstraintSet.START : ConstraintSet.END,
                dimensionCache.margin5);
        constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.END,
                endTarget, ConstraintSet.END, dimensionCache.margin5);
        constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.BOTTOM,
                ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, dimensionCache.margin5);

        // 设置导航栏可见性
        binding.maiIconBar.setVisibility(
                SettingsManager.getNavigationBarShow() ? View.VISIBLE : View.GONE);
    }
    
    /**
     * 设置RecyclerView为垂直布局
     * 参考原有HomeMapLayoutHelper的实现
     */
    protected void setupVerticalRecyclerView() {
        if (pagerLayoutManager != null) {
            try {
                // 设置方向
                pagerLayoutManager.setOrientation(PagerGridLayoutManager.VERTICAL);
                // 设置行列数
                pagerLayoutManager.setRows(SettingsManager.getMapListCount());
                pagerLayoutManager.setColumns(1);
                
                Log.d(TAG, "RecyclerView已设置为垂直布局，行数: " + SettingsManager.getMapListCount() + ", 列数: 1");
            } catch (Exception e) {
                Log.e(TAG, "设置垂直布局失败: " + e.getMessage());
            }
        } else {
            Log.e(TAG, "无法设置垂直布局: pagerLayoutManager为空");
        }
    }
    
    /**
     * 设置RecyclerView为水平布局
     * 参考原有HomeMapLayoutHelper的实现
     */
    protected void setupHorizontalRecyclerView() {
        if (pagerLayoutManager != null) {
            try {
                // 设置方向
                pagerLayoutManager.setOrientation(PagerGridLayoutManager.HORIZONTAL);
                // 设置行列数 - 关键是要设置行为1，列为卡片数量
                pagerLayoutManager.setRows(1);
                pagerLayoutManager.setColumns(SettingsManager.getMapListCount());
                
                Log.d(TAG, "RecyclerView已成功设置为水平布局，行数: 1, 列数: " + SettingsManager.getMapListCount());
                
                // 确认设置成功
                if (pagerLayoutManager.getOrientation() != PagerGridLayoutManager.HORIZONTAL) {
                    Log.w(TAG, "设置水平布局失败: 方向未改变，再次尝试设置");
                    // 再次尝试设置
                    pagerLayoutManager.setOrientation(PagerGridLayoutManager.HORIZONTAL);
                    pagerLayoutManager.setRows(1);
                    pagerLayoutManager.setColumns(SettingsManager.getMapListCount());
                }
            } catch (Exception e) {
                Log.e(TAG, "设置水平布局失败: " + e.getMessage());
            }
        } else {
            Log.e(TAG, "无法设置水平布局: pagerLayoutManager为空");
        }
    }
} 