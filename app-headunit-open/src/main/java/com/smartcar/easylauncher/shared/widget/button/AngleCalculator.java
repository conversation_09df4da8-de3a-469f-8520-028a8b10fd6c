package com.smartcar.easylauncher.shared.widget.button;

/**
 * 角度计算器类，用于计算按钮在圆形布局中的角度和移动距离。
 *
 * <AUTHOR>
 * @date 2016/9/10
 */
public class AngleCalculator {

    /** 起始角度（弧度制） */
    private final double startAngleRadians;
    /** 平均角度（弧度制），用于计算按钮之间的角度间隔 */
    private double averageAngleRadians;
    /** 标识起始角度是否等于结束角度 */
    private final boolean angleStartEqualsEnd;

    /**
     * 构造函数，初始化角度计算器。
     *
     * @param startAngleDegree 起始角度（度数制）
     * @param endAngleDegree 结束角度（度数制）
     * @param expandButtonCount 需要展开的按钮数量
     */
    public AngleCalculator(float startAngleDegree, float endAngleDegree, int expandButtonCount) {
        // 判断起始角度是否等于结束角度
        angleStartEqualsEnd = (endAngleDegree - startAngleDegree) == 0;
        // 将起始角度和结束角度转换为0-360度之间
        startAngleDegree = startAngleDegree % 360;
        endAngleDegree = endAngleDegree % 360;
        // 将起始角度转换为弧度制
        this.startAngleRadians = Math.toRadians(startAngleDegree);
        double endAngleRadians = Math.toRadians(endAngleDegree);

        // 如果按钮数量大于1，则计算平均角度
        if (expandButtonCount > 1) {
            this.averageAngleRadians = (endAngleRadians - this.startAngleRadians) / (expandButtonCount - 1);
            // 调整平均角度，以避免起始角度和结束角度相等且为360度时，第一个按钮覆盖最后一个按钮的情况
            regulateAverageAngle(endAngleRadians, expandButtonCount);
        }
    }

    /**
     * 根据按钮索引和半径，计算按钮在X方向上的移动距离。
     *
     * @param radius 半径，包括主按钮半径、子按钮半径和按钮间隙的dp值转换后的px值
     * @param buttonIndex 按钮索引，从起始角度开始计数，值为1到expandButtonCount
     * @return 按钮在X方向上的移动距离（px）
     */
    public int getMoveX(int radius, int buttonIndex) {
        double angle = getCurrentAngle(buttonIndex); // 获取当前按钮的角度
        int moveX;
        // 如果平均角度为0，则按索引比例移动；否则，按固定角度移动
        if (averageAngleRadians == 0) {
            moveX = (int) (Math.cos(angle) * radius) * buttonIndex;
        } else {
            moveX = (int) (Math.cos(angle) * radius);
        }
        return moveX;
    }

    /**
     * 根据按钮索引和半径，计算按钮在Y方向上的移动距离。
     *
     * @param radius 半径，包括主按钮半径、子按钮半径和按钮间隙的dp值转换后的px值
     * @param buttonIndex 按钮索引，从起始角度开始计数，值为1到expandButtonCount
     * @return 按钮在Y方向上的移动距离（px）
     */
    public int getMoveY(int radius, int buttonIndex) {
        double angle = getCurrentAngle(buttonIndex); // 获取当前按钮的角度
        int moveY;
        // 如果平均角度为0，则按索引比例移动；否则，按固定角度移动
        if (averageAngleRadians == 0) {
            moveY = (int) (Math.sin(angle) * radius) * buttonIndex;
        } else {
            moveY = (int) (Math.sin(angle) * radius);
        }
        return moveY;
    }

    /**
     * 调整平均角度，以避免起始角度和结束角度相等且为360度时，第一个按钮覆盖最后一个按钮的情况。
     *
     * @param endAngleRadians 结束角度（弧度制）
     * @param expandButtonCount 需要展开的按钮数量
     */
    private void regulateAverageAngle(double endAngleRadians, int expandButtonCount) {
        // 如果起始角度不等于结束角度，但转换为弧度制后相等，则调整平均角度
        if (!angleStartEqualsEnd && startAngleRadians == endAngleRadians) {
            double tmp = 2 * Math.PI / expandButtonCount; // 计算每个按钮应占的角度
            // 根据平均角度的正负，设置调整后的平均角度
            if (averageAngleRadians < 0) {
                averageAngleRadians = -tmp;
            } else {
                averageAngleRadians = tmp;
            }
        }
    }

    /**
     * 根据按钮索引，获取当前按钮的角度。
     *
     * @param buttonIndex 按钮索引，从起始角度开始计数，值为1到expandButtonCount
     * @return 当前按钮的角度（弧度制）
     */
    private double getCurrentAngle(int buttonIndex) {
        // 根据起始角度、平均角度和按钮索引，计算当前按钮的角度
        return startAngleRadians + averageAngleRadians * (buttonIndex - 1);
    }
}
