package com.smartcar.easylauncher.shared.utils;

import android.content.Context;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.telephony.TelephonyManager;

import com.smartcar.easylauncher.data.network.manager.NetworkStateManager;
import com.smartcar.easylauncher.data.network.receiver.SystemStateReceiver;
import com.smartcar.easylauncher.data.network.state.NetworkState;
import com.smartcar.easylauncher.data.network.state.NetworkType;

/**
 * 移动数据状态辅助工具类
 * 提供注册广播接收器、获取移动数据状态等功能
 */
public class MobileDataStatusHelper {
    private static final String TAG = "MobileDataStatusHelper";
    private static SystemStateReceiver receiver;
    
    /**
     * 注册系统状态广播接收器
     * @param context 上下文
     */
    public static void registerReceiver(Context context) {
        if (receiver == null) {
            receiver = new SystemStateReceiver();
            IntentFilter filter = SystemStateReceiver.createIntentFilter();
            context.getApplicationContext().registerReceiver(receiver, filter);
            MyLog.i(TAG, "系统状态广播接收器注册成功");
        } else {
            MyLog.w(TAG, "系统状态广播接收器已注册，无需重复注册");
        }
    }
    
    /**
     * 取消注册系统状态广播接收器
     * @param context 上下文
     */
    public static void unregisterReceiver(Context context) {
        if (receiver != null) {
            try {
                context.getApplicationContext().unregisterReceiver(receiver);
                receiver = null;
                MyLog.i(TAG, "系统状态广播接收器已取消注册");
            } catch (Exception e) {
                MyLog.e(TAG, "取消注册系统状态广播接收器失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 获取当前移动数据网络状态
     * @param context 上下文
     * @return 网络状态信息
     */
    public static NetworkState getMobileNetworkState(Context context) {
        return NetworkStateManager.getInstance(context).getCurrentState();
    }
    
    /**
     * 手动刷新网络状态
     * @param context 上下文
     */
    public static void refreshNetworkState(Context context) {
        MyLog.d(TAG, "手动刷新网络状态");
        NetworkStateManager.getInstance(context).updateNetworkState();
    }
    
    /**
     * 检查移动数据是否连接
     * @param context 上下文
     * @return 是否连接
     */
    public static boolean isMobileDataConnected(Context context) {
        NetworkState state = NetworkStateManager.getInstance(context).getCurrentState();
        return state.isConnected() && state.getType() == NetworkType.MOBILE;
    }
    
    /**
     * 强制更新移动数据状态
     * @param context 上下文
     */
    public static void forceUpdateMobileState(Context context) {
        MyLog.d(TAG, "强制更新移动数据状态");
        NetworkStateManager.getInstance(context).updateMobileState(true);
    }
    
    /**
     * 强制更新WiFi状态
     * @param context 上下文
     */
    public static void forceUpdateWifiState(Context context) {
        MyLog.d(TAG, "强制更新WiFi状态");
        NetworkStateManager.getInstance(context).updateWifiState(true);
    }
    
    /**
     * 获取信号强度等级 (0-4)
     * @param context 上下文
     * @return 信号强度等级
     */
    public static int getSignalLevel(Context context) {
        NetworkState state = NetworkStateManager.getInstance(context).getCurrentState();
        return state.getSignalStrength().getLevel();
    }
    
    /**
     * 获取信号强度描述
     * @param context 上下文
     * @return 信号强度描述 (无信号/极弱/较弱/良好/强)
     */
    public static String getSignalDescription(Context context) {
        NetworkState state = NetworkStateManager.getInstance(context).getCurrentState();
        return state.getSignalStrength().getLevelDescription();
    }
    
    /**
     * 检查是否为5G网络
     * @param context 上下文
     * @return 是否为5G网络
     */
    public static boolean is5GNetwork(Context context) {
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        if (telephonyManager != null) {
            int networkType = telephonyManager.getNetworkType();
            return networkType == 20 || networkType == 41 || networkType == 60; // 支持多种5G网络类型码
        }
        return false;
    }
    
    /**
     * 获取当前网络类型名称
     * @param context 上下文
     * @return 网络类型名称 (WIFI/5G/4G/3G/2G/UNKNOWN)
     */
    public static String getNetworkTypeName(Context context) {
        NetworkState state = NetworkStateManager.getInstance(context).getCurrentState();
        if (state.getType() == NetworkType.WIFI) {
            return "WIFI";
        } else if (state.getType() == NetworkType.MOBILE) {
            TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            if (telephonyManager != null) {
                int networkType = telephonyManager.getNetworkType();
                
                // 检查5G网络
                if (networkType == 20 || networkType == 41 || networkType == 60) {
                    return "5G";
                }
                
                // 检查4G网络
                if (networkType == TelephonyManager.NETWORK_TYPE_LTE) {
                    return "4G";
                }
                
                // 检查3G网络
                if (networkType == TelephonyManager.NETWORK_TYPE_UMTS ||
                    networkType == TelephonyManager.NETWORK_TYPE_HSDPA ||
                    networkType == TelephonyManager.NETWORK_TYPE_HSPA ||
                    networkType == TelephonyManager.NETWORK_TYPE_HSPAP ||
                    networkType == TelephonyManager.NETWORK_TYPE_EVDO_0 ||
                    networkType == TelephonyManager.NETWORK_TYPE_EVDO_A ||
                    networkType == TelephonyManager.NETWORK_TYPE_EVDO_B) {
                    return "3G";
                }
                
                // 检查2G网络
                if (networkType == TelephonyManager.NETWORK_TYPE_GPRS ||
                    networkType == TelephonyManager.NETWORK_TYPE_EDGE ||
                    networkType == TelephonyManager.NETWORK_TYPE_CDMA ||
                    networkType == TelephonyManager.NETWORK_TYPE_1xRTT ||
                    networkType == TelephonyManager.NETWORK_TYPE_IDEN) {
                    return "2G";
                }
                
                return "未知移动网络";
            }
        }
        return "无网络";
    }
    
    /**
     * 生成网络状态诊断信息
     * @param context 上下文
     * @return 诊断信息字符串
     */
    public static String getNetworkDiagnosticInfo(Context context) {
        NetworkState state = NetworkStateManager.getInstance(context).getCurrentState();
        StringBuilder info = new StringBuilder();
        
        info.append("网络连接状态: ").append(state.isConnected() ? "已连接" : "未连接").append("\n");
        info.append("网络类型: ").append(state.getType().name()).append("\n");
        info.append("信号强度: ").append(state.getSignalStrength().getLevel())
            .append("/4 (").append(state.getSignalStrength().getLevelDescription()).append(")\n");
        
        // 获取更多网络信息
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
        
        if (activeNetwork != null) {
            info.append("详细网络信息:\n");
            info.append("  类型名称: ").append(activeNetwork.getTypeName()).append("\n");
            info.append("  子类型名称: ").append(activeNetwork.getSubtypeName()).append("\n");
            info.append("  状态: ").append(activeNetwork.getState()).append("\n");
            info.append("  详细状态: ").append(activeNetwork.getDetailedState()).append("\n");
            info.append("  额外信息: ").append(activeNetwork.getExtraInfo()).append("\n");
        }
        
        return info.toString();
    }
} 