package com.smartcar.easylauncher.shared.adapter.weather;


import android.content.Context;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.AbsoluteSizeSpan;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.data.model.weather.WeatherDataModel;
import com.smartcar.easylauncher.infrastructure.skin.SkinManager;
import com.smartcar.easylauncher.shared.utils.time.TimeTools;
import com.smartcar.easylauncher.shared.utils.icon.WeatherUtils;


/**
 * 天气适配器
 * <AUTHOR>
 */
public class WeatherForecastAdapter extends BaseQuickAdapter<WeatherDataModel.WeatherInfo, QuickViewHolder> {
    public static final String TAG = WeatherForecastAdapter.class.getSimpleName();

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder quickViewHolder, int i, @Nullable WeatherDataModel.WeatherInfo localDayWeatherForecast) {
        assert localDayWeatherForecast != null;
        int icon = WeatherUtils.INSTANCE.getWeatherEnum(localDayWeatherForecast.getWeatherDescription()).getIcon();
        ImageView view = quickViewHolder.getView(R.id.weather);
        view.setImageDrawable(SkinManager.getInstance().getDrawable(icon));
        String temp = localDayWeatherForecast.getMaxTemperature() + "/" + localDayWeatherForecast.getMinTemperature() + "°";
        if (temp.indexOf("/") > 0) {
            SpannableString spannableString = new SpannableString(temp);
            spannableString.setSpan(
                    new AbsoluteSizeSpan(14, true),
                    temp.indexOf("/"),
                    temp.length(),
                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
            );
            quickViewHolder.setText(R.id.temp, spannableString);
        } else {
            quickViewHolder.setText(R.id.temp, temp);
        }
        quickViewHolder.setText(R.id.day, TimeTools.getWeek(localDayWeatherForecast.getDateTime()));
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.item_weather_day, viewGroup);
    }
}
