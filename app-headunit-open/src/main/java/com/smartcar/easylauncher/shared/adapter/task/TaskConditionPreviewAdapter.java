package com.smartcar.easylauncher.shared.adapter.task;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.TaskConditionConst;
import com.smartcar.easylauncher.data.model.task.ConditionModel;

/**
 * 任务条件预览适配器
 * 用于在推荐任务预览页面展示触发条件
 * 
 * <AUTHOR>
 */
public class TaskConditionPreviewAdapter extends BaseQuickAdapter<ConditionModel, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int position, @Nullable ConditionModel item) {
        if (item == null) return;
        
        // 设置条件图标
        setConditionIcon(helper, item.getId());
        
        // 设置条件名称
        helper.setText(R.id.tv_condition_name, getConditionDisplayName(item));
        
        // 设置条件描述
        helper.setText(R.id.tv_condition_description, getConditionDescription(item));
    }
    
    /**
     * 设置条件图标
     */
    private void setConditionIcon(QuickViewHolder helper, int conditionId) {
        int iconRes = R.drawable.ic_task_qidong; // 默认图标

        switch (conditionId) {
            case TaskConditionConst.TYPE_WIFI_SWITCH:
            case TaskConditionConst.TYPE_NETWORK_STATE:
                iconRes = R.drawable.ic_task_wifi;
                break;
            case TaskConditionConst.TYPE_BLUETOOTH_SWITCH:
            case TaskConditionConst.TYPE_BLUETOOTH_DEVICE:
                iconRes = R.drawable.ic_task_ble;
                break;
            case TaskConditionConst.TYPE_DEVICE_START:
                iconRes = R.drawable.ic_task_kaiji;
                break;
            case TaskConditionConst.TYPE_START:
                iconRes = R.drawable.ic_task_qidong;
                break;
            case TaskConditionConst.TYPE_MUSIC_STATE:
                iconRes = R.drawable.ic_task_yinyue;
                break;
            case TaskConditionConst.TYPE_SUN:
                iconRes = R.drawable.ic_task_richu;
                break;
            case TaskConditionConst.TYPE_SPEED:
                iconRes = R.drawable.ic_task_chesu;
                break;
            case TaskConditionConst.TYPE_GEO_FENCE:
                iconRes = R.drawable.ic_task_diliweilan;
                break;
            case TaskConditionConst.TYPE_TIME_RANGE:
            case TaskConditionConst.TYPE_TIMING:
            case TaskConditionConst.TYPE_PERIOD:
                iconRes = R.drawable.ic_task_shijianfanwei;
                break;
            case TaskConditionConst.TYPE_SCREEN:
                iconRes = R.drawable.ic_task_screen;
                break;
        }

        helper.setImageResource(R.id.iv_condition_icon, iconRes);
    }
    
    /**
     * 获取条件显示名称
     */
    private String getConditionDisplayName(ConditionModel condition) {
        if (condition.getName() != null && !condition.getName().isEmpty()) {
            return condition.getName();
        }
        
        switch (condition.getId()) {
            case TaskConditionConst.TYPE_WIFI_SWITCH:
                return "WiFi状态";
            case TaskConditionConst.TYPE_NETWORK_STATE:
                return "网络状态";
            case TaskConditionConst.TYPE_BLUETOOTH_SWITCH:
                return "蓝牙开关";
            case TaskConditionConst.TYPE_BLUETOOTH_DEVICE:
                return "蓝牙设备";
            case TaskConditionConst.TYPE_DEVICE_START:
                return "设备开机";
            case TaskConditionConst.TYPE_START:
                return "智车启动";
            case TaskConditionConst.TYPE_MUSIC_STATE:
                return "音乐状态";
            case TaskConditionConst.TYPE_SUN:
                return "日出日落";
            case TaskConditionConst.TYPE_SPEED:
                return "车速";
            case TaskConditionConst.TYPE_GEO_FENCE:
                return "地理围栏";
            case TaskConditionConst.TYPE_TIME_RANGE:
                return "时间范围";
            case TaskConditionConst.TYPE_TIMING:
                return "定时";
            case TaskConditionConst.TYPE_PERIOD:
                return "周期";
            case TaskConditionConst.TYPE_SCREEN:
                return "屏幕";
            default:
                return "未知条件";
        }
    }
    
    /**
     * 获取条件描述
     */
    private String getConditionDescription(ConditionModel condition) {
        switch (condition.getId()) {
            case TaskConditionConst.TYPE_WIFI_SWITCH:
                return condition.isSwitchState() ? "WiFi开启时触发" : "WiFi关闭时触发";
            case TaskConditionConst.TYPE_BLUETOOTH_SWITCH:
                return condition.isSwitchState() ? "蓝牙开启时触发" : "蓝牙关闭时触发";
            case TaskConditionConst.TYPE_BLUETOOTH_DEVICE:
                String deviceName = condition.getBluetoothName();
                return "连接到" + (deviceName != null ? deviceName : "指定设备") + "时触发";
            case TaskConditionConst.TYPE_DEVICE_START:
                return "设备开机时自动触发";
            case TaskConditionConst.TYPE_START:
                return "智车启动时自动触发";
            case TaskConditionConst.TYPE_MUSIC_STATE:
                switch (condition.getMusicState()) {
                    case 0: return "音乐暂停时触发";
                    case 1: return "音乐播放时触发";
                    default: return "音乐状态变化时触发";
                }
            case TaskConditionConst.TYPE_SUN:
                return condition.getSunriseSunset() == 0 ? "日落时触发" : "日出时触发";
            case TaskConditionConst.TYPE_SPEED:
                int speed = condition.getSpeed();
                int speedType = condition.getSpeedType();
                if (speedType == 0) {
                    return "车速等于" + speed + "km/h时触发";
                } else if (speedType == 1) {
                    return "车速大于" + speed + "km/h时触发";
                } else {
                    return "车速小于" + speed + "km/h时触发";
                }
            case TaskConditionConst.TYPE_TIME_RANGE:
                String startTime = condition.getStartTime();
                String endTime = condition.getEndTime();
                return "在" + (startTime != null ? startTime : "开始时间") + 
                       "到" + (endTime != null ? endTime : "结束时间") + "期间触发";
            case TaskConditionConst.TYPE_TIMING:
                String fixedTime = condition.getFixedTime();
                return "在" + (fixedTime != null ? fixedTime : "指定时间") + "触发";
            case TaskConditionConst.TYPE_PERIOD:
                return getCycleDescription(condition.getCycleType());
            default:
                return "满足指定条件时触发";
        }
    }
    
    /**
     * 获取周期描述
     */
    private String getCycleDescription(int cycleType) {
        switch (cycleType) {
            case 0: return "每天触发";
            case 1: return "工作日触发";
            case 2: return "周末触发";
            case 3: return "每周一触发";
            case 4: return "每周二触发";
            case 5: return "每周三触发";
            case 6: return "每周四触发";
            case 7: return "每周五触发";
            case 8: return "每周六触发";
            case 9: return "每周日触发";
            default: return "按周期触发";
        }
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int viewType) {
        return new QuickViewHolder(R.layout.item_task_condition_preview, viewGroup);
    }
}
