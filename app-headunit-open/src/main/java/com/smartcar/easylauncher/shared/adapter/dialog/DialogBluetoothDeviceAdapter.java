package com.smartcar.easylauncher.shared.adapter.dialog;

import android.annotation.SuppressLint;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.smartcar.easylauncher.data.model.system.BluetoothDeviceModel;
import com.timmy.tdialog.TDialog;
import com.timmy.tdialog.base.BindViewHolder;
import com.timmy.tdialog.base.TBaseAdapter;

import java.util.List;

/**
 * 蓝牙设备列表适配器
 * <AUTHOR>
 */
public class DialogBluetoothDeviceAdapter extends TBaseAdapter<BluetoothDeviceModel> {

    private final int layoutRes;
    private final List<BluetoothDeviceModel> datas;

    private TDialog dialog;
    private int choosePosition = 0;
    private OnAdapterItemClickListener adapterItemClickListener;


    public DialogBluetoothDeviceAdapter(int layoutRes, List<BluetoothDeviceModel> datas) {
        super(layoutRes, datas);
        this.layoutRes = layoutRes;
        this.datas = datas;
    }

    @NonNull
    @Override
    public BindViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new BindViewHolder(LayoutInflater.from(parent.getContext()).inflate(layoutRes, parent, false));
    }


    @Override
    protected void onBind(BindViewHolder holder, int position, BluetoothDeviceModel appInfo) {

    }

    @Override
    public void onBindViewHolder(final BindViewHolder holder, @SuppressLint("RecyclerView") final int position) {
        onBind(holder, position, datas.get(position));
        BluetoothDeviceModel info = datas.get(position);
        holder.itemView.setOnClickListener(v -> {

            datas.get(position).setSelect(!info.isSelect());
            if (choosePosition != position) {
                datas.get(choosePosition).setSelect(false);
            }
//                if (info.isSelect()) {
//                    holder.grid_item_app_delete.setVisibility(View.VISIBLE);
//                } else {
//                    holder.grid_item_app_delete.setVisibility(View.GONE);
//                }
            choosePosition = position;
            notifyDataSetChanged();
            adapterItemClickListener.onItemClick(holder, position, datas.get(position), dialog);
        });
    }


    @Override
    public int getItemCount() {
        return datas.size();
    }

    @Override
    public void setTDialog(TDialog tDialog) {
        this.dialog = tDialog;
    }

    @Override
    public void setOnAdapterItemClickListener(OnAdapterItemClickListener listener) {
        this.adapterItemClickListener = listener;
        super.setOnAdapterItemClickListener(listener);
    }
}
