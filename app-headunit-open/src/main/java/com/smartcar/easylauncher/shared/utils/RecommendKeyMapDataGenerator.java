package com.smartcar.easylauncher.shared.utils;

import android.view.KeyEvent;

import com.smartcar.easylauncher.core.constants.KeyMapConstants;
import com.smartcar.easylauncher.data.model.system.RecommendKeyMapModel;

import java.util.ArrayList;
import java.util.List;

/**
 * 推荐按键映射数据生成器
 * 生成智能按键发现页面的推荐按键映射数据
 * 
 * <AUTHOR>
 * @date 2024/12/18
 */
public class RecommendKeyMapDataGenerator {
    
    private static final String TAG = "RecommendKeyMapDataGenerator";
    
    /**
     * 生成推荐按键映射列表
     */
    public static List<RecommendKeyMapModel> generateRecommendKeyMaps() {
        MyLog.d(TAG, "generateRecommendKeyMaps: 开始生成推荐按键映射");
        List<RecommendKeyMapModel> keyMaps = new ArrayList<>();

        // 媒体控制类按键映射
        keyMaps.add(createMediaPlayPauseKeyMap());
        keyMaps.add(createMediaNextKeyMap());
        keyMaps.add(createMediaPreviousKeyMap());
        
        // 应用启动类按键映射
        keyMaps.add(createLaunchMusicAppKeyMap());
        keyMaps.add(createLaunchNavigationKeyMap());
        keyMaps.add(createLaunchPhoneKeyMap());
        
        // 系统操作类按键映射
        keyMaps.add(createHomeKeyMap());
        keyMaps.add(createBackKeyMap());
        
        // 自定义功能按键映射
        keyMaps.add(createVoiceAssistantKeyMap());
        keyMaps.add(createScreenshotKeyMap());

        MyLog.d(TAG, "generateRecommendKeyMaps: 生成完成，按键映射数量=" + keyMaps.size());
        return keyMaps;
    }
    
    /**
     * 创建媒体播放/暂停按键映射
     */
    private static RecommendKeyMapModel createMediaPlayPauseKeyMap() {
        RecommendKeyMapModel keyMap = new RecommendKeyMapModel(
            "key_media_play_pause",
            "音乐播放/暂停",
            "按下方向盘按键控制音乐播放和暂停，让您专注驾驶的同时轻松控制音乐",
            "媒体控制"
        );
        
        keyMap.setKeyCode(KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE);
        keyMap.setKeyName("播放/暂停键");
        keyMap.setActionType(KeyMapConstants.ActionType.MEDIA_CONTROL);
        keyMap.setActionData(KeyMapConstants.MediaControlType.PLAY_PAUSE);
        keyMap.setRating(5);
        keyMap.setUsageCount(8520);
        keyMap.setHot(true);
        keyMap.addTag("热门");
        keyMap.addTag("媒体");
        keyMap.addTag("音乐");
        
        return keyMap;
    }
    
    /**
     * 创建下一曲按键映射
     */
    private static RecommendKeyMapModel createMediaNextKeyMap() {
        RecommendKeyMapModel keyMap = new RecommendKeyMapModel(
            "key_media_next",
            "下一曲",
            "快速切换到下一首歌曲，无需离开方向盘操作屏幕",
            "媒体控制"
        );
        
        keyMap.setKeyCode(KeyEvent.KEYCODE_MEDIA_NEXT);
        keyMap.setKeyName("下一曲键");
        keyMap.setActionType(KeyMapConstants.ActionType.MEDIA_CONTROL);
        keyMap.setActionData(KeyMapConstants.MediaControlType.NEXT);
        keyMap.setRating(4);
        keyMap.setUsageCount(6340);
        keyMap.addTag("媒体");
        keyMap.addTag("音乐");
        keyMap.addTag("切换");
        
        return keyMap;
    }
    
    /**
     * 创建上一曲按键映射
     */
    private static RecommendKeyMapModel createMediaPreviousKeyMap() {
        RecommendKeyMapModel keyMap = new RecommendKeyMapModel(
            "key_media_previous",
            "上一曲",
            "快速返回上一首歌曲，方便重复播放喜欢的音乐",
            "媒体控制"
        );
        
        keyMap.setKeyCode(KeyEvent.KEYCODE_MEDIA_PREVIOUS);
        keyMap.setKeyName("上一曲键");
        keyMap.setActionType(KeyMapConstants.ActionType.MEDIA_CONTROL);
        keyMap.setActionData(KeyMapConstants.MediaControlType.PREVIOUS);
        keyMap.setRating(4);
        keyMap.setUsageCount(5280);
        keyMap.addTag("媒体");
        keyMap.addTag("音乐");
        keyMap.addTag("切换");
        
        return keyMap;
    }
    
    /**
     * 创建启动音乐应用按键映射
     */
    private static RecommendKeyMapModel createLaunchMusicAppKeyMap() {
        RecommendKeyMapModel keyMap = new RecommendKeyMapModel(
            "key_launch_music",
            "启动音乐应用",
            "一键启动您最喜欢的音乐应用，快速进入音乐世界",
            "应用启动"
        );
        
        keyMap.setKeyCode(KeyEvent.KEYCODE_F1);
        keyMap.setKeyName("F1键");
        keyMap.setActionType(KeyMapConstants.ActionType.LAUNCH_APP);
        keyMap.setActionData("com.netease.cloudmusic"); // 网易云音乐包名示例
        keyMap.setRating(4);
        keyMap.setUsageCount(4560);
        keyMap.setNew(true);
        keyMap.addTag("新品");
        keyMap.addTag("应用");
        keyMap.addTag("音乐");
        
        return keyMap;
    }
    
    /**
     * 创建启动导航应用按键映射
     */
    private static RecommendKeyMapModel createLaunchNavigationKeyMap() {
        RecommendKeyMapModel keyMap = new RecommendKeyMapModel(
            "key_launch_navigation",
            "启动导航应用",
            "快速启动导航应用，为您的出行提供便利",
            "应用启动"
        );
        
        keyMap.setKeyCode(KeyEvent.KEYCODE_F2);
        keyMap.setKeyName("F2键");
        keyMap.setActionType(KeyMapConstants.ActionType.LAUNCH_APP);
        keyMap.setActionData("com.autonavi.minimap"); // 高德地图包名示例
        keyMap.setRating(5);
        keyMap.setUsageCount(7230);
        keyMap.setHot(true);
        keyMap.addTag("热门");
        keyMap.addTag("应用");
        keyMap.addTag("导航");
        keyMap.addTag("出行");
        
        return keyMap;
    }
    
    /**
     * 创建启动电话应用按键映射
     */
    private static RecommendKeyMapModel createLaunchPhoneKeyMap() {
        RecommendKeyMapModel keyMap = new RecommendKeyMapModel(
            "key_launch_phone",
            "启动电话应用",
            "一键启动电话应用，方便接打电话和查看通话记录",
            "应用启动"
        );
        
        keyMap.setKeyCode(KeyEvent.KEYCODE_F3);
        keyMap.setKeyName("F3键");
        keyMap.setActionType(KeyMapConstants.ActionType.LAUNCH_APP);
        keyMap.setActionData("com.android.dialer"); // 电话应用包名
        keyMap.setRating(4);
        keyMap.setUsageCount(3890);
        keyMap.addTag("应用");
        keyMap.addTag("电话");
        keyMap.addTag("通讯");
        
        return keyMap;
    }
    
    /**
     * 创建返回主页按键映射
     */
    private static RecommendKeyMapModel createHomeKeyMap() {
        RecommendKeyMapModel keyMap = new RecommendKeyMapModel(
            "key_system_home",
            "返回主页",
            "快速返回车机主页，方便切换到其他功能",
            "系统操作"
        );
        
        keyMap.setKeyCode(KeyEvent.KEYCODE_HOME);
        keyMap.setKeyName("主页键");
        keyMap.setActionType(KeyMapConstants.ActionType.SYSTEM_ACTION);
        keyMap.setActionData(KeyMapConstants.SystemActionType.HOME);
        keyMap.setRating(5);
        keyMap.setUsageCount(9120);
        keyMap.setHot(true);
        keyMap.addTag("热门");
        keyMap.addTag("系统");
        keyMap.addTag("主页");
        
        return keyMap;
    }
    
    /**
     * 创建返回按键映射
     */
    private static RecommendKeyMapModel createBackKeyMap() {
        RecommendKeyMapModel keyMap = new RecommendKeyMapModel(
            "key_system_back",
            "返回上级",
            "快速返回上一个页面，提升操作效率",
            "系统操作"
        );
        
        keyMap.setKeyCode(KeyEvent.KEYCODE_BACK);
        keyMap.setKeyName("返回键");
        keyMap.setActionType(KeyMapConstants.ActionType.SYSTEM_ACTION);
        keyMap.setActionData(KeyMapConstants.SystemActionType.BACK);
        keyMap.setRating(4);
        keyMap.setUsageCount(6780);
        keyMap.addTag("系统");
        keyMap.addTag("返回");
        keyMap.addTag("导航");
        
        return keyMap;
    }
    
    /**
     * 创建语音助手按键映射
     */
    private static RecommendKeyMapModel createVoiceAssistantKeyMap() {
        RecommendKeyMapModel keyMap = new RecommendKeyMapModel(
            "key_voice_assistant",
            "语音助手",
            "一键唤醒语音助手，解放双手进行语音操作",
            "智能功能"
        );
        
        keyMap.setKeyCode(KeyEvent.KEYCODE_F4);
        keyMap.setKeyName("F4键");
        keyMap.setActionType(KeyMapConstants.ActionType.BROADCAST);
        keyMap.setActionData("com.smartcar.easylauncher.VOICE_ASSISTANT");
        keyMap.setRating(5);
        keyMap.setUsageCount(5670);
        keyMap.setNew(true);
        keyMap.addTag("新品");
        keyMap.addTag("语音");
        keyMap.addTag("智能");
        keyMap.addTag("助手");
        
        return keyMap;
    }
    
    /**
     * 创建截屏按键映射
     */
    private static RecommendKeyMapModel createScreenshotKeyMap() {
        RecommendKeyMapModel keyMap = new RecommendKeyMapModel(
            "key_screenshot",
            "快速截屏",
            "一键截取当前屏幕内容，方便保存重要信息",
            "实用工具"
        );
        
        keyMap.setKeyCode(KeyEvent.KEYCODE_F5);
        keyMap.setKeyName("F5键");
        keyMap.setActionType(KeyMapConstants.ActionType.BROADCAST);
        keyMap.setActionData("com.smartcar.easylauncher.SCREENSHOT");
        keyMap.setRating(3);
        keyMap.setUsageCount(2340);
        keyMap.addTag("工具");
        keyMap.addTag("截屏");
        keyMap.addTag("实用");
        
        return keyMap;
    }
}
