package com.smartcar.easylauncher.shared.utils;

import android.content.Context;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

// 定义RecyclerItemClickListener类
public class RecyclerItemClickListener implements RecyclerView.OnItemTouchListener {
    // 定义OnItemClickListener接口
    public interface OnItemClickListener {
        void onLongItemClick(View view, int position);
        void onLongClick();
    }

    // 定义GestureDetector对象
    private GestureDetector gestureDetector;

    // 定义OnItemClickListener对象
    private OnItemClickListener listener;

    // 定义构造方法，传入Context，RecyclerView和OnItemClickListener对象
    public RecyclerItemClickListener(Context context, final RecyclerView recyclerView, OnItemClickListener listener) {
        this.listener = listener;
        // 创建GestureDetector对象，并重写onLongPress方法
        gestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public void onLongPress(MotionEvent e) {
                // 根据触摸的位置，找到对应的子视图
                View childView = recyclerView.findChildViewUnder(e.getX(), e.getY());
                // 如果子视图不为空，并且OnItemClickListener不为空，则调用onLongItemClick方法
                if (childView != null && listener != null) {
                    listener.onLongItemClick(childView, recyclerView.getChildAdapterPosition(childView));
                }else {
                    listener.onLongClick();
                }
            }
        });
    }

    // 实现onInterceptTouchEvent方法，返回false表示不拦截触摸事件
    @Override
    public boolean onInterceptTouchEvent(@NonNull RecyclerView rv, @NonNull MotionEvent e) {
        // 把触摸事件传递给GestureDetector对象
        gestureDetector.onTouchEvent(e);
        return false;
    }

    // 实现onTouchEvent方法，什么都不做
    @Override
    public void onTouchEvent(@NonNull RecyclerView rv, @NonNull MotionEvent e) {

    }

    // 实现onRequestDisallowInterceptTouchEvent方法，什么都不做
    @Override
    public void onRequestDisallowInterceptTouchEvent(boolean disallowIntercept) {

    }
}