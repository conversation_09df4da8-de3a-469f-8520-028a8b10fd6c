package com.smartcar.easylauncher.shared.dialog.flexible;


import android.content.Context;
import android.os.Build;
import android.view.WindowManager;

import com.smartcar.easylauncher.shared.utils.permission.OverlayPermissionUtils;

/**
 * 对话框帮助类
 * 用于快速创建各种类型的对话框
 *
 * <AUTHOR>
 * @version 4.0
 */

public class DialogHelper {
    /**
     * 普通应用级窗口
     * 无需任何权限，仅在应用内显示
     */
    public static final int WINDOW_TYPE_NORMAL = WindowManager.LayoutParams.TYPE_APPLICATION;

    /**
     * 系统级窗口（Android 8.0以下）
     * 需要悬浮窗权限，可以显示在其他应用之上
     */
    public static final int WINDOW_TYPE_SYSTEM = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;

    /**
     * 系统级窗口（Android 8.0及以上）
     * 需要悬浮窗权限，适配Android 8.0及以上系统
     */
    public static final int WINDOW_TYPE_APPLICATION_OVERLAY = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;

    /**
     * 检查是否支持系统级窗口
     *
     * @param context 上下文对象
     * @return 是否支持系统级窗口
     */
    public static boolean isSupportSystemWindow(Context context) {
        // 检查系统版本和悬浮窗权限
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.M &&
                OverlayPermissionUtils.isWindow(context);
    }

    /**
     * 获取当前系统支持的窗口类型
     *
     * @param context 上下文对象
     * @return 适合当前系统版本的窗口类型
     */
    public static int getSystemWindowType(Context context) {
        if (!isSupportSystemWindow(context)) {
            return WINDOW_TYPE_NORMAL;
        }
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.O ?
                WINDOW_TYPE_APPLICATION_OVERLAY : WINDOW_TYPE_SYSTEM;
    }
}
