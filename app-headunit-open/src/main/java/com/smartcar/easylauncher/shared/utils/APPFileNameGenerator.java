package com.smartcar.easylauncher.shared.utils;


import com.elvishew.xlog.printer.file.naming.FileNameGenerator;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

/**
 * APP文件名生成器类，实现了FileNameGenerator接口。
 * 该类主要用于生成基于特定日期的文件名。
 */
public class APPFileNameGenerator implements FileNameGenerator {

    /**
     * 使用ThreadLocal来存储SimpleDateFormat对象，保证每个线程都有自己独立的日期格式实例。
     * 这样做是为了避免在多线程环境下对SimpleDateFormat的共享和修改，从而引发线程安全问题。
     */
    ThreadLocal<SimpleDateFormat> mLocalDateFormat = new ThreadLocal<SimpleDateFormat>() {

        /**
         * 初始化ThreadLocal中的SimpleDateFormat对象。
         * 这里设置为美国时区下的"yyyy-MM-dd"格式，确保日期格式的一致性。
         * 注意：虽然这里指定了Locale.US，但日期格式"yyyy-MM-dd"本身并不受Locale影响。
         */
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd HH", Locale.US);
        }
    };

    /**
     * 实现FileNameGenerator接口的isFileNameChangeable方法。
     * 该方法表明文件名是否可以根据某些条件（如时间戳）变化。
     * 在这里，我们返回true，表示文件名是可以变化的。
     */
    @Override
    public boolean isFileNameChangeable() {
        return true;
    }

    /**
     * 实现FileNameGenerator接口的generateFileName方法。
     * 该方法根据给定的日志级别（此处未使用）和时间戳生成一个文件名。
     * 文件名是以"yyyy-MM-dd"格式表示的日期字符串。
     *
     * @param logLevel 日志级别，但在本实现中未使用。
     * @param timestamp 时间戳，用于生成文件名对应的日期。
     * @return 生成的文件名字符串。
     */
    @Override
    public String generateFileName(int logLevel, long timestamp) {
        // 从ThreadLocal中获取SimpleDateFormat实例
        SimpleDateFormat sdf = mLocalDateFormat.get();
        // 设置默认时区，尽管SimpleDateFormat在初始化时已经指定了格式，但这里重新设置时区以确保无误
        sdf.setTimeZone(TimeZone.getDefault());
        // 使用时间戳生成日期字符串，并作为文件名返回
        return sdf.format(new Date(timestamp));
    }
}
