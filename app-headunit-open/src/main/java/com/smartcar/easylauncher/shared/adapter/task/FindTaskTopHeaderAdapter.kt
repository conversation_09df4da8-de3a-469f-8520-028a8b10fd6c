package com.smartcar.easylauncher.shared.adapter.task

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter4.BaseSingleItemAdapter
import com.chad.library.adapter4.fullspan.FullSpanAdapterType
import com.smartcar.easylauncher.R

/**
 * 发现任务页面头部适配器
 * 显示标题和任务数量
 * <AUTHOR>
 * @date 2024/12/14
 */
class FindTaskTopHeaderAdapter : BaseSingleItemAdapter<Any, FindTaskTopHeaderAdapter.VH>(),
    FullSpanAdapterType {

    companion object {
        val HEAD_VIEWTYPE = 0x10000556
    }

    private var taskCount: Int = 0
    private var isLoading: Boolean = true

    class VH(view: View) : RecyclerView.ViewHolder(view) {
        val tvTaskCount: TextView = view.findViewById(R.id.tv_task_count)
    }

    override fun onCreateViewHolder(context: Context, parent: ViewGroup, viewType: Int): VH {
        return VH(LayoutInflater.from(parent.context).inflate(R.layout.find_task_top_view, parent, false))
    }

    override fun onBindViewHolder(holder: VH, item: Any?) {
        updateTaskCountDisplay(holder)
    }

    override fun getItemViewType(position: Int, list: List<Any>): Int {
        return HEAD_VIEWTYPE
    }

    /**
     * 更新任务数量显示
     */
    fun updateTaskCount(count: Int, loading: Boolean = false) {
        this.taskCount = count
        this.isLoading = loading
        notifyDataSetChanged()
    }

    /**
     * 更新任务数量显示文本
     */
    private fun updateTaskCountDisplay(holder: VH) {
        val countText = when {
            isLoading -> "加载中..."
            taskCount == 0 -> "暂无任务"
            taskCount == 1 -> "1个任务"
            else -> "${taskCount}个任务"
        }
        holder.tvTaskCount.text = countText
    }
}