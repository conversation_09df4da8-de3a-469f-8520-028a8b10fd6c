package com.smartcar.easylauncher.shared.widget.layout.map;

import androidx.constraintlayout.widget.ConstraintSet;
import androidx.fragment.app.FragmentActivity;

import com.smartcar.easylauncher.core.constants.SettingsConstants;
import com.smartcar.easylauncher.databinding.FragmentMapHomeBinding;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.shared.view.pagergridlayoutmanager.PagerGridLayoutManager;

/**
 * 首页地图布局1实现
 * 布局1配置：右侧状态栏 + 左侧卡片列表 + 中间内容区域 + 底部导航栏
 * 适用场景：传统的三栏布局，状态栏位于右上角
 */
public class HomeMapLayout1 extends AbstractHomeMapLayout {

    /**
     * 构造函数
     *
     * @param binding            数据绑定对象
     * @param activity           Fragment所属的Activity
     * @param pagerLayoutManager RecyclerView的布局管理器
     */
    public HomeMapLayout1(FragmentMapHomeBinding binding,
                          FragmentActivity activity,
                          PagerGridLayoutManager pagerLayoutManager) {
        super(binding, activity, pagerLayoutManager);
    }

    @Override
    public void applyLayout(ConstraintSet constraintSet) {
        // 获取状态栏和导航栏的显示状态
        boolean statusBarVisible = SettingsManager.getStatusBarShow();
        boolean navigationBarVisible = SettingsManager.getNavigationBarShow();

        // 配置状态栏（右上角位置）
        if (statusBarVisible) {
            configureStatusBar1(constraintSet,
                    ConstraintSet.WRAP_CONTENT,  // 宽度
                    dimensionCache.statusBarHeight50,  // 高度
                    dimensionCache.margin10,  // 顶部边距
                    dimensionCache.margin25  // 右边距
            );
        } else {
            // 状态栏不显示时，隐藏并清除约束
            hideStatusBar(constraintSet);
        }

        // 配置RecyclerView（卡片列表）- 布局1特殊处理
        configureRecyclerViewLayout1(constraintSet);

        // 配置主内容区域 - 布局1特殊处理
        configureFrameLayoutLayout1(constraintSet, navigationBarVisible);

        // 配置底部导航栏
        if (navigationBarVisible) {
            configureNavigationBar(constraintSet,
                    0,  // 宽度（0dp表示MATCH_CONSTRAINT）
                    dimensionCache.getNavigationBarHeight(),  // 高度 - 动态获取
                    binding.rvHome.getId(),  // 左侧约束目标
                    ConstraintSet.PARENT_ID  // 右侧约束目标
            );
        } else {
            // 导航栏不显示时，隐藏并清除约束
            hideNavigationBar(constraintSet);
        }

        // 设置RecyclerView为垂直布局
        setupVerticalRecyclerView();
    }

    @Override
    public String getLayoutType() {
        return SettingsConstants.DEFAULT_MAP_LAYOUT_1;
    }

    /**
     * 配置布局1专用的状态栏
     */
    private void configureStatusBar1(ConstraintSet constraintSet, int width, int height, int topMargin, int endMargin) {
        // 清空主布局状态栏的约束
        constraintSet.clear(binding.mainLayoutStatusBar.getId());

        // 设置主布局状态栏的宽度
        constraintSet.constrainWidth(binding.mainLayoutStatusBar.getId(), width);

        // 设置主布局状态栏的高度
        constraintSet.constrainHeight(binding.mainLayoutStatusBar.getId(), height);

        // 将主布局状态栏的顶部与父布局的顶部对齐，并设置上边距
        constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.TOP,
                binding.frameLayout.getId(), ConstraintSet.TOP, topMargin);

        // 右对齐
        // 将主布局状态栏的结束位置与父布局的结束位置对齐，并设置右边距
        constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.END,
                ConstraintSet.PARENT_ID, ConstraintSet.END, endMargin);

        // 设置状态栏可见性
        // 根据设置管理器中状态栏的显示状态，设置主布局状态栏的可见性
        binding.mainLayoutStatusBar.setVisibility(
                SettingsManager.getStatusBarShow() ? android.view.View.VISIBLE : android.view.View.GONE);
    }

    /**
     * 配置布局1专用的RecyclerView
     * 布局1中RecyclerView的顶部直接连接到父容器顶部，不受状态栏影响
     */
    private void configureRecyclerViewLayout1(ConstraintSet constraintSet) {
        constraintSet.clear(binding.rvHome.getId());
        constraintSet.constrainWidth(binding.rvHome.getId(), dimensionCache.mapCardWidth);
        constraintSet.constrainHeight(binding.rvHome.getId(), ConstraintSet.MATCH_CONSTRAINT);

        // 布局1中RecyclerView顶部直接连接到父容器顶部
        constraintSet.connect(binding.rvHome.getId(), ConstraintSet.TOP,
                ConstraintSet.PARENT_ID, ConstraintSet.TOP, dimensionCache.margin5);
        constraintSet.connect(binding.rvHome.getId(), ConstraintSet.START,
                ConstraintSet.PARENT_ID, ConstraintSet.START);
        constraintSet.connect(binding.rvHome.getId(), ConstraintSet.BOTTOM,
                ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM);
    }

    /**
     * 配置布局1专用的FrameLayout
     * 布局1中FrameLayout的顶部直接连接到父容器顶部，不受状态栏影响
     *
     * @param navigationBarVisible 导航栏是否可见
     */
    private void configureFrameLayoutLayout1(ConstraintSet constraintSet, boolean navigationBarVisible) {
        constraintSet.clear(binding.frameLayout.getId());
        constraintSet.constrainWidth(binding.frameLayout.getId(), ConstraintSet.MATCH_CONSTRAINT);
        constraintSet.constrainHeight(binding.frameLayout.getId(), ConstraintSet.MATCH_CONSTRAINT);

        // 布局1中FrameLayout顶部直接连接到父容器顶部
        constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.TOP,
                ConstraintSet.PARENT_ID, ConstraintSet.TOP, dimensionCache.margin5);
        constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.END,
                ConstraintSet.PARENT_ID, ConstraintSet.END, dimensionCache.margin5);
        constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.START,
                binding.rvHome.getId(), ConstraintSet.END, dimensionCache.margin5);

        // 设置底部约束 - 根据导航栏显示状态
        if (navigationBarVisible) {
            // 导航栏显示时，FrameLayout底部与导航栏顶部对齐
            constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.BOTTOM,
                    binding.maiIconBar.getId(), ConstraintSet.TOP, dimensionCache.margin5);
        } else {
            // 导航栏不显示时，FrameLayout底部与父容器底部对齐
            constraintSet.connect(binding.frameLayout.getId(), ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, dimensionCache.margin5);
        }
    }
} 