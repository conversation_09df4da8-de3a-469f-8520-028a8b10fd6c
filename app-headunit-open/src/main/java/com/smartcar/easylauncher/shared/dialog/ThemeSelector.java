package com.smartcar.easylauncher.shared.dialog;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;

import androidx.core.util.Consumer;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.dialog.DialogGridAdapter;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.data.model.system.AppInfo;
import com.smartcar.easylauncher.shared.utils.AppInfoProvider;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;
import com.timmy.tdialog.TDialog;
import com.timmy.tdialog.base.BindViewHolder;
import com.timmy.tdialog.base.TBaseAdapter;
import com.timmy.tdialog.list.TListDialog;
import com.timmy.tdialog.listener.OnViewClickListener;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 横向应用选择
 */
public class ThemeSelector {

    private static Context context;
    private static List<AppInfo> mAllAppList;
    private static String appinfo;
    private static AppInfo Info;

    public static void selectApp(Context context, int type, Consumer<AppInfo> callback) {
        ThemeSelector.context = context;
        appinfo = "";
        Info = null;
        if (DataManager.getAppListData().isEmpty()) {
            AppInfoProvider mProvider = AppInfoProvider.getInstance(context);
            mProvider.queryAppInfo().thenAccept(appInfos -> {
                mAllAppList = appInfos;
                showDialog(type, callback);
            });
        } else {
            mAllAppList = new Gson().fromJson(DataManager.getAppListData(), new TypeToken<List<AppInfo>>() {
            }.getType());
            mAllAppList = mAllAppList.stream().filter(appInfo -> appInfo.getState() == 0).collect(Collectors.toList());
            showDialog(type, callback);
        }
    }

    private static void showDialog(int type, Consumer<AppInfo> callback) {
        new TListDialog.Builder(((FragmentActivity) context).getSupportFragmentManager())
                .setListLayoutRes(R.layout.dialog_app_select, LinearLayoutManager.HORIZONTAL)
                .setLayoutManager(new GridLayoutManager(context, 5))
                .setWidth(DensityUtils.dp2px(context, 460))
                .setHeight(DensityUtils.dp2px(context, 310))
                .addOnClickListener(R.id.root, R.id.ll_payment, R.id.ll_xiezai, R.id.ll_guanbi)
                .setAdapter(new DialogGridAdapter<AppInfo>(R.layout.select_item_app, mAllAppList) {
                    @Override
                    protected void onBind(BindViewHolder holder, int position, AppInfo info) {
                        holder.setText(R.id.grid_item_app_name, info.getAppName());
                        Glide.with(context).load(info.getLogoPath()).into((ImageView) holder.getView(R.id.grid_item_app_icon));
                        if (info.isSelect()) {
                            holder.getView(R.id.grid_item_app_delete).setVisibility(View.VISIBLE);
                        } else {
                            holder.getView(R.id.grid_item_app_delete).setVisibility(View.GONE);
                        }
                    }
                })
                .setOnAdapterItemClickListener(new TBaseAdapter.OnAdapterItemClickListener<AppInfo>() {
                    @Override
                    public void onItemClick(BindViewHolder holder, int position, AppInfo info, TDialog tDialog) {
                        if (info.isSelect()) {
                            appinfo = new Gson().toJson(info).toString();
                            Info = info;
                        } else {
                            Info = null;
                            appinfo = "";
                        }
                    }
                })
                .setOnViewClickListener(new OnViewClickListener() {
                    @Override
                    public void onViewClick(BindViewHolder viewHolder, View view, TDialog tDialog) {
                        switch (view.getId()) {
                            case R.id.ll_payment:
                                if (appinfo == null || appinfo.isEmpty()) {
                                    MToast.makeTextShort( "选择你要添加的应用");
                                    return;
                                }
                                callback.accept(Info);
                                // ...省略其他设置代码...
                                tDialog.dismiss();
                                break;
                            case R.id.ll_xiezai:
                                tDialog.dismiss();
                                break;
                            case R.id.ll_guanbi:
                                tDialog.dismiss();
                                break;
                            case R.id.root:
                                tDialog.dismiss();
                                break;
                            default:
                        }
                    }
                })
                // ...省略其他设置代码...
                .create()
                .show();
    }
}
