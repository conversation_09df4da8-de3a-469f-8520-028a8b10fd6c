package com.smartcar.easylauncher.shared.dialog.flexible;

import android.content.Context;
import android.os.Build;
import android.util.Log;
import android.view.WindowManager;

import androidx.annotation.LayoutRes;
import androidx.annotation.StyleRes;

import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.shared.utils.permission.OverlayPermissionUtils;

/**
 * 对话框构建器
 */
public class FlexibleDialogBuilder {
    private final DialogController.DialogParams params;
    private final Context context;

    public FlexibleDialogBuilder(Context context) {
        this.context = context;
        this.params = new DialogController.DialogParams();
    }

    public FlexibleDialogBuilder setTheme(@StyleRes int themeResId) {
        params.themeResId = themeResId;
        return this;
    }

    public FlexibleDialogBuilder setLayout(@LayoutRes int layoutResId) {
        params.layoutResId = layoutResId;
        return this;
    }

    public FlexibleDialogBuilder setGravity(int gravity) {
        params.gravity = gravity;
        return this;
    }

    public FlexibleDialogBuilder setWidth(int width) {
        params.width = width;
        return this;
    }

    public FlexibleDialogBuilder setHeight(int height) {
        params.height = height;
        return this;
    }

    public FlexibleDialogBuilder setAnimation(int animation) {
        params.animation = animation;
        return this;
    }

    public FlexibleDialogBuilder setCancelable(boolean cancelable) {
        params.cancelable = cancelable;
        return this;
    }

    public FlexibleDialogBuilder setCanceledOnTouchOutside(boolean canceled) {
        params.canceledOnTouchOutside = canceled;
        return this;
    }

    public FlexibleDialogBuilder setDimAmount(float dimAmount) {
        params.dimAmount = dimAmount;
        return this;
    }

    /**
     * 设置对话框窗口类型
     * 支持三种类型：
     * 1. WINDOW_TYPE_NORMAL：普通应用级窗口，无需权限
     * 2. WINDOW_TYPE_SYSTEM：系统级窗口，Android 8.0以下无需权限，8.0及以上需要悬浮窗权限
     * 3. WINDOW_TYPE_APPLICATION_OVERLAY：Android 8.0及以上系统的系统级窗口，需要悬浮窗权限
     *
     * @param windowType 窗口类型，参考 DialogHelper 中的常量定义
     * @return FlexibleDialogBuilder 实例，支持链式调用
     */
    public FlexibleDialogBuilder setWindowType(int windowType) {
        try {
            // 如果是普通窗口类型，直接设置并返回
            if (windowType == DialogHelper.WINDOW_TYPE_NORMAL) {
                params.windowType = WindowManager.LayoutParams.TYPE_APPLICATION;
                return this;
            }

            // Android 8.0 及以上版本需要检查悬浮窗权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                if (!OverlayPermissionUtils.isWindow(context)) {
                    params.windowType = WindowManager.LayoutParams.TYPE_APPLICATION;
                    MToast.makeTextShort("需要悬浮窗权限才能显示系统级对话框");
                    return this;
                }
                params.windowType = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
            } else {
                // Android 8.0 以下版本直接使用系统级窗口类型
                params.windowType = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
            }

        } catch (Exception e) {
            // 发生异常时使用默认的应用级窗口类型
            params.windowType = WindowManager.LayoutParams.TYPE_APPLICATION;
            Log.e("FlexibleDialog", "设置窗口类型失败: " + e.getMessage());
        }
        return this;
    }

    public FlexibleDialogBuilder setViewBinder(DialogController.DialogParams.ViewBinder viewBinder) {
        params.viewBinder = viewBinder;
        return this;
    }

    public FlexibleDialogBuilder setOnDialogEventListener(DialogController.DialogParams.OnDialogEventListener listener) {
        params.onDialogEventListener = listener;
        return this;
    }

    public FlexibleDialogBuilder setOnViewClickListener(DialogController.DialogParams.OnViewClickListener listener) {
        params.onViewClickListener = listener;
        return this;
    }

    public FlexibleDialogBuilder addOnClickListener(int... ids) {
        params.ids = ids;
        return this;
    }

    public FlexibleDialog create() {
        return new FlexibleDialog(context, new DialogController(context, params));
    }
}