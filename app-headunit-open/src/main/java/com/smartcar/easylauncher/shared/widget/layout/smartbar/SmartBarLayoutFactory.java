package com.smartcar.easylauncher.shared.widget.layout.smartbar;

import androidx.fragment.app.FragmentActivity;

import com.smartcar.easylauncher.core.constants.SettingsConstants;
import com.smartcar.easylauncher.databinding.FragmentSmartBarBinding;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.util.HashMap;
import java.util.Map;

/**
 * 智能导航栏布局工厂类
 * 负责创建和缓存不同的SmartBar布局实现
 * 
 * 参考HomeMapLayoutFactory的设计模式，提供统一的布局创建和管理
 * 
 * <AUTHOR>
 * @since 2024-12-18
 */
public class SmartBarLayoutFactory {

    private static final String TAG = "SmartBarLayoutFactory";

    // 布局缓存，避免重复创建相同的布局对象
    private final Map<String, ISmartBarLayout> layoutCache = new HashMap<>();
    
    // UI组件引用
    private final FragmentSmartBarBinding binding;
    private final FragmentActivity activity;

    /**
     * 构造函数
     *
     * @param binding  数据绑定对象
     * @param activity Fragment所属的Activity
     */
    public SmartBarLayoutFactory(FragmentSmartBarBinding binding, FragmentActivity activity) {
        this.binding = binding;
        this.activity = activity;
    }

    /**
     * 根据当前设置获取对应的布局实现
     * 自动判断当前应该使用哪种布局类型
     * 
     * @return 对应类型的布局实现
     */
    public ISmartBarLayout getCurrentLayout() {
        String layoutType = determineCurrentLayoutType();
        return getLayout(layoutType);
    }

    /**
     * 获取指定类型的布局实现
     * 如果布局已经创建过，则从缓存中获取，否则创建新的布局实现
     *
     * @param layoutType 布局类型标识符
     * @return 对应类型的布局实现
     */
    public ISmartBarLayout getLayout(String layoutType) {
        try {
            // 如果缓存中已有该布局，直接返回
            if (layoutCache.containsKey(layoutType)) {
                MyLog.d(TAG, "从缓存获取布局: " + layoutType);
                return layoutCache.get(layoutType);
            }

            // 根据布局类型创建相应的布局实现
            ISmartBarLayout layout = createLayout(layoutType);
            
            // 将新创建的布局添加到缓存中
            layoutCache.put(layoutType, layout);
            MyLog.d(TAG, "创建并缓存新布局: " + layoutType);
            
            return layout;
        } catch (Exception e) {
            MyLog.e(TAG, "获取布局失败: " + layoutType, e);
            // 返回默认的底部布局
            return getDefaultLayout();
        }
    }

    /**
     * 创建布局实现
     */
    private ISmartBarLayout createLayout(String layoutType) {
        switch (layoutType) {
            case "bottom":
                return new SmartBarBottomLayout(binding, activity);
            case "side":
                return new SmartBarSideLayout(binding, activity);
            default:
                MyLog.w(TAG, "未知布局类型: " + layoutType + "，使用默认底部布局");
                return new SmartBarBottomLayout(binding, activity);
        }
    }

    /**
     * 获取默认布局（底部布局）
     */
    private ISmartBarLayout getDefaultLayout() {
        return getLayout("bottom");
    }

    /**
     * 根据当前设置确定应该使用的布局类型
     * 
     * @return 布局类型标识符
     */
    private String determineCurrentLayoutType() {
        try {
            int homeType = SettingsManager.getDefaultHome();
            
            switch (homeType) {
                case SettingsConstants.HOME_CARD_LAYOUT_TYPE:
                    return determineCardLayoutType();
                case SettingsConstants.HOME_MAP_LAYOUT_TYPE:
                    return determineMapLayoutType();
                case SettingsConstants.HOME_PIP_LAYOUT_TYPE:
                    // PIP布局默认使用底部布局
                    return "bottom";
                default:
                    MyLog.w(TAG, "未知首页类型: " + homeType + "，使用默认底部布局");
                    return "bottom";
            }
        } catch (Exception e) {
            MyLog.e(TAG, "确定布局类型失败", e);
            return "bottom";
        }
    }

    /**
     * 确定卡片布局类型
     */
    private String determineCardLayoutType() {
        String cardLayout = SettingsManager.getCardLayout();
        
        switch (cardLayout) {
            case SettingsConstants.DEFAULT_CARD_LAYOUT_1:
            case SettingsConstants.DEFAULT_CARD_LAYOUT_2:
                return "bottom";
            case SettingsConstants.DEFAULT_CARD_LAYOUT_3:
                return "side";
            default:
                MyLog.w(TAG, "未知卡片布局: " + cardLayout + "，使用默认底部布局");
                return "bottom";
        }
    }

    /**
     * 确定地图布局类型
     */
    private String determineMapLayoutType() {
        String mapLayout = SettingsManager.getMapLayout();
        
        switch (mapLayout) {
            case SettingsConstants.DEFAULT_MAP_LAYOUT_1:
            case SettingsConstants.DEFAULT_MAP_LAYOUT_2:
            case SettingsConstants.DEFAULT_MAP_LAYOUT_5:
            case SettingsConstants.DEFAULT_MAP_LAYOUT_6:
                return "bottom";
            case SettingsConstants.DEFAULT_MAP_LAYOUT_3:
            case SettingsConstants.DEFAULT_MAP_LAYOUT_4:
                return "side";
            default:
                MyLog.w(TAG, "未知地图布局: " + mapLayout + "，使用默认底部布局");
                return "bottom";
        }
    }

    /**
     * 检查当前是否为侧边栏布局
     * 
     * @return true表示侧边栏布局，false表示底部栏布局
     */
    public boolean isSidebarLayout() {
        return getCurrentLayout().isSidebarLayout();
    }

    /**
     * 检查当前布局是否支持音乐控制
     * 
     * @return true表示支持音乐控制，false表示不支持
     */
    public boolean supportsMusicControl() {
        return getCurrentLayout().supportsMusicControl();
    }

    /**
     * 清除布局缓存
     * 在需要释放内存或重新创建布局时调用
     */
    public void clearCache() {
        layoutCache.clear();
        MyLog.d(TAG, "布局缓存已清除");
    }
}
