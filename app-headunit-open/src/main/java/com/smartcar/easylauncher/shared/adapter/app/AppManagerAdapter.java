
package com.smartcar.easylauncher.shared.adapter.app;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.data.model.system.AppInfo;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;

/**
 * show all app
 * <AUTHOR>
 */
public class AppManagerAdapter extends BaseQuickAdapter<AppInfo, QuickViewHolder> {


    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder quickViewHolder, int i, @Nullable AppInfo info) {
        assert info != null;
        quickViewHolder.setText(R.id.grid_item_app_name, info.getAppName());

        // 获取设置的图标大小
        int iconSize = DensityUtils.dp2px(getContext(), SettingsManager.getIconSize());

        // 设置图标大小
        ImageView iconView = quickViewHolder.getView(R.id.grid_item_app_icon);
        ViewGroup.LayoutParams params = iconView.getLayoutParams();
        params.width = iconSize;
        params.height = iconSize;
        iconView.setLayoutParams(params);

        Glide.with(getContext()).load(info.getLogoPath()).into((ImageView) quickViewHolder.getView(R.id.grid_item_app_icon));
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(LayoutInflater.from(context).inflate(R.layout.gridview_item_app, viewGroup, false));
    }

}