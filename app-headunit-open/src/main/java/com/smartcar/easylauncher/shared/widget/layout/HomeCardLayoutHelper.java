package com.smartcar.easylauncher.shared.widget.layout;

import android.view.View;

import androidx.constraintlayout.widget.ConstraintSet;

import com.smartcar.easylauncher.databinding.FragmentCardHomeBinding;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.view.pagergridlayoutmanager.PagerGridLayoutManager;

/**
 * 首页卡片布局辅助类
 * 包含各种特殊布局配置的具体实现方法
 *
 * 职责分离：
 * - HomeCardLayoutManager：主要布局逻辑和策略选择
 * - HomeCardLayoutHelper：具体的布局配置实现
 * - LayoutDimensionCache：尺寸缓存管理
 *
 * <AUTHOR> Team
 * @version 1.0
 */
public class HomeCardLayoutHelper {

    private static final String TAG = "HomeCardLayoutHelper";

    private final FragmentCardHomeBinding binding;
    private final PagerGridLayoutManager layoutManager;
    private final ConstraintSet constraintSet;
    private final LayoutDimensionCache dimensionCache;

    /**
     * 构造函数
     */
    public HomeCardLayoutHelper(FragmentCardHomeBinding binding,
                              PagerGridLayoutManager layoutManager,
                              ConstraintSet constraintSet,
                              LayoutDimensionCache dimensionCache) {
        this.binding = binding;
        this.layoutManager = layoutManager;
        this.constraintSet = constraintSet;
        this.dimensionCache = dimensionCache;
    }

    /**
     * 配置RecyclerView为全屏布局（用于卡片布局1）
     */
    public void configureRecyclerViewFullscreen() {
        try {
            constraintSet.clear(binding.rvHome.getId());
            constraintSet.constrainWidth(binding.rvHome.getId(), ConstraintSet.MATCH_CONSTRAINT);
            constraintSet.constrainHeight(binding.rvHome.getId(), ConstraintSet.MATCH_CONSTRAINT);

            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.START,
                                ConstraintSet.PARENT_ID, ConstraintSet.START, dimensionCache.margin5);
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.END,
                                ConstraintSet.PARENT_ID, ConstraintSet.END, dimensionCache.margin5);
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.TOP,
                                ConstraintSet.PARENT_ID, ConstraintSet.TOP, dimensionCache.margin5);
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.BOTTOM,
                                ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, dimensionCache.margin5);
                                
            MyLog.d(TAG, "配置RecyclerView全屏布局完成");
            
        } catch (Exception e) {
            MyLog.e(TAG, "配置RecyclerView全屏布局失败", e);
        }
    }

    /**
     * 配置RecyclerView为中心布局（用于卡片布局2）
     */
    public void configureRecyclerViewCenter() {
        try {
            constraintSet.clear(binding.rvHome.getId());
            constraintSet.constrainWidth(binding.rvHome.getId(), ConstraintSet.MATCH_CONSTRAINT);
            constraintSet.constrainHeight(binding.rvHome.getId(), ConstraintSet.MATCH_CONSTRAINT);

            // 设置顶部约束
            if (binding.mainLayoutStatusBar != null && SettingsManager.getStatusBarShow()) {
                constraintSet.connect(binding.rvHome.getId(), ConstraintSet.TOP,
                                    binding.mainLayoutStatusBar.getId(), ConstraintSet.BOTTOM, 0);
            } else {
                constraintSet.connect(binding.rvHome.getId(), ConstraintSet.TOP,
                                    ConstraintSet.PARENT_ID, ConstraintSet.TOP, dimensionCache.margin5);
            }

            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.START,
                                ConstraintSet.PARENT_ID, ConstraintSet.START, dimensionCache.margin5);
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.END,
                                ConstraintSet.PARENT_ID, ConstraintSet.END, dimensionCache.margin5);

            // 设置底部约束
            if (binding.maiIconBar != null && SettingsManager.getNavigationBarShow()) {
                constraintSet.connect(binding.rvHome.getId(), ConstraintSet.BOTTOM,
                                    binding.maiIconBar.getId(), ConstraintSet.TOP, dimensionCache.margin5);
            } else {
                constraintSet.connect(binding.rvHome.getId(), ConstraintSet.BOTTOM,
                                    ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, dimensionCache.margin5);
            }
            
            MyLog.d(TAG, "配置RecyclerView中心布局完成");
            
        } catch (Exception e) {
            MyLog.e(TAG, "配置RecyclerView中心布局失败", e);
        }
    }

    /**
     * 配置RecyclerView为右侧布局（用于卡片布局3）
     */
    public void configureRecyclerViewRight() {
        try {
            constraintSet.clear(binding.rvHome.getId());
            constraintSet.constrainWidth(binding.rvHome.getId(), ConstraintSet.MATCH_CONSTRAINT);
            constraintSet.constrainHeight(binding.rvHome.getId(), ConstraintSet.MATCH_CONSTRAINT);

            // 设置顶部约束
            if (binding.mainLayoutStatusBar != null && SettingsManager.getStatusBarShow()) {
                constraintSet.connect(binding.rvHome.getId(), ConstraintSet.TOP,
                                    binding.mainLayoutStatusBar.getId(), ConstraintSet.BOTTOM, 0);
            } else {
                constraintSet.connect(binding.rvHome.getId(), ConstraintSet.TOP,
                                    ConstraintSet.PARENT_ID, ConstraintSet.TOP, dimensionCache.margin5);
            }

            // 设置左侧约束（导航栏右侧）
            if (binding.maiIconBar != null && SettingsManager.getNavigationBarShow()) {
                constraintSet.connect(binding.rvHome.getId(), ConstraintSet.START,
                                    binding.maiIconBar.getId(), ConstraintSet.END, dimensionCache.margin5);
            } else {
                constraintSet.connect(binding.rvHome.getId(), ConstraintSet.START,
                                    ConstraintSet.PARENT_ID, ConstraintSet.START, dimensionCache.margin5);
            }

            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.END,
                                ConstraintSet.PARENT_ID, ConstraintSet.END, dimensionCache.margin5);
            constraintSet.connect(binding.rvHome.getId(), ConstraintSet.BOTTOM,
                                ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, dimensionCache.margin5);
                                
            MyLog.d(TAG, "配置RecyclerView右侧布局完成");
            
        } catch (Exception e) {
            MyLog.e(TAG, "配置RecyclerView右侧布局失败", e);
        }
    }

    /**
     * 配置状态栏为顶部布局
     */
    public void configureStatusBarTop() {
        try {
            constraintSet.clear(binding.mainLayoutStatusBar.getId());
            constraintSet.constrainWidth(binding.mainLayoutStatusBar.getId(), ConstraintSet.MATCH_CONSTRAINT);
            constraintSet.constrainHeight(binding.mainLayoutStatusBar.getId(), dimensionCache.statusBarHeight30);

            constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.TOP,
                                ConstraintSet.PARENT_ID, ConstraintSet.TOP);
            constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.START,
                                ConstraintSet.PARENT_ID, ConstraintSet.START);
            constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.END,
                                ConstraintSet.PARENT_ID, ConstraintSet.END);

            // 设置状态栏可见性
            binding.mainLayoutStatusBar.setVisibility(
                SettingsManager.getStatusBarShow() ? View.VISIBLE : View.GONE);
                
            MyLog.d(TAG, "配置状态栏顶部布局完成");
            
        } catch (Exception e) {
            MyLog.e(TAG, "配置状态栏顶部布局失败", e);
        }
    }

    /**
     * 配置状态栏为左侧布局
     */
    public void configureStatusBarLeft() {
        try {
            constraintSet.clear(binding.mainLayoutStatusBar.getId());
            constraintSet.constrainWidth(binding.mainLayoutStatusBar.getId(), dimensionCache.statusBarHeight40);
            constraintSet.constrainHeight(binding.mainLayoutStatusBar.getId(), ConstraintSet.MATCH_CONSTRAINT);

            constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.TOP,
                                ConstraintSet.PARENT_ID, ConstraintSet.TOP);
            constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.START,
                                ConstraintSet.PARENT_ID, ConstraintSet.START);
            constraintSet.connect(binding.mainLayoutStatusBar.getId(), ConstraintSet.BOTTOM,
                                ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM);

            // 设置状态栏可见性
            binding.mainLayoutStatusBar.setVisibility(
                SettingsManager.getStatusBarShow() ? View.VISIBLE : View.GONE);
                
            MyLog.d(TAG, "配置状态栏左侧布局完成");
            
        } catch (Exception e) {
            MyLog.e(TAG, "配置状态栏左侧布局失败", e);
        }
    }

    /**
     * 配置导航栏为底部布局
     */
    public void configureNavigationBarBottom() {
        try {
            constraintSet.clear(binding.maiIconBar.getId());
            constraintSet.constrainWidth(binding.maiIconBar.getId(), ConstraintSet.MATCH_CONSTRAINT);
            constraintSet.constrainHeight(binding.maiIconBar.getId(), ConstraintSet.WRAP_CONTENT);

            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.START,
                                ConstraintSet.PARENT_ID, ConstraintSet.START, dimensionCache.margin5);
            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.END,
                                ConstraintSet.PARENT_ID, ConstraintSet.END, dimensionCache.margin5);
            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.BOTTOM,
                                ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, dimensionCache.margin5);

            // 设置导航栏可见性
            binding.maiIconBar.setVisibility(
                SettingsManager.getNavigationBarShow() ? View.VISIBLE : View.GONE);
                
            MyLog.d(TAG, "配置导航栏底部布局完成");
            
        } catch (Exception e) {
            MyLog.e(TAG, "配置导航栏底部布局失败", e);
        }
    }

    /**
     * 配置导航栏为左侧布局
     */
    public void configureNavigationBarLeft() {
        try {
            constraintSet.clear(binding.maiIconBar.getId());
            constraintSet.constrainWidth(binding.maiIconBar.getId(), ConstraintSet.WRAP_CONTENT);
            constraintSet.constrainHeight(binding.maiIconBar.getId(), ConstraintSet.MATCH_CONSTRAINT);

            // 设置顶部约束
            if (binding.mainLayoutStatusBar != null && SettingsManager.getStatusBarShow()) {
                constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.TOP,
                                    binding.mainLayoutStatusBar.getId(), ConstraintSet.BOTTOM, 0);
            } else {
                constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.TOP,
                                    ConstraintSet.PARENT_ID, ConstraintSet.TOP, dimensionCache.margin5);
            }

            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.START,
                                ConstraintSet.PARENT_ID, ConstraintSet.START);
            constraintSet.connect(binding.maiIconBar.getId(), ConstraintSet.BOTTOM,
                                ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, dimensionCache.margin5);

            // 设置导航栏可见性
            binding.maiIconBar.setVisibility(
                SettingsManager.getNavigationBarShow() ? View.VISIBLE : View.GONE);
                
            MyLog.d(TAG, "配置导航栏左侧布局完成");
            
        } catch (Exception e) {
            MyLog.e(TAG, "配置导航栏左侧布局失败", e);
        }
    }

    /**
     * 设置RecyclerView为横向布局模式（卡片布局固定为横向）
     */
    public void setupHorizontalRecyclerView() {
        try {
            if (layoutManager != null) {
                layoutManager.setOrientation(PagerGridLayoutManager.HORIZONTAL);
                layoutManager.setRows(1);
                layoutManager.setColumns(3); // 卡片布局固定为1行3列
                MyLog.d(TAG, "设置RecyclerView横向布局完成");
            }
        } catch (Exception e) {
            MyLog.e(TAG, "设置RecyclerView横向布局失败", e);
        }
    }

    /**
     * 获取当前布局管理器
     * @return PagerGridLayoutManager实例
     */
    public PagerGridLayoutManager getLayoutManager() {
        return layoutManager;
    }

    /**
     * 获取尺寸缓存
     * @return LayoutDimensionCache实例
     */
    public LayoutDimensionCache getDimensionCache() {
        return dimensionCache;
    }
}
