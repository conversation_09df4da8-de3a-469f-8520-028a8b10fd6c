package com.smartcar.easylauncher.shared.adapter.common;


import android.content.Context;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.dragswipe.listener.DragAndSwipeDataCallback;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.data.model.system.AppInfo;

/**
 * 可拖拽的Adapter
 * <AUTHOR>
 */
public class DragAndSwipeAdapter extends BaseQuickAdapter<AppInfo, QuickViewHolder> implements DragAndSwipeDataCallback {

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup parent, int viewType) {
        return new QuickViewHolder(R.layout.item_draggable_view, parent);
    }

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder holder, int position, AppInfo item) {

        Glide.with(getContext()).load(item.getLogoPath()).into((ImageView) holder.getView(R.id.iv_icon));
        holder.setText(R.id.tv_name, item.getAppName());
        holder.setText(R.id.tv_path, item.getRemark());
    }

    @Override
    public void dataMove(int fromPosition, int toPosition) {
        move(fromPosition, toPosition);
    }

    @Override
    public void dataRemoveAt(int position) {
        removeAt(position);
    }
}
