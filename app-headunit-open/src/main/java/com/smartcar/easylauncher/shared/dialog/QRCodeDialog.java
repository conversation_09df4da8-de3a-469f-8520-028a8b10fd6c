package com.smartcar.easylauncher.shared.dialog;

import android.content.Context;
import android.graphics.Bitmap;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.FragmentManager;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;
import com.timmy.tdialog.TDialog;
import com.timmy.tdialog.base.BindViewHolder;
import com.timmy.tdialog.listener.OnBindViewListener;
import com.timmy.tdialog.listener.OnViewClickListener;

public class QRCodeDialog {

    public static void showQRCodeDialog(FragmentManager fragmentManager, Context context, 
                                        String title, String subtitle, String description, 
                                        Bitmap qrCodeBitmap, boolean showCancelButton,
                                        DialogClickListener listener) {
        if (fragmentManager == null || context == null || listener == null) {
            throw new IllegalArgumentException("Parameters cannot be null");
        }

        TDialog.Builder builder = new TDialog.Builder(fragmentManager)
                .setLayoutRes(R.layout.dialog_qrcode)
                .setWidth(DensityUtils.dp2px(context, 440))
                .addOnClickListener(R.id.okButton, R.id.cancelButton)
                .setCancelableOutside(true);

        builder.setOnViewClickListener(new OnViewClickListener() {
            @Override
            public void onViewClick(BindViewHolder viewHolder, View view, TDialog tDialog) {
                if (view.getId() == R.id.okButton) {
                    listener.onPositiveClick();
                    tDialog.dismiss();
                } else if (view.getId() == R.id.cancelButton) {
                    listener.onNegativeClick();
                    tDialog.dismiss();
                }
            }
        });

        builder.setOnBindViewListener(new OnBindViewListener() {
            @Override
            public void bindView(BindViewHolder viewHolder) {
                ImageView qrCodeImageView = viewHolder.getView(R.id.iv_qrcode);
                TextView titleTextView = viewHolder.getView(R.id.tv_title);
                TextView subtitleTextView = viewHolder.getView(R.id.tv_subtitle);
                TextView descriptionTextView = viewHolder.getView(R.id.tv_description);
                Button cancelButton = viewHolder.getView(R.id.cancelButton);

                if (title != null && !title.isEmpty()) {
                    titleTextView.setText(title);
                }
                
                if (subtitle != null && !subtitle.isEmpty()) {
                    subtitleTextView.setText(subtitle);
                } else {
                    subtitleTextView.setVisibility(View.GONE);
                }
                
                if (description != null && !description.isEmpty()) {
                    descriptionTextView.setText(description);
                }
                
                if (qrCodeBitmap != null) {
                    qrCodeImageView.setImageBitmap(qrCodeBitmap);
                }
                
                // 控制取消按钮的显示
                if (showCancelButton) {
                    cancelButton.setVisibility(View.VISIBLE);
                } else {
                    cancelButton.setVisibility(View.GONE);
                }
            }
        });

        TDialog dialog = builder.create();
        try {
            dialog.show(fragmentManager);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 简化版本的方法，兼容旧的调用方式
    public static void showQRCodeDialog(FragmentManager fragmentManager, Context context, 
                                        String title, String description, DialogClickListener listener) {
        showQRCodeDialog(fragmentManager, context, title, null, description, null, false, listener);
    }
    
    // 带取消按钮的简化版本
    public static void showQRCodeDialogWithCancel(FragmentManager fragmentManager, Context context, 
                                                 String title, String description, DialogClickListener listener) {
        showQRCodeDialog(fragmentManager, context, title, null, description, null, true, listener);
    }

    public interface DialogClickListener {
        void onPositiveClick();
        
        // 提供默认实现，使旧代码不需要实现此方法
        default void onNegativeClick() {
            // 默认空实现
        }
    }
}