package com.smartcar.easylauncher.shared.adapter.task

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.chad.library.adapter4.BaseQuickAdapter
import com.smartcar.easylauncher.R
import com.smartcar.easylauncher.core.constants.TaskResultConst
import com.smartcar.easylauncher.databinding.ItemHeaderAndFooterBinding
import com.smartcar.easylauncher.data.model.task.ResultModel

/**
 * https://github.com/CymChad/BaseRecyclerViewAdapterHelper
 */
class NewResultAddAdapter : BaseQuickAdapter<ResultModel, NewResultAddAdapter.VH> {
    constructor(list: List<ResultModel>) : super(list)

    constructor() : super(emptyList())
    class VH(var binding: ItemHeaderAndFooterBinding) : RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(context: Context, parent: ViewGroup, viewType: Int): VH {
        val binding =
            ItemHeaderAndFooterBinding.inflate(LayoutInflater.from(context), parent, false)
        return VH(binding)
    }

    override fun onBindViewHolder(holder: VH, position: Int, item: ResultModel?) {
        holder.binding.gridItemAppName.text = item?.name
        holder.binding.gridItemNumber.text = item?.execute
        when (item?.id) {
            TaskResultConst.RESULT_ID_DELAY -> {
                holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_yanshi)
                //holder.setImageResource(R.id.iv_icon, R.drawable.ic_task_wifi);
                Glide.with(context).load(R.drawable.ic_task_yanshi)
                    .into(holder.binding.gridItemAppIcon)
                holder.binding.gridItemNumber.text = "" + item.delayTime + item.execute
            }

            TaskResultConst.RESULT_ID_WIFI  -> {
                //   holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_wifi_switch)
                Glide.with(context).load(R.drawable.ic_task_wifi_switch)
                    .into(holder.binding.gridItemAppIcon)
            }

            TaskResultConst.RESULT_ID_BLUETOOTH  -> {
                //   holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_ble_switch)
                Glide.with(context).load(R.drawable.ic_task_ble_switch)
                    .into(holder.binding.gridItemAppIcon)
            }

            TaskResultConst.RESULT_ID_VOLUME  -> {
                //    holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_shengyin)
                Glide.with(context).load(R.drawable.ic_task_shengyin)
                    .into(holder.binding.gridItemAppIcon)
            }

            TaskResultConst.RESULT_ID_BRIGHTNESS  -> {
                //  holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_zhuti)
                Glide.with(context).load(R.drawable.ic_task_zhuti)
                    .into(holder.binding.gridItemAppIcon)
            }

            TaskResultConst.RESULT_ID_THEME  -> {
                //      holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_liangdu)
                Glide.with(context).load(R.drawable.ic_task_liangdu)
                    .into(holder.binding.gridItemAppIcon)
            }

            TaskResultConst.RESULT_ID_APP  -> {
                //    holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_app)
                Glide.with(context).load(R.drawable.ic_task_app)
                    .into(holder.binding.gridItemAppIcon)
            }

            TaskResultConst.RESULT_ID_NAV  -> {
                //   holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_kuaijiedaohagn)
                Glide.with(context).load(R.drawable.ic_task_kuaijiedaohagn)
                    .into(holder.binding.gridItemAppIcon)
            }
            TaskResultConst.RESULT_ID_MUSIC  -> {
                //   holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_yinyue)
                Glide.with(context).load(R.drawable.ic_task_yinyue)
                    .into(holder.binding.gridItemAppIcon)
            }
            TaskResultConst.RESULT_ID_TIP  -> {
                //   holder.binding.gridItemAppIcon.setImageResource(R.drawable.ic_task_yinyue)
                Glide.with(context).load(R.drawable.ic_task_tip)
                    .into(holder.binding.gridItemAppIcon)
            }

            else -> {}
        }
    }

}