package com.smartcar.easylauncher.shared.utils;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.smartcar.easylauncher.app.App;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.data.model.system.AppInfo;
import com.smartcar.easylauncher.infrastructure.skin.SkinManager;
import com.smartcar.easylauncher.shared.utils.file.FileUtils;
import com.smartcar.easylauncher.shared.utils.music.MusicAppChecker;
import com.smartcar.easylauncher.shared.utils.thread.ThreadPoolUtil;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import java9.util.concurrent.CompletableFuture;

/**
 * 应用信息提供者，负责管理和获取应用信息
 */
public class AppInfoProvider {
    // 常量定义
    private static final String TAG = "AppInfoProvider";
    private static final String APP_NO_FILTER = "no_package_filter";
    private static final String LOGO_CACHE_DIR = "/logoCache/";
    private static final long CACHE_EXPIRY_TIME = 5 * 60 * 1000; // 5分钟缓存过期时间
    private static final int APP_TYPE_FUNCTION = 0;   // 功能应用
    private static final int APP_TYPE_SYSTEM = 1;     // 系统应用
    private static final int APP_TYPE_THIRD_PARTY = 2; // 第三方应用
    private static final int EXECUTE_TYPE_NORMAL = 0; // 普通执行
    private static final int EXECUTE_TYPE_SPECIAL = 1; // 特殊执行

    // 单例实例
    private static volatile AppInfoProvider instance;

    // 包管理器
    private PackageManager mPackageManager;

    // 弱引用上下文，防止内存泄漏
    private WeakReference<Context> mContextRef;

    // 应用列表
    private List<AppInfo> mlistAppInfoSystem = new ArrayList<>();
    private List<AppInfo> mlistAppInfoThrid = new ArrayList<>();
    private List<AppInfo> mlistAppInfoFunction = new ArrayList<>();
    private List<AppInfo> mlistAppInfo = new ArrayList<>();

    // 图标缓存，避免重复读取和创建图标文件
    private Map<String, String> mIconPathCache = new ConcurrentHashMap<>();

    // 缓存标志，避免频繁读写本地存储
    private boolean mDataDirty = true;
    private long mLastUpdateTime = 0;

    // ResolveInfo列表
    private List<ResolveInfo> resolveInfos;
    private Intent mainIntent;

    // 私有构造函数，防止外部实例化
    private AppInfoProvider(Context context) {
        Context appContext = context.getApplicationContext();
        this.mContextRef = new WeakReference<>(appContext);
        this.mPackageManager = appContext.getPackageManager();
    }

    // 获取单例实例
    public static AppInfoProvider getInstance(Context context) {
        if (instance == null) {
            synchronized (AppInfoProvider.class) {
                if (instance == null) {
                    instance = new AppInfoProvider(context);
                }
            }
        }
        return instance;
    }

    // 获取Context对象，避免空指针
    private Context getContext() {
        Context context = mContextRef.get();
        if (context == null) {
            // 如果Context为空，使用ApplicationContext
            context = App.getContextInstance();
            // 重新设置弱引用和包管理器
            if (context != null) {
                mContextRef = new WeakReference<>(context);
                mPackageManager = context.getPackageManager();
            }
        }
        return context;
    }

    /**
     * 查询所有启动Activity
     */
    private synchronized void initAllActivity() {
        // 检查Context和PackageManager是否有效
        Context context = getContext();
        if (context == null) {
            MyLog.e(TAG, "Context为空，无法初始化应用列表");
            resolveInfos = new ArrayList<>();
            return;
        }

        if (mPackageManager == null) {
            mPackageManager = context.getPackageManager();
        }

        if (mainIntent == null) {
            mainIntent = new Intent(Intent.ACTION_MAIN, null);
            mainIntent.addCategory(Intent.CATEGORY_LAUNCHER);
        }

        // 查询所有启动Activity
        try {
            resolveInfos = mPackageManager.queryIntentActivities(mainIntent, PackageManager.COMPONENT_ENABLED_STATE_DEFAULT);
            // 按名称排序，这是系统默认的排序方式
            Collections.sort(resolveInfos, new ResolveInfo.DisplayNameComparator(mPackageManager));
            MyLog.v(TAG, "成功初始化应用列表，共找到 " + resolveInfos.size() + " 个应用");
        } catch (Exception e) {
            MyLog.e(TAG, "初始化应用列表失败: " + e.getMessage());
            resolveInfos = new ArrayList<>();
        }
    }

    /**
     * 查询所有应用信息
     */
    public CompletableFuture<List<AppInfo>> queryAppInfo() {
        // 检查缓存是否有效
        if (!mDataDirty && mlistAppInfo != null && !mlistAppInfo.isEmpty() &&
                System.currentTimeMillis() - mLastUpdateTime < CACHE_EXPIRY_TIME) {
            return CompletableFuture.completedFuture(new ArrayList<>(mlistAppInfo));  // 返回副本避免并发修改
        }

        // 异步执行查询任务
        return CompletableFuture.supplyAsync(() -> {
            synchronized (this) {  // 添加同步块，防止并发修改异常
                try {
                    if (resolveInfos == null || resolveInfos.isEmpty()) {
                        initAllActivity();
                    }
                    List<AppInfo> newAppInfoList = new ArrayList<>();
                    List<AppInfo> newAppInfoSystem = new ArrayList<>();
                    List<AppInfo> newAppInfoThrid = new ArrayList<>();

                    for (ResolveInfo reInfo : resolveInfos) {
                        String pkgName = reInfo.activityInfo.packageName;
                        AppInfo appInfo = null;
                        if (TextUtils.equals(pkgName, APP_NO_FILTER) || TextUtils.equals(APP_NO_FILTER, APP_NO_FILTER)) {
                            appInfo = createAppInfoFromResolveInfo(reInfo);
                        }
                        if (appInfo != null) {
                            if (App.getContextInstance().getPackageName().equals(appInfo.getPackageName())) {
                                continue;
                            }
                            if (appInfo.isSystem()) {
                                newAppInfoSystem.add(appInfo);
                            } else {
                                newAppInfoThrid.add(appInfo);
                            }
                        }
                    }
                    newAppInfoList.addAll(getFunctionInfo());
                    newAppInfoList.addAll(newAppInfoSystem);
                    newAppInfoList.addAll(newAppInfoThrid);
                    String appinfo = new Gson().toJson(newAppInfoList);
                    DataManager.setAppListData(appinfo);

                    // 更新缓存状态
                    mlistAppInfoSystem = newAppInfoSystem;
                    mlistAppInfoThrid = newAppInfoThrid;
                    mlistAppInfo = newAppInfoList;
                    mDataDirty = false;
                    mLastUpdateTime = System.currentTimeMillis();

                    return new ArrayList<>(newAppInfoList);  // 返回副本避免并发修改
                } catch (Exception e) {
                    // 如果发生异常，将异常包装为一个 RuntimeException 并抛出
                    throw new RuntimeException(e);
                }
            }
        });
    }

    /**
     * 获取图标路径，使用缓存机制
     */
    private String getAppIconPath(String packageName, Drawable icon) {
        if (TextUtils.isEmpty(packageName) || icon == null) {
            return null;
        }

        // 先从缓存获取
        if (mIconPathCache.containsKey(packageName)) {
            String cachedPath = mIconPathCache.get(packageName);
            File iconFile = new File(cachedPath);
            if (iconFile.exists()) {
                return cachedPath;
            }
        }

        try {
            Context context = getContext();
            if (context == null) {
                return null;
            }

            String sdCardDir = FileUtils.getSDPath(context);
            if (TextUtils.isEmpty(sdCardDir)) {
                return null;
            }

            sdCardDir += LOGO_CACHE_DIR;
            File cacheDir = new File(sdCardDir);
            if (!cacheDir.exists()) {
                cacheDir.mkdirs();
            }

            FileUtils.addIgnore(sdCardDir);
            Bitmap bmp = FileUtils.drawableToBitmap(icon);
            String logoPath = FileUtils.saveBitmap(bmp, context, sdCardDir, packageName + ".png");

            // 缓存路径
            if (!TextUtils.isEmpty(logoPath)) {
                mIconPathCache.put(packageName, logoPath);
            }

            return logoPath;
        } catch (Exception e) {
            MyLog.e(TAG, "保存应用图标失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 提取公共方法，减少代码重复
     */
    private AppInfo createAppInfoFromResolveInfo(ResolveInfo reInfo) {
        try {
            String pkgName = reInfo.activityInfo.packageName;
            String activityName = reInfo.activityInfo.name;
            String appLabel = (String) reInfo.loadLabel(mPackageManager);
            Drawable icon = reInfo.loadIcon(mPackageManager);
            Intent launchIntent = new Intent();
            launchIntent.setComponent(new ComponentName(pkgName, activityName));

            // 获取并缓存图标
            String logoPath = getAppIconPath(pkgName, icon);

            AppInfo appInfo = new AppInfo();
            appInfo.setPackageName(pkgName);
            appInfo.setAppName(appLabel);
            appInfo.setActivityName(activityName);
            appInfo.setIntent(launchIntent);
            appInfo.setLogoPath(logoPath);
            appInfo.setState(0);
            appInfo.setExecuteType(EXECUTE_TYPE_NORMAL);

            // 判断是否是音乐应用
            boolean isMusicApp = MusicAppChecker.isMusicApp(appLabel);
            appInfo.setMusicApp(isMusicApp);

            PackageInfo mPackageInfo = getContext().getPackageManager().getPackageInfo(pkgName, 0);
            if ((mPackageInfo.applicationInfo.flags & ApplicationInfo.FLAG_SYSTEM) <= 0) {
                appInfo.setSystem(false);
                appInfo.setType(APP_TYPE_THIRD_PARTY);
            } else {
                appInfo.setSystem(true);
                appInfo.setType(APP_TYPE_SYSTEM);
            }

            return appInfo;
        } catch (PackageManager.NameNotFoundException e) {
            MyLog.v(TAG, "包名未找到: " + e.getMessage());
            return null;
        } catch (Exception e) {
            MyLog.v(TAG, "创建AppInfo异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 查询所有应用信息
     */
    public CompletableFuture<List<AppInfo>> queryAllAppInfo() {
        // 使用缓存变量
        if (!mDataDirty && mlistAppInfo != null && !mlistAppInfo.isEmpty() &&
                System.currentTimeMillis() - mLastUpdateTime < CACHE_EXPIRY_TIME) {
            return CompletableFuture.completedFuture(new ArrayList<>(mlistAppInfo));  // 返回副本避免并发修改
        }

        // 异步执行查询任务
        return CompletableFuture.supplyAsync(() -> {
            synchronized (this) {  // 添加同步块，防止并发修改异常
                try {
                    if (resolveInfos == null || resolveInfos.isEmpty()) {
                        initAllActivity();
                    }
                    List<AppInfo> newAppInfoList = new ArrayList<>();
                    List<AppInfo> newAppInfoSystem = new ArrayList<>();
                    List<AppInfo> newAppInfoThrid = new ArrayList<>();

                    for (ResolveInfo reInfo : resolveInfos) {
                        String pkgName = reInfo.activityInfo.packageName;
                        AppInfo appInfo = null;
                        if (TextUtils.equals(pkgName, APP_NO_FILTER) || TextUtils.equals(APP_NO_FILTER, APP_NO_FILTER)) {
                            appInfo = createAppInfoFromResolveInfo(reInfo);
                        }
                        if (appInfo != null) {
                            if (App.getContextInstance().getPackageName().equals(appInfo.getPackageName())) {
                                continue;
                            }
                            if (appInfo.isSystem()) {
                                newAppInfoSystem.add(appInfo);
                            } else {
                                newAppInfoThrid.add(appInfo);
                            }
                        }
                    }
                    newAppInfoList.addAll(getFunctionInfo());
                    newAppInfoList.addAll(newAppInfoSystem);
                    newAppInfoList.addAll(newAppInfoThrid);

                    // 更新缓存状态但不保存到本地存储
                    mlistAppInfoSystem = newAppInfoSystem;
                    mlistAppInfoThrid = newAppInfoThrid;
                    mlistAppInfo = newAppInfoList;
                    mDataDirty = false;
                    mLastUpdateTime = System.currentTimeMillis();

                    return new ArrayList<>(newAppInfoList);  // 返回副本避免并发修改
                } catch (Exception e) {
                    // 如果发生异常，将异常包装为一个 RuntimeException 并抛出
                    throw new RuntimeException(e);
                }
            }
        });
    }

    /**
     * 同步版本的查询所有应用信息方法
     * 用于避免在refreshAppInfo中的嵌套异步调用导致的死锁问题
     */
    private synchronized List<AppInfo> queryAllAppInfoSync() {
        try {
            if (resolveInfos == null || resolveInfos.isEmpty()) {
                initAllActivity();
            }
            List<AppInfo> newAppInfoList = new ArrayList<>();
            List<AppInfo> newAppInfoSystem = new ArrayList<>();
            List<AppInfo> newAppInfoThrid = new ArrayList<>();

            for (ResolveInfo reInfo : resolveInfos) {
                String pkgName = reInfo.activityInfo.packageName;
                AppInfo appInfo = null;
                if (TextUtils.equals(pkgName, APP_NO_FILTER) || TextUtils.equals(APP_NO_FILTER, APP_NO_FILTER)) {
                    appInfo = createAppInfoFromResolveInfo(reInfo);
                }
                if (appInfo != null) {
                    if (App.getContextInstance().getPackageName().equals(appInfo.getPackageName())) {
                        continue;
                    }
                    if (appInfo.isSystem()) {
                        newAppInfoSystem.add(appInfo);
                    } else {
                        newAppInfoThrid.add(appInfo);
                    }
                }
            }
            newAppInfoList.addAll(getFunctionInfo());
            newAppInfoList.addAll(newAppInfoSystem);
            newAppInfoList.addAll(newAppInfoThrid);

            return newAppInfoList;
        } catch (Exception e) {
            MyLog.e(TAG, "同步查询应用信息失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    public CompletableFuture<List<AppInfo>> refreshAppInfo() {
        // 标记数据为脏数据，强制刷新
        mDataDirty = true;

        return CompletableFuture.supplyAsync(() -> {
            try {
                // 获取新的应用信息列表 - 避免嵌套同步调用导致死锁
                List<AppInfo> newAppInfoList = queryAllAppInfoSync();  // 使用同步版本避免死锁

                // 从本地获取旧的应用信息列表
                String oldAppInfoJson = DataManager.getAppListData();
                List<AppInfo> oldAppInfoList = null;
                if (!TextUtils.isEmpty(oldAppInfoJson)) {
                    try {
                        oldAppInfoList = new Gson().fromJson(oldAppInfoJson, new TypeToken<List<AppInfo>>() {
                        }.getType());
                    } catch (Exception e) {
                        MyLog.e(TAG, "解析旧应用数据失败: " + e.getMessage());
                        oldAppInfoList = new ArrayList<>();
                    }
                } else {
                    oldAppInfoList = new ArrayList<>();
                }

                // 创建一个HashMap来存储新的应用信息
                Map<String, AppInfo> newAppInfoMap = new HashMap<>();
                for (AppInfo newApp : newAppInfoList) {
                    newAppInfoMap.put(newApp.getActivityName(), newApp);
                }

                // 创建两个新的列表来存储最终的应用信息
                List<AppInfo> finalAppInfoListType0 = new ArrayList<>();
                List<AppInfo> finalAppInfoListOtherTypes = new ArrayList<>();

                // 遍历旧的应用信息列表
                for (AppInfo oldApp : oldAppInfoList) {
                    if (oldApp == null || TextUtils.isEmpty(oldApp.getActivityName())) {
                        continue;
                    }

                    AppInfo newApp = newAppInfoMap.get(oldApp.getActivityName());

                    // 如果在新的应用信息中找到了旧的应用信息，那么使用新的应用信息但保留旧的状态
                    if (newApp != null) {
                        // 保留用户设置的状态
                        newApp.setState(oldApp.getState());

                        if (newApp.getType() == 0) {
                            finalAppInfoListType0.add(newApp);
                        } else {
                            finalAppInfoListOtherTypes.add(newApp);
                        }
                        newAppInfoMap.remove(oldApp.getActivityName());
                    }
                }

                // 添加新数据中存在而旧数据中没有的应用信息到尾部
                for (Map.Entry<String, AppInfo> entry : newAppInfoMap.entrySet()) {
                    if (entry.getValue().getType() == 0) {
                        finalAppInfoListType0.add(entry.getValue());
                    } else {
                        finalAppInfoListOtherTypes.add(entry.getValue());
                    }
                }

                // 将类型为0的应用信息和其他类型的应用信息合并到一个列表中
                finalAppInfoListType0.addAll(finalAppInfoListOtherTypes);

                // 将最终的应用信息列表保存到本地
                String finalAppInfoJson = new Gson().toJson(finalAppInfoListType0);
                DataManager.setAppListData(finalAppInfoJson);

                // 更新内存缓存
                synchronized (this) {  // 添加同步块，防止并发修改
                    mlistAppInfo = new ArrayList<>(finalAppInfoListType0);  // 创建副本避免并发修改
                    mDataDirty = false;
                    mLastUpdateTime = System.currentTimeMillis();
                }

                MyLog.v(TAG, "刷新应用信息成功");
                return finalAppInfoListType0;
            } catch (Exception e) {
                MyLog.e(TAG, "刷新应用信息失败: " + e.getMessage());
                throw new RuntimeException(e);
            }
        });
    }

    public boolean isMusicApp(String packageName, Context context) {
        if (TextUtils.isEmpty(packageName) || context == null) {
            return false;
        }

        try {
            PackageManager pm = context.getPackageManager();
            Intent intent = new Intent("android.intent.action.MEDIA_BUTTON");
            List<ResolveInfo> resolveInfos = pm.queryBroadcastReceivers(intent, 0);
            for (ResolveInfo resolveInfo : resolveInfos) {
                if (resolveInfo.activityInfo.packageName.equals(packageName)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            MyLog.e(TAG, "检查音乐应用失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 根据包名通过resolveInfos获取应用信息
     */
    public void getAppInfo(String packageName) {
        if (TextUtils.isEmpty(packageName)) {
            MyLog.e(TAG, "包名为空，无法获取应用信息");
            return;
        }

        ThreadPoolUtil.getThreadPoolExecutor().execute(() -> {
            synchronized (this) {  // 添加同步块，防止并发修改异常
                // 先检查内存缓存
                boolean appExists = false;
                if (mlistAppInfo != null && !mlistAppInfo.isEmpty()) {
                    for (AppInfo app : mlistAppInfo) {
                        if (TextUtils.equals(app.getPackageName(), packageName)) {
                            appExists = true;
                            break;
                        }
                    }
                } else {
                    // 如果内存缓存为空，则从本地存储获取
                    String appListJson = DataManager.getAppListData();
                    if (!TextUtils.isEmpty(appListJson)) {
                        try {
                            mlistAppInfo = new Gson().fromJson(appListJson, new TypeToken<List<AppInfo>>() {
                            }.getType());
                            for (AppInfo app : mlistAppInfo) {
                                if (TextUtils.equals(app.getPackageName(), packageName)) {
                                    appExists = true;
                                    break;
                                }
                            }
                        } catch (Exception e) {
                            MyLog.e(TAG, "解析应用列表失败: " + e.getMessage());
                            mlistAppInfo = new ArrayList<>();
                        }
                    } else {
                        mlistAppInfo = new ArrayList<>();
                    }
                }

                if (appExists) {
                    MyLog.v(TAG, "已经存在该应用,不需要添加");
                    return;
                }

                AppInfo appInfo = null;
                if (resolveInfos == null || resolveInfos.isEmpty()) {
                    initAllActivity();
                }

                for (ResolveInfo reInfo : resolveInfos) {
                    if (TextUtils.equals(reInfo.activityInfo.packageName, packageName)) {
                        appInfo = createAppInfoFromResolveInfo(reInfo);
                        if (appInfo != null) {
                            appInfo.setType(APP_TYPE_THIRD_PARTY); // 设置为第三方应用
                            break;
                        }
                    }
                }

                if (appInfo == null) {
                    MyLog.e(TAG, "未找到应用信息: " + packageName);
                    return;
                }

                // 添加到内存缓存和本地存储
                List<AppInfo> newAppInfoList = new ArrayList<>(mlistAppInfo);  // 创建副本避免并发修改
                newAppInfoList.add(appInfo);
                mlistAppInfo = newAppInfoList;
                String appInfoJson = new Gson().toJson(mlistAppInfo);
                DataManager.setAppListData(appInfoJson);
                mDataDirty = false;
                mLastUpdateTime = System.currentTimeMillis();
            }
        });
    }

    /**
     * 根据包名删除应用
     */
    public void deleteApp(String packageName) {
        if (TextUtils.isEmpty(packageName)) {
            MyLog.e(TAG, "包名为空，无法删除应用");
            return;
        }

        ThreadPoolUtil.getThreadPoolExecutor().execute(() -> {
            synchronized (this) {  // 添加同步块，防止并发修改异常
                boolean removed = false;

                // 先检查内存缓存
                if (mlistAppInfo != null && !mlistAppInfo.isEmpty()) {
                    List<AppInfo> newAppInfoList = new ArrayList<>(mlistAppInfo);  // 创建副本避免并发修改
                    for (int i = 0; i < newAppInfoList.size(); i++) {
                        AppInfo appInfo = newAppInfoList.get(i);
                        if (TextUtils.equals(appInfo.getPackageName(), packageName)) {
                            newAppInfoList.remove(i);
                            mlistAppInfo = newAppInfoList;
                            removed = true;
                            break;
                        }
                    }
                } else {
                    // 如果内存缓存为空，则从本地存储获取
                    String appListJson = DataManager.getAppListData();
                    if (!TextUtils.isEmpty(appListJson)) {
                        try {
                            List<AppInfo> appInfoList = new Gson().fromJson(appListJson, new TypeToken<List<AppInfo>>() {
                            }.getType());
                            for (int i = 0; i < appInfoList.size(); i++) {
                                AppInfo appInfo = appInfoList.get(i);
                                if (TextUtils.equals(appInfo.getPackageName(), packageName)) {
                                    appInfoList.remove(i);
                                    mlistAppInfo = appInfoList;
                                    removed = true;
                                    break;
                                }
                            }
                        } catch (Exception e) {
                            MyLog.e(TAG, "解析应用列表失败: " + e.getMessage());
                            mlistAppInfo = new ArrayList<>();
                        }
                    } else {
                        mlistAppInfo = new ArrayList<>();
                    }
                }

                if (removed) {
                    String appInfoJson = new Gson().toJson(mlistAppInfo);
                    DataManager.setAppListData(appInfoJson);
                    MyLog.i(TAG, "成功删除应用: " + packageName);
                } else {
                    MyLog.i(TAG, "未找到要删除的应用: " + packageName);
                }
            }
        });
    }

    /**
     * 创建应用信息
     *
     * @param appName      应用名称
     * @param remark       应用备注
     * @param activityName 应用的Activity名称
     * @param type         类型
     * @param state        状态
     * @param drawableId   应用图标资源ID
     * @param executeType  执行类型
     * @param pageType     页面类型：0=Activity, 1=Scene, 2=通用
     * @return 应用信息对象
     */
    private AppInfo createAppInfo(String appName, String remark, String activityName, int type, int state, int drawableId, int executeType, int pageType) {
        Intent launchIntent = new Intent();
        launchIntent.setComponent(new ComponentName("com.smartcar.easylauncher", activityName));
        String cacheKey = "com.smartcar.easylauncher" + "_" + appName;
        String logoPath = null;
        if (mIconPathCache.containsKey(cacheKey)) {
            logoPath = mIconPathCache.get(cacheKey);
            File iconFile = new File(logoPath);
            if (!iconFile.exists()) {
                logoPath = null;
            }
        }
        if (logoPath == null) {
            String sdCardDir = FileUtils.getSDPath(getContext());
            if (!TextUtils.isEmpty(sdCardDir)) {
                sdCardDir += LOGO_CACHE_DIR;
                File cacheDir = new File(sdCardDir);
                if (!cacheDir.exists()) {
                    cacheDir.mkdirs();
                }
                FileUtils.addIgnore(sdCardDir);
                Bitmap bmp = FileUtils.drawableToBitmap(SkinManager.getInstance().getDrawable(drawableId));
                logoPath = FileUtils.saveBitmap(bmp, getContext(), sdCardDir, appName + ".png");
                if (!TextUtils.isEmpty(logoPath)) {
                    mIconPathCache.put(cacheKey, logoPath);
                }
            }
        }
        AppInfo appInfo = new AppInfo();
        appInfo.setPackageName("com.smartcar.easylauncher");
        appInfo.setAppName(appName);
        appInfo.setRemark(remark);
        appInfo.setActivityName(activityName);
        appInfo.setIntent(launchIntent);
        appInfo.setLogoPath(logoPath);
        appInfo.setSystem(true);
        appInfo.setType(type);
        appInfo.setState(state);
        appInfo.setExecuteType(executeType);
        appInfo.setPageType(pageType);
        return appInfo;
    }

    public List<AppInfo> getFunctionInfo() {
        synchronized (this) {  // 添加同步块，防止并发修改异常
            if (!mlistAppInfoFunction.isEmpty()) {
                return new ArrayList<>(mlistAppInfoFunction);  // 返回副本避免并发修改
            }
            mlistAppInfoFunction = new ArrayList<>();
            try {
                // 主页、更多、主题、设置等为Activity页面
                AppInfo main = createAppInfo("主页", "", "com.smartcar.easylauncher.ui.home.HomeActivity", APP_TYPE_FUNCTION, 0, R.drawable.home_more_ic_home_selector, EXECUTE_TYPE_NORMAL, 0);
                //  AppInfo more = createAppInfo("更多", "", "com.smartcar.easylauncher.ui.more.MoreActivity", APP_TYPE_FUNCTION, 0, R.drawable.home_more_ic_application_selector, EXECUTE_TYPE_NORMAL, 0);
                //  AppInfo more = createAppInfo("更多", "", MoreScene.class.getSimpleName(), APP_TYPE_FUNCTION, 0, R.drawable.home_more_ic_application_selector, EXECUTE_TYPE_NORMAL, 1);
                // Scene类型功能入口，activityName字段存注册表key（如"MoreScene"）
                AppInfo more = createAppInfo("更多", "", "MoreScene", APP_TYPE_FUNCTION, 0, R.drawable.home_more_ic_application_selector, EXECUTE_TYPE_NORMAL, 1);
                AppInfo theme = createAppInfo("主题", "", "ThemeTabNavigationScene", APP_TYPE_FUNCTION, 0, R.drawable.home_more_ic_theme_selector, EXECUTE_TYPE_NORMAL, 1);
                AppInfo settingAppInfo2 = createAppInfo("智车设置", "", "SettingTabNavigationScene", APP_TYPE_FUNCTION, 0, R.drawable.home_more_ic_setting_selector, EXECUTE_TYPE_NORMAL, 1);
                AppInfo settingAppInfo4 = createAppInfo("按键映射", "", "KeySettingTabNavigationScene", APP_TYPE_FUNCTION, 0, R.drawable.home_more_ic_setting_selector, EXECUTE_TYPE_NORMAL, 1);
                AppInfo smartAppInfo = createAppInfo("智能任务", "", "TaskTabNavigationScene", APP_TYPE_FUNCTION, 0, R.drawable.home_more_ic_smart_task_selector, EXECUTE_TYPE_NORMAL, 1);
                AppInfo fastTransferAppInfo = createAppInfo("闪传", "局域网传输，摆脱U盘，", "FastTransferScene", APP_TYPE_FUNCTION, 0, R.drawable.home_more_ic_fast_transfer_selector, EXECUTE_TYPE_NORMAL, 1);

                AppInfo smartFloat = createAppInfo("悬浮球", "", "com.smartcar.easylauncher.modules.touch.ui.activity.SmartFloatActivity", APP_TYPE_FUNCTION, 0, R.drawable.home_more_ic_float_selector, EXECUTE_TYPE_NORMAL, 0);
                AppInfo accountAppInfokt = createAppInfo("个人中心", "", "PersonalSceneKt", APP_TYPE_FUNCTION, 0, R.drawable.home_more_ic_account_selector, EXECUTE_TYPE_NORMAL, 1);
                AppInfo screenCleanAppInfo = createAppInfo("屏幕清洁", "清洁车机屏幕", "ScreenCleanScene", APP_TYPE_FUNCTION, 0, R.drawable.ic_screen_clean, EXECUTE_TYPE_NORMAL, 1);
                AppInfo concert = createAppInfo("昼夜转换", "", "day_and_night", APP_TYPE_FUNCTION, 0, R.drawable.ic_menu_convert, EXECUTE_TYPE_SPECIAL, 2);
                mlistAppInfoFunction.add(main);
                mlistAppInfoFunction.add(more);
                mlistAppInfoFunction.add(theme);
                mlistAppInfoFunction.add(settingAppInfo2);
                mlistAppInfoFunction.add(settingAppInfo4);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    AppInfo gestureAppInfo = createAppInfo("全面触控", "手机一样的体验", "com.smartcar.easylauncher.gesture.other.GestureSettingActivity", APP_TYPE_FUNCTION, 0, R.drawable.home_more_ic_gesture_selector, EXECUTE_TYPE_NORMAL, 0);
                    mlistAppInfoFunction.add(gestureAppInfo);
                }
                mlistAppInfoFunction.add(smartAppInfo);
                mlistAppInfoFunction.add(fastTransferAppInfo);
                mlistAppInfoFunction.add(smartFloat);
                mlistAppInfoFunction.add(accountAppInfokt);
                mlistAppInfoFunction.add(screenCleanAppInfo);
                mlistAppInfoFunction.add(concert);
                List<ResolveInfo> allLaunchers = getAllLaunchers(getContext());
                if (!allLaunchers.isEmpty()) {
                    AppInfo launchersAppInfo = createLaunchersAppInfo(allLaunchers.get(0), APP_TYPE_FUNCTION, 0, R.drawable.home_more_ic_launcher_selector, EXECUTE_TYPE_NORMAL);
                    launchersAppInfo.setPageType(2); // 通用
                    mlistAppInfoFunction.add(launchersAppInfo);
                }
            } catch (Exception e) {
                MyLog.e(TAG, "创建功能应用列表失败: " + e.getMessage());
            }
            return new ArrayList<>(mlistAppInfoFunction);  // 返回副本避免并发修改
        }
    }

    /**
     * 原生桌面
     *
     * @param allLaunchers 原生桌面
     * @param type         类型
     * @param state        状态
     * @param drawableId   应用图标资源ID
     * @return 应用信息对象
     */
    private AppInfo createLaunchersAppInfo(ResolveInfo allLaunchers, int type, int state, int drawableId, int executeType) {
        ActivityInfo activityInfo = allLaunchers.activityInfo;
        ComponentName componentName = new ComponentName(activityInfo.packageName, activityInfo.name);
        Intent intent = new Intent(Intent.ACTION_MAIN);
        intent.addCategory(Intent.CATEGORY_HOME);
        intent.setComponent(componentName);

        // 缓存key
        String cacheKey = activityInfo.packageName + "_launcher";

        String logoPath = null;
        // 先从缓存获取图标路径
        if (mIconPathCache.containsKey(cacheKey)) {
            logoPath = mIconPathCache.get(cacheKey);
            File iconFile = new File(logoPath);
            if (!iconFile.exists()) {
                logoPath = null;
            }
        }

        // 缓存不存在，重新生成图标文件
        if (logoPath == null) {
            // 获取SD卡路径并创建logoCache文件夹
            String sdCardDir = FileUtils.getSDPath(getContext());
            if (!TextUtils.isEmpty(sdCardDir)) {
                sdCardDir += LOGO_CACHE_DIR;
                File cacheDir = new File(sdCardDir);
                if (!cacheDir.exists()) {
                    cacheDir.mkdirs();
                }

                FileUtils.addIgnore(sdCardDir);

                // 获取图标并保存到SD卡
                Bitmap bmp = FileUtils.drawableToBitmap(SkinManager.getInstance().getDrawable(drawableId));
                logoPath = FileUtils.saveBitmap(bmp, getContext(), sdCardDir, activityInfo.packageName + ".png");

                // 添加到缓存
                if (!TextUtils.isEmpty(logoPath)) {
                    mIconPathCache.put(cacheKey, logoPath);
                }
            }
        }

        // 创建一个AppInfo对象，用于存储应用信息
        AppInfo appInfo = new AppInfo();
        appInfo.setPackageName(activityInfo.packageName);
        appInfo.setAppName((String) allLaunchers.loadLabel(mPackageManager));
        appInfo.setActivityName(activityInfo.name);
        appInfo.setIntent(intent);
        appInfo.setLogoPath(logoPath);
        appInfo.setSystem(true);
        appInfo.setType(type);
        appInfo.setState(state);
        appInfo.setExecuteType(executeType);
        return appInfo;
    }


    public List<ResolveInfo> getAllLaunchers(Context context) {
        if (context == null) {
            return new ArrayList<>();
        }

        try {
            Intent intent = new Intent(Intent.ACTION_MAIN);
            intent.addCategory(Intent.CATEGORY_HOME);
            PackageManager pm = context.getPackageManager();
            return pm.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY);
        } catch (Exception e) {
            MyLog.e(TAG, "获取原生桌面列表失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 清理资源，释放内存
     */
    public void release() {
        // 清空所有缓存数据
        if (mlistAppInfo != null) {
            mlistAppInfo.clear();
        }
        if (mlistAppInfoSystem != null) {
            mlistAppInfoSystem.clear();
        }
        if (mlistAppInfoThrid != null) {
            mlistAppInfoThrid.clear();
        }
        if (mlistAppInfoFunction != null) {
            mlistAppInfoFunction.clear();
        }
        if (mIconPathCache != null) {
            mIconPathCache.clear();
        }
        if (resolveInfos != null) {
            resolveInfos.clear();
        }

        // 标记数据为脏数据，下次需要重新加载
        mDataDirty = true;
        mLastUpdateTime = 0;
    }
}