package com.smartcar.easylauncher.shared.widget.layout;

import android.content.Context;

import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;

/**
 * 布局尺寸缓存类
 * 用于缓存常用的尺寸计算结果，避免重复计算，提升性能
 * <p>
 * 设计目标：
 * - 减少重复的dp到px转换计算
 * - 提供统一的尺寸管理
 * - 支持动态配置更新
 *
 * <AUTHOR> Team
 * @version 1.0
 */
public class LayoutDimensionCache {

    // 状态栏高度配置
    public final int statusBarHeight40;  // 40dp状态栏高度
    public final int statusBarHeight30;  // 30dp状态栏高度  
    public final int statusBarHeight50;  // 55dp状态栏高度

    // 导航栏配置
    public final int navigationBarWidth;  // 导航栏宽度


    // 常用边距配置
    public final int margin5;   // 5dp边距
    public final int margin10;  // 10dp边距
    public final int margin25;  // 25dp边距
    public final int margin2;   // 2dp边距

    // 卡片尺寸配置
    public final int mapCardWidth;   // 地图卡片宽度
    public final int mapCardHeight;  // 地图卡片高度
    public final int fixedCardWidth; // 固定卡片宽度（250dp）

    // Activity引用，用于dp转换
    private final Context activity;

    /**
     * 构造函数
     *
     * @param activity Fragment所属的Activity，用于dp到px的转换
     */
    public LayoutDimensionCache(Context activity) {
        this.activity = activity;

        // 初始化状态栏高度
        this.statusBarHeight40 = DensityUtils.dp2px(activity, 40);
        this.statusBarHeight30 = DensityUtils.dp2px(activity, 30);
        this.statusBarHeight50 = DensityUtils.dp2px(activity, 50);

        // 初始化导航栏宽度
        this.navigationBarWidth = DensityUtils.dp2px(activity, 70);

        // 初始化边距
        this.margin5 = DensityUtils.dp2px(activity, 5);
        this.margin10 = DensityUtils.dp2px(activity, 10);
        this.margin25 = DensityUtils.dp2px(activity, 25);
        this.margin2 = DensityUtils.dp2px(activity, 2);

        // 初始化卡片尺寸（从设置管理器获取）
        this.mapCardWidth = DensityUtils.dp2px(activity, SettingsManager.getMapCardWidth());
        this.mapCardHeight = DensityUtils.dp2px(activity, SettingsManager.getMapCardHeight());
        this.fixedCardWidth = DensityUtils.dp2px(activity, 250);
    }

    /**
     * 获取状态栏顶部约束位置
     * 根据状态栏是否显示返回相应的约束目标
     *
     * @param statusBarId 状态栏的View ID
     * @return 约束目标ID
     */
    public int getStatusBarTopConstraint(int statusBarId) {
        return SettingsManager.getStatusBarShow() ? statusBarId : android.R.id.content;
    }

    /**
     * 获取状态栏顶部边距
     * 根据状态栏是否显示返回相应的边距值
     *
     * @return 边距值（px）
     */
    public int getStatusBarTopMargin() {
        return SettingsManager.getStatusBarShow() ? 0 : margin5;
    }

    /**
     * 获取导航栏边距
     * 根据导航栏是否显示返回相应的边距值
     *
     * @return 边距值（px）
     */
    public int getNavigationBarMargin() {
        return SettingsManager.getNavigationBarShow() ? margin2 : margin5;
    }

    /**
     * 动态获取导航栏高度
     * 根据当前设置实时计算导航栏高度，避免缓存过期问题
     *
     * @return 导航栏高度（px）
     */
    public int getNavigationBarHeight() {
        // 根据是否显示名称动态计算高度：显示名称时80dp，不显示时60dp
        int heightDp = SettingsManager.getNavigationBarNameShow() ? 80 : 60;
        return DensityUtils.dp2px(activity, heightDp);
    }

    /**
     * 刷新缓存
     * 当设置发生变化时调用此方法更新缓存
     * 注意：由于某些值是final的，需要重新创建缓存对象
     */
    public LayoutDimensionCache refresh() {
        return new LayoutDimensionCache(activity);
    }
}
