package com.smartcar.easylauncher.shared.utils;

import android.app.ActivityManager;
import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.os.Build;
import android.os.Environment;
import android.os.StatFs;
import android.provider.Settings;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.WindowManager;

import com.smartcar.easylauncher.shared.utils.permission.BluetoothPermissionUtils;
import com.smartcar.easylauncher.shared.utils.system.CpuInfoMapper;
import com.smartcar.easylauncher.shared.utils.system.DeviceIdUtil;
import com.smartcar.easylauncher.shared.utils.system.DeviceUtil;
import com.smartcar.easylauncher.shared.utils.system.DeviceUtils;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.lang.reflect.Method;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 设备信息统一收集与缓存提供器
 * 单例模式，负责所有设备、系统、网络、蓝牙等信息的收集与缓存
 */
public class DeviceInfoProvider {
    private static DeviceInfoProvider instance;
    private final Map<String, String> cache = new HashMap<>();
    private boolean isCached = false;
    private static final DecimalFormat sizeFormat = new DecimalFormat("0.00");

    public static final String KEY_BRAND = "brand";
    public static final String KEY_MODEL = "model";
    public static final String KEY_APP_VERSION = "appVersion";
    public static final String KEY_APP_VERSION_CODE = "appVersionCode";
    public static final String KEY_ANDROID_VERSION = "androidVersion";
    public static final String KEY_API_LEVEL = "apiLevel";
    public static final String KEY_SECURITY_PATCH = "securityPatch";
    public static final String KEY_DEVICE_ID = "deviceId";
    public static final String KEY_ANDROID_ID = "androidId";
    public static final String KEY_SERIAL_NUMBER = "serialNumber";
    public static final String KEY_PHYSICAL = "physical";
    public static final String KEY_APP = "app";
    public static final String KEY_DENSITY = "density";
    public static final String KEY_REFRESH = "refresh";
    public static final String KEY_CPU = "cpu";
    public static final String KEY_KERNEL = "kernel";
    public static final String KEY_RAM = "ram";
    public static final String KEY_STORAGE = "storage";
    public static final String KEY_IP = "ip";
    public static final String KEY_MAC = "mac";
    public static final String KEY_BLUETOOTH = "bluetooth";

    // API Level -> Android版本号映射表
    private static final Map<Integer, String> API_TO_ANDROID_VERSION = new HashMap<>();
    static {
        API_TO_ANDROID_VERSION.put(34, "14");
        API_TO_ANDROID_VERSION.put(33, "13");
        API_TO_ANDROID_VERSION.put(32, "12L");
        API_TO_ANDROID_VERSION.put(31, "12");
        API_TO_ANDROID_VERSION.put(30, "11");
        API_TO_ANDROID_VERSION.put(29, "10");
        API_TO_ANDROID_VERSION.put(28, "9");
        API_TO_ANDROID_VERSION.put(27, "8.1");
        API_TO_ANDROID_VERSION.put(26, "8.0");
        API_TO_ANDROID_VERSION.put(25, "7.1");
        API_TO_ANDROID_VERSION.put(24, "7.0");
        API_TO_ANDROID_VERSION.put(23, "6.0");
        API_TO_ANDROID_VERSION.put(22, "5.1");
        API_TO_ANDROID_VERSION.put(21, "5.0");
        API_TO_ANDROID_VERSION.put(20, "4.4W");
        API_TO_ANDROID_VERSION.put(19, "4.4");
        API_TO_ANDROID_VERSION.put(18, "4.3");
        API_TO_ANDROID_VERSION.put(17, "4.2");
        API_TO_ANDROID_VERSION.put(16, "4.1");
        API_TO_ANDROID_VERSION.put(15, "4.0.3");
        API_TO_ANDROID_VERSION.put(14, "4.0");
        API_TO_ANDROID_VERSION.put(13, "3.2");
        API_TO_ANDROID_VERSION.put(12, "3.1");
        API_TO_ANDROID_VERSION.put(11, "3.0");
        API_TO_ANDROID_VERSION.put(10, "2.3.3");
        API_TO_ANDROID_VERSION.put(9, "2.3");
        API_TO_ANDROID_VERSION.put(8, "2.2");
        API_TO_ANDROID_VERSION.put(7, "2.1");
        API_TO_ANDROID_VERSION.put(6, "2.0.1");
        API_TO_ANDROID_VERSION.put(5, "2.0");
        API_TO_ANDROID_VERSION.put(4, "1.6");
        API_TO_ANDROID_VERSION.put(3, "1.5");
        API_TO_ANDROID_VERSION.put(2, "1.1");
        API_TO_ANDROID_VERSION.put(1, "1.0");
    }

    private DeviceInfoProvider() {}

    public static DeviceInfoProvider getInstance() {
        if (instance == null) {
            synchronized (DeviceInfoProvider.class) {
                if (instance == null) {
                    instance = new DeviceInfoProvider();
                }
            }
        }
        return instance;
    }

    public Map<String, String> getAllInfo(Context context) {
        if (isCached) return new HashMap<>(cache);
        Map<String, String> info = new HashMap<>();
        try {
            // 基本信息
            info.put(KEY_BRAND, safe(DeviceUtil.getPhoneBrand()));
            info.put(KEY_MODEL, safe(DeviceUtil.getPhoneModel()));
            info.put(KEY_APP_VERSION, safe(DeviceUtil.getVersionName(context)));
            info.put(KEY_APP_VERSION_CODE, String.valueOf(DeviceUtil.getVersionCode(context)));
            // Android版本号校验与映射
            int apiLevel = Build.VERSION.SDK_INT;
            String apiLevelStr = String.valueOf(apiLevel);
            String mappedVersion = API_TO_ANDROID_VERSION.getOrDefault(apiLevel, "未知");
            String rawVersion = getAndroidVersionRaw();
            // 校验：如原始字符串与映射不符，则以映射为准
            String finalVersion;
            if (rawVersion != null && (rawVersion.equals(mappedVersion) || rawVersion.startsWith(mappedVersion))) {
                finalVersion = mappedVersion;
                info.put("versionSource", "raw");
            } else {
                finalVersion = mappedVersion;
                info.put("versionSource", "api");
            }
            info.put(KEY_ANDROID_VERSION, finalVersion);
            info.put(KEY_API_LEVEL, apiLevelStr);
            info.put("androidVersionRaw", rawVersion);
            info.put(KEY_SECURITY_PATCH, getSecurityPatch());
            info.put(KEY_DEVICE_ID, safe(DeviceIdUtil.getDeviceId(context)));
            info.put(KEY_ANDROID_ID, safe(Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID)));
            info.put(KEY_SERIAL_NUMBER, getSerialNumber());
            // 显示信息
            info.putAll(loadDisplayInfo(context));
            // CPU/内核
            info.put(KEY_CPU, getDetailedCpuInfo());
            info.put(KEY_KERNEL, loadKernelInfo());
            // 内存/存储
            info.putAll(setupMemoryInfo(context));
            // 网络/蓝牙
            info.put(KEY_IP, safe(NetworkUtils.getIPAddress(true)));
            info.put(KEY_MAC, getMac());
            info.put(KEY_BLUETOOTH, getBluetoothInfo(context));
        } catch (Exception e) {
            // 统一异常处理
            e.printStackTrace();
        }
        cache.clear();
        cache.putAll(info);
        isCached = true;
        return new HashMap<>(cache);
    }

    public void invalidateCache() {
        isCached = false;
        cache.clear();
    }

    private String safe(String v) {
        return (v == null || v.trim().isEmpty()) ? "未知" : v.trim();
    }

    private String getAndroidVersion() {
        if (cache.containsKey(KEY_ANDROID_VERSION)) return cache.get(KEY_ANDROID_VERSION);
        String androidVersion = "";
        try {
            Process process = Runtime.getRuntime().exec("getprop ro.build.version.release");
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            androidVersion = reader.readLine();
            reader.close();
        } catch (Exception e) {
            androidVersion = Build.VERSION.RELEASE;
        }
        cache.put(KEY_ANDROID_VERSION, androidVersion);
        return androidVersion;
    }

    private String getSecurityPatch() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return Build.VERSION.SECURITY_PATCH;
        } else {
            return "系统版本过低";
        }
    }

    private Map<String, String> loadDisplayInfo(Context context) {
        Map<String, String> displayInfo = new HashMap<>();
        try {
            WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
            Display display = wm.getDefaultDisplay();
            DisplayMetrics metrics = new DisplayMetrics();
            display.getMetrics(metrics);
            // 物理分辨率
            android.graphics.Point realSize = new android.graphics.Point();
            display.getRealSize(realSize);
            displayInfo.put(KEY_PHYSICAL, String.format("%d × %d", realSize.x, realSize.y));
            // 应用分辨率
            android.graphics.Point size = new android.graphics.Point();
            display.getSize(size);
            displayInfo.put(KEY_APP, String.format("%d × %d", size.x, size.y));
            // 屏幕密度
            displayInfo.put(KEY_DENSITY, String.format("%d dpi (%.2f)", metrics.densityDpi, metrics.density));
            // 刷新率
            displayInfo.put(KEY_REFRESH, String.format("%.0f Hz", display.getRefreshRate()));
        } catch (Exception e) {
            displayInfo.put(KEY_PHYSICAL, "未知");
            displayInfo.put(KEY_APP, "未知");
            displayInfo.put(KEY_DENSITY, "未知");
            displayInfo.put(KEY_REFRESH, "未知");
        }
        return displayInfo;
    }

    private String loadKernelInfo() {
        String kernelVersion = System.getProperty("os.version");
        if (kernelVersion == null || kernelVersion.isEmpty() || "unknown".equalsIgnoreCase(kernelVersion)) {
            BufferedReader reader = null;
            try {
                reader = new BufferedReader(new FileReader("/proc/version"));
                String line = reader.readLine();
                if (line != null && !line.isEmpty()) {
                    String[] parts = line.split(" ");
                    if (parts.length >= 3) {
                        kernelVersion = parts[2];
                    } else {
                        kernelVersion = line;
                    }
                }
            } catch (Exception e) {
                kernelVersion = Build.VERSION.RELEASE;
            } finally {
                if (reader != null) {
                    try { reader.close(); } catch (IOException e) {}
                }
            }
        }
        if (kernelVersion == null || kernelVersion.isEmpty() || "unknown".equalsIgnoreCase(kernelVersion)) {
            kernelVersion = "未知";
        }
        return kernelVersion;
    }

    private String getDetailedCpuInfo() {
        String socModel = getSystemProperty("ro.soc.model", "");
        if (!socModel.isEmpty() && !socModel.equalsIgnoreCase("unknown")) {
            String cpuName = CpuInfoMapper.mapSocToName(socModel);
            if (!cpuName.isEmpty()) {
                return cpuName;
            }
        }
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader("/proc/cpuinfo"));
            String line;
            Map<String, String> cpuInfo = new HashMap<>();
            Set<String> cpuParts = new HashSet<>();
            while ((line = reader.readLine()) != null) {
                String[] parts = line.split(":");
                if (parts.length > 1) {
                    String key = parts[0].trim();
                    String value = parts[1].trim();
                    cpuInfo.put(key, value);
                    if (key.equals("CPU part")) {
                        cpuParts.add(value);
                    }
                }
            }
            if (!cpuParts.isEmpty()) {
                String cpuName = CpuInfoMapper.mapCpuPartsToName(cpuParts);
                if (!cpuName.isEmpty()) {
                    return cpuName;
                }
            }
            String[] fields = {"Hardware", "model name", "Processor", "CPU architecture", "cpu model", "CPU variant", "CPU part", "CPU implementation"};
            for (String field : fields) {
                String value = cpuInfo.get(field);
                if (value != null && !value.isEmpty() && !value.equalsIgnoreCase("unknown") && !value.equalsIgnoreCase("0000")) {
                    return value;
                }
            }
        } catch (Exception e) {
        } finally {
            try { if (reader != null) reader.close(); } catch (IOException e) {}
        }
        return "未知处理器";
    }

    private Map<String, String> setupMemoryInfo(Context context) {
        Map<String, String> memoryInfo = new HashMap<>();
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            ActivityManager.MemoryInfo memInfo = new ActivityManager.MemoryInfo();
            activityManager.getMemoryInfo(memInfo);
            long totalMemory = memInfo.totalMem;
            long availMemory = memInfo.availMem;
            long standardizedTotalMemory = standardizeMemorySize(totalMemory);
            StatFs statFs = new StatFs(Environment.getDataDirectory().getPath());
            long availStorage;
            long totalStorage;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                availStorage = statFs.getAvailableBytes();
                totalStorage = statFs.getTotalBytes();
            } else {
                long blockSize = statFs.getBlockSize();
                availStorage = (long) statFs.getAvailableBlocks() * blockSize;
                totalStorage = (long) statFs.getBlockCount() * blockSize;
            }
            long standardizedTotalStorage = standardizeStorageSize(totalStorage);
            String ramInfo = String.format("%s/%s", formatSize(standardizedTotalMemory - availMemory), formatSize(standardizedTotalMemory));
            String storageInfo = String.format("%s/%s", formatSize(standardizedTotalStorage - availStorage), formatSize(standardizedTotalStorage));
            memoryInfo.put(KEY_RAM, ramInfo);
            memoryInfo.put(KEY_STORAGE, storageInfo);
        } catch (Exception e) {
            memoryInfo.put(KEY_RAM, "未知");
            memoryInfo.put(KEY_STORAGE, "未知");
        }
        return memoryInfo;
    }

    private long standardizeMemorySize(long bytes) {
        double gbDouble = bytes / (1024.0 * 1024.0 * 1024.0);
        long[] standardSizes = {16, 12, 8, 6, 4, 3, 2};
        for (long standardSize : standardSizes) {
            double minValue = standardSize * 0.85;
            double maxValue = standardSize * 0.90;
            if (gbDouble >= minValue && gbDouble < maxValue) {
                return standardSize * 1024 * 1024 * 1024;
            }
        }
        for (long standardSize : standardSizes) {
            double minValue = standardSize * 0.80;
            double maxValue = standardSize * 0.92;
            if (gbDouble >= minValue && gbDouble < maxValue) {
                return standardSize * 1024 * 1024 * 1024;
            }
        }
        return bytes;
    }

    private long standardizeStorageSize(long bytes) {
        double gbDouble = bytes / (1024.0 * 1024.0 * 1024.0);
        long[] standardSizes = {1024, 512, 256, 128, 64, 32, 16, 8};
        for (long standardSize : standardSizes) {
            double minValue = standardSize * 0.75;
            double maxValue = standardSize * 0.85;
            if (gbDouble >= minValue && gbDouble < maxValue) {
                return standardSize * 1024 * 1024 * 1024;
            }
        }
        for (long standardSize : standardSizes) {
            double minValue = standardSize * 0.70;
            double maxValue = standardSize * 0.76;
            if (gbDouble >= minValue && gbDouble < maxValue) {
                return standardSize * 1024 * 1024 * 1024;
            }
        }
        return bytes;
    }

    private String formatSize(float size) {
        float result = size;
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int index = 0;
        while (result >= 1024 && index < units.length - 1) {
            result /= 1024;
            index++;
        }
        return sizeFormat.format(result) + " " + units[index];
    }

    private String getMac() {
        if (cache.containsKey(KEY_MAC)) return cache.get(KEY_MAC);
        List<String> macList = DeviceUtils.getMacAddressList();
        String macAddress = macList.size() >= 1 ? macList.get(0) : "";
        cache.put(KEY_MAC, macAddress);
        return macAddress;
    }

    private String getSerialNumber() {
        if (cache.containsKey(KEY_SERIAL_NUMBER)) return cache.get(KEY_SERIAL_NUMBER);
        String serial = null;
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                serial = Build.getSerial();
            } else {
                serial = Build.SERIAL;
            }
        } catch (Exception e) {
            try {
                serial = getSystemProperty("ro.serialno", "");
                if (serial.isEmpty()) {
                    serial = getSystemProperty("ro.boot.serialno", "");
                }
            } catch (Exception ex) {}
        }
        String result = serial != null ? serial : "未知";
        cache.put(KEY_SERIAL_NUMBER, result);
        return result;
    }

    private String getSystemProperty(String key, String defaultValue) {
        try {
            Class<?> systemProperties = Class.forName("android.os.SystemProperties");
            Method get = systemProperties.getMethod("get", String.class, String.class);
            return (String) get.invoke(null, key, defaultValue);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    private String getBluetoothInfo(Context context) {
        if (cache.containsKey(KEY_BLUETOOTH)) return cache.get(KEY_BLUETOOTH);
        StringBuilder bluetoothInfo = new StringBuilder();
        try {
            BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
            if (adapter != null) {
                // 检查蓝牙连接权限
                if (!BluetoothPermissionUtils.hasBluetoothConnectPermission(context)) {
                    bluetoothInfo.append("蓝牙权限不足，无法获取详细信息");
                } else {
                    try {
                        bluetoothInfo.append("设备名称: ").append(adapter.getName()).append("   ");
                        bluetoothInfo.append("MAC地址: ").append(adapter.getAddress()).append("   ");
                        bluetoothInfo.append("状态: ").append(adapter.isEnabled() ? "已启用" : "未启用");
                    } catch (SecurityException e) {
                        bluetoothInfo.append("蓝牙权限不足: ").append(e.getMessage());
                    }
                }
            } else {
                bluetoothInfo.append("设备不支持蓝牙");
            }
        } catch (Exception e) {
            bluetoothInfo.append("获取蓝牙信息失败: ").append(e.getMessage());
        }
        String result = bluetoothInfo.toString();
        cache.put(KEY_BLUETOOTH, result);
        return result;
    }

    /**
     * 获取原始Android版本字符串（可能被厂商篡改，仅作参考）
     */
    private String getAndroidVersionRaw() {
        String androidVersion = "";
        try {
            Process process = Runtime.getRuntime().exec("getprop ro.build.version.release");
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            androidVersion = reader.readLine();
            reader.close();
        } catch (Exception e) {
            androidVersion = Build.VERSION.RELEASE;
        }
        return androidVersion;
    }
}