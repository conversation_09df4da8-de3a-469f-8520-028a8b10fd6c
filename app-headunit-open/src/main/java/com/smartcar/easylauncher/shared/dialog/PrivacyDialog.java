package com.smartcar.easylauncher.shared.dialog;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.text.method.LinkMovementMethod;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.widget.TextView;
import androidx.fragment.app.FragmentManager;
import androidx.annotation.NonNull;
import java.lang.ref.WeakReference;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;
import com.smartcar.easylauncher.shared.utils.ui.HyperlinkText;
import com.timmy.tdialog.TDialog;
import com.timmy.tdialog.base.BindViewHolder;
import com.timmy.tdialog.listener.OnBindViewListener;
import com.timmy.tdialog.listener.OnViewClickListener;

/**
 * 隐私协议弹窗
 */
public class PrivacyDialog {
    private static final String TAG = "PrivacyDialog";
    private static volatile PrivacyDialog instance;
    private static final float SCREEN_WIDTH_ASPECT = 1f;
    private static final float SCREEN_HEIGHT_ASPECT = 1f;
    
    // 添加静态标志以全局控制弹窗显示状态
    private static boolean isPrivacyDialogShowing = false;
    
    private WeakReference<TDialog> dialogRef;
    private WeakReference<Context> contextRef;
    private boolean isShowing = false; // 实例级别的状态标记
    
    private PrivacyDialog() {
        // 私有构造方法
    }
    
    public static PrivacyDialog getInstance() {
        if (instance == null) {
            synchronized (PrivacyDialog.class) {
                if (instance == null) {
                    instance = new PrivacyDialog();
                }
            }
        }
        return instance;
    }

    /**
     * 检查隐私弹窗是否正在显示（全局状态）
     * @return true表示全局有隐私弹窗正在显示
     */
    public static boolean isDialogShowing() {
        return isPrivacyDialogShowing;
    }
    
    /**
     * 检查隐私弹窗是否正在显示（实例状态）
     * @return true表示弹窗正在显示，false表示弹窗未显示
     */
    public boolean isShowing() {
        return isShowing && dialogRef != null && dialogRef.get() != null && dialogRef.get().isVisible();
    }

    /**
     * 关闭隐私协议弹窗
     */
    public void dismiss() {
        MyLog.v(TAG, "dismiss: 开始关闭隐私弹窗");
        
        // 先重置状态，避免在dismiss过程中再次尝试显示弹窗
        boolean wasShowing = isShowing;
        isShowing = false;
        isPrivacyDialogShowing = false;
        
        if (dialogRef != null && dialogRef.get() != null) {
            TDialog dialog = dialogRef.get();
            if (dialog.isVisible()) {
                dialog.dismiss();
                MyLog.v(TAG, "dismiss: 隐私弹窗已关闭");
            }
        }
        
        // 清理引用
        dialogRef = null;
        contextRef = null;
        
        // 发布延迟消息以确保状态完全重置
        if (wasShowing) {
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                MyLog.v(TAG, "dismiss: 隐私弹窗状态已完全重置");
                isPrivacyDialogShowing = false;
            }, 100);
        }
    }

    /**
     * 隐私协议弹窗回调接口
     */
    public interface PrivacyDialogCallback {
        /**
         * 用户同意隐私协议
         */
        void onAgree();

        /**
         * 用户不同意隐私协议
         */
        void onDisagree();
    }

    /**
     * 显示隐私协议弹窗
     *
     * @param context 上下文
     * @param fragmentManager FragmentManager实例
     * @param callback 回调接口
     */
    public void show(@NonNull Context context, @NonNull FragmentManager fragmentManager, @NonNull PrivacyDialogCallback callback) {
        // 检查全局状态，如果已有弹窗在显示，则不显示
        if (isPrivacyDialogShowing) {
            MyLog.v(TAG, "show: 全局已有隐私弹窗在显示，不再显示新弹窗");
            return;
        }
        
        // 检查实例状态，如果已有弹窗在显示，则不显示
        if (isShowing()) {
            MyLog.v(TAG, "show: 实例已有隐私弹窗在显示，不再显示新弹窗");
            return;
        }

        MyLog.v(TAG, "show: 开始显示隐私弹窗");
        // 设置全局状态为显示中
        isPrivacyDialogShowing = true;
        
        contextRef = new WeakReference<>(context);
        
        TDialog dialog = new TDialog.Builder(fragmentManager)
                .setLayoutRes(R.layout.dialog_change_avatar)
                .setWidth(DensityUtils.dp2px(contextRef.get(), 700))
                .setHeight(DensityUtils.dp2px(contextRef.get(), 320))
                .setGravity(Gravity.CENTER)
                .setCancelableOutside(false)
                .addOnClickListener(R.id.bt_concur, R.id.bt_cancel)
                .setOnViewClickListener(new OnViewClickListener() {
                    @Override
                    public void onViewClick(BindViewHolder viewHolder, View view, TDialog tDialog) {
                        switch (view.getId()) {
                            case R.id.bt_concur:
                                callback.onAgree();
                                dismiss();
                                break;
                            case R.id.bt_cancel:
                                callback.onDisagree();
                                dismiss();
                                break;
                        }
                    }
                })
                .setOnBindViewListener(new OnBindViewListener() {
                    @Override
                    public void bindView(BindViewHolder bindViewHolder) {
                        if (contextRef != null && contextRef.get() != null) {
                            Context context = contextRef.get();
                            TextView privacyTextView = bindViewHolder.getView(R.id.tv_user_and_privacy);
                            privacyTextView.setText(HyperlinkText.getClickableSpan(
                                    context, 
                                    context.getString(R.string.user_and_privacy)
                            ));
                            privacyTextView.setMovementMethod(LinkMovementMethod.getInstance());
                        }
                    }
                })
                .setOnKeyListener((dialog1, keyCode, event) -> keyCode == KeyEvent.KEYCODE_BACK)
                .create();

        dialogRef = new WeakReference<>(dialog);

        try {
            isShowing = true; // 标记实例状态为显示中
            dialog.show(fragmentManager);
            MyLog.v(TAG, "show: 隐私弹窗显示成功");
        } catch (Exception e) {
            MyLog.e(TAG, "show: 隐私弹窗显示失败: " + e.getMessage());
            isShowing = false; // 发生异常时重置实例状态
            isPrivacyDialogShowing = false; // 发生异常时重置全局状态
            dismiss(); // 发生异常时清理资源
        }
    }

    /**
     * 重置所有弹窗状态，通常在应用启动时调用
     */
    public static void resetDialogState() {
        isPrivacyDialogShowing = false;
        MyLog.v(TAG, "resetDialogState: 重置隐私弹窗全局状态");
        
        if (instance != null) {
            instance.dismiss();
            instance = null;
        }
    }
} 