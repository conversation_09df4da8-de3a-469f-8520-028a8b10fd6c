package com.smartcar.easylauncher.shared.adapter.setting;


import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.dragswipe.listener.DragAndSwipeDataCallback;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.modules.touch.floating.action.IFloatWindowAction;
import com.smartcar.easylauncher.modules.touch.model.FloatWindowActionModel;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 悬浮窗拖拽适配器
 * <AUTHOR>
 */
public class FloatDragAdapter extends BaseQuickAdapter<FloatWindowActionModel, QuickViewHolder> implements DragAndSwipeDataCallback {
    private final ConcurrentHashMap<Integer, IFloatWindowAction> mActionMap;

    public FloatDragAdapter(ConcurrentHashMap<Integer, IFloatWindowAction> actionMap) {
        this.mActionMap = actionMap;
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup parent, int viewType) {
        return new QuickViewHolder(R.layout.item_custom_menu, parent);
    }

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder holder, int position, FloatWindowActionModel item) {
        assert item != null;
        int actionId = item.getActionId();
        IFloatWindowAction action = mActionMap.get(actionId);
        assert action != null;
        holder.setImageDrawable(R.id.action_icon_iv, action.getActionIconDrawable());
        holder.setText(R.id.action_name_tv, action.getActionName());
    }

    @Override
    public void dataMove(int fromPosition, int toPosition) {
        move(fromPosition, toPosition);
    }

    @Override
    public void dataRemoveAt(int position) {
        removeAt(position);
    }
}
