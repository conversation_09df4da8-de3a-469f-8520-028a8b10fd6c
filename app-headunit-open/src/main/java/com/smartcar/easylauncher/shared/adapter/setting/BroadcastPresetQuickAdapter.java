package com.smartcar.easylauncher.shared.adapter.setting;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.BroadcastPresets;

/**
 * 广播预设适配器
 * 使用 BaseRecyclerViewAdapterHelper4 框架
 * 
 * <AUTHOR>
 * @date 2024/07/19
 */
public class BroadcastPresetQuickAdapter extends BaseQuickAdapter<BroadcastPresetQuickAdapter.PresetItem, QuickViewHolder> {
    
    // 预设选择监听器
    private OnPresetSelectedListener listener;
    
    /**
     * 预设选择监听器接口
     */
    public interface OnPresetSelectedListener {
        /**
         * 当选择预设时调用
         * 
         * @param preset 预设模板
         */
        void onPresetSelected(BroadcastPresets.PresetTemplate preset);
    }
    
    /**
     * 设置预设选择监听器
     * 
     * @param listener 监听器
     */
    public void setOnPresetSelectedListener(OnPresetSelectedListener listener) {
        this.listener = listener;
    }
    
    /**
     * 预设数据项
     */
    public static class PresetItem {
        private BroadcastPresets.PresetTemplate preset;
        private String type;  // 类型标识：系统、媒体、应用
        
        public PresetItem(BroadcastPresets.PresetTemplate preset, String type) {
            this.preset = preset;
            this.type = type;
        }
        
        public BroadcastPresets.PresetTemplate getPreset() {
            return preset;
        }
        
        public String getType() {
            return type;
        }
    }
    
    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup parent, int viewType) {
        return new QuickViewHolder(R.layout.item_broadcast_preset_card, parent);
    }
    
    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder holder, int position, @Nullable PresetItem item) {
        if (item == null) return;
        
        BroadcastPresets.PresetTemplate preset = item.getPreset();
        
        // 设置类型标识
        TextView tvPresetType = holder.getView(R.id.tv_preset_type);
        tvPresetType.setText(item.getType());
        
        // 设置预设名称和描述
        holder.setText(R.id.tv_preset_name, preset.getName());
        holder.setText(R.id.tv_preset_description, preset.getDescription());
        holder.setText(R.id.tv_preset_action, preset.getAction());
        
        // 设置选择按钮点击事件
        View btnSelect = holder.getView(R.id.btn_select);
        btnSelect.setOnClickListener(v -> {
            if (listener != null) {
                listener.onPresetSelected(preset);
            }
        });
        
        // 设置整个卡片的点击事件
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onPresetSelected(preset);
            }
        });
    }
} 