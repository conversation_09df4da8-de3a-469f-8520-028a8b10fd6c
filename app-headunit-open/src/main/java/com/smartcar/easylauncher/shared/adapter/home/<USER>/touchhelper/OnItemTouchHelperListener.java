package com.smartcar.easylauncher.shared.adapter.home.channel.touchhelper;

import com.smartcar.easylauncher.data.model.common.Channel;

/**
 * 用于监听ItemTouchHelper相关操作的监听器接口
 * <AUTHOR>
 */
public interface OnItemTouchHelperListener {

    /**
     * 当Item被点击时调用
     *
     * @param position 被点击Item的位置
     */
    void onItemClick(int position);

    /**
     * 当Item开始拖动时调用，注意参数名应为position而非postion，避免拼写错误
     *
     * @param position 开始拖动的Item的位置
     */
    void onItemDragStart(int position); // 修正了参数名 postion -> position

    /**
     * 当Item移动时调用
     *
     * @param startPosition Item的起始位置
     * @param endPosition   Item的目标位置
     */
    void onItemMove(int startPosition, int endPosition); // 修正了参数名 starPosition -> startPosition

    /**
     * 当Item被移除时调用
     *
     * @param position 被移除Item的位置
     */
    void onItemDismiss(int position);

    /**
     * 当TPMS（可能是某种特定权限或功能）的权限发生变化时调用
     *
     * @param channel 关联的Channel对象
     */
    void onTpmsPermission(Channel channel); // 如果Channel是一个自定义类型，需要确保它已经被正确导入
}
