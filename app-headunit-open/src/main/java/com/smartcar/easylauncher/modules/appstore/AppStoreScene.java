package com.smartcar.easylauncher.modules.appstore;

import static com.bytedance.scene.navigation.NavigationSceneGetter.requireNavigationScene;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.maning.mndialoglibrary.MProgressDialog;
import com.smartcar.easylauncher.BuildConfig;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.app.OptimizedAppAdapter;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.databinding.ActivityMoreBinding;
import com.smartcar.easylauncher.infrastructure.event.cody.MoreSettingScopeBus;
import com.smartcar.easylauncher.infrastructure.event.scope.notice.cody.GeneralNotificationScopeBus;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.data.model.common.GeneralNoticeModel;
import com.smartcar.easylauncher.data.model.common.MenuModel;
import com.smartcar.easylauncher.data.model.system.AppInfo;
import com.smartcar.easylauncher.data.model.system.MoreSettingModel;
import com.smartcar.easylauncher.modules.appstore.setting.MoreSettingDialogFragment;
import com.smartcar.easylauncher.shared.utils.AppInfoProvider;
import com.smartcar.easylauncher.shared.utils.SceneFactory;
import com.smartcar.easylauncher.shared.utils.AppRefreshDiagnostic;
import com.smartcar.easylauncher.shared.utils.AppUtils;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.thread.ThreadPoolUtil;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;
import com.smartcar.easylauncher.shared.view.pagergridlayoutmanager.PagerGridLayoutManager;
import com.smartcar.easylauncher.shared.view.popup.PopupWindow;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import cody.bus.ElegantLog;
import cody.bus.ObserverWrapper;

/**
 * 更多应用场景
 * 展示所有已安装应用并提供管理功能
 * 
 * 性能优化点：
 * 1. 使用视图绑定减少findViewById调用
 * 2. 懒加载数据，避免启动时阻塞UI
 * 3. 使用DiffUtil进行高效列表更新
 * 4. 使用项目统一线程池进行后台任务处理
 * 5. 简化UI更新逻辑，提高代码可读性
 * 6. 减少不必要的对象创建和GC压力
 *
 * <AUTHOR>
 */
public class AppStoreScene extends BaseScene implements View.OnClickListener {

    /**
     * 日志标签
     */
    private static final String TAG = "MoreScene";
    
    /**
     * Gson实例 - 用于JSON序列化/反序列化
     * 静态单例以减少对象创建
     */
    private static final Gson GSON = new GsonBuilder().create();
    
    /**
     * 主线程Handler - 用于UI更新
     */
    private final Handler mUIHandler = new Handler(Looper.getMainLooper());

    /**
     * 刷新超时时间（毫秒）
     */
    private static final long REFRESH_TIMEOUT = 30000; // 30秒超时

    /**
     * 超时任务Runnable
     */
    private Runnable timeoutRunnable;
    
    /**
     * 应用显示状态 
     * 0 - 显示正常应用 
     * 1 - 显示隐藏的应用
     */
    private int state = 0;
    
    /**
     * 应用信息提供者
     */
    private AppInfoProvider mProvider;
    
    /**
     * 所有应用列表数据
     */
    private List<AppInfo> mAllAppList;
    
    /**
     * 优化的应用适配器
     */
    private OptimizedAppAdapter mAppAdapter;
    
    /**
     * 视图绑定对象
     */
    private ActivityMoreBinding binding;
    
    /**
     * 分页网格布局管理器
     */
    private PagerGridLayoutManager layoutManager;
    
    /**
     * 弹出菜单窗口
     */
    private PopupWindow popupWindow;
    
    /**
     * 更多设置观察者
     */
    private ObserverWrapper<MoreSettingModel> mMoreNotice;

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        assemblyObservation();
        uiSettingsObservation();
        binding.moreBtnBack.setOnClickListener(this);
        binding.moreMenuContainer.setOnClickListener(this);
        initView();
        getData();
    }

    @NonNull
    @Override
    public ViewGroup onCreateNewView(@NonNull LayoutInflater layoutInflater, @NonNull ViewGroup viewGroup, @Nullable Bundle bundle) {
        binding = ActivityMoreBinding.inflate(getLayoutInflater());
        return binding.getRoot();
    }

    /**
     * 组件初始化完成通知观察者
     * 处理应用安装/卸载等通知
     */
    private void assemblyObservation() {
        // 注册组件通知监听
        GeneralNotificationScopeBus.eventBean().observe(this, new ObserverWrapper<>() {
            @Override
            public void onChanged(final GeneralNoticeModel value) {
                // 记录通知内容
                ElegantLog.d(value.toString());
                MyLog.v(TAG, "组件通知: 类型" + value.type + " 状态" + value.state);
                
                // 忽略非激活状态的通知
                if (!value.state) {
                    return;
                }
                
                // 根据通知类型执行相应操作
                switch (value.type) {
                    case 3: // 应用卸载通知
                        // 更新应用列表，移除已卸载的应用
                        updateData(value.content);
                        break;
                    case 4: // 应用安装通知
                        // 暂未实现安装应用处理
                        // installData(value.content);
                        break;
                    default:
                        // 忽略其他类型通知
                }
            }
        });
    }

    /**
     * 监听设置界面的配置变更
     * 处理布局、行列数和图标大小的变更
     */
    private void uiSettingsObservation() {
        // 注册设置变更监听
        MoreSettingScopeBus.eventBean().observeForever(mMoreNotice = new ObserverWrapper<>(true) {
            @Override
            public void onChanged(final MoreSettingModel value) {
                // 记录设置变更
                ElegantLog.d(value.toString());
                MyLog.v(TAG, "设置变更: 类型 " + value.getType());
                
                // 确保布局管理器已初始化
                if (layoutManager == null) {
                    return;
                }

                // 根据设置类型执行相应操作
                switch (value.type) {
                    case 0: // 滑动方向设置变更
                        layoutManager.setOrientation(SettingsManager.getSlidingMode());
                        break;
                    case 1: // 行数设置变更
                        layoutManager.setRows(value.getRowNumber());
                        break;
                    case 2: // 列数设置变更
                        layoutManager.setColumns(value.getColumnNumber());
                        break;
                    case 3: // 图标大小设置变更
                        // 重置适配器中的图标尺寸缓存并强制刷新
                        if (mAppAdapter != null) {
                            MyLog.v(TAG, "图标大小设置变更 - 设置值: " + SettingsManager.getIconSize() + "dp");
                            // 调用适配器的resetIconSize方法强制刷新图标大小
                            mAppAdapter.resetIconSize();
                        }
                        break;
                    default:
                        // 忽略其他类型设置变更
                }
            }
        });
    }

    /**
     * 安装或卸载应用后更新数据
     * 从应用列表中移除指定包名的应用
     *
     * @param packageName 要移除的应用包名
     */
    private void updateData(String packageName) {
        // 使用线程池执行后台任务
        ThreadPoolUtil.getThreadPoolExecutor().execute(() -> {
            // 检查列表是否已初始化
            if (mAllAppList != null && !mAllAppList.isEmpty()) {
                // 使用迭代器安全移除匹配的应用
                mAllAppList.removeIf(appInfo -> 
                    TextUtils.equals(appInfo.getPackageName(), packageName));
            }
            
            // 记录更新后的应用数量
            MyLog.v(TAG, "应用列表更新: 剩余" + (mAllAppList != null ? mAllAppList.size() : 0) + "个应用");
            
            // 在UI线程上更新界面
            runOnUiThread(() -> {
                if (mAppAdapter != null) {
                    mAppAdapter.updateData(mAllAppList);
                }
            });
        });
    }

    /**
     * 应用安装处理方法
     * 重新查询所有应用信息
     *
     * @param packageName 新安装的应用包名
     */
    private void installData(String packageName) {
        // 重新查询所有应用信息
        AppInfoProvider.getInstance(requireActivity()).queryAppInfo();
    }

    /**
     * 打开弹出菜单
     * 根据类型显示不同的菜单项
     *
     * @param view           锚点视图
     * @param item           应用信息(可为null)
     * @param adapterPosition 适配器中的位置
     * @param type           菜单类型：0=应用菜单, 1=更多菜单
     */
    private void openPopup(View view, AppInfo item, int adapterPosition, int type) {
        // 根据类型创建菜单项列表
        List<MenuModel> menuData;
        if (type == 0) {
            menuData = createItemMenus(item);
        } else {
            menuData = createMoreMenus();
        }

        // 创建弹出窗口
        popupWindow = new PopupWindow.Builder(requireActivity())
                .setPopupWidth(DensityUtils.dp2px(requireActivity(), 120)) // 设置宽度
                .setItemClickListener((position, menuItem) -> {
                    // 处理菜单项点击
                    handleMenuItemClick(menuItem, item, adapterPosition);
                })
                .setAdapterData(menuData) // 设置菜单数据
                .build();
                
        // 显示弹出窗口
        popupWindow.showPopup(view);
    }
    
    /**
     * 处理菜单项点击事件
     * 
     * @param menuItem 点击的菜单项
     * @param appInfo 相关的应用信息
     * @param position 适配器中的位置
     */
    private void handleMenuItemClick(MenuModel menuItem, AppInfo appInfo, int position) {
        // 关闭弹出窗口
        popupWindow.dismiss();
        
        // 根据菜单项代码执行相应操作
        switch (menuItem.getCode()) {
            case 0: // 查看应用信息
                AppUtils.goToAppInfo(requireActivity(), appInfo.getPackageName());
                break;
            case 1: // 卸载应用
                AppUtils.uninstallApp(requireActivity(), appInfo.getPackageName());
                break;
            case 2: // 隐藏/显示应用
                hideApp(appInfo, position);
                break;
            case 3: // 置顶应用
                int targetPosition = (int) mAllAppList.stream()
                        .filter(app -> app.getType() == 0)
                        .count();
                topping(appInfo, position, targetPosition);
                break;
            case 10: // 刷新应用列表
                showDialog("正在重构应用信息...");
                refreshData();
                break;
            case 11: // 切换显示/隐藏管理模式
                state = (state == 0) ? 1 : 0;
                getData();
                break;
            case 12: // 打开设置对话框
                openMoreSettingDialog();
                break;
            default:
                // 忽略未知菜单项
        }
    }

    /**
     * 创建应用项菜单列表
     *
     * @param item 应用信息
     * @return 菜单项列表
     */
    private List<MenuModel> createItemMenus(AppInfo item) {
        List<MenuModel> items = new ArrayList<>();
        
        // 添加应用信息菜单项
        items.add(new MenuModel("应用信息", 0, R.drawable.ic_info));
        
        // 根据应用类型添加不同的菜单项
        if (item.getType() == 0) {
            // 系统核心应用 - 不提供额外操作
        } else if (item.getType() == 1) {
            // 系统应用 - 提供隐藏/显示和置顶操作
            
            // 根据当前状态显示隐藏/显示选项
            String hideText = (state == 0) ? "隐藏" : "显示";
            int hideIcon = (state == 0) ? R.drawable.ic_hide : R.drawable.ic_display;
            items.add(new MenuModel(hideText, 2, hideIcon));
            
            // 添加置顶选项
            items.add(new MenuModel("置顶", 3, R.drawable.ic_topping));
        } else if (item.getType() == 2) {
            // 用户安装的应用 - 提供完整操作
            
            // 添加卸载选项
            items.add(new MenuModel("卸载", 1, R.drawable.ic_uninstall));
            
            // 根据当前状态显示隐藏/显示选项
            String hideText = (state == 0) ? "隐藏" : "显示";
            int hideIcon = (state == 0) ? R.drawable.ic_hide : R.drawable.ic_display;
            items.add(new MenuModel(hideText, 2, hideIcon));
            
            // 添加置顶选项
            items.add(new MenuModel("置顶", 3, R.drawable.ic_topping));
        }

        return items;
    }

    /**
     * 创建更多菜单列表
     *
     * @return 菜单项列表
     */
    private List<MenuModel> createMoreMenus() {
        List<MenuModel> items = new ArrayList<>();
        
        // 添加刷新应用选项
        items.add(new MenuModel("刷新应用", 10, R.drawable.ic_refresh));
        
        // 根据当前状态添加隐藏/显示管理选项
        String modeText = (state == 0) ? "隐藏管理" : "显示管理";
        int modeIcon = (state == 0) ? R.drawable.ic_hide : R.drawable.ic_display;
        items.add(new MenuModel(modeText, 11, modeIcon));
        
        // 添加设置选项
        items.add(new MenuModel("设置", 12, R.drawable.ic_setting));
        
        return items;
    }

    /**
     * 初始化数据
     */
    private void initView() {
        // 根据构建配置决定是否开启调试日志
        PagerGridLayoutManager.setDebug(BuildConfig.DEBUG);

        // 创建并配置分页网格布局管理器
        layoutManager = new PagerGridLayoutManager(
                SettingsManager.getRowNumber(),
                SettingsManager.getColumnNumber(),
                SettingsManager.getSlidingMode()
        );
        
        // 设置RecyclerView的布局管理器
        binding.moreGridview.setLayoutManager(layoutManager);
        
        // 关闭RecyclerView的动画，提高性能
        binding.moreGridview.setItemAnimator(null);

        // 设置滑动参数 - 每像素滑动时间
        // 不可过小，避免划过再回退的情况
        layoutManager.setMillisecondPreInch(70);
        
        // 设置最大滚动时间 - 提高滚动流畅度
        layoutManager.setMaxScrollOnFlingDuration(100);
    }

    /**
     * 消息处理方法
     * 处理后台线程发送的消息，更新UI
     */
    private void initAppAdapter() {
        // 创建优化的应用适配器
        mAppAdapter = new OptimizedAppAdapter();
        
        // 启用空视图
        mAppAdapter.setEmptyViewEnable(true);
        mAppAdapter.setEmptyViewLayout(requireActivity(), R.layout.nodata_view);
        
        // 设置项点击事件 - 打开应用
        mAppAdapter.setOnItemClickListener((adapter, view, position) -> {
            AppInfo appInfo = mAllAppList.get(position);
            if (appInfo.getPageType() == 1) {
                // Scene类型跳转
                com.bytedance.scene.Scene sceneInstanceByName = 
                        SceneFactory.getSceneInstanceByName(appInfo.getActivityName());
                requireNavigationScene(AppStoreScene.this).push(sceneInstanceByName);
            } else {
                // 普通应用跳转
                AppUtils.openPackageActivity(requireActivity(), appInfo);
            }
        });
        
        // 设置长按事件 - 显示操作菜单
        mAppAdapter.setOnItemLongClickListener((adapter, view, position) -> {
            openPopup(view.findViewById(R.id.grid_item_app_icon), 
                     adapter.getItem(position), position, 0);
            return true;
        });
        
        // 绑定数据到适配器
        mAppAdapter.updateData(mAllAppList);
        
        // 设置RecyclerView的适配器
        binding.moreGridview.setAdapter(mAppAdapter);
    }

    /**
     * 平滑滚动到指定位置
     * 使用ViewTreeObserver确保布局完成后再滚动
     * 
     * @param position 目标位置
     */
    private void smoothScrollToPosition(final int position) {
        binding.moreGridview.getViewTreeObserver().addOnGlobalLayoutListener(
                new ViewTreeObserver.OnGlobalLayoutListener() {
                    @Override
                    public void onGlobalLayout() {
                        // 移除监听器，避免重复调用
                        binding.moreGridview.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                        // 平滑滚动到指定位置
                        binding.moreGridview.smoothScrollToPosition(position);
                    }
                });
    }

    /**
     * 在UI线程上执行操作
     * 
     * @param runnable 要执行的操作
     */
    private void runOnUiThread(Runnable runnable) {
        if (Thread.currentThread() == Looper.getMainLooper().getThread()) {
            runnable.run();
        } else {
            mUIHandler.post(runnable);
        }
    }

    /**
     * 获取应用数据
     * 优先从本地缓存获取，如无缓存则异步加载
     */
    public void getData() {
        // 使用项目线程池执行后台任务
        ThreadPoolUtil.getThreadPoolExecutor().execute(() -> {
            try {
                // 优先获取本地缓存数据
                if (DataManager.getAppListData().isEmpty()) {
                    // 本地无缓存，初始化Provider并异步查询应用信息
                    mProvider = AppInfoProvider.getInstance(requireActivity());

                    // 调用异步查询方法，添加异常处理
                    mProvider.queryAppInfo()
                        .thenAccept(appInfos -> {
                            try {
                                // 请求完成后关闭加载对话框
                                dismissDialog();

                                // 保存应用列表数据
                                mAllAppList = appInfos;

                                // 记录日志
                                MyLog.v(TAG, "获取到APP启动器信息: " + appInfos.size() + "个应用");

                                // 在UI线程上更新界面
                                runOnUiThread(this::initAppAdapter);
                            } catch (Exception e) {
                                MyLog.e(TAG, "处理查询结果失败: " + e.getMessage());
                                dismissDialog();
                            }
                        })
                        .exceptionally(throwable -> {
                            // 异常处理：确保对话框被关闭
                            MyLog.e(TAG, "查询应用信息失败: " + throwable.getMessage());
                            dismissDialog();

                            // 在UI线程显示错误提示
                            runOnUiThread(() -> {
                                MyLog.e(TAG, "应用加载失败，请稍后重试");
                            });

                            return null;
                        });
                } else {
                    // 从本地缓存加载数据
                    mAllAppList = GSON.fromJson(
                            DataManager.getAppListData(),
                            new TypeToken<List<AppInfo>>(){}.getType()
                    );

                    // 过滤应用列表
                    mAllAppList = filterAppList(mAllAppList, state, "更多", "主页");

                    // 在UI线程上更新界面
                    runOnUiThread(this::initAppAdapter);
                }
            } catch (Exception e) {
                MyLog.e(TAG, "获取应用数据失败: " + e.getMessage());
                dismissDialog();
            }
        });
    }

    /**
     * 刷新应用数据
     * 重新获取所有应用信息并更新UI
     */
    private void refreshData() {
        // 执行诊断检查
        MyLog.d(TAG, "开始应用刷新，执行诊断检查...");
        AppRefreshDiagnostic.runDiagnostic(requireActivity());

        // 显示加载对话框
        showDialog("正在重构应用信息...");

        // 设置超时机制
        setupRefreshTimeout();

        // 确保Provider已初始化
        if (mProvider == null) {
            mProvider = AppInfoProvider.getInstance(requireActivity());
        }

        // 调用异步刷新方法，添加异常处理
        mProvider.refreshAppInfo()
            .thenAccept(appInfoList -> {
                try {
                    // 取消超时任务
                    cancelRefreshTimeout();

                    // 请求完成后关闭加载对话框
                    dismissDialog();

                    // 保存应用列表数据
                    mAllAppList = appInfoList;

                    // 转换为JSON并保存（调试用）
                    String json = GSON.toJson(mAllAppList);
                    MyLog.v(TAG, "刷新应用信息: " + appInfoList.size() + "个应用");

                    // 过滤应用列表
                    mAllAppList = filterAppList(mAllAppList, state, "更多", "主页");

                    // 在UI线程上更新界面
                    runOnUiThread(this::initAppAdapter);
                } catch (Exception e) {
                    MyLog.e(TAG, "处理刷新结果失败: " + e.getMessage());
                    cancelRefreshTimeout();
                    dismissDialog();
                }
            })
            .exceptionally(throwable -> {
                // 异常处理：确保对话框被关闭
                MyLog.e(TAG, "刷新应用信息失败: " + throwable.getMessage());
                cancelRefreshTimeout();
                dismissDialog();

                // 在UI线程显示错误提示
                runOnUiThread(() -> {
                    // 可以添加错误提示，比如Toast
                    MyLog.e(TAG, "应用刷新失败，请稍后重试");
                });

                return null;
            });
    }

    /**
     * 设置刷新超时机制
     */
    private void setupRefreshTimeout() {
        cancelRefreshTimeout(); // 先取消之前的超时任务

        timeoutRunnable = () -> {
            MyLog.w(TAG, "应用刷新超时，强制关闭加载对话框");
            dismissDialog();

            // 显示超时提示
            runOnUiThread(() -> {
                MyLog.e(TAG, "应用刷新超时，请检查网络或稍后重试");
            });
        };

        mUIHandler.postDelayed(timeoutRunnable, REFRESH_TIMEOUT);
    }

    /**
     * 取消刷新超时任务
     */
    private void cancelRefreshTimeout() {
        if (timeoutRunnable != null) {
            mUIHandler.removeCallbacks(timeoutRunnable);
            timeoutRunnable = null;
        }
    }

    /**
     * 隐藏应用
     * 修改应用状态并保存到本地
     *
     * @param item     应用信息
     * @param position 列表中的位置
     */
    private void hideApp(AppInfo item, int position) {
        // 从本地获取完整的应用列表
        List<AppInfo> allAppList = GSON.fromJson(
                DataManager.getAppListData(), 
                new TypeToken<List<AppInfo>>() {}.getType()
        );
        
        // 查找并修改应用的状态
        allAppList.stream()
                .filter(appInfo -> appInfo.getPackageName().equals(item.getPackageName()))
                .findFirst()
                .ifPresent(appInfo -> appInfo.setState(state == 0 ? 1 : 0));

        // 保存修改后的应用列表
        String json = GSON.toJson(allAppList);
        DataManager.setAppListData(json);
        
        // 从当前显示的列表中移除该应用并更新适配器
        if (mAppAdapter != null) {
            mAllAppList.remove(position);
            mAppAdapter.updateData(mAllAppList);
        }
    }

    /**
     * 置顶应用
     * 将应用移动到列表前部
     *
     * @param item           应用信息
     * @param position       当前位置
     * @param targetPosition 目标位置
     */
    private void topping(AppInfo item, int position, int targetPosition) {
        // 如果位置相同，不需要操作
        if (position == targetPosition) {
            return;
        }
        
        // 更新内存中的列表
        if (position < mAllAppList.size() && targetPosition < mAllAppList.size()) {
            AppInfo appInfo = mAllAppList.remove(position);
            mAllAppList.add(targetPosition, appInfo);
            
            // 更新适配器
            if (mAppAdapter != null) {
                mAppAdapter.updateData(mAllAppList);
            }
        }

        // 获取完整的应用列表
        List<AppInfo> allAppList = GSON.fromJson(
                DataManager.getAppListData(), 
                new TypeToken<List<AppInfo>>() {}.getType()
        );
        
        // 查找应用在完整列表中的位置
        int listPosition = IntStream.range(0, allAppList.size())
                .filter(i -> item.getPackageName().equals(allAppList.get(i).getPackageName()))
                .findFirst()
                .orElse(-1);

        // 应用不在列表中，直接返回
        if (listPosition == -1) {
            return;
        }
        
        // 计算目标位置 - 应用类型为0的元素数量
        int targetListPosition = (int) allAppList.stream()
                .filter(appInfo -> appInfo.getType() == 0)
                .count();
        
        // 根据目标位置和当前位置的关系决定移动方式
        if (targetListPosition < listPosition) {
            // 向前移动 - 旋转子列表使目标元素移动到前面
            Collections.rotate(allAppList.subList(targetListPosition, listPosition + 1), 1);
        } else {
            // 向后移动 - 旋转子列表使目标元素移动到后面
            Collections.rotate(allAppList.subList(listPosition, targetListPosition + 1), -1);
        }

        // 保存修改后的应用列表
        DataManager.setAppListData(GSON.toJson(allAppList));
    }

    /**
     * 获取所有桌面启动器
     * 查询系统中所有注册为桌面的应用
     *
     * @param context 上下文
     * @return 启动器列表
     */
    public List<ResolveInfo> getAllLaunchers(Context context) {
        Intent intent = new Intent(Intent.ACTION_MAIN);
        intent.addCategory(Intent.CATEGORY_HOME);
        PackageManager pm = context.getPackageManager();
        return pm.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY);
    }

    /**
     * 过滤应用列表
     * 根据状态和排除的应用名称过滤列表
     *
     * @param appList  完整应用列表
     * @param state    显示状态（0=正常, 1=隐藏）
     * @param appName  要排除的应用名称1
     * @param appName1 要排除的应用名称2
     * @return 过滤后的应用列表
     */
    public List<AppInfo> filterAppList(List<AppInfo> appList, int state, String appName, String appName1) {
        // 使用Stream进行高效过滤
        return appList.stream()
                .filter(appInfo -> 
                    // 过滤状态匹配的应用
                    appInfo.getState() == state && 
                    // 排除指定名称的应用
                    !appInfo.getAppName().equals(appName) && 
                    !appInfo.getAppName().equals(appName1))
                .collect(Collectors.toList());
    }

    /**
     * 显示加载对话框
     *
     * @param content 对话框显示内容
     */
    private void showDialog(String content) {
        MProgressDialog.showProgress(requireActivity(), content);
    }

    /**
     * 隐藏加载对话框
     */
    private void dismissDialog() {
        MProgressDialog.dismissProgress();
    }

    /**
     * 点击事件处理
     *
     * @param view 点击的视图
     */
    @SuppressLint("NonConstantResourceId")
    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.more_btn_back:
                // 返回上一页
                requireNavigationScene(this).pop();
                break;
            case R.id.more_menu_container:
                // 打开更多菜单
                openPopup(view, null, 0, 1);
                break;
            default:
                // 忽略其他点击事件
        }
    }

    /**
     * 页面恢复事件
     * 可在此处添加页面恢复时的刷新逻辑
     */
    @Override
    public void onResume() {
        super.onResume();
        // 页面恢复时的操作（可根据需要添加）
    }

    /**
     * 页面销毁事件
     * 清理资源，避免内存泄漏
     */
    @Override
    public void onDestroy() {
        super.onDestroy();

        // 取消超时任务
        cancelRefreshTimeout();

        // 确保关闭加载对话框
        dismissDialog();

        // 解注册观察者，避免内存泄漏
        if (mMoreNotice != null) {
            MoreSettingScopeBus.eventBean().removeObserver(mMoreNotice);
            mMoreNotice = null;
        }

        // 解注册全局通知观察者
        GeneralNotificationScopeBus.eventBean().removeObservers(this);

        // 清理资源引用
        mAllAppList = null;
        mAppAdapter = null;
        layoutManager = null;

        // 关闭任何可能打开的弹窗
        if (popupWindow != null) {
            popupWindow.dismiss();
            popupWindow = null;
        }
    }

    private void openMoreSettingDialog() {
        FragmentActivity fragmentActivity = (FragmentActivity) requireActivity();
        MoreSettingDialogFragment fragment = new MoreSettingDialogFragment();
        fragment.show(fragmentActivity.getSupportFragmentManager(), MoreSettingDialogFragment.class.getSimpleName());
    }
}