package com.smartcar.easylauncher.modules.touch.ui.activity;

import android.content.res.Resources;
import android.os.Bundle;
import android.view.View;

import androidx.fragment.app.Fragment;

import com.angcyo.tablayout.DslTabLayoutConfig;
import com.angcyo.tablayout.delegate.ViewPager1Delegate;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.setting.SettingFragmentPagerAdapter;
import com.smartcar.easylauncher.core.base.BaseActivity;
import com.smartcar.easylauncher.databinding.ActivitySmartFloatBinding;
import com.smartcar.easylauncher.infrastructure.interfaces.SkinChangeListener;
import com.smartcar.easylauncher.data.model.theme.SkinModel;
import com.smartcar.easylauncher.infrastructure.skin.SkinManager;
import com.smartcar.easylauncher.modules.touch.ui.fragment.CustomMenuFragment;
import com.smartcar.easylauncher.modules.touch.ui.fragment.FloatWindowSettingFragment;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import me.jessyan.autosize.AutoSizeCompat;
import me.jessyan.autosize.AutoSizeConfig;

public class SmartFloatActivity extends BaseActivity implements SkinChangeListener {


    private SettingFragmentPagerAdapter settingFragmentPagerAdapter;
    private ActivitySmartFloatBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivitySmartFloatBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        registerSkinChangeListener();
        initTabLayout();
        ViewPager1Delegate.Companion.install(binding.myViewPager, binding.tabLayout, true);
        initTab(savedInstanceState == null);
    }


    @Override
    public void initImmersionBar() {
        super.initImmersionBar();
    }

    @Override
    public void onSkinChange(SkinModel messageEvent) {
        initTabLayout();
    }

    private void initTabLayout() {
        binding.tabLayout.getTabIndicator().setIndicatorDrawable(SkinManager.getInstance().getDrawable(R.drawable.indicator_bottom_line));
        binding.tabLayout.invalidate();
        binding.tabLayout.getTabLayoutConfig().setTabSelectColor(SkinManager.getInstance().getColor(R.color.tab_select_color));
        binding.tabLayout.getTabLayoutConfig().setTabDeselectColor(SkinManager.getInstance().getColor(R.color.tab_deselect_color));
        binding.tabLayout.getDslSelector().updateStyle();
    }

    private void initTab(boolean state) {
        binding.tabLayout.configTabLayoutConfig(new Function1<DslTabLayoutConfig, Unit>() {
            @Override
            public Unit invoke(DslTabLayoutConfig dslTabLayoutConfig) {
                return null;
            }
        });

        List<Fragment> list = new ArrayList<>();
        Class[] fragmentClasses = new Class[]{FloatWindowSettingFragment.class, CustomMenuFragment.class/*, PersonalizedSettingFragment.class, SystemSettingsFragment.class, AboutUsFragment.class*//*, LaboratorySettingFragment.class*/};
        for (Class fragmentClass : fragmentClasses) {
            Fragment fragment = null;
            if (state) {
                try {
                    fragment = (Fragment) fragmentClass.newInstance();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                List<Fragment> fragmentList = getSupportFragmentManager().getFragments();
                for (Fragment item : fragmentList) {
                    String fragmentTag = item.getClass().getSimpleName();
                    if (fragmentTag.compareTo(fragmentClass.getSimpleName()) == 0) {
                        fragment = item;
                        break;
                    }
                }
            }
            if (fragment == null) {
                try {
                    fragment = (Fragment) fragmentClass.newInstance();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            list.add(fragment);
        }

        settingFragmentPagerAdapter = new SettingFragmentPagerAdapter(getSupportFragmentManager(), list);
        binding.myViewPager.setAdapter(settingFragmentPagerAdapter);
        binding.myViewPager.setCurrentItem(0);
        binding.myViewPager.setOffscreenPageLimit(1);
        binding.btBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });
    }


    @Override
    public Resources getResources() {
        //需要升级到 v1.1.2 及以上版本才能使用 AutoSizeCompat
        int width = AutoSizeConfig.getInstance().getScreenWidth();
        int height = AutoSizeConfig.getInstance().getScreenHeight();
        AutoSizeCompat.autoConvertDensity(super.getResources(), 900, width > height);
        return super.getResources();
    }


//    @Override
//    public void bindViewData() {
//
//    }
}