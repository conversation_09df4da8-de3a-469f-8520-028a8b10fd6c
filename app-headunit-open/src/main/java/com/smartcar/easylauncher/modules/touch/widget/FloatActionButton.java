package com.smartcar.easylauncher.modules.touch.widget;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.Nullable;

/**
 * FloatActionButton类，继承自AppCompatImageView，主要用于显示一个浮动的动作按钮。
 * 该类可以在代码中直接创建实例，也可以在XML布局文件中使用。
 *
 * @since 1.0.0
 */
public class FloatActionButton extends androidx.appcompat.widget.AppCompatImageView {

    /**
     * 构造函数，用于在代码中创建FloatActionButton对象。
     *
     * @param context 应用程序的上下文环境
     */
    public FloatActionButton(Context context) {
        super(context);
    }

    /**
     * 构造函数，用于在XML布局文件中使用该自定义控件。
     *
     * @param context 应用程序的上下文环境
     * @param attrs   包含XML布局文件中定义的属性集合
     */
    public FloatActionButton(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    /**
     * 构造函数，用于在XML布局文件中使用该自定义控件，并且还可以在布局文件中设置style样式。
     *
     * @param context         应用程序的上下文环境
     * @param attrs           包含XML布局文件中定义的属性集合
     * @param defStyleAttr    定义在主题中的默认样式属性
     */
    public FloatActionButton(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    // 如果需要，可以在这里添加更多的方法和成员变量，并为其添加相应的Javadoc注释
}
