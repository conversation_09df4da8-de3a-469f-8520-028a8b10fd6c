package com.smartcar.easylauncher.modules.tpms.protocol.v3;

import com.google.gson.Gson;
import com.smartcar.easylauncher.infrastructure.event.scope.device.cody.TirePressureScopeBus;
import com.smartcar.easylauncher.data.model.vehicle.TireCodeQueryModel;
import com.smartcar.easylauncher.data.model.vehicle.TireOperateResultModel;
import com.smartcar.easylauncher.modules.tpms.device.TpmsDevice;
import com.smartcar.easylauncher.modules.tpms.protocol.base.Protocol;
import com.smartcar.easylauncher.modules.tpms.protocol.base.model.TpmsData;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.List;

import android.os.Handler;
import android.os.Looper;
import android.os.HandlerThread;

/**
 * V3协议实现
 */
public class V3Protocol implements Protocol {
    private static final String TAG = "V3Protocol";

    // 协议常量
    private static final int HANDSHAKE_TIMEOUT = 5000;  // 握手超时5秒
    private static final int HEARTBEAT_INTERVAL = 10000; // 心跳间隔1秒
    private static final int MAX_HANDSHAKE_RETRIES = 3; // 最大握手重试次数

    // 命令字
    /**
     * 控制命令字
     */
    private static final byte CONTROL_COMMAND = 6;

    /**
     * ID查询命令
     */
    private static final byte ID_QUERY_COMMAND = 7;

    /**
     * ID数据响应
     */
    private static final byte ID_DATA_RESPONSE = 9;

    /**
     * 电池电压查询命令字
     */
    private static final byte BATTERY_VOLTAGE_RESPONSE = 10;

    /**
     * 状态查询命令字
     */
    private static final byte STATUS_QUERY = 25;

    private final TpmsDevice device;
    private final V3ProtocolEncoder encoder;
    private final V3ProtocolDecoder decoder;
    private Protocol.Callback callback;

    // 状态标志
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicBoolean isHandshakeComplete = new AtomicBoolean(false);

    // 替换Timer为Handler
    private Handler handshakeHandler;
    private Handler heartbeatHandler;
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    private int handshakeRetries = 0;

    private final V3PacketBuffer packetBuffer;

    public V3Protocol(TpmsDevice device) {
        this.device = device;
        this.encoder = new V3ProtocolEncoder();
        this.decoder = new V3ProtocolDecoder();
        this.packetBuffer = new V3PacketBuffer();

        HandlerThread handlerThread = new HandlerThread("TpmsProtocolThread");
        handlerThread.start();
        handshakeHandler = new Handler(handlerThread.getLooper());
        heartbeatHandler = new Handler(handlerThread.getLooper());
    }

    @Override
    public void start() {
        if (!isRunning.compareAndSet(false, true)) {
            return;
        }

        MyLog.d(TAG, "启动V3协议");
        startHandshake();
    }

    @Override
    public void stop() {
        if (!isRunning.compareAndSet(true, false)) {
            return;
        }

        MyLog.d(TAG, "停止V3协议");
        stopHeartbeat();
        stopHandshake();
        isHandshakeComplete.set(false);
        handshakeRetries = 0;
    }

    @Override
    public void handleData(byte[] data) {
        if (!isRunning.get()) {
            MyLog.d(TAG, "协议未运行，忽略数据");
            return;
        }

        try {
            MyLog.d(TAG, "收到原始数据: " + bytesToHex(data));

            // 处理接收到的数据
            List<byte[]> packets = packetBuffer.handleBuffer(data, data.length);
            MyLog.d(TAG, "解析得到 " + packets.size() + " 个数据包");

            // 处理解析出的数据包
            for (byte[] packet : packets) {
                MyLog.d(TAG, "处理数据包: " + bytesToHex(packet));

                // 解析命令类型
                byte command = packet[2];
                //解析命令标识
                byte Identifier = packet[3];
                MyLog.d(TAG, "命令类型和命令标识:   " + command + " ------ " + Identifier);

                switch (command) {
                    case CONTROL_COMMAND:
                        if (Identifier == -91) {
                            MyLog.d(TAG, "属于握手数据");
                            if (!isHandshakeComplete.get()) {
                                MyLog.d(TAG, "处理握手响应");
                                handleHandshakeResponse(packet);
                            } else {
                                MyLog.d(TAG, "已完成握手，忽略握手响应");
                            }
                        } else {
                            //胎压学习解析
                            tireLearn(packet);
                            MyLog.d(TAG, "轮胎学习数据，学习状态  " + Identifier + " 学习轮胎" + packet[4]);
                        }

                        break;
                    case ID_QUERY_COMMAND:
                        if (isHandshakeComplete.get()) {
                            MyLog.d(TAG, "处理换胎结果");
                            handleTireExchangeResult(packet);
                        } else {
                            MyLog.d(TAG, "已完成握手，忽略握手响应");
                        }
                        break;

                    case ID_DATA_RESPONSE:
                        if (isHandshakeComplete.get()) {
                            MyLog.d(TAG, "ID数据响应");
                            postTireIDData(packet);
                        }
                        break;

                    case 0x08:
                    case BATTERY_VOLTAGE_RESPONSE:
                        if (isHandshakeComplete.get()) {
                            MyLog.d(TAG, "处理轮胎状态数据");
                            handleTireStatusData(packet);
                        } else {
                            MyLog.d(TAG, "未完成握手，忽略轮胎状态数据");
                        }
                        break;

                    default:
                        MyLog.w(TAG, "未知命令类型: 0x" + String.format("%02X", command));
                        break;
                }
            }
        } catch (Exception e) {
            MyLog.e(TAG, "数据处理异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 胎压学习解析
     *
     * @param packet
     */
    private void tireLearn(byte[] packet) {
        TireOperateResultModel tireOperateResultModel = decoder.tireLearn(packet);
        TirePressureScopeBus.eventTireOperateResponse().post(tireOperateResultModel);
    }

    /**
     * 处理换胎结果
     *
     * @param packet
     */
    private void handleTireExchangeResult(byte[] packet) {
        int decodeTireExchangeResult = decoder.decodeTireExchangeResult(packet);
        TirePressureScopeBus.eventTireOperateResponse().post(new TireOperateResultModel(0, decodeTireExchangeResult));
    }

    @Override
    public void queryTireIds() {
        // V3协议特定的查询实现
        byte[] command = encoder.encodeTirePressureQuery();
        device.send(command);
    }

    @Override
    public void exchangeTires(byte pos1, byte pos2) {
        //轮胎参数缓存到 decoder
        decoder.setTireExchangeParams(pos1, pos2);
        // V3协议特定的交换实现
        byte[] command = encoder.encodeTireExchange(
                pos1,
                pos2
        );
        device.send(command);
    }

    @Override
    public void learn(byte key) {
        byte[] encodeLearn = encoder.encodeLearn(key);
        device.send(encodeLearn);
    }

    @Override
    public void cancel() {
        byte[] command = encoder.encodeCancel();
        device.send(command);
    }

    /**
     * 发送轮胎 id 数据
     *
     * @param data
     */
    private void postTireIDData(byte[] data) {
        TireCodeQueryModel tireID = decoder.decodeTireId(data);
        TirePressureScopeBus.eventTireCodeQueryBean().post(tireID);
    }

    @Override
    public void setCallback(Protocol.Callback callback) {
        this.callback = callback;
    }

    /**
     * 开始握手
     */
    private void startHandshake() {
        MyLog.d(TAG, "开始握手");
        handshakeRetries = 0;
        isHandshakeComplete.set(false);

        // 停止之前的定时任务
        stopHandshake();
        stopHeartbeat();

        // 创建握手任务
        final Runnable handshakeTask = new Runnable() {
            @Override
            public void run() {
                if (!isRunning.get() || isHandshakeComplete.get()) {
                    stopHandshake();
                    return;
                }

                if (handshakeRetries >= MAX_HANDSHAKE_RETRIES) {
                    MyLog.e(TAG, "握手重试次数超限");
                    mainHandler.post(() -> handleError("握手失败"));
                    stopHandshake();
                    return;
                }

                // 发送握手命令
                byte[] handshakeCmd = encoder.encodeHandshake();
                device.send(handshakeCmd);
                handshakeRetries++;
                MyLog.d(TAG, "发送握手命令，重试次数: " + handshakeRetries);

                // 继续下一次握手尝试
                handshakeHandler.postDelayed(this, HANDSHAKE_TIMEOUT);
            }
        };

        // 开始握手任务
        handshakeHandler.post(handshakeTask);
    }

    /**
     * 处理握手响应
     */
    private void handleHandshakeResponse(byte[] data) {
        if (decoder.validateHandshakeResponse(data)) {
            if (isHandshakeComplete.compareAndSet(false, true)) {
                MyLog.d(TAG, "握手成功");

                stopHandshake();
                startHeartbeat();
            }
        }
    }

    /**
     * 处理轮胎状态数据
     */
    private void handleTireStatusData(byte[] data) {
        try {
            TpmsData tpmsData = decoder.decode(data);
            if (tpmsData != null && callback != null) {
                MyLog.d(TAG, "处理轮胎状态数据  " + new Gson().toJson(tpmsData));
                callback.onData(tpmsData);
            }
        } catch (Exception e) {
            MyLog.e(TAG, "数据处理异常: " + e.getMessage());
            e.printStackTrace();
            
            // 添加更详细的日志
            if (e.getMessage().contains("d != java.lang.Float")) {
                MyLog.e(TAG, "类型转换错误，请检查pressure字段的类型");
            }
        }
    }

    /**
     * 启动心跳
     */
    private void startHeartbeat() {
        MyLog.d(TAG, "启动心跳");
        stopHeartbeat();

        final Runnable heartbeatTask = new Runnable() {
            @Override
            public void run() {
                if (!isRunning.get() || !isHandshakeComplete.get()) {
                    stopHeartbeat();
                    return;
                }

                try {
                    byte[] heartbeatCmd = encoder.encodeHeartbeat();
                    //  device.send(heartbeatCmd);
                    MyLog.d(TAG, "发送心跳命令");
                    // 继续下一次心跳
                    heartbeatHandler.postDelayed(this, HEARTBEAT_INTERVAL);
                } catch (Exception e) {
                    MyLog.e(TAG, "发送心跳异常: " + e.getMessage());
                    stopHeartbeat();
                }
            }
        };

        // 开始心跳任务
        heartbeatHandler.post(heartbeatTask);
    }

    /**
     * 停止心跳
     */
    private void stopHeartbeat() {
        if (heartbeatHandler != null) {
            heartbeatHandler.removeCallbacksAndMessages(null);
        }
    }

    /**
     * 停止握手
     */
    private void stopHandshake() {
        if (handshakeHandler != null) {
            handshakeHandler.removeCallbacksAndMessages(null);
        }
    }

    /**
     * 处理错误
     */
    private void handleError(String error) {
        MyLog.e(TAG, error);
        if (callback != null) {
            // TODO: 实现错误数据模型并通知
        }
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b));
        }
        return sb.toString().trim();
    }

    /**
     * 释放资源
     */
    public void release() {
        stop();
        if (handshakeHandler != null) {
            handshakeHandler.getLooper().quit();
            handshakeHandler = null;
        }
        if (heartbeatHandler != null) {
            heartbeatHandler.getLooper().quit();
            heartbeatHandler = null;
        }
        packetBuffer.clear();
    }
} 