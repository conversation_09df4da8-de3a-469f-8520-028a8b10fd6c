package com.smartcar.easylauncher.modules.keysetting;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.LayoutAnimationController;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bytedance.scene.group.UserVisibleHintGroupScene;
import com.chad.library.adapter4.QuickAdapterHelper;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.setting.BroadcastPresetQuickAdapter;
import com.smartcar.easylauncher.shared.adapter.personal.CarModelHeaderAdapter;
import com.smartcar.easylauncher.core.constants.BroadcastPresets;
import com.smartcar.easylauncher.databinding.SceneBroadcastPresetBinding;

import java.util.ArrayList;
import java.util.List;

/**
 * 广播预设场景
 * 用于显示和选择不同车型的广播预设模板
 *
 * <AUTHOR>
 * @date 2024/07/16
 */
public class BroadcastPresetScene extends UserVisibleHintGroupScene {
    private static final String TAG = BroadcastPresetScene.class.getSimpleName();

    // 视图绑定对象
    private SceneBroadcastPresetBinding binding;

    // 数据对象
    private List<CarModelHeaderAdapter.CarModelItem> carModels;
    private List<BroadcastPresetQuickAdapter.PresetItem> allPresets;
    private List<BroadcastPresetQuickAdapter.PresetItem> filteredPresets;

    // 当前选中的车型
    private String selectedCarModel = "all";

    // 适配器
    private BroadcastPresetQuickAdapter presetAdapter;
    private CarModelHeaderAdapter carModelHeaderAdapter;
    private QuickAdapterHelper adapterHelper;

    // 预设选择回调
    private OnPresetSelectedListener listener;

    // 预设类型
    private static final String TYPE_SYSTEM = "系统";
    private static final String TYPE_MEDIA = "媒体";
    private static final String TYPE_APP = "应用";

    // 滚动状态
    private boolean isHeaderCollapsed = false;

    /**
     * 预设模板选择监听器
     */
    public interface OnPresetSelectedListener {
        /**
         * 当选择预设模板时调用
         *
         * @param preset 选择的预设模板
         */
        void onPresetSelected(BroadcastPresets.PresetTemplate preset);
    }

    /**
     * 构造函数
     *
     * @param listener 预设模板选择监听器
     */
    public BroadcastPresetScene(OnPresetSelectedListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewGroup onCreateView(@NonNull LayoutInflater inflater, @NonNull ViewGroup container, Bundle savedInstanceState) {
        binding = SceneBroadcastPresetBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // 初始化数据
        initData();

        // 初始化视图
        initView();

        // 初始化事件监听
        initListeners();
    }

    /**
     * 初始化数据
     */
    private void initData() {
        // 初始化车型数据 - 减少车型选项，只保留最常用的几个
        carModels = new ArrayList<>();
        carModels.add(new CarModelHeaderAdapter.CarModelItem("all", "所有车型", R.drawable.ic_apps));
        carModels.add(new CarModelHeaderAdapter.CarModelItem("大众", "大众", R.drawable.ic_apps));
        carModels.add(new CarModelHeaderAdapter.CarModelItem("丰田", "丰田", R.drawable.ic_apps));
        carModels.add(new CarModelHeaderAdapter.CarModelItem("本田", "本田", R.drawable.ic_apps));

        // 初始化预设数据 - 直接将所有预设合并到一个列表
        allPresets = new ArrayList<>();
        
        // 添加系统预设
        List<BroadcastPresets.PresetTemplate> systemPresets = BroadcastPresets.getSystemPresets();
        for (BroadcastPresets.PresetTemplate preset : systemPresets) {
            allPresets.add(new BroadcastPresetQuickAdapter.PresetItem(preset, TYPE_SYSTEM));
        }
        
        // 添加媒体预设
        List<BroadcastPresets.PresetTemplate> mediaPresets = BroadcastPresets.getMediaPresets();
        for (BroadcastPresets.PresetTemplate preset : mediaPresets) {
            allPresets.add(new BroadcastPresetQuickAdapter.PresetItem(preset, TYPE_MEDIA));
        }
        
        // 添加应用预设
        List<BroadcastPresets.PresetTemplate> appPresets = BroadcastPresets.getAppPresets();
        for (BroadcastPresets.PresetTemplate preset : appPresets) {
            allPresets.add(new BroadcastPresetQuickAdapter.PresetItem(preset, TYPE_APP));
        }
        
        // 初始化筛选后的预设数据
        filteredPresets = new ArrayList<>(allPresets);
    }

    /**
     * 初始化视图
     */
    private void initView() {
        // 设置预设RecyclerView
        binding.rvPresets.setLayoutManager(new LinearLayoutManager(getActivity()));
        
        // 创建预设适配器
        presetAdapter = new BroadcastPresetQuickAdapter();
        presetAdapter.submitList(filteredPresets);
        
        // 创建车型头部适配器
        carModelHeaderAdapter = new CarModelHeaderAdapter();
        carModelHeaderAdapter.setItem(new CarModelHeaderAdapter.CarModelHeader(carModels, 0));
        
        // 使用QuickAdapterHelper组合适配器
        adapterHelper = new QuickAdapterHelper.Builder(presetAdapter)
                .build();
        
        // 添加车型头部适配器
        adapterHelper.addBeforeAdapter(carModelHeaderAdapter);
        
        // 设置组合后的适配器
        binding.rvPresets.setAdapter(adapterHelper.getAdapter());
        
        // 设置列表动画
        LayoutAnimationController animation = new LayoutAnimationController(
                android.view.animation.AnimationUtils.loadAnimation(getActivity(), R.anim.item_animation_from_bottom),
                0.1f  // 减小延迟，提高性能
        );
        binding.rvPresets.setLayoutAnimation(animation);
        
        // 设置回到顶部按钮初始状态为隐藏
        binding.btnScrollTop.setVisibility(View.GONE);
    }

    /**
     * 初始化事件监听
     */
    private void initListeners() {
        // 返回按钮点击事件
        binding.btnBack.setOnClickListener(v -> {
            getNavigationScene(this).pop();
        });
        
        // 回到顶部按钮点击事件
        binding.btnScrollTop.setOnClickListener(v -> {
            binding.rvPresets.smoothScrollToPosition(0);
            animateButtonHide(binding.btnScrollTop);
        });
        
        // 监听预设列表滚动
        binding.rvPresets.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                
                LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                if (layoutManager != null) {
                    int firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition();
                    
                    // 当滚动到一定位置后显示回到顶部按钮
                    if (firstVisibleItemPosition > 10) {
                        if (binding.btnScrollTop.getVisibility() == View.GONE) {
                            animateButtonShow(binding.btnScrollTop);
                        }
                    } else {
                        if (binding.btnScrollTop.getVisibility() == View.VISIBLE) {
                            animateButtonHide(binding.btnScrollTop);
                        }
                    }
                    
                    // 处理头部折叠
                    handleHeaderCollapse(dy, firstVisibleItemPosition);
                }
            }
        });
        
        // 设置车型选择监听器
        carModelHeaderAdapter.setOnCarModelSelectedListener((carModel, position) -> {
            // 选择车型
            selectCarModel(carModel, position);
        });
        
        // 设置预设选择监听器
        presetAdapter.setOnPresetSelectedListener(this::onPresetSelected);
    }
    
    /**
     * 处理头部折叠
     * 
     * @param dy 垂直滚动距离
     * @param firstVisibleItemPosition 第一个可见项的位置
     */
    private void handleHeaderCollapse(int dy, int firstVisibleItemPosition) {
        // 向上滚动且第一个可见项不是头部时，折叠头部
        if (dy > 0 && firstVisibleItemPosition > 0 && !isHeaderCollapsed) {
            isHeaderCollapsed = true;
            carModelHeaderAdapter.setCollapsed(true);
        }
        // 向下滚动到顶部时，展开头部
        else if (dy < 0 && firstVisibleItemPosition <= 1 && isHeaderCollapsed) {
            isHeaderCollapsed = false;
            carModelHeaderAdapter.setCollapsed(false);
        }
    }

    /**
     * 显示按钮动画
     * 
     * @param button 按钮视图
     */
    private void animateButtonShow(View button) {
        button.setVisibility(View.VISIBLE);
        button.setAlpha(0f);
        button.animate()
                .alpha(1f)
                .setDuration(200)
                .setInterpolator(new AccelerateDecelerateInterpolator())
                .start();
    }
    
    /**
     * 隐藏按钮动画
     * 
     * @param button 按钮视图
     */
    private void animateButtonHide(View button) {
        button.animate()
                .alpha(0f)
                .setDuration(200)
                .setInterpolator(new AccelerateDecelerateInterpolator())
                .withEndAction(() -> button.setVisibility(View.GONE))
                .start();
    }

    /**
     * 选择车型
     *
     * @param carModel 车型名称
     * @param position 选中的位置
     */
    private void selectCarModel(String carModel, int position) {
        // 更新选中的车型
        selectedCarModel = carModel;
        
        // 更新适配器选中状态
        carModelHeaderAdapter.updateSelectedPosition(position);
        
        // 根据车型筛选预设模板
        filterPresetsByCarModel();
        
        // 显示提示
        MToast.makeTextShort(getActivity(), "已选择" + (carModel.equals("all") ? "所有车型" : carModel) + "预设模板");
    }

    /**
     * 根据车型筛选预设模板
     */
    private void filterPresetsByCarModel() {
        if (selectedCarModel.equals("all")) {
            // 显示所有预设
            filteredPresets = new ArrayList<>(allPresets);
        } else {
            // 筛选特定车型的预设
            filteredPresets = new ArrayList<>();
            
            // 遍历所有预设，按车型筛选
            for (BroadcastPresetQuickAdapter.PresetItem item : allPresets) {
                // 检查预设是否适用于选中的车型
                BroadcastPresets.PresetTemplate preset = item.getPreset();
                if (preset.isGeneral() || selectedCarModel.equals(preset.getCarModel())) {
                    filteredPresets.add(item);
                }
            }
        }
        
        // 更新适配器数据
        presetAdapter.submitList(filteredPresets);
        
        // 检查是否存在预设模板
        checkEmpty();
    }

    /**
     * 检查是否存在预设模板
     */
    private void checkEmpty() {
        boolean isEmpty = filteredPresets.isEmpty();
        
        // 设置空状态视图可见性
        if (isEmpty && binding.layoutEmpty.getVisibility() == View.GONE) {
            binding.layoutEmpty.setVisibility(View.VISIBLE);
            binding.rvPresets.setVisibility(View.GONE);
            
            // 添加淡入动画
            AlphaAnimation fadeIn = new AlphaAnimation(0.0f, 1.0f);
            fadeIn.setDuration(300);
            binding.layoutEmpty.startAnimation(fadeIn);
        } else if (!isEmpty && binding.layoutEmpty.getVisibility() == View.VISIBLE) {
            // 添加淡出动画
            AlphaAnimation fadeOut = new AlphaAnimation(1.0f, 0.0f);
            fadeOut.setDuration(300);
            fadeOut.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {}
                
                @Override
                public void onAnimationEnd(Animation animation) {
                    binding.layoutEmpty.setVisibility(View.GONE);
                    binding.rvPresets.setVisibility(View.VISIBLE);
                    
                    // 添加淡入动画
                    AlphaAnimation fadeIn = new AlphaAnimation(0.0f, 1.0f);
                    fadeIn.setDuration(300);
                    binding.rvPresets.startAnimation(fadeIn);
                }
                
                @Override
                public void onAnimationRepeat(Animation animation) {}
            });
            binding.layoutEmpty.startAnimation(fadeOut);
        }
    }

    /**
     * 当选择预设模板时调用
     *
     * @param preset 选择的预设模板
     */
    private void onPresetSelected(BroadcastPresets.PresetTemplate preset) {
        if (listener != null) {
            listener.onPresetSelected(preset);
        }
        
        // 返回上一个场景
        getNavigationScene(this).pop();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
} 