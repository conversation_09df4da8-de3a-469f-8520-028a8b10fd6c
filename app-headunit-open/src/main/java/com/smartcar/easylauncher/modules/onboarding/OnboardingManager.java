package com.smartcar.easylauncher.modules.onboarding;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.os.LocaleList;

import com.bytedance.scene.Scene;
import com.smartcar.easylauncher.app.App;
import com.smartcar.easylauncher.core.manager.ActivityManager;
import com.smartcar.easylauncher.core.manager.AuthorityManager;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.util.Locale;

/**
 * 引导流程管理器
 * 负责管理引导流程的状态、配置和导航
 *
 * <AUTHOR>
 */
public class OnboardingManager {
    private static final String TAG = "OnboardingManager";

    private static volatile OnboardingManager instance;

    private OnboardingManager() {
        // 私有构造函数
    }


    public static OnboardingManager getInstance() {
        if (instance == null) {
            synchronized (OnboardingManager.class) {
                if (instance == null) {
                    instance = new OnboardingManager();
                }
            }
        }
        return instance;
    }

    /**
     * 检查是否需要显示引导流程
     * 如果用户已完成引导流程或已同意隐私协议，则不需要显示
     */
    public boolean shouldShowOnboarding() {
        try {
            // 如果已经完成引导流程，则不再显示
            if (isOnboardingCompleted()) {
                return false;
            }
            
            // 如果已经同意隐私协议，则不再显示引导流程
            // 如果AuthorityManager.getUseLicense()抛出异常，默认显示引导流程
            return !AuthorityManager.getUseLicense();
        } catch (Exception e) {
            MyLog.e(TAG, "检查引导状态时出错: " + e.getMessage());
            // 出现异常时，默认显示引导流程
            return true;
        }
    }

    /**
     * 获取引导流程的第一个场景
     */
    public Scene getFirstOnboardingScene() {
        return new OnboardingLanguageScene();
    }

    /**
     * 检查引导流程是否已完成
     */
    public boolean isOnboardingCompleted() {
        return SettingsManager.isOnboardingCompleted();
    }

    /**
     * 设置引导流程完成状态
     */
    public void setOnboardingCompleted(boolean completed) {
        MyLog.v(TAG, "设置引导流程完成状态: " + completed);
        SettingsManager.setOnboardingCompleted(completed);
    }

    /**
     * 保存选择的语言
     */
    public void saveSelectedLanguage(String language) {
        MyLog.v(TAG, "保存选择的语言: " + language);
        SettingsManager.setSelectedLanguage(language);
        // 不再这里应用语言设置，改为由调用者决定何时应用
        // applyLanguage(language);
    }

    /**
     * 获取选择的语言
     */
    public String getSelectedLanguage() {
        return SettingsManager.getSelectedLanguage();
    }

    /**
     * 应用语言设置到整个应用
     * @param languageCode 语言代码，如zh, en, fr等
     */
    public void applyLanguage(String languageCode) {
        MyLog.v(TAG, "应用语言设置: " + languageCode);
        
        if (App.getContextInstance() == null) {
            MyLog.e(TAG, "应用语言失败: Context为空");
            return;
        }
        
        try {
            // 将语言代码转换为Locale对象
            Locale locale = getLocaleFromLanguageCode(languageCode);
            
            // 保存当前的Locale配置
            Locale.setDefault(locale);
            
            // 应用语言设置到应用上下文
            Context appContext = App.getContextInstance();
            updateContextConfiguration(appContext, locale);
            
            // 应用语言设置到当前Activity
            Context currentContext = ActivityManager.getInstance().getTopActivityContext();
            if (currentContext != null && currentContext != appContext) {
                updateContextConfiguration(currentContext, locale);
            }
            
            MyLog.v(TAG, "语言设置应用成功: " + locale.getDisplayName());
            
            // 不再重启Activity
        } catch (Exception e) {
            MyLog.e(TAG, "应用语言设置出错: " + e.getMessage());
        }
    }
    
    /**
     * 根据语言代码获取对应的Locale对象
     */
    private Locale getLocaleFromLanguageCode(String languageCode) {
        return switch (languageCode) {
            case "zh" -> Locale.SIMPLIFIED_CHINESE;
            case "zh_tw" -> Locale.TRADITIONAL_CHINESE;
            case "en" -> Locale.ENGLISH;
            case "ja" -> Locale.JAPANESE;
            case "ko" -> Locale.KOREAN;
            case "fr" -> Locale.FRENCH;
            case "de" -> Locale.GERMAN;
            case "it" -> Locale.ITALIAN;
            case "es" -> new Locale("es");
            case "pt" -> new Locale("pt");
            case "ru" -> new Locale("ru");
            case "ar" -> new Locale("ar");
            default ->
                // 默认使用系统语言
                    Locale.getDefault();
        };
    }
    
    /**
     * 更新上下文的配置
     * @param context 上下文
     * @param locale 语言设置
     */
    private void updateContextConfiguration(Context context, Locale locale) {
        if (context == null) return;
        
        Resources resources = context.getResources();
        Configuration config = new Configuration(resources.getConfiguration());
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            // Android 7.0及以上
            LocaleList localeList = new LocaleList(locale);
            config.setLocales(localeList);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            // Android 4.2及以上
            config.setLocale(locale);
        } else {
            // 旧版本Android
            config.locale = locale;
        }
        
        // 更新资源配置
        resources.updateConfiguration(config, resources.getDisplayMetrics());
    }

    /**
     * 重置引导流程状态
     * 用于测试或重新引导
     */
    public void resetOnboardingState() {
        MyLog.v(TAG, "重置引导流程状态");
        SettingsManager.setOnboardingCompleted(false);
    }
} 