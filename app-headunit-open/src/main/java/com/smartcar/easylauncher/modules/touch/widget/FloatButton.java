package com.smartcar.easylauncher.modules.touch.widget;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.Nullable;

/**
 * FloatButton类，继承自AppCompatImageView，主要用于显示一个浮动的按钮。
 * 提供三个构造函数以满足不同使用场景：代码中直接创建、在XML布局文件中使用以及设置style样式。
 */
public class FloatButton extends androidx.appcompat.widget.AppCompatImageView {

    /**
     * 构造函数，用于在代码中直接创建FloatButton对象。
     *
     * @param context 应用程序上下文
     */
    public FloatButton(Context context) {
        super(context);
    }

    /**
     * 构造函数，用于在XML布局文件中使用该自定义控件。
     *
     * @param context 应用程序上下文
     * @param attrs   属性集合，包含从XML文件中解析的属性
     */
    public FloatButton(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    /**
     * 构造函数，用于在XML布局文件中使用该自定义控件，并且还可以在布局文件中设置style样式。
     *
     * @param context       应用程序上下文
     * @param attrs         属性集合，包含从XML文件中解析的属性
     * @param defStyleAttr  一个默认样式属性，用于在XML中定义样式
     */
    public FloatButton(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    // 注意：由于FloatButton类只提供了基本的构造函数，并且没有额外的逻辑或功能，
    // 因此在这个简单的例子中，可能不需要进行额外的代码优化。
    // 如果未来在FloatButton类中添加了新的功能或逻辑，那么可以考虑进行代码优化。
}
