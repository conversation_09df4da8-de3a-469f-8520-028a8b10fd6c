package com.smartcar.easylauncher.modules.setting;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bytedance.scene.Scene;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.databinding.SceneLanguageSelectionBinding;
import com.smartcar.easylauncher.modules.onboarding.OnboardingManager;
import com.smartcar.easylauncher.modules.onboarding.adapter.LanguageAdapter;
import com.smartcar.easylauncher.modules.onboarding.model.LanguageItem;
import com.smartcar.easylauncher.shared.utils.MyLog;

import static com.bytedance.scene.navigation.NavigationSceneGetter.requireNavigationScene;

import java.util.ArrayList;
import java.util.List;

/**
 * 语言选择场景 - 用于设置页面
 * 基于引导页面的语言选择功能，简化为设置页面使用
 *
 * <AUTHOR>
 */
public class LanguageSelectionScene extends Scene {
    private static final String TAG = LanguageSelectionScene.class.getSimpleName();
    
    private SceneLanguageSelectionBinding binding;
    private LanguageAdapter languageAdapter;
    private List<LanguageItem> languageItems;
    private OnboardingManager onboardingManager;
    private String currentLanguageCode;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        onboardingManager = OnboardingManager.getInstance();
        currentLanguageCode = onboardingManager.getSelectedLanguage();
        MyLog.v(TAG, "语言选择场景创建，当前语言: " + currentLanguageCode);
    }

    @NonNull
    @Override
    public ViewGroup onCreateView(@NonNull LayoutInflater inflater, @NonNull ViewGroup container, Bundle savedInstanceState) {
        binding = SceneLanguageSelectionBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView();
        setupListeners();
    }

    /**
     * 初始化视图
     */
    private void initView() {
        MyLog.v(TAG, "语言选择场景初始化视图");


        // 准备语言数据
        prepareLanguageData();

        // 设置RecyclerView
        binding.rvLanguages.setLayoutManager(new LinearLayoutManager(getActivity()));
        languageAdapter = new LanguageAdapter();
        
        // 设置当前选中的语言
        languageAdapter.setSelectedLanguage(currentLanguageCode);
        languageAdapter.submitList(languageItems);
        binding.rvLanguages.setAdapter(languageAdapter);
    }

    /**
     * 设置监听器
     */
    private void setupListeners() {
        // 返回按钮点击事件
        binding.btBack.setOnClickListener(v -> {
            MyLog.v(TAG, "点击返回按钮");
            requireNavigationScene(this).pop();
        });

        // 设置语言选择监听器
        languageAdapter.setOnLanguageSelectedListener(languageItem -> {
            MyLog.v(TAG, "用户选择语言: " + languageItem.getCode());
            
            // 如果选择的是当前语言，直接返回
            if (languageItem.getCode().equals(currentLanguageCode)) {
                MyLog.v(TAG, "选择的是当前语言，直接返回");
                requireNavigationScene(LanguageSelectionScene.this).pop();
                return;
            }

            // 保存并应用语言设置
            onboardingManager.saveSelectedLanguage(languageItem.getCode());
            onboardingManager.applyLanguage(languageItem.getCode());
            
            // 显示切换成功提示
            MToast.makeTextShort(getString(R.string.language_changed_success));
            
            // 延迟一下再返回，让用户看到反馈
            binding.getRoot().postDelayed(() -> {
                MyLog.v(TAG, "语言切换完成，返回设置页面");
                requireNavigationScene(LanguageSelectionScene.this).pop();
            }, 500);
        });
    }

    /**
     * 准备语言数据
     */
    private void prepareLanguageData() {
        languageItems = new ArrayList<>();

        // 添加中文
        languageItems.add(new LanguageItem(
                "zh",
                getString(R.string.language_zh),
                getString(R.string.language_sample_zh)
        ));

        languageItems.add(new LanguageItem(
                "zh_tw",
                getString(R.string.language_zh_tw),
                getString(R.string.language_sample_zh_tw)
        ));

        // 添加英文
        languageItems.add(new LanguageItem(
                "en",
                getString(R.string.language_en),
                getString(R.string.language_sample_en)
        ));

        // 添加日语
        languageItems.add(new LanguageItem(
                "ja",
                getString(R.string.language_ja),
                getString(R.string.language_sample_ja)
        ));
        
        // 添加韩语
        languageItems.add(new LanguageItem(
                "ko",
                getString(R.string.language_ko),
                getString(R.string.language_sample_ko)
        ));
        
        // 添加俄语
        languageItems.add(new LanguageItem(
                "ru",
                getString(R.string.language_ru),
                getString(R.string.language_sample_ru)
        ));
        
        // 添加法语
        languageItems.add(new LanguageItem(
                "fr",
                getString(R.string.language_fr),
                getString(R.string.language_sample_fr)
        ));
        
        // 添加德语
        languageItems.add(new LanguageItem(
                "de",
                getString(R.string.language_de),
                getString(R.string.language_sample_de)
        ));
        
        // 添加西班牙语
        languageItems.add(new LanguageItem(
                "es",
                getString(R.string.language_es),
                getString(R.string.language_sample_es)
        ));
        
        // 添加意大利语
        languageItems.add(new LanguageItem(
                "it",
                getString(R.string.language_it),
                getString(R.string.language_sample_it)
        ));
        
        // 添加葡萄牙语
        languageItems.add(new LanguageItem(
                "pt",
                getString(R.string.language_pt),
                getString(R.string.language_sample_pt)
        ));
        
        // 添加阿拉伯语
        languageItems.add(new LanguageItem(
                "ar",
                getString(R.string.language_ar),
                getString(R.string.language_sample_ar)
        ));
    }

    /**
     * 获取当前语言的显示名称
     */
    private String getCurrentLanguageName() {
        for (LanguageItem item : languageItems) {
            if (item.getCode().equals(currentLanguageCode)) {
                return item.getName();
            }
        }
        return getString(R.string.language_zh); // 默认返回中文
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
