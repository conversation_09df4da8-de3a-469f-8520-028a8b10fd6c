package com.smartcar.easylauncher.modules.onboarding;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;
import static com.bytedance.scene.navigation.NavigationSceneGetter.requireNavigationScene;

import android.annotation.SuppressLint;
import android.content.res.Configuration;
import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;

import com.bytedance.scene.Scene;
import com.bytedance.scene.interfaces.PushOptions;
import com.bytedance.scene.navigation.ConfigurationChangedListener;
import com.bytedance.scene.navigation.NavigationScene;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.util.Locale;

/**
 * 引导流程场景基类
 * 提供引导流程中所有场景的基础功能和公共方法
 *
 * <AUTHOR>
 */
public abstract class BaseOnboardingScene<T extends ViewBinding> extends Scene {
    protected static final String TAG = BaseOnboardingScene.class.getSimpleName();
    protected T binding;
    protected OnboardingManager onboardingManager;
    // 是否允许返回（第一个页面不允许返回）
    protected boolean allowBack = true;
    // 保存上次配置信息
    protected int lastOrientation = Configuration.ORIENTATION_UNDEFINED;
    protected int lastScreenWidth = 0;
    protected int lastScreenHeight = 0;
    protected Locale lastLocale = null;

    @NonNull
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @NonNull ViewGroup container, Bundle savedInstanceState) {
        MyLog.v(TAG, getClass().getSimpleName() + " - onCreateView");
        onboardingManager = OnboardingManager.getInstance();
        binding = getViewBinding(inflater, container);
        initView();

        // 保存初始配置
        Configuration config = getResources().getConfiguration();
        lastOrientation = config.orientation;
        lastScreenWidth = config.screenWidthDp;
        lastScreenHeight = config.screenHeightDp;

        // 保存初始区域设置
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            lastLocale = config.getLocales().get(0);
        } else {
            lastLocale = config.locale;
        }

        return binding.getRoot();
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        requireNavigationScene(this).addConfigurationChangedListener(this, new ConfigurationChangedListener() {
            @SuppressLint("RestrictedApi")
            @Override
            public void onConfigurationChanged(@NonNull Configuration configuration) {
                // 判断配置变化类型
                boolean isOrientationChange = lastOrientation != configuration.orientation;
                boolean isScreenSizeChange = lastScreenWidth != configuration.screenWidthDp
                        || lastScreenHeight != configuration.screenHeightDp;

                // 获取当前区域设置
                Locale currentLocale;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    currentLocale = configuration.getLocales().get(0);
                } else {
                    currentLocale = configuration.locale;
                }

                // 判断语言是否改变
                boolean isLocaleChange = lastLocale != null && !lastLocale.equals(currentLocale);

                // 输出详细日志
                logConfigChanges(configuration, isOrientationChange, isScreenSizeChange, isLocaleChange);

                // 更新保存的配置信息
                lastOrientation = configuration.orientation;
                lastScreenWidth = configuration.screenWidthDp;
                lastScreenHeight = configuration.screenHeightDp;
                lastLocale = currentLocale;

                if (isOrientationChange || isScreenSizeChange) {
                    // 屏幕旋转或尺寸变化时，需要重建场景
                    MyLog.v(TAG, "屏幕方向或尺寸改变，重建场景");
                    requireNavigationScene(BaseOnboardingScene.this).recreate(BaseOnboardingScene.this);
                } else {
                    // 其他变化（如语言变化）时，只需更新资源
                    MyLog.v(TAG, "其他配置改变（可能是语言），更新资源");
                    updateLanguageResources();
                }
            }
        });
        setupListeners();
    }

    /**
     * 记录配置变化的详细信息
     */
    private void logConfigChanges(Configuration configuration, boolean isOrientationChange,
                                  boolean isScreenSizeChange, boolean isLocaleChange) {
        StringBuilder logBuilder = new StringBuilder();
        logBuilder.append(getClass().getSimpleName())
                .append(" - 配置变化: ");

        // 屏幕方向变化
        if (isOrientationChange) {
            String orientationStr = configuration.orientation == Configuration.ORIENTATION_LANDSCAPE ?
                    "横屏" : configuration.orientation == Configuration.ORIENTATION_PORTRAIT ?
                    "竖屏" : "未定义";
            logBuilder.append("方向=").append(orientationStr).append(", ");
        }

        // 屏幕尺寸变化
        if (isScreenSizeChange) {
            logBuilder.append("尺寸=[").append(configuration.screenWidthDp)
                    .append("x").append(configuration.screenHeightDp).append("], ");
        }

        // 语言变化
        if (isLocaleChange) {
            Locale currentLocale;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                currentLocale = configuration.getLocales().get(0);
            } else {
                currentLocale = configuration.locale;
            }
            logBuilder.append("语言=").append(currentLocale.getLanguage())
                    .append("_").append(currentLocale.getCountry());
        }

        MyLog.v(TAG, logBuilder.toString());
    }

    /**
     * 更新语言资源
     * 当语言改变时，此方法会被调用以更新UI元素
     */
    protected void updateLanguageResources() {
        // 基类提供空实现，子类可以覆盖此方法来更新特定UI元素
        // 确保资源配置与当前语言一致
        String currentLanguage = onboardingManager.getSelectedLanguage();
        MyLog.v(TAG, "更新语言资源: " + currentLanguage);

        // 如果子类实现了特定的updateLanguage方法，可以在这里反射调用
        try {
            this.getClass().getMethod("updateLanguage", String.class);
            this.getClass().getMethod("updateLanguage", String.class)
                    .invoke(this, currentLanguage);
            MyLog.v(TAG, "调用子类updateLanguage方法成功");
        } catch (Exception e) {
            // 忽略NoSuchMethodException，这是正常的，因为不是所有子类都实现了此方法
            if (!(e instanceof NoSuchMethodException)) {
                MyLog.e(TAG, "调用updateLanguage方法失败: " + e.getMessage());
            }
        }

        // 特殊处理隐私场景
        if (this.getClass().getSimpleName().equals("OnboardingPrivacyScene")) {
            try {
                // 尝试调用setupPrivacyLinks方法
                this.getClass().getMethod("setupPrivacyLinks");
                this.getClass().getMethod("setupPrivacyLinks").invoke(this);
                MyLog.v(TAG, "语言更新后重新设置隐私链接成功");

                // 尝试调用updatePermissionCardTexts方法
                this.getClass().getMethod("updatePermissionCardTexts");
                this.getClass().getMethod("updatePermissionCardTexts").invoke(this);
                MyLog.v(TAG, "语言更新后重新设置权限卡片文本成功");
            } catch (Exception e) {
                // 忽略NoSuchMethodException
                if (!(e instanceof NoSuchMethodException)) {
                    MyLog.e(TAG, "语言更新后重新设置隐私UI失败: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 获取ViewBinding实例
     */
    protected abstract T getViewBinding(LayoutInflater inflater, ViewGroup container);

    /**
     * 初始化视图
     */
    protected abstract void initView();

    /**
     * 设置监听器
     */
    protected abstract void setupListeners();

    /**
     * 导航到下一个场景
     */
    protected void navigateToNext(Scene nextScene) {
        PushOptions pushOptions = new PushOptions.Builder().setAnimation(requireActivity(), R.anim.slide_in_from_right, R.anim.slide_out_to_left).build();
        NavigationScene navigationScene = getNavigationScene(this);
        if (navigationScene != null) {
            navigationScene.push(nextScene, pushOptions);
        }
    }

    /**
     * 返回上一个场景
     */
    protected void navigateBack() {
        if (!allowBack) {
            return;
        }
        NavigationScene navigationScene = getNavigationScene(this);
        if (navigationScene != null) {
            navigationScene.pop();
        }
    }


    /**
     * 导航到主场景并清除引导流程
     */
    protected void completeOnboarding() {
        onboardingManager.setOnboardingCompleted(true);
        NavigationScene navigationScene = getNavigationScene(this);
        if (navigationScene != null) {
            // 移除所有引导场景，回到根场景
            navigationScene.popToRoot(null);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}