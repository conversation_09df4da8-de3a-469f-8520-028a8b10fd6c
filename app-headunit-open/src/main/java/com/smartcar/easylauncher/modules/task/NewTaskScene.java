package com.smartcar.easylauncher.modules.task;

import static com.bytedance.scene.navigation.NavigationSceneGetter.requireNavigationScene;

import android.graphics.Rect;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.QuickAdapterHelper;
import com.github.gzuliyujiang.wheelpicker.DelayedPlatePicker;
import com.github.gzuliyujiang.wheelpicker.NumberPicker;
import com.github.gzuliyujiang.wheelpicker.OptionPicker;
import com.github.gzuliyujiang.wheelpicker.SpeedPlatePicker;
import com.github.gzuliyujiang.wheelpicker.ThreeLevelLinkagePicker;
import com.github.gzuliyujiang.wheelpicker.TimePicker;
import com.github.gzuliyujiang.wheelpicker.TimeSlotPlatePicker;
import com.github.gzuliyujiang.wheelpicker.VolumePlatePicker;
import com.github.gzuliyujiang.wheelpicker.annotation.TimeMode;
import com.github.gzuliyujiang.wheelpicker.entity.TimeEntity;
import com.github.gzuliyujiang.wheelpicker.impl.UnitTimeFormatter;
import com.github.gzuliyujiang.wheelpicker.widget.LinkageWheelLayout;
import com.github.gzuliyujiang.wheelpicker.widget.NumberWheelLayout;
import com.github.gzuliyujiang.wheelpicker.widget.OptionWheelLayout;
import com.github.gzuliyujiang.wheelpicker.widget.TimeWheelLayout;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.common.FooterAdapter;
import com.smartcar.easylauncher.shared.adapter.task.NewResultAddAdapter;
import com.smartcar.easylauncher.shared.adapter.task.NewTaskAddAdapter;
import com.smartcar.easylauncher.shared.adapter.task.ResultFooterAdapter;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.core.constants.TaskConditionConst;
import com.smartcar.easylauncher.core.constants.TaskResultConst;
import com.smartcar.easylauncher.databinding.SceneNewTaskBinding;
import com.smartcar.easylauncher.shared.dialog.BluetoothDeviceSelector;
import com.smartcar.easylauncher.shared.dialog.HorizontalAppSelector;
import com.smartcar.easylauncher.shared.dialog.InputDialog;
import com.smartcar.easylauncher.shared.dialog.OptionBottomListDialog;
import com.smartcar.easylauncher.shared.dialog.ResultBottomListDialog;
import com.smartcar.easylauncher.infrastructure.event.cody.TaskNotificationScopeBus;
import com.smartcar.easylauncher.infrastructure.interfaces.AppSelectionCallback;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.data.model.common.GoodsCategoryBean;
import com.smartcar.easylauncher.data.model.system.AppInfo;
import com.smartcar.easylauncher.data.model.task.ConditionModel;
import com.smartcar.easylauncher.data.model.task.ResultModel;
import com.smartcar.easylauncher.data.model.task.TaskModel;
import com.smartcar.easylauncher.data.model.task.TaskNotificationModel;
import com.smartcar.easylauncher.data.model.task.TaskOptionModel;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.music.MusicControlUtil;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;
import com.smartcar.easylauncher.shared.view.pagergridlayoutmanager.PagerGridLayoutManager;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 新建任务
 *
 * <AUTHOR>
 */
public class NewTaskScene extends BaseScene {
    public static final String TAG = NewTaskScene.class.getSimpleName();

    private SceneNewTaskBinding binding;
    private NewTaskAddAdapter newTaskAddAdapter;
    private QuickAdapterHelper conditionHelper;
    private QuickAdapterHelper resultHelper;
    private FooterAdapter footerAdapter;
    private ResultFooterAdapter resultFooterAdapter;
    private NewResultAddAdapter newResultAddAdapter;
    private TaskModel taskModel;
    private FragmentActivity activity;


    public static Bundle createArguments(TaskModel taskModel) {
        Bundle bundle = new Bundle();
        bundle.putSerializable("taskModel", taskModel);
        return bundle;
    }


    @NonNull
    @Override
    protected ViewGroup onCreateNewView(@NonNull LayoutInflater layoutInflater, @Nullable ViewGroup viewGroup, @Nullable Bundle bundle) {
        binding = SceneNewTaskBinding.inflate(layoutInflater, viewGroup, false);
        return binding.getRoot();
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        initData();
        initViews();
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        // 设置取消按钮
        binding.btnCancel.setOnClickListener(v -> requireNavigationScene(this).pop());
        MyLog.d(TAG, "视图初始化完成");
    }


    private void initData() {
        //初始化条件设配器方法
        initCondition();
        //初始化结果设配器方法
        initResult();
        //初始化条件尾部适配器
        initFooter();
        //初始化结果尾部适配器
        initResultFooter();

        Bundle extras = getArguments();
        if (extras != null && extras.containsKey("taskModel")) {
            taskModel = (TaskModel) extras.get("taskModel");
            if (taskModel != null) {
                binding.etName.setText(taskModel.getTaskName());
                binding.sbText.setChecked(taskModel.isModel());
                newTaskAddAdapter.submitList(taskModel.getConditions());
                newResultAddAdapter.submitList(taskModel.getResults());

            }
        }
        conditionHelper.addAfterAdapter(footerAdapter);
        resultHelper.addAfterAdapter(resultFooterAdapter);

        // 强制刷新RecyclerView显示
        conditionHelper.getAdapter().notifyDataSetChanged();
        resultHelper.getAdapter().notifyDataSetChanged();

        MyLog.d(TAG, "Footer适配器添加完成，强制刷新显示");

        // 设置保存按钮点击事件
        binding.btnSave.setOnClickListener(view -> {

            if (taskModel != null) {
                if (binding.etName.getText().toString().trim().isEmpty()) {
                    MToast.makeTextShort("❌ 请输入任务名称");
                    return;
                }
                if (newTaskAddAdapter.getItems().isEmpty()) {
                    MToast.makeTextShort("❌ 请至少添加一个触发条件");
                    return;
                }
                if (newResultAddAdapter.getItems().isEmpty()) {
                    MToast.makeTextShort("❌ 请至少添加一个执行结果");
                    return;
                }
                //更新任务
                updateTask(taskModel);
            } else {
                if (binding.etName.getText().toString().trim().isEmpty()) {
                    MToast.makeTextShort("❌ 请输入任务名称");
                    return;
                }
                if (newTaskAddAdapter.getItems().isEmpty()) {
                    MToast.makeTextShort("❌ 请至少添加一个触发条件");
                    return;
                }
                if (newResultAddAdapter.getItems().isEmpty()) {
                    MToast.makeTextShort("❌ 请至少添加一个执行结果");
                    return;
                }
                //保存任务
                saveTask();
            }
        });

        // 返回按钮已在initViews()中设置
    }

    private void updateTask(TaskModel taskModelToUpdate) {
        try {
            // 获取当前的任务列表
            String taskListJson = DataManager.getTask();

            List<TaskModel> taskList = new ArrayList<>();
            if (taskListJson != null && !taskListJson.isEmpty()) {
                Type type = new TypeToken<ArrayList<TaskModel>>() {
                }.getType();
                taskList = new Gson().fromJson(taskListJson, type);
            }

            // 找到要更新的任务并更新它
            for (TaskModel taskModel : taskList) {
                if (taskModel.getId().equals(taskModelToUpdate.getId())) {
                    taskModel.setTaskName(binding.etName.getText().toString());
                    taskModel.setStatus(taskModelToUpdate.isStatus());
                    taskModel.setModel(binding.sbText.isChecked());
                    taskModel.setConditions((ArrayList<ConditionModel>) newTaskAddAdapter.getItems());
                    taskModel.setResults((ArrayList<ResultModel>) newResultAddAdapter.getItems());
                    taskModel.setSaveTime(String.valueOf(System.currentTimeMillis()));
                    break;
                }
            }

            // 将更新后的任务列表保存回去
            DataManager.setTask(new Gson().toJson(taskList));
            MyLog.v("taskModel = ", new Gson().toJson(taskModelToUpdate));
            MToast.makeTextShort("✅ 任务「" + taskModelToUpdate.getTaskName() + "」更新成功");
            TaskNotificationScopeBus.eventBean().post(new TaskNotificationModel(1, taskModel));
            requireNavigationScene(this).pop();
        } catch (JsonSyntaxException e) {
            MToast.makeTextShort("任务更新失败");
            // 这里处理JSON解析错误
            MyLog.e(TAG, "JSON解析错误: " + e.getMessage());
        }
    }

    private void saveTask() {
        // 创建新的任务
        TaskModel taskModel = new TaskModel();
        taskModel.setId(String.valueOf(System.currentTimeMillis()));
        taskModel.setTaskName(binding.etName.getText().toString());
        taskModel.setStatus(true);
        taskModel.setModel(binding.sbText.isChecked());
        taskModel.setConditions((ArrayList<ConditionModel>) newTaskAddAdapter.getItems());
        taskModel.setResults((ArrayList<ResultModel>) newResultAddAdapter.getItems());
        taskModel.setSaveTime(String.valueOf(System.currentTimeMillis()));

        // 获取当前的任务列表
        String taskListJson = DataManager.getTask();

        List<TaskModel> taskList = new ArrayList<>();
        if (taskListJson != null && !taskListJson.isEmpty()) {
            Type type = new TypeToken<ArrayList<TaskModel>>() {
            }.getType();
            taskList = new Gson().fromJson(taskListJson, type);
        }

        // 将新的任务添加到任务列表中
        taskList.add(taskModel);

        // 将更新后的任务列表保存回去
        DataManager.setTask(new Gson().toJson(taskList));
        MyLog.v("taskModel = ", new Gson().toJson(taskModel));
        MToast.makeTextShort("✅ 任务「" + taskModel.getTaskName() + "」保存成功");
        TaskNotificationScopeBus.eventBean().post(new TaskNotificationModel(0, taskModel));
        requireNavigationScene(this).pop();
    }


    /**
     * 条件列表适配器
     */
    private void initCondition() {
        //条件适配器
        // 简化ItemDecoration，RecyclerView已有背景和内边距
        binding.rvIf.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                outRect.set(DensityUtils.dp2px(requireActivity(), 4)
                        , DensityUtils.dp2px(requireActivity(), 4)
                        , DensityUtils.dp2px(requireActivity(), 4)
                        , DensityUtils.dp2px(requireActivity(), 4));
            }
        });
        // 恢复原来的PagerGridLayoutManager，1行4列水平分页布局
        PagerGridLayoutManager layoutManager = new PagerGridLayoutManager(
                1,
                4,
                PagerGridLayoutManager.HORIZONTAL);
        //设置滑动每像素需要花费的时间
        layoutManager.setMillisecondPreInch(100);
        //设置最大滚动时间
        layoutManager.setMaxScrollOnFlingDuration(500);

        binding.rvIf.setLayoutManager(layoutManager);
        newTaskAddAdapter = new NewTaskAddAdapter();
        newTaskAddAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            // MToast.makeTextShort(NewTaskActivity.this, "点击了" + i);
        });
        newTaskAddAdapter.addOnItemChildClickListener(R.id.iv_close, (baseQuickAdapter, view, i) -> {
            newTaskAddAdapter.removeAt(i);
            updateFooterAdapter();
        });
        conditionHelper = new QuickAdapterHelper.Builder(newTaskAddAdapter).build();
        binding.rvIf.setAdapter(conditionHelper.getAdapter());

        // 确保RecyclerView可见
        binding.rvIf.setVisibility(View.VISIBLE);

        MyLog.d(TAG, "条件适配器设置完成，当前数据数量: " + newTaskAddAdapter.getItemCount());

    }

    /**
     * 结果列表适配器
     */
    private void initResult() {
        //结果的适配器
        // 简化ItemDecoration，RecyclerView已有背景和内边距
        binding.rvComeNear.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                outRect.set(DensityUtils.dp2px(requireActivity(), 4)
                        , DensityUtils.dp2px(requireActivity(), 4)
                        , DensityUtils.dp2px(requireActivity(), 4)
                        , DensityUtils.dp2px(requireActivity(), 4));
            }
        });
        // 恢复原来的PagerGridLayoutManager，1行4列水平分页布局
        PagerGridLayoutManager resultLayoutManager = new PagerGridLayoutManager(
                1,
                4,
                PagerGridLayoutManager.HORIZONTAL);
        //设置滑动每像素需要花费的时间
        resultLayoutManager.setMillisecondPreInch(100);
        //设置最大滚动时间
        resultLayoutManager.setMaxScrollOnFlingDuration(500);

        binding.rvComeNear.setLayoutManager(resultLayoutManager);
        newResultAddAdapter = new NewResultAddAdapter();
        newResultAddAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {

        });
        newResultAddAdapter.addOnItemChildClickListener(R.id.iv_close, (baseQuickAdapter, view, i) -> {
            newResultAddAdapter.removeAt(i);
            updateResultFooterAdapter();
        });
        resultHelper = new QuickAdapterHelper.Builder(newResultAddAdapter).build();
        binding.rvComeNear.setAdapter(resultHelper.getAdapter());

        // 确保RecyclerView可见
        binding.rvComeNear.setVisibility(View.VISIBLE);

        MyLog.d(TAG, "结果适配器设置完成，当前数据数量: " + newResultAddAdapter.getItemCount());
    }

    /**
     * 条件尾部添加
     */
    private void initFooter() {
        footerAdapter = new FooterAdapter(false);
        footerAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> new OptionBottomListDialog(requireActivity())
                .setData(TaskConditionConst.init())
                .setOnItemSelectedListener((position, item) -> {
                    //Toast.makeText(NewTaskActivity.this, "click:" + item, Toast.LENGTH_SHORT).show();
                    switch (item.getId()) {
                        case TaskConditionConst.TYPE_WIFI_SWITCH:
                            List<GoodsCategoryBean> data = new ArrayList<>();
                            data.add(new GoodsCategoryBean(1, "打开", true));
                            data.add(new GoodsCategoryBean(2, "关闭", false));
                            conditionalSwitchOptions(item, data);
                            break;
                        case TaskConditionConst.TYPE_NETWORK_STATE:
                            List<GoodsCategoryBean> network = new ArrayList<>();
                            network.add(new GoodsCategoryBean(1, "有网络", true));
                            network.add(new GoodsCategoryBean(2, "无网络", false));
                            conditionalSwitchOptions(item, network);
                            break;
                        case TaskConditionConst.TYPE_BLUETOOTH_SWITCH:
                            List<GoodsCategoryBean> blrData = new ArrayList<>();
                            blrData.add(new GoodsCategoryBean(1, "打开", true));
                            blrData.add(new GoodsCategoryBean(2, "关闭", false));
                            conditionalSwitchOptions(item, blrData);
                            break;
                        case TaskConditionConst.TYPE_BLUETOOTH_DEVICE:
                            //蓝牙设备选择
                            BluetoothDeviceSelector.selectBle(requireActivity(), device -> {
                                ConditionModel conditionModel = new ConditionModel();
                                conditionModel.setName(item.getName());
                                conditionModel.setId(item.getId());
                                conditionModel.setBluetoothName(device.getName());
                                conditionModel.setBluetoothAddress(device.getAddress());
                                conditionModel.setExecute(device.getName());
                                conditionModel.setStatus(false);
                                conditionModel.setType(item.getType());
                                conditionModel.setKey(item.getKey());
                                // 添加选中的数据到条件适配器
                                newTaskAddAdapter.add(conditionModel);
                                // 更新尾部适配器
                                updateFooterAdapter();
                            });
                            break;
                        case TaskConditionConst.TYPE_DEVICE_START:
                            ConditionModel conditionModel = new ConditionModel();
                            conditionModel.setName(item.getName());
                            conditionModel.setId(item.getId());
                            conditionModel.setExecute("休眠设备不支持");
                            conditionModel.setStatus(false);
                            conditionModel.setType(item.getType());
                            conditionModel.setKey(item.getKey());
                            // 添加选中的数据到条件适配器
                            newTaskAddAdapter.add(conditionModel);
                            // 更新尾部适配器
                            updateFooterAdapter();
                            break;
                        case TaskConditionConst.TYPE_START:
                            ConditionModel conditionMode2 = new ConditionModel();
                            conditionMode2.setName(item.getName());
                            conditionMode2.setId(item.getId());
                            conditionMode2.setExecute("休眠设备不支持");
                            conditionMode2.setStatus(false);
                            conditionMode2.setType(item.getType());
                            conditionMode2.setKey(item.getKey());
                            // 添加选中的数据到条件适配器
                            newTaskAddAdapter.add(conditionMode2);
                            // 更新尾部适配器
                            updateFooterAdapter();
                            break;
                        case TaskConditionConst.TYPE_MUSIC_STATE:
                            List<GoodsCategoryBean> musicState = new ArrayList<>();
                            musicState.add(new GoodsCategoryBean(0, "暂停", false));
                            musicState.add(new GoodsCategoryBean(1, "播放", true));
                            conditionalSwitchOptions(item, musicState);
                            break;
                        case TaskConditionConst.TYPE_SUN:
                            List<GoodsCategoryBean> sun = new ArrayList<>();
                            sun.add(new GoodsCategoryBean(0, "白天时", true));
                            sun.add(new GoodsCategoryBean(1, "夜晚时", false));
                            conditionalSwitchOptions(item, sun);
                            break;
                        case TaskConditionConst.TYPE_SPEED:
                            speedOption(item);
                            break;
                        case TaskConditionConst.TYPE_GEO_FENCE:
                            break;
                        case TaskConditionConst.TYPE_TIME_RANGE:
                            timeSlot(item);
                            break;
                        case TaskConditionConst.TYPE_TIMING:
                            onTime24(item);
                            break;
                        case TaskConditionConst.TYPE_PERIOD:
                            //周期选择方法
                            cycleSelection(item);
                            break;
                        case TaskConditionConst.TYPE_SCREEN:
                            List<GoodsCategoryBean> screen = new ArrayList<>();
                            screen.add(new GoodsCategoryBean(0, "亮屏", true));
                            screen.add(new GoodsCategoryBean(1, "灭屏", false));
                            conditionalSwitchOptions(item, screen);
                            break;
                        case TaskConditionConst.TYPE_WAKE_UP:
                            // 设备唤醒条件，直接添加，无需额外配置
                            ConditionModel wakeUpCondition = new ConditionModel();
                            wakeUpCondition.setName(item.getName());
                            wakeUpCondition.setId(item.getId());
                            wakeUpCondition.setExecute("时");
                            wakeUpCondition.setType(item.getType());
                            wakeUpCondition.setKey(item.getKey());
                            newTaskAddAdapter.add(wakeUpCondition);
                            updateFooterAdapter();
                            break;
                        default:
                    }
                })
                .show());
    }

    private void cycleSelection(TaskOptionModel item) {
        ThreeLevelLinkagePicker picker = new ThreeLevelLinkagePicker(requireActivity());
        picker.setWidth(DensityUtils.dp2px(requireActivity(), 420));
        picker.setHeight(DensityUtils.dp2px(requireActivity(), 260));
        picker.getTitleView().setVisibility(View.GONE);
        picker.setDefaultValue("秒", "4", "");
        LinkageWheelLayout wheelLayout = picker.getWheelLayout();
        wheelLayout.setTextSize(DensityUtils.dp2px(requireActivity(), 16));
        wheelLayout.setSelectedTextSize(DensityUtils.dp2px(requireActivity(), 19));
        picker.setOnCarPlatePickedListener((first, second, third) -> {
            ConditionModel conditionModel = new ConditionModel();
            conditionModel.setName(item.getName());
            conditionModel.setStatus(false);
            conditionModel.setCycleType(first.getId());
            conditionModel.setCycleStartTime(second.getId());
            conditionModel.setCycleEndTime(third.getId());
            conditionModel.setType(item.getType());
            conditionModel.setId(item.getId());
            conditionModel.setKey(item.getKey());
            conditionModel.setExecute(first.getName() + second.getName() + "-" + third.getName());
            // 添加选中的数据到条件适配器
            newTaskAddAdapter.add(conditionModel);
            // 更新尾部适配器
            updateFooterAdapter();
        });
        picker.show();
    }

    /**
     * 条件选择弹窗
     *
     * @param optionModel
     * @param data
     */
    public void conditionalSwitchOptions(TaskOptionModel optionModel, List<GoodsCategoryBean> data) {
        OptionPicker picker = new OptionPicker(requireActivity());
        picker.setTitle("状态选择");
        picker.setData(data);
        picker.setDefaultPosition(0);
        picker.setWidth(DensityUtils.dp2px(requireActivity(), 420));
        picker.setHeight(DensityUtils.dp2px(requireActivity(), 260));
        picker.getTitleView().setVisibility(View.GONE);
        OptionWheelLayout wheelLayout = picker.getWheelLayout();
        wheelLayout.setTextSize(DensityUtils.dp2px(requireActivity(), 19));
        wheelLayout.setSelectedTextSize(DensityUtils.dp2px(requireActivity(), 22));
        picker.setOnOptionPickedListener((position, item) -> {
            GoodsCategoryBean bean = (GoodsCategoryBean) item;
            ConditionModel conditionModel = new ConditionModel();
            conditionModel.setName(optionModel.getName());
            conditionModel.setId(optionModel.getId());
            switch (optionModel.getId()) {
                case TaskConditionConst.TYPE_WIFI_SWITCH:
                    conditionModel.setExecute(bean.getName());
                    conditionModel.setType(optionModel.getType());
                    conditionModel.setSwitchState(bean.isSwitchState());
                    conditionModel.setKey(optionModel.getKey());
                    break;
                case TaskConditionConst.TYPE_BLUETOOTH_SWITCH:
                    conditionModel.setExecute(bean.getName());
                    conditionModel.setType(optionModel.getType());
                    conditionModel.setSwitchState(bean.isSwitchState());
                    conditionModel.setKey(optionModel.getKey());
                    break;
                case TaskConditionConst.TYPE_NETWORK_STATE:
                    conditionModel.setExecute(bean.getName());
                    conditionModel.setType(optionModel.getType());
                    conditionModel.setSwitchState(bean.isSwitchState());
                    conditionModel.setKey(optionModel.getKey());
                    break;
                case TaskConditionConst.TYPE_MUSIC_STATE:
                    conditionModel.setExecute(bean.getName());
                    conditionModel.setMusicState(bean.getId());
                    conditionModel.setType(optionModel.getType());
                    conditionModel.setSwitchState(bean.isSwitchState());
                    conditionModel.setKey(optionModel.getKey());
                    break;
                case TaskConditionConst.TYPE_SUN:
                    conditionModel.setExecute(bean.getName());
                    conditionModel.setMusicState(bean.getId());
                    conditionModel.setType(optionModel.getType());
                    conditionModel.setSwitchState(bean.isSwitchState());
                    conditionModel.setKey(optionModel.getKey());
                    break;
                case TaskConditionConst.TYPE_SCREEN:
                    conditionModel.setExecute(bean.getName());
                    conditionModel.setMusicState(bean.getId());
                    conditionModel.setType(optionModel.getType());
                    conditionModel.setSwitchState(bean.isSwitchState());
                    conditionModel.setKey(optionModel.getKey());
                    break;
                default:
            }
            // 添加选中的数据到条件适配器
            newTaskAddAdapter.add(conditionModel);
            // 更新尾部适配器
            updateFooterAdapter();
        });
        picker.show();
    }

    /**
     * 车速选择
     *
     * @param item
     */
    private void speedOption(TaskOptionModel item) {
        SpeedPlatePicker picker = new SpeedPlatePicker(requireActivity());
        picker.setWidth(DensityUtils.dp2px(requireActivity(), 420));
        picker.setHeight(DensityUtils.dp2px(requireActivity(), 260));
        picker.getTitleView().setVisibility(View.GONE);
        picker.setDefaultValue("大于", "80", "");
        LinkageWheelLayout wheelLayout = picker.getWheelLayout();
        wheelLayout.setTextSize(DensityUtils.dp2px(requireActivity(), 16));
        wheelLayout.setSelectedTextSize(DensityUtils.dp2px(requireActivity(), 19));
        picker.setOnCarPlatePickedListener((province, letter) -> {
            ConditionModel conditionModel = new ConditionModel();
            conditionModel.setName(item.getName());
            conditionModel.setSpeed(Integer.parseInt(letter));
            conditionModel.setSpeedType(province.getId());
            conditionModel.setStatus(false);
            conditionModel.setType(item.getType());
            conditionModel.setId(item.getId());
            conditionModel.setKey(item.getKey());
            conditionModel.setExecute(province.getName() + " " + letter + " km/h");
            // 添加选中的数据到条件适配器
            newTaskAddAdapter.add(conditionModel);
            // 更新尾部适配器
            updateFooterAdapter();
        });
        picker.show();

    }

    /**
     * 时间段选择
     *
     * @param item
     */
    private void timeSlot(TaskOptionModel item) {
        TimeSlotPlatePicker picker = new TimeSlotPlatePicker(requireActivity());
        picker.setWidth(DensityUtils.dp2px(requireActivity(), 420));
        picker.setHeight(DensityUtils.dp2px(requireActivity(), 260));
        picker.getTitleView().setVisibility(View.GONE);
        picker.setDefaultValue("08:00", "08:30", "");
        LinkageWheelLayout linkageWheelLayout = picker.getWheelLayout();
        linkageWheelLayout.setTextSize(DensityUtils.dp2px(requireActivity(), 16));
        linkageWheelLayout.setSelectedTextSize(DensityUtils.dp2px(requireActivity(), 19));
        picker.setOnCarPlatePickedListener((province, letter) -> {
            ConditionModel conditionModel = new ConditionModel();
            conditionModel.setName(item.getName());
            conditionModel.setStartTime(province);
            conditionModel.setEndTime(letter);
            conditionModel.setStatus(false);
            conditionModel.setType(item.getType());
            conditionModel.setId(item.getId());
            conditionModel.setKey(item.getKey());
            conditionModel.setExecute(province + "-" + letter);
            // 添加选中的数据到条件适配器
            newTaskAddAdapter.add(conditionModel);
            // 更新尾部适配器
            updateFooterAdapter();
        });
        picker.show();
    }

    /**
     * 定时选择
     */
    public void onTime24(TaskOptionModel item) {
        TimePicker picker = new TimePicker(requireActivity());
        TimeWheelLayout wheelLayout = picker.getWheelLayout();
        wheelLayout.setTextSize(DensityUtils.dp2px(requireActivity(), 16));
        wheelLayout.setSelectedTextSize(DensityUtils.dp2px(requireActivity(), 19));
        wheelLayout.setTimeMode(TimeMode.HOUR_24_NO_SECOND);
        wheelLayout.setTimeFormatter(new UnitTimeFormatter());
        wheelLayout.setDefaultValue(TimeEntity.now());
        wheelLayout.setResetWhenLinkage(false);
        picker.setWidth(DensityUtils.dp2px(requireActivity(), 420));
        picker.setHeight(DensityUtils.dp2px(requireActivity(), 260));
        picker.getTitleView().setVisibility(View.GONE);
        picker.setOnTimePickedListener((hour, minute, second) -> {
            ConditionModel conditionModel = new ConditionModel();
            conditionModel.setName(item.getName());
            conditionModel.setStatus(false);
            conditionModel.setFixedTime(hour + ":" + minute);
            conditionModel.setType(item.getType());
            conditionModel.setId(item.getId());
            conditionModel.setKey(item.getKey());
            conditionModel.setExecute(hour + ":" + minute);
            // 添加选中的数据到条件适配器
            newTaskAddAdapter.add(conditionModel);
            // 更新尾部适配器
            updateFooterAdapter();
        });
        picker.show();
    }

    /**
     * 结果尾部添加
     */
    private void initResultFooter() {
        resultFooterAdapter = new ResultFooterAdapter(false);
        resultFooterAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> new ResultBottomListDialog(requireActivity())
                .setData(TaskResultConst.initResultList())
                .setOnItemSelectedListener((position, item) -> {
                    ResultModel resultModel = new ResultModel();
                    switch (item.getId()) {
                        //延时
                        case TaskResultConst.RESULT_ID_DELAY:
                            //延时弹窗方法
                            timeDelayedOption(resultModel, item);
                            break;
                        //WIFI状态
                        case TaskResultConst.RESULT_ID_WIFI:
                            List<GoodsCategoryBean> wifiSwitch = new ArrayList<>();
                            wifiSwitch.add(new GoodsCategoryBean(0, "打开", false));
                            wifiSwitch.add(new GoodsCategoryBean(1, "关闭", true));
                            resultGeneralSwitchOptions(item, wifiSwitch);
                            break;
                        //蓝牙状态
                        case TaskResultConst.RESULT_ID_BLUETOOTH:
                            List<GoodsCategoryBean> bleSwitch = new ArrayList<>();
                            bleSwitch.add(new GoodsCategoryBean(0, "打开", false));
                            bleSwitch.add(new GoodsCategoryBean(1, "关闭", true));
                            resultGeneralSwitchOptions(item, bleSwitch);
                            break;
                        //声音
                        case TaskResultConst.RESULT_ID_VOLUME:
                            soundSelection(resultModel, item);
                            break;
                        //亮度
                        case TaskResultConst.RESULT_ID_BRIGHTNESS:
                            // 亮度
                            brightnessSelection(resultModel, item);
                            break;
                        //主题
                        case TaskResultConst.RESULT_ID_THEME:
                            break;
                        //打开APP
                        case TaskResultConst.RESULT_ID_APP:
                            HorizontalAppSelector.selectApp(requireActivity(), 99, 0, new AppSelectionCallback() {
                                @Override
                                public void onAppSelected(AppInfo appInfo) {
                                    // 在这里处理选择的应用信息
                                    resultModel.setName(item.getName());
                                    resultModel.setId(item.getId());
                                    resultModel.setType(4);
                                    resultModel.setAppInfo(appInfo);
                                    resultModel.setExecute(appInfo.getAppName());
                                    // 添加选中的数据到条件适配器
                                    newResultAddAdapter.add(resultModel);
                                    // 更新尾部适配器
                                    updateResultFooterAdapter();
                                }

                                @Override
                                public void onAppUninstalled(int position) {

                                }
                            }, false);
                            break;
                        case TaskResultConst.RESULT_ID_NAV:
                            //打开快捷导航
                            List<GoodsCategoryBean> sun = new ArrayList<>();
                            sun.add(new GoodsCategoryBean(0, "去公司", false));
                            sun.add(new GoodsCategoryBean(1, "回家", true));
                            resultGeneralSwitchOptions(item, sun);
                            break;
                        case TaskResultConst.RESULT_ID_MUSIC:
                            //音乐操作
                            List<GoodsCategoryBean> musicState = new ArrayList<>();
                            musicState.add(new GoodsCategoryBean(MusicControlUtil.SUSPEND, "暂停", false));
                            musicState.add(new GoodsCategoryBean(MusicControlUtil.PLAY, "播放", true));
                            musicState.add(new GoodsCategoryBean(MusicControlUtil.UPPER, "上一曲", false));
                            musicState.add(new GoodsCategoryBean(MusicControlUtil.NEXT, "下一曲", false));
                            resultGeneralSwitchOptions(item, musicState);
                            break;
                        case TaskResultConst.RESULT_ID_TIP:
                            //提示弹窗
                            tipOption(resultModel, item);
                            break;

                        default:
                    }

                })
                .show());
    }

    private void tipOption(ResultModel resultModel, TaskOptionModel item) {
        activity = (FragmentActivity) requireActivity();
        InputDialog.showDialog(requireActivity(), activity.getSupportFragmentManager(), text -> {
            resultModel.setName(item.getName());
            resultModel.setId(item.getId());
            resultModel.setType(1);
            resultModel.setExecute(text);
            // 添加选中的数据到条件适配器
            newResultAddAdapter.add(resultModel);
            // 更新尾部适配器
            updateResultFooterAdapter();

        });
    }

    private void brightnessSelection(ResultModel resultModel, TaskOptionModel item) {
        NumberPicker picker = new NumberPicker(requireActivity());
        picker.setRange(1, 100, 1);
        picker.setDefaultValue(80);
        picker.setTitle("亮度");
        picker.setWidth(DensityUtils.dp2px(requireActivity(), 420));
        picker.setHeight(DensityUtils.dp2px(requireActivity(), 260));
        picker.getTitleView().setVisibility(View.GONE);
        NumberWheelLayout wheelLayout = picker.getWheelLayout();
        wheelLayout.setTextSize(DensityUtils.dp2px(requireActivity(), 16));
        wheelLayout.setSelectedTextSize(DensityUtils.dp2px(requireActivity(), 19));
        picker.setOnNumberPickedListener((position, number) -> {
            resultModel.setBrightness(number.intValue());
            resultModel.setExecute(number.intValue() + "%");
            resultModel.setName(item.getName());
            resultModel.setId(item.getId());
            resultModel.setType(1);
            // 添加选中的数据到条件适配器
            newResultAddAdapter.add(resultModel);
            // 更新尾部适配器
            updateResultFooterAdapter();
        });
        picker.getWheelLayout().setOnNumberSelectedListener((position, item1) -> picker.getTitleView().setText(picker.getWheelView().formatItem(position)));
        picker.setFormatter(item12 -> item12 + " %");

        picker.show();
    }


    /**
     * 音量选择
     *
     * @param resultModel
     * @param item
     */
    private void soundSelection(ResultModel resultModel, TaskOptionModel item) {
        VolumePlatePicker picker = new VolumePlatePicker(requireActivity());
        picker.setWidth(DensityUtils.dp2px(requireActivity(), 420));
        picker.setHeight(DensityUtils.dp2px(requireActivity(), 260));
        picker.getTitleView().setVisibility(View.GONE);
        // picker.setDefaultValue(new ConditionalEntity(1, "减少", false), "5", "");
        LinkageWheelLayout wheelLayout = picker.getWheelLayout();
        wheelLayout.setTextSize(DensityUtils.dp2px(requireActivity(), 16));
        wheelLayout.setSelectedTextSize(DensityUtils.dp2px(requireActivity(), 19));
        picker.setOnCarPlatePickedListener((province, letter) -> {
            resultModel.setExecute(province.getName() + " " + letter);
            resultModel.setName(item.getName());
            resultModel.setId(item.getId());
            resultModel.setVolume(Integer.parseInt(letter));
            resultModel.setVolumeType(province.getId());
            // 添加选中的数据到条件适配器
            newResultAddAdapter.add(resultModel);
            // 更新尾部适配器
            updateResultFooterAdapter();
        });
        picker.show();
    }


    /**
     * 延时弹窗
     *
     * @param resultModel
     * @param item
     */
    private void timeDelayedOption(ResultModel resultModel, TaskOptionModel item) {

        DelayedPlatePicker picker = new DelayedPlatePicker(requireActivity());
        picker.setWidth(DensityUtils.dp2px(requireActivity(), 420));
        picker.setHeight(DensityUtils.dp2px(requireActivity(), 260));
        picker.getTitleView().setVisibility(View.GONE);
        picker.setDefaultValue("秒", "4", "");
        LinkageWheelLayout wheelLayout = picker.getWheelLayout();
        wheelLayout.setTextSize(DensityUtils.dp2px(requireActivity(), 16));
        wheelLayout.setSelectedTextSize(DensityUtils.dp2px(requireActivity(), 19));
        picker.setOnCarPlatePickedListener((province, letter) -> {
            resultModel.setDelayTime(Integer.parseInt(letter));
            resultModel.setTimeUnit("秒".equals(province) ? TimeUnit.SECONDS : TimeUnit.MINUTES);
            resultModel.setExecute(province);
            resultModel.setName(item.getName());
            resultModel.setId(item.getId());
            resultModel.setType(5);
            // 添加选中的数据到条件适配器
            newResultAddAdapter.add(resultModel);
            // 更新尾部适配器
            updateResultFooterAdapter();
        });
        picker.show();

    }


    /**
     * 结果统一开关选择
     *
     * @param optionModel
     * @param data
     */
    public void resultGeneralSwitchOptions(TaskOptionModel optionModel, List<GoodsCategoryBean> data) {

        OptionPicker picker = new OptionPicker(requireActivity());
        picker.setTitle("状态选择");
        picker.setData(data);
        picker.setDefaultPosition(0);
        picker.setWidth(DensityUtils.dp2px(requireActivity(), 420));
        picker.setHeight(DensityUtils.dp2px(requireActivity(), 260));
        picker.getTitleView().setVisibility(View.GONE);
        OptionWheelLayout wheelLayout = picker.getWheelLayout();
        wheelLayout.setTextSize(DensityUtils.dp2px(requireActivity(), 19));
        wheelLayout.setSelectedTextSize(DensityUtils.dp2px(requireActivity(), 22));
        picker.setOnOptionPickedListener((position, item) -> {
            GoodsCategoryBean bean = (GoodsCategoryBean) item;
            ResultModel resultModel = new ResultModel();
            resultModel.setName(optionModel.getName());
            resultModel.setId(optionModel.getId());
            switch (optionModel.getId()) {
                case TaskResultConst.RESULT_ID_WIFI:
                    resultModel.setExecute(bean.getName());
                    resultModel.setType(optionModel.getType());
                    resultModel.setNavigation(bean.getId());
                    break;
                case TaskResultConst.RESULT_ID_BLUETOOTH:
                    resultModel.setExecute(bean.getName());
                    resultModel.setType(optionModel.getType());
                    resultModel.setNavigation(bean.getId());
                    break;
                case TaskResultConst.RESULT_ID_NAV:
                    resultModel.setExecute(bean.getName());
                    resultModel.setType(optionModel.getType());
                    resultModel.setNavigation(bean.getId());
                    break;
                case TaskResultConst.RESULT_ID_MUSIC:
                    resultModel.setExecute(bean.getName());
                    resultModel.setMusicEvent(bean.getId());
                    break;

                default:
            }
            // 添加选中的数据到条件适配器
            newResultAddAdapter.add(resultModel);
            // 更新尾部适配器
            updateFooterAdapter();
        });
        picker.show();
    }

    /**
     * 更新条件列表和任务名称
     */
    private void updateFooterAdapter() {
        // 检查数据源是否为空
        boolean isEmpty = !newTaskAddAdapter.getItems().isEmpty();
        // 根据数据源是否为空来更新尾部适配器
        footerAdapter.updateDataStatus(isEmpty);
        if (isEmpty) {
            String option = Objects.requireNonNull(newTaskAddAdapter.getItem(0)).getId() == TaskConditionConst.TYPE_DEVICE_START ||
                    newTaskAddAdapter.getItem(0).getId() == TaskConditionConst.TYPE_START ? "" :
                    Objects.requireNonNull(newTaskAddAdapter.getItem(0)).getExecute();
            binding.etName.setText("如果" + Objects.requireNonNull(newTaskAddAdapter.getItem(0)).getName() + option);
        } else {
            binding.etName.setText("");
        }

    }

    /**
     * 刷新结果列表
     */
    private void updateResultFooterAdapter() {
        // 检查数据源是否为空
        boolean isEmpty = !newResultAddAdapter.getItems().isEmpty();
        resultFooterAdapter.updateDataStatus(isEmpty);
    }


}