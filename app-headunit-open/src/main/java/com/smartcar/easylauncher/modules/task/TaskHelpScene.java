
package com.smartcar.easylauncher.modules.task;


import static android.content.Context.POWER_SERVICE;
import static com.smartcar.easylauncher.shared.utils.AppConfig.getPackageName;

import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.PowerManager;
import android.provider.Settings;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.databinding.FragmentTaskHelpBinding;
import com.smartcar.easylauncher.modules.other.WebActivity;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.permission.BackgroundActivityPermissionUtils;
import com.smartcar.easylauncher.shared.utils.permission.OverlayPermissionUtils;
import com.smartcar.easylauncher.shared.utils.system.DesktopUtils;

import java.util.Objects;

/**
 * 任务使用帮助
 *
 * <AUTHOR>
 */
public class TaskHelpScene extends BaseScene {
    public static final String TAG = TaskHelpScene.class.getSimpleName();
    private FragmentTaskHelpBinding binding;


    @NonNull
    @Override
    protected ViewGroup onCreateNewView(@NonNull LayoutInflater layoutInflater, @Nullable ViewGroup viewGroup, @Nullable Bundle bundle) {
        binding = FragmentTaskHelpBinding.inflate(layoutInflater, viewGroup, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        binding.faqClickMe.setOnClickListener(view1 -> {
            Intent intent = new Intent(getActivity(), WebActivity.class);
            Bundle bundle = new Bundle();
            bundle.putString("ProductUrl", Const.TASK_QFA);
            intent.putExtras(bundle);
            requireActivity().startActivity(intent);
        });

        binding.btDesktop.setOnClickListener(view12 -> {
            DesktopUtils.clearDefaultLauncher(Objects.requireNonNull(requireActivity()));
            DesktopUtils.openResolverLauncher(requireActivity());
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
//                    if (DesktopUtils.isComponentEnabled(getActivity(), "com.smartcar.easylauncher.ui.home.HomeActivity")) {
//                        desktopDialog(1, "默认桌面", "设置默认桌面体验沉浸式操作", getString(R.string.confirm),
//                                getString(R.string.cancel));
//                    } else {
//                        desktopDialog(1, "热更新准备就绪", "点击确定重新打开设置默认桌面", getString(R.string.confirm),
//                                getString(R.string.cancel));
//                    }
//                } else {
//                    desktopDialog(1, "默认桌面", "设置默认桌面体验沉浸式操作", getString(R.string.confirm),
//                            getString(R.string.cancel));
//                }

        });
        binding.btDesktop2.setOnClickListener(view13 -> {
            setDefault();
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
//                    if (DesktopUtils.isComponentEnabled(getActivity(), "com.smartcar.easylauncher.ui.home.HomeActivity")) {
//                        desktopDialog(2, "默认桌面", "设置默认桌面体验沉浸式操作", getString(R.string.confirm),
//                                getString(R.string.cancel));
//                    } else {
//                        desktopDialog(2, "热更新准备就绪", "点击确定重新打开设置默认桌面", getString(R.string.confirm),
//                                getString(R.string.cancel));
//                    }
//                } else {
//                    desktopDialog(2, "默认桌面", "设置默认桌面体验沉浸式操作", getString(R.string.confirm),
//                            getString(R.string.cancel));
//                }

        });


        binding.btNetwork.setOnClickListener(view14 -> {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (!Settings.System.canWrite(getActivity())) {
                    try {
                        Intent intent = new Intent(Settings.ACTION_MANAGE_WRITE_SETTINGS);
                        intent.setData(Uri.parse("package:" + getPackageName()));
                        requireActivity().startActivity(intent);
                    } catch (Exception e) {
                        MToast.makeTextShort("无法直接打开设置页面");
                    }

                } else {
                    MToast.makeTextShort("已经开启修改系统设置权限");
                }
            } else {
                MToast.makeTextShort("已经开启修改系统设置权限");
            }
        });

        binding.btDianchi.setOnClickListener(view15 -> {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PowerManager powerManager = (PowerManager) Objects.requireNonNull(getActivity()).getSystemService(POWER_SERVICE);

                boolean hasIgnored = powerManager.isIgnoringBatteryOptimizations(getActivity().getPackageName());
                //  判断当前APP是否有加入电池优化的白名单，如果没有，弹出加入电池优化的白名单的设置对话框。
                if (!hasIgnored) {
                    try {
                        Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
                        intent.setData(Uri.parse("package:" + getActivity().getPackageName()));
                        if (intent.resolveActivity(getActivity().getPackageManager()) != null) {
                            requireActivity().startActivity(intent);
                        }
                    } catch (Exception e) {
                        MToast.makeTextShort("无法直接打开设置页面");
                    }
                } else {
                    MToast.makeTextShort("已经开启忽略电池优化");
                    MyLog.d("ignoreBattery", "hasIgnored");
                }
            } else {
                MToast.makeTextShort("已经开启忽略电池优化");
            }
        });
        binding.btHoutai.setOnClickListener(view16 -> {
            if (BackgroundActivityPermissionUtils.hasBackgroundActivityPermission(getActivity())) {
                MToast.makeTextShort("已经开启后台弹窗权限");
            } else {
                BackgroundActivityPermissionUtils.requestBackgroundActivityPermission(getActivity(), 1);
            }
        });
        binding.btXuanfuchuang.setOnClickListener(view17 -> {
            if (OverlayPermissionUtils.hasOverlayPermission(getActivity())) {
                MToast.makeTextShort("已经开启悬浮窗权限");
            } else {
                OverlayPermissionUtils.requestOverlayPermission(getActivity(), 1);
            }
        });
    }


    /**
     * 选择桌面弹窗
     */
    public void desktopDialog(int type, String title, String content, String ok, String cancel) {
//        GeneralDialog.showGeneralDialog(getChildFragmentManager(), getActivity(), title, content, ok, cancel, new GeneralDialog.DialogClickListener() {
//            @Override
//            public void onPositiveClick() {
//                if (DesktopUtils.isComponentEnabled(Objects.requireNonNull(getActivity()), "com.smartcar.easylauncher.ui.home.HomeActivity")) {
//                    if (type == 1) {
//                        DesktopUtils.clearDefaultLauncher(getActivity());
//                        DesktopUtils.openResolverLauncher(getActivity());
//                    } else {
//                        setDefault();
//                    }
//
//                } else {
//                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
//                        DesktopUtils.modifyComponent(getActivity());
//                    } else {
//                        DesktopUtils.modifyComponent(getActivity());
//                        DesktopUtils.clearDefaultLauncher(getActivity());
//                        DesktopUtils.openResolverLauncher(getActivity());
//                    }
//                }
//            }
//
//            @Override
//            public void onNegativeClick() {
//                // do something
//            }
//        });


    }

    public void setDefault() {
        try {
            Intent localIntent = new Intent("android.settings.HOME_SETTINGS");
            requireActivity().startActivity(localIntent);
        } catch (Exception e) {
            MToast.makeTextShort("选择桌面失败，请联系开发者");
            e.printStackTrace();
        }

    }
}
