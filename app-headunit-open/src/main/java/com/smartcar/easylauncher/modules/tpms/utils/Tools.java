package com.smartcar.easylauncher.modules.tpms.utils;


import android.app.ActivityManager;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Process;
import android.util.Log;
import android.widget.Toast;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
/* loaded from: classes.dex */
public class Tools {
    private static boolean flag = true;
    static Toast toast = null;
    static final String usbHeartbeatServer = "com.cz.usbserial.activity.HeartbeatServer";
    static final String usbService = "com.cz.usbserial.activity.TpmsServer";

    public static boolean isUSBService(Context context) {
        List<ActivityManager.RunningServiceInfo> mServiceList = ((ActivityManager) context.getSystemService("activity")).getRunningServices(30);
        for (ActivityManager.RunningServiceInfo info : mServiceList) {
            if (usbService.equals(info.service.getClassName())) {
                return true;
            }
        }
        return false;
    }

    public static boolean isUSBHeartbeatServer(Context context) {
        List<ActivityManager.RunningServiceInfo> mServiceList = ((ActivityManager) context.getSystemService("activity")).getRunningServices(30);
        for (ActivityManager.RunningServiceInfo info : mServiceList) {
            if (usbHeartbeatServer.equals(info.service.getClassName())) {
                return true;
            }
        }
        return false;
    }

    public static void Toast(Context context, String text) {
        if (toast != null) {
            toast.cancel();
        }
        toast = Toast.makeText(context, text, 0);
        toast.show();
    }

    public static void Log(String str) {
        if (flag) {
            Log.v("TPMS", str);
        }
    }

    public static byte[] dealData(byte[] buff) {
        int len = buff.length / 10;
        for (int i = 0; i < len; i++) {
            if (buff[i * 10] == 85 && buff[(i * 10) + 1] == -86) {
                byte[] b = new byte[buff[(i * 10) + 2]];
                for (int y = 0; y < 10; y++) {
                    b[y] = buff[(i * 10) + y];
                }
                if (isDataBoolean2(b)) {
                    return buff;
                }
            }
        }
        return null;
    }

    private static boolean isDataBoolean2(byte[] buff) {
        byte sum = buff[0];
        for (int i = 1; i < buff.length - 1; i++) {
            sum = (byte) (buff[i] ^ sum);
        }
        return sum == buff[buff.length + (-1)];
    }

    public static boolean isWarn2(byte[] data, int[] i) {
        if (data.length == 10) {
            isDataBoolean(data);
            return false;
        }
        return false;
    }

    private static boolean isDataBoolean(byte[] buff) {
        byte sum = buff[0];
        for (int i = 1; i < buff.length - 2; i++) {
            sum = (byte) (buff[i] ^ sum);
        }
        return sum == buff[buff.length + (-1)];
    }

    public static boolean checkData(byte[] data) {
        byte sum = data[data.length - 1];
        byte dat = data[0];
        for (int i = 1; i < data.length - 2; i++) {
            dat = (byte) (data[i] ^ dat);
        }
        return dat == sum;
    }

    public static boolean isHP(byte b, int i) {
        return ((int) (((double) Integer.valueOf(Integer.toBinaryString(b & 255), 2).intValue()) * 3.44d)) > (i * 10) + 250;
    }

    public static boolean isLP(byte b, int i) {
        return ((int) (((double) Integer.valueOf(Integer.toBinaryString(b & 255), 2).intValue()) * 3.44d)) < (i * 10) + 180;
    }

    public static boolean isHT(int b, int i) {
        return b > i;
    }

    public static String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder("");
        if (src == null || src.length <= 0) {
            return null;
        }
        for (byte b : src) {
            int v = b & 255;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }

    public static String byteToHexString(byte b) {
        String stmp = Integer.toHexString(b & 255);
        if (stmp.length() == 1) {
            stmp = "0" + stmp;
        }
        return stmp.toUpperCase();
    }

    public static double getType_T(String str) {
        double d_T = Integer.parseInt(str, 16) - 50;
        return d_T;
    }

    public static byte[] sum(byte[] s) {
        byte checkdata = 0;
        for (int i = 0; i < s.length - 1; i++) {
            checkdata = (byte) (s[i] ^ checkdata);
        }
        s[s.length - 1] = checkdata;
        return s;
    }

    public static String byteToHexString(byte[] b) {
        StringBuilder sb = new StringBuilder("");
        for (byte c : b) {
            String stmp = Integer.toHexString(c & 255);
            sb.append(stmp.length() == 1 ? "0" + stmp : stmp);
            sb.append(" ");
        }
        return sb.toString().toUpperCase().trim();
    }

    public static byte[] requestData(byte[] data, int length) {
        byte[] buff = new byte[length];
        for (int i = 0; i < buff.length; i++) {
            buff[i] = data[i];
        }
        Log.d("REQUEST", byteToHexString(buff));
        return buff;
    }

    public static byte[] sendPaassword(byte b) {
        return sum(new byte[]{85, -86, 6, 90, 17});
    }

    public static boolean isPassword(byte b1, byte b2) {
        if (b2 == 255) {
            return true;
        }
        byte b = (byte) ((((((((b1 ^ 32) ^ 21) ^ 16) ^ 1) ^ 2) ^ 3) ^ 4) ^ 5);
        return b != b2;
    }

    public static byte isPasswordByte(byte[] buff) {
        for (int i = 0; i < buff.length; i++) {
            try {
                if (buff[i] == 85 && buff[i + 1] == -86 && buff[i + 2] == 6 && buff[i + 3] == -91) {
                    return buff[i + 4];
                }
            } catch (Exception e) {
                return (byte) -1;
            }
        }
        return (byte) -1;
    }

    public static String getCurProcessName(Context context) {
        int pid = Process.myPid();
        ActivityManager mActivityManager = (ActivityManager) context.getSystemService("activity");
        for (ActivityManager.RunningAppProcessInfo appProcess : mActivityManager.getRunningAppProcesses()) {
            if (appProcess.pid == pid) {
                return appProcess.processName;
            }
        }
        return null;
    }

    public static double div(double v1, double v2, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.divide(b2, scale, 4).doubleValue();
    }

    public static byte[] getMergeBytes(byte[] pByteA, int numA, byte[] pByteB, int numB) {
        byte[] b = new byte[numA + numB];
        for (int i = 0; i < numA; i++) {
            b[i] = pByteA[i];
        }
        for (int i2 = 0; i2 < numB; i2++) {
            b[numA + i2] = pByteB[i2];
        }
        return b;
    }

    public static boolean isNetworkAvailable(Context context) {
        NetworkInfo info;
        ConnectivityManager connectivity = (ConnectivityManager) context.getSystemService("connectivity");
        return connectivity != null && (info = connectivity.getActiveNetworkInfo()) != null && info.isConnected() && info.getState() == NetworkInfo.State.CONNECTED;
    }

    public static String getVersionName(Context context) throws Exception {
        PackageManager packageManager = context.getPackageManager();
        PackageInfo packInfo = packageManager.getPackageInfo(context.getPackageName(), 0);
        String version = packInfo.versionName;
        return version;
    }

    public static String getAppBuildTime(Context context) {
        String result = "";
        try {
            ApplicationInfo ai = context.getPackageManager().getApplicationInfo(context.getPackageName(), 0);
            ZipFile zf = new ZipFile(ai.sourceDir);
            ZipEntry ze = zf.getEntry("META-INF/MANIFEST.MF");
            long time = ze.getTime();
            SimpleDateFormat formatter = (SimpleDateFormat) SimpleDateFormat.getInstance();
            formatter.applyPattern("yyyy/MM/dd HH:mm");
            result = formatter.format(new Date(time));
            zf.close();
            return result;
        } catch (Exception e) {
            return result;
        }
    }
}
