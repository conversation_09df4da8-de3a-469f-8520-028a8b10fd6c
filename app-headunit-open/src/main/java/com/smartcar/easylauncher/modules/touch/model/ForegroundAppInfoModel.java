package com.smartcar.easylauncher.modules.touch.model;

/**
 * <b>Package:</b> com.zh.touchassistant.model <br>
 * <b>FileName:</b> CurrentActivityModel <br>
 * <b>Create Date:</b> 2018/12/17  下午3:06 <br>
 * <b>Author:</b> zihe <br>
 * <b>Description:</b>  <br>
 */
public class ForegroundAppInfoModel extends BaseDataModel {
    // 前台应用的包名
    private String foregroundAppPackageName;
    // 前台应用的活动类名
    private String foregroundActivityClassName;

    // 构造函数，接收前台应用的包名和活动类名作为参数
    public ForegroundAppInfoModel(String foregroundAppPackageName, String foregroundActivityClassName) {
        this.foregroundAppPackageName = foregroundAppPackageName;
        this.foregroundActivityClassName = foregroundActivityClassName;
    }

    // 获取前台应用的包名
    public String getForegroundAppPackageName() {
        return foregroundAppPackageName;
    }

    // 获取前台应用的活动类名
    public String getForegroundActivityClassName() {
        return foregroundActivityClassName;
    }

    // 重写toString方法，返回前台应用的包名和活动类名
    @Override
    public String toString() {
        return "CurrentActivityModel{" +
                "foregroundAppPackageName='" + foregroundAppPackageName + '\'' +
                ", foregroundActivityClassName='" + foregroundActivityClassName + '\'' +
                '}';
    }
}