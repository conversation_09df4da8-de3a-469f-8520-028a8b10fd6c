package com.smartcar.easylauncher.modules.personal;

import static com.bytedance.scene.navigation.NavigationSceneGetter.requireNavigationScene;
import static com.zhpan.bannerview.constants.PageStyle.MULTI_PAGE_SCALE;
import static com.zhpan.indicator.enums.IndicatorSlideMode.SMOOTH;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.chad.library.adapter4.QuickAdapterHelper;
import com.github.AAChartModel.AAChartCore.AAChartCreator.AAChartEvents;
import com.github.AAChartModel.AAChartCore.AAChartCreator.AAChartModel;
import com.github.AAChartModel.AAChartCore.AAChartCreator.AAChartView;
import com.github.AAChartModel.AAChartCore.AAChartCreator.AASeriesElement;
import com.github.AAChartModel.AAChartCore.AAChartEnum.AAChartFontWeightType;
import com.github.AAChartModel.AAChartCore.AAChartEnum.AAChartType;
import com.github.AAChartModel.AAChartCore.AAOptionsModel.AADataLabels;
import com.github.AAChartModel.AAChartCore.AAOptionsModel.AAItemStyle;
import com.github.AAChartModel.AAChartCore.AAOptionsModel.AAOptions;
import com.github.AAChartModel.AAChartCore.AAOptionsModel.AAPie;
import com.github.AAChartModel.AAChartCore.AAOptionsModel.AAStyle;
import com.github.AAChartModel.AAChartCore.AATools.AAGradientColor;
import com.github.AAChartModel.AAChartCore.AATools.AALinearGradientDirection;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.smartcar.blurview.BVConstraintLayout;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.common.DragAndSwipeAdapter;
import com.smartcar.easylauncher.shared.adapter.home.HomeTopHeaderAdapter;
import com.smartcar.easylauncher.shared.adapter.personal.HonorTagAdapter;
import com.smartcar.easylauncher.shared.adapter.personal.PersonalBannerAdapter;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.core.constants.StatusCodeModel;
import com.smartcar.easylauncher.databinding.ScenePersonalBinding;
import com.smartcar.easylauncher.data.database.entity.Expense;
import com.smartcar.easylauncher.shared.dialog.GeneralDialog;
import com.smartcar.easylauncher.shared.dialog.LoginDialog;
import com.smartcar.easylauncher.infrastructure.interfaces.SkinChangeListener;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.core.manager.UserManager;
import com.smartcar.easylauncher.data.model.system.AppInfo;
import com.smartcar.easylauncher.data.model.theme.SkinModel;
import com.smartcar.easylauncher.data.model.user.HonorTag;
import com.smartcar.easylauncher.data.model.user.PersonalBannerModel;
import com.smartcar.easylauncher.data.model.user.UserModel;
import com.smartcar.easylauncher.data.model.vehicle.DefaultVehicleDataModel;
import com.smartcar.easylauncher.data.model.vehicle.ExpenseListModel;
import com.smartcar.easylauncher.data.repository.TripRepository;
import com.smartcar.easylauncher.infrastructure.skin.SkinManager;
import com.smartcar.easylauncher.modules.car.CarTabNavigationScene;
import com.smartcar.easylauncher.modules.expense.ExpenseTabNavigationScene;
import com.smartcar.easylauncher.modules.notice.NoticeTabNavigationScene;
import com.smartcar.easylauncher.modules.trip.TripTabNavigationScene;
import com.smartcar.easylauncher.shared.utils.AppUtils;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.Utilities;
import com.smartcar.easylauncher.shared.utils.location.DistanceConverter;
import com.smartcar.easylauncher.shared.utils.time.TimeFormatter;
import com.smartcar.easylauncher.shared.view.CircleImageView;
import com.smartcar.easylauncher.shared.view.behavior.AppBarLayoutOverScrollViewBehavior;
import com.smartcar.easylauncher.shared.widget.spanbuilder.Spans;
import com.zhpan.bannerview.BannerViewPager;
import com.zhpan.bannerview.constants.IndicatorGravity;
import com.zhpan.bannerview.utils.BannerUtils;
import com.zhpan.indicator.enums.IndicatorStyle;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import java9.util.concurrent.CompletableFuture;
import kotlin.Pair;
import rxhttp.RxHttp;
import rxhttp.wrapper.cache.CacheMode;


/**
 * 个人中心场景类 - 车载启动器个人信息页面
 * 功能特性：
 * 1. 用户信息展示与管理（头像、昵称、标签等）
 * 2. 车辆信息展示（品牌、车牌、总里程等）
 * 3. 费用统计与图表展示（饼图、折线图）
 * 4. 行程数据展示（今日里程、时长等）
 * 5. 轮播图展示与热门应用推荐
 * 性能优化策略：
 * 1. ViewStub延迟加载重型组件（图表、轮播图、RecyclerView）
 * 2. 分阶段UI初始化（极简UI -> 次要组件 -> 重型组件）
 * 3. 异步数据处理与错峰网络请求
 * 4. 图表配置缓存与样式复用
 * 5. 详细性能监控与日志记录
 *
 * <AUTHOR>
 * @date 2024/07/04
 * @version 2.0 - 性能优化版本，支持ViewStub延迟加载
 */
public class PersonalScene extends BaseScene implements View.OnClickListener, SkinChangeListener {

    private static final String TAG = PersonalScene.class.getSimpleName();

    // ================================ UI组件相关 ================================

    /** 视图绑定对象，用于访问布局中的UI组件 */
    private ScenePersonalBinding binding;

    /** 适配器助手，用于管理Header和Footer */
    private QuickAdapterHelper helper;

    // ================================ 数据相关 ================================

    /** 费用数据列表，用于图表展示和统计计算 */
    private List<Expense> expensesList;

    // ================================ 图表配置缓存 ================================

    /** 饼图配置缓存，避免重复创建AAOptions对象 */
    private AAOptions aaPieChartOptions;

    /** 数据准备完成标志，用于皮肤切换时的图表重绘 */
    private boolean mDataPrepared = false;
    

    /** 提示框样式缓存 */
    private AAStyle mTooltipTextStyle;

    /** 图例样式缓存 */
    private AAItemStyle mLegendItemStyle;

    // ================================ 性能监控相关 ================================

    /** 页面开始加载时间戳，用于计算总加载时间 */
    private long mPageStartTime;

    /** 首次可交互时间戳，用于衡量用户感知性能 */
    private long mFirstInteractionTime;

    /** 首次内容显示时间戳，用于衡量内容加载速度 */
    private long mFirstContentTime;

    /** 基础UI就绪标志，标识最基本的交互功能是否可用 */
    private boolean mIsBasicUIReady = false;

    /** 已完成的网络请求数量，用于跟踪数据加载进度 */
    private int mCompletedRequests = 0;

    /** 总网络请求数量（用户信息、车辆信息、费用数据） */
    private final int mTotalRequests = 3;

    // ================================ ViewStub延迟加载控制 ================================
    // 这些标志位用于控制重型组件的延迟加载，避免阻塞UI线程

    /** 饼图ViewStub是否已inflate */
    private boolean isPieChartInflated = false;

    /** 折线图ViewStub是否已inflate */
    private boolean isLineChartInflated = false;

    /** 轮播图ViewStub是否已inflate */
    private boolean isBannerInflated = false;

    /** RecyclerView ViewStub是否已inflate */
    private boolean isRecyclerViewInflated = false;

    /** 图表模糊蒙版ViewStub是否已inflate */
    private boolean isChartOverlayInflated = false;

    /** VIP模糊蒙版ViewStub是否已inflate */
    private boolean isVipOverlayInflated = false;

    // ================================ 延迟加载的视图引用 ================================
    // 这些视图在ViewStub inflate后才会被赋值

    /** 饼图视图引用，用于显示费用分布 */
    private AAChartView pieChartView = null;

    /** 折线图视图引用，用于显示里程趋势 */
    private AAChartView lineChartView = null;

    /** 轮播图视图引用，用于显示推广内容 */
    private BannerViewPager<PersonalBannerModel.RowsDTO> bannerView = null;

    /** 热门应用列表视图引用 */
    private RecyclerView recyclerView = null;

    /** 图表区域模糊蒙版布局引用 */
    private View chartOverlayLayout = null;

    /** VIP区域模糊蒙版布局引用 */
    private View vipOverlayLayout = null;

    // ================================ 生命周期方法 ================================

    /**
     * 创建新视图 - 页面的核心入口方法
     * 采用分阶段加载策略，优化用户感知性能：
     * 1. 立即创建视图绑定和极简UI（返回按钮等）
     * 2. 延迟16ms初始化次要组件（一帧时间内）
     * 3. 延迟50ms开始数据加载（避免阻塞UI）
     * 4. 延迟500ms加载重型组件（图表、轮播图等）
     *
     * @param layoutInflater 布局填充器
     * @param viewGroup 父视图组
     * @param bundle 保存的状态数据
     * @return 创建的根视图
     */
    @NonNull
    @Override
    protected ViewGroup onCreateNewView(@NonNull LayoutInflater layoutInflater, @Nullable ViewGroup viewGroup, @Nullable Bundle bundle) {
        // 记录页面开始加载时间，用于性能监控
        mPageStartTime = System.currentTimeMillis();
        MyLog.d(TAG, "页面开始加载");

        // ================================ 阶段1：视图绑定创建 ================================
        long bindingStart = System.currentTimeMillis();

        // 使用应用级别的LayoutInflater缓存，避免重复创建
        LayoutInflater cachedInflater = getLayoutInflater();

        // 创建视图绑定对象，这是访问UI组件的入口
        binding = ScenePersonalBinding.inflate(cachedInflater);
        MyLog.d(TAG, "视图绑定创建: " + (System.currentTimeMillis() - bindingStart) + "ms");

        // ================================ 阶段2：极简基础UI初始化 ================================
        // 只初始化最关键的交互功能，确保用户能立即操作
        initMinimalUI();

        // ================================ 阶段3：分阶段延迟加载策略 ================================
        Handler mainHandler = new Handler(Looper.getMainLooper());

        // 延迟16ms初始化次要组件（一帧时间内，约60fps）
        // 这样可以让主线程有时间完成基础UI的渲染
        mainHandler.postDelayed(this::initSecondaryComponents, 16);

        // 延迟50ms开始数据加载
        // 给UI足够时间完成初始化，避免网络请求阻塞界面
        mainHandler.postDelayed(this::loadAllData, 50);

        // 延迟500ms加载重型组件（图表、轮播图、RecyclerView）
        // 这些组件初始化耗时较长，延迟加载可以显著提升感知性能
        mainHandler.postDelayed(this::loadHeavyComponents, 500);

        // ================================ 性能监控 ================================
        // 记录页面创建完成时间，用于性能分析
        long pageCreateTime = System.currentTimeMillis() - mPageStartTime;
        MyLog.d(TAG, "页面创建完成，总耗时: " + pageCreateTime + "ms");

        return binding.getRoot();
    }

    /**
     * 场景创建时的初始化
     * 注册皮肤变化监听器，确保主题切换时UI能正确更新
     */
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 注册皮肤变化监听器，当用户切换主题时会收到回调
        registerSkinChangeListener();
    }

    // ================================ UI初始化方法 ================================

    /**
     * 极简UI初始化 - 第一阶段初始化
     * 只初始化最关键的交互功能，确保用户能立即操作：
     * 1. 返回按钮点击事件
     * 2. 头部图标显示状态
     * 3. 基础布局透明度设置
     * 设计原则：
     * - 最小化初始化内容，提升首次可交互时间
     * - 避免访问ViewStub中的重型组件
     * - 确保基础导航功能立即可用
     */
    private void initMinimalUI() {
        long stepStart = System.currentTimeMillis();
        MyLog.d(TAG, "开始极简UI初始化");

        // 记录首次可交互时间，这是用户感知性能的重要指标
        mFirstInteractionTime = System.currentTimeMillis();

        // ================================ 基础交互功能 ================================
        // 设置返回按钮点击事件，这是用户最重要的导航功能
        binding.btBack.setOnClickListener(this);

        // ================================ 头部图标状态设置 ================================
        // 确保整个标题布局可见，避免透明度问题
        binding.ucHeadTitle.titleLayout.setAlpha(1f);

        // 确保设置和消息图标始终可见，这是重要的功能入口
        binding.ucHeadTitle.ucSettingIv.setVisibility(View.VISIBLE);
        binding.ucHeadTitle.ucMsgIv.setVisibility(View.VISIBLE);
        binding.ucHeadTitle.ucSettingIv.setAlpha(1f);
        binding.ucHeadTitle.ucMsgIv.setAlpha(1f);

        // 设置初始图标为白色（展开状态），符合设计规范
        binding.ucHeadTitle.ucSettingIv.setImageResource(R.drawable.icon_setting_day);
        binding.ucHeadTitle.ucMsgIv.setImageResource(R.drawable.icon_msg_day);

        // ================================ 重要提醒 ================================
        // 注意：不要在这里访问图表视图，它们现在是ViewStub
        // 图表相关的初始化将在loadHeavyComponents()中进行

        // ================================ 状态标记与性能监控 ================================
        mIsBasicUIReady = true;
        long uiTime = System.currentTimeMillis() - stepStart;
        MyLog.d(TAG, "极简UI初始化完成: " + uiTime + "ms");
        MyLog.d(TAG, "首次可交互时间: " + (mFirstInteractionTime - mPageStartTime) + "ms");
    }

    /**
     * 初始化次要组件 - 第二阶段初始化
     * 在基础UI就绪后延迟16ms执行，初始化非关键的UI组件：
     * 1. 头像和消息按钮点击事件
     * 2. 图表样式配置缓存
     * 3. 折叠头部滚动监听
     * 4. 本地行程数据观察
     * 设计原则：
     * - 在一帧时间内完成，不影响UI流畅度
     * - 避免初始化重型组件（图表、轮播图等）
     * - 为后续数据加载做好准备
     */
    private void initSecondaryComponents() {
        long secondaryStart = System.currentTimeMillis();
        MyLog.d(TAG, "开始初始化次要组件");

        // ================================ 次要交互功能 ================================
        // 设置头像点击事件（登录/退出登录）
        binding.ucAvater.setOnClickListener(this);

        // 设置消息按钮点击事件
        binding.ucHeadTitle.ucMsgIv.setOnClickListener(this);

        // ================================ 样式配置初始化 ================================
        // 初始化图表公共样式配置，避免后续重复创建
        initCommonStyles();

        // ================================ 头部滚动效果 ================================
        // 初始化折叠头部的滚动监听和动画效果
        initHeaderView();

        // ================================ 重要提醒 ================================
        // 注意：RecyclerView和Banner现在使用ViewStub，不在这里初始化
        // 它们将在loadHeavyComponents()中延迟加载，避免阻塞UI线程

        // ================================ 数据观察 ================================
        // 观察本地行程数据变化，实时更新里程和时长显示
        observeTripData();

        // ================================ 性能监控 ================================
        // 记录首次内容显示时间，用于衡量内容加载速度
        mFirstContentTime = System.currentTimeMillis();

        long secondaryTime = System.currentTimeMillis() - secondaryStart;
        MyLog.d(TAG, "次要组件初始化完成: " + secondaryTime + "ms");
        MyLog.d(TAG, "首次内容显示时间: " + (mFirstContentTime - mPageStartTime) + "ms");
    }

    /**
     * 加载重型组件 - 第三阶段初始化
     * 在基础功能就绪后延迟500ms执行，加载耗时的重型组件：
     * 1. 轮播图ViewStub延迟加载
     * 2. RecyclerView ViewStub延迟加载
     * 设计原则：
     * - 这些组件初始化耗时较长，延迟加载避免阻塞UI
     * - 使用ViewStub技术，按需inflate视图
     * - 异步处理，不影响用户的基础操作
     * 注意：图表组件将在有数据时才加载，进一步优化性能
     */
    private void loadHeavyComponents() {
        long heavyStart = System.currentTimeMillis();
        MyLog.d(TAG, "🚀 开始加载重型组件");

        // ================================ 轮播图延迟加载 ================================
        // 轮播图包含图片加载和复杂布局，延迟加载可显著提升性能
        inflateBannerIfNeeded();

        // ================================ 热门应用列表延迟加载 ================================
        // RecyclerView包含多个应用项，延迟加载避免阻塞主线程
        inflateRecyclerViewIfNeeded();

        // ================================ 性能监控 ================================
        long heavyTime = System.currentTimeMillis() - heavyStart;
        MyLog.d(TAG, "🚀 重型组件加载完成: " + heavyTime + "ms");

        // 注意：图表组件（饼图、折线图）将在以下情况下才加载：
        // 1. 用户登录且有费用数据时
        // 2. 用户未登录时显示默认图表
        // 这样可以进一步优化首屏加载性能
    }


    // ================================ 图表管理方法 ================================

    /**
     * 强制显示图表 - 登录后确保图表可见
     * 当用户登录成功后调用此方法，确保图表能正确显示：
     * 1. 立即加载图表ViewStub（饼图、折线图）
     * 2. 根据数据情况显示真实图表或默认图表
     * 3. 延迟执行避免与登录流程冲突
     * 使用场景：
     * - 用户登录成功后
     * - 数据加载完成后
     * - 需要刷新图表显示时
     */
    public void forceShowCharts() {
        MyLog.d(TAG, "🚀 强制显示图表");

        // ================================ 立即加载图表ViewStub ================================
        // 饼图用于显示费用分布
        inflatePieChartIfNeeded();

        // 折线图用于显示里程趋势
        inflateLineChartIfNeeded();

        // ================================ 延迟绘制图表 ================================
        // 延迟500ms确保ViewStub完全inflate并且数据加载完成
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.postDelayed(() -> {
            if (expensesList != null && !expensesList.isEmpty()) {
                // 有费用数据，显示真实的统计图表
                MyLog.d(TAG, "🚀 有费用数据，更新图表");
                updateCharts(Utilities.Companion.getThisMonthExpenses(expensesList));
            } else {
                // 无费用数据，显示示例图表和引导信息
                MyLog.d(TAG, "🚀 无费用数据，显示默认图表");
                showDefaultCharts();
            }
        }, 500);
    }

    // ================================ ViewStub延迟加载方法 ================================

    /**
     * 延迟加载图表模糊蒙版 - 按需显示
     * 当用户未登录或无数据时，在图表区域显示模糊蒙版和引导信息：
     * 1. 检查ViewStub是否已inflate
     * 2. 异步inflate避免阻塞UI
     * 3. 设置inflate监听器获取视图引用
     * 使用场景：
     * - 用户未登录时显示登录引导
     * - 已登录但无费用数据时显示添加引导
     */
    private void inflateChartOverlayIfNeeded() {
        if (!isChartOverlayInflated) {
            long startTime = System.currentTimeMillis();
            MyLog.d(TAG, "🔄 开始延迟加载图表模糊蒙版");

            try {
                ViewStub stubChartOverlay = binding.personalContent.getRoot().findViewById(R.id.stub_chart_overlay);
                if (stubChartOverlay != null) {
                    // 设置inflate监听器，获取inflate后的视图引用
                    stubChartOverlay.setOnInflateListener((stub, inflated) -> {
                        chartOverlayLayout = inflated;
                        MyLog.d(TAG, "✅ 图表模糊蒙版延迟加载完成");
                    });

                    // 执行inflate操作
                    chartOverlayLayout = stubChartOverlay.inflate();
                    isChartOverlayInflated = true;

                    // 性能监控
                    long inflateTime = System.currentTimeMillis() - startTime;
                    MyLog.d(TAG, "📊 图表模糊蒙版延迟加载耗时: " + inflateTime + "ms");
                }
            } catch (Exception e) {
                MyLog.e(TAG, "❌ 延迟加载图表模糊蒙版失败", e);
            }
        }
    }

    /**
     * 延迟加载VIP模糊蒙版 - 按需显示
     *
     * 在里程图表区域显示模糊蒙版和引导信息：
     * 1. 检查ViewStub是否已inflate
     * 2. 异步inflate避免阻塞UI
     * 3. 设置inflate监听器获取视图引用
     * 使用场景：
     * - 用户未登录时显示登录引导
     * - 已登录但无里程数据时显示添加引导
     */
    private void inflateVipOverlayIfNeeded() {
        if (!isVipOverlayInflated) {
            long startTime = System.currentTimeMillis();
            MyLog.d(TAG, "🔄 开始延迟加载VIP模糊蒙版");

            try {
                ViewStub stubVipOverlay = binding.personalContent.getRoot().findViewById(R.id.stub_vip_overlay);
                if (stubVipOverlay != null) {
                    // 设置inflate监听器，获取inflate后的视图引用
                    stubVipOverlay.setOnInflateListener((stub, inflated) -> {
                        vipOverlayLayout = inflated;
                        MyLog.d(TAG, "✅ VIP模糊蒙版延迟加载完成");
                    });

                    // 执行inflate操作
                    vipOverlayLayout = stubVipOverlay.inflate();
                    isVipOverlayInflated = true;

                    // 性能监控
                    long inflateTime = System.currentTimeMillis() - startTime;
                    MyLog.d(TAG, "📊 VIP模糊蒙版延迟加载耗时: " + inflateTime + "ms");
                }
            } catch (Exception e) {
                MyLog.e(TAG, "❌ 延迟加载VIP模糊蒙版失败", e);
            }
        }
    }

    /**
     * 延迟加载饼图视图 - 费用分布图表
     * 饼图用于显示费用分布（加油、维修、保险、税费等）：
     * 1. 检查ViewStub是否已inflate
     * 2. 异步inflate避免阻塞UI线程
     * 3. 配置图表基础属性（透明背景等）
     * 4. 设置inflate监听器获取视图引用
     * 性能优化：
     * - 使用CompletableFuture异步处理
     * - 在主线程中执行UI操作
     * - 详细的性能监控和错误处理
     */
    private void inflatePieChartIfNeeded() {
        if (!isPieChartInflated) {
            long startTime = System.currentTimeMillis();
            MyLog.d(TAG, "🔄 开始延迟加载饼图视图");

            try {
                ViewStub stubPieChart = binding.personalContent.getRoot().findViewById(R.id.stub_pie_chart);
                if (stubPieChart != null) {
                    // 异步inflate，避免阻塞UI线程
                    CompletableFuture.runAsync(() -> {
                        Handler mainHandler = new Handler(Looper.getMainLooper());
                        mainHandler.post(() -> {
                            try {
                                // 设置inflate监听器
                                stubPieChart.setOnInflateListener((stub, inflated) -> {
                                    pieChartView = (AAChartView) inflated;
                                    // 设置透明背景，与页面背景融合
                                    pieChartView.setIsClearBackgroundColor(true);
                                    MyLog.d(TAG, "✅ 饼图视图延迟加载完成");
                                });

                                // 执行inflate操作
                                pieChartView = (AAChartView) stubPieChart.inflate();
                                isPieChartInflated = true;

                                // 性能监控
                                long inflateTime = System.currentTimeMillis() - startTime;
                                MyLog.d(TAG, "📊 饼图视图延迟加载耗时: " + inflateTime + "ms");
                            } catch (Exception e) {
                                MyLog.e(TAG, "❌ 延迟加载饼图视图失败", e);
                            }
                        });
                    });
                }
            } catch (Exception e) {
                MyLog.e(TAG, "❌ 延迟加载饼图视图失败", e);
            }
        }
    }

    /**
     * 延迟加载折线图视图 - 里程趋势图表
     * 折线图用于显示每日里程趋势（本月每天的行驶公里数）：
     * 1. 检查ViewStub是否已inflate
     * 2. 异步inflate避免阻塞UI线程
     * 3. 配置图表基础属性（透明背景等）
     * 4. 设置inflate监听器获取视图引用
     * 性能优化：
     * - 使用CompletableFuture异步处理
     * - 在主线程中执行UI操作
     * - 详细的性能监控和错误处理
     */
    private void inflateLineChartIfNeeded() {
        if (!isLineChartInflated) {
            long startTime = System.currentTimeMillis();
            MyLog.d(TAG, "🔄 开始延迟加载折线图视图");

            try {
                ViewStub stubLineChart = binding.personalContent.getRoot().findViewById(R.id.stub_line_chart);
                if (stubLineChart != null) {
                    // 异步inflate，避免阻塞UI线程
                    CompletableFuture.runAsync(() -> {
                        Handler mainHandler = new Handler(Looper.getMainLooper());
                        mainHandler.post(() -> {
                            try {
                                // 设置inflate监听器
                                stubLineChart.setOnInflateListener((stub, inflated) -> {
                                    lineChartView = (AAChartView) inflated;
                                    // 设置透明背景，与页面背景融合
                                    lineChartView.setIsClearBackgroundColor(true);
                                    MyLog.d(TAG, "✅ 折线图视图延迟加载完成");
                                });

                                // 执行inflate操作
                                lineChartView = (AAChartView) stubLineChart.inflate();
                                isLineChartInflated = true;

                                // 性能监控
                                long inflateTime = System.currentTimeMillis() - startTime;
                                MyLog.d(TAG, "📊 折线图视图延迟加载耗时: " + inflateTime + "ms");
                            } catch (Exception e) {
                                MyLog.e(TAG, "❌ 延迟加载折线图视图失败", e);
                            }
                        });
                    });
                }
            } catch (Exception e) {
                MyLog.e(TAG, "❌ 延迟加载折线图视图失败", e);
            }
        }
    }

    // ================================ 数据加载方法 ================================

    /**
     * 加载所有数据 - 数据加载的总入口
     * 根据用户登录状态加载不同的数据：
     * 1. 已登录：加载用户信息、车辆信息、费用数据
     * 2. 未登录：显示未登录状态和默认图表
     * 3. 公共数据：轮播图数据（无论是否登录都需要）
     * 4. 设置所有点击事件监听器
     * 性能优化：
     * - 错峰加载不同类型的数据
     * - 异步网络请求避免阻塞UI
     * - 详细的性能监控和日志记录
     */
    private void loadAllData() {
        MyLog.d(TAG, "开始加载数据 - " + PersonalSceneConfig.getDevicePerformanceLevel());

        // ================================ 根据登录状态加载数据 ================================
        if (UserManager.isLogin()) {
            // 用户已登录，加载完整的用户数据
            loadUserData();
        } else {
            // 用户未登录，显示未登录状态
            showUnloggedState();
            // 模拟3个请求都完成，用于性能监控
            mCompletedRequests = mTotalRequests;
            checkAllRequestsCompleted();
        }

        // ================================ 加载公共数据 ================================
        // 无论是否登录都需要加载的数据（轮播图等）
        loadCommonData();

        // ================================ 设置交互功能 ================================
        // 设置所有点击事件监听器
        setupAllClickListeners();
    }


    /**
     * 加载已登录用户的数据 - 错峰请求策略
     * 采用错峰请求策略，避免并发请求导致的性能问题：
     * 1. 立即请求用户信息（最重要，优先级最高）
     * 2. 延迟100ms请求车辆信息（避免与用户信息请求冲突）
     * 3. 延迟200ms请求费用数据（最耗时，最后请求）
     * 4. 立即更新基础UI显示
     * 性能优化：
     * - 错峰请求避免网络拥塞
     * - 详细的性能监控和时间记录
     * - 异步处理不阻塞UI线程
     */
    private void loadUserData() {
        long dataLoadStart = System.currentTimeMillis();
        MyLog.d(TAG, "开始加载用户数据");

        // ================================ 步骤1：用户信息请求（最高优先级） ================================
        long userInfoStart = System.currentTimeMillis();
        getUserInfo();
        MyLog.d(TAG, "数据步骤1-用户信息请求启动: " + (System.currentTimeMillis() - userInfoStart) + "ms");

        // ================================ 错峰请求策略 ================================
        Handler mainHandler = new Handler(Looper.getMainLooper());

        // 步骤2：延迟100ms获取车辆信息，避免与用户信息请求冲突
        mainHandler.postDelayed(() -> {
            long carInfoStart = System.currentTimeMillis();
            getDefaultCar();
            MyLog.d(TAG, "数据步骤2-车辆信息请求启动: " + (System.currentTimeMillis() - carInfoStart) + "ms");
        }, 100);

        // 步骤3：延迟200ms获取费用数据，这是最耗时的请求
        mainHandler.postDelayed(() -> {
            long expenseStart = System.currentTimeMillis();
            loadExpenseData();
            MyLog.d(TAG, "数据步骤3-费用数据请求启动: " + (System.currentTimeMillis() - expenseStart) + "ms");
        }, 200);

        // ================================ 步骤4：立即更新基础UI ================================
        // 不等待网络请求完成，先更新基础的UI显示
        long uiUpdateStart = System.currentTimeMillis();
        updateUserUI();
        MyLog.d(TAG, "数据步骤4-UI更新: " + (System.currentTimeMillis() - uiUpdateStart) + "ms");

        // ================================ 性能监控 ================================
        MyLog.d(TAG, "用户数据加载总耗时: " + (System.currentTimeMillis() - dataLoadStart) + "ms");
    }


    // ================================ 样式配置方法 ================================

    /**
     * 初始化公共样式配置 - 图表样式缓存
     * 预先创建图表样式对象并缓存，避免在图表绘制时重复创建：
     * 1. XY轴标签样式（字体、颜色、大小）
     * 2. 提示框样式（背景、边框、文字）
     * 3. 图例样式（字体、颜色、大小）
     * 性能优化：
     * - 样式对象复用，减少内存分配
     * - 支持主题切换时的样式更新
     * - 统一的视觉风格管理
     */
    private void initCommonStyles() {

        // ================================ 提示框样式 ================================
        // 用于图表悬浮提示框的文字样式
        mTooltipTextStyle = AAStyle.style(SkinManager.getInstance().getColorHexString(R.color.title_color));

        // ================================ 图例样式 ================================
        // 用于图表图例的文字样式
        mLegendItemStyle = new AAItemStyle()
                .color(SkinManager.getInstance().getColorHexString(R.color.title_color))  // 使用主题颜色
                .fontSize(13)                                                              // 字体大小
                .fontWeight(AAChartFontWeightType.Thin);                                  // 字体粗细
    }


    // ================================ 交互事件配置 ================================

    /**
     * 设置完整的点击事件监听 - 功能区域点击事件
     * 为页面中的功能区域设置点击事件监听器：
     * 1. 我的爱车 - 跳转到车辆管理页面
     * 2. 消费记录 - 跳转到费用管理页面
     * 3. 今日数据 - 跳转到今日行程页面
     * 4. 全部数据 - 跳转到历史行程页面
     * 注意：基础的导航按钮（返回、头像等）在initMinimalUI中设置
     */
    private void setupAllClickListeners() {
        binding.personalContent.elMycar.setOnClickListener(this);      // 我的爱车
        binding.personalContent.elCost.setOnClickListener(this);       // 消费记录
        binding.personalContent.elToday.setOnClickListener(this);      // 今日数据
        binding.personalContent.elAlldata.setOnClickListener(this);    // 全部数据
    }

    // ================================ 性能监控方法 ================================

    /**
     * 检查所有网络请求是否完成 - 性能监控核心方法
     * 跟踪网络请求的完成情况，当所有请求完成时：
     * 1. 记录总加载时间
     * 2. 生成详细的性能报告
     * 3. 评估页面加载性能等级
     * 线程安全：使用synchronized确保计数器的准确性
     */
    private synchronized void checkAllRequestsCompleted() {
        mCompletedRequests++;
        MyLog.d(TAG, "已完成请求: " + mCompletedRequests + "/" + mTotalRequests);

        if (mCompletedRequests >= mTotalRequests) {
            // ================================ 所有请求完成 ================================
            long totalTime = System.currentTimeMillis() - mPageStartTime;
            MyLog.d(TAG, "所有数据加载完成，页面完全加载完成，总耗时: " + totalTime + "ms");

            // ================================ 性能监控报告 ================================
            if (PersonalSceneConfig.isPerformanceMonitoringEnabled()) {
                logPerformanceMetrics(totalTime);
            }
        }
    }

    /**
     * 记录性能指标 - 详细性能报告
     * 生成完整的性能分析报告，包括：
     * 1. 设备信息和总加载时间
     * 2. 关键性能指标（首次可交互、首次内容显示）
     * 3. 组件状态信息
     * 4. 配置参数信息
     * 5. 性能等级评估
     * 用于性能优化和问题诊断
     */
    private void logPerformanceMetrics(long totalLoadTime) {
        MyLog.i(TAG, "=== PersonalScene 性能报告 ===");

        // ================================ 基础信息 ================================
        MyLog.i(TAG, "设备信息: " + PersonalSceneConfig.getDevicePerformanceLevel());
        MyLog.i(TAG, "总加载时间: " + totalLoadTime + "ms");

        // ================================ 关键性能指标 ================================
        if (mFirstInteractionTime > 0) {
            long firstInteractionDelay = mFirstInteractionTime - mPageStartTime;
            MyLog.i(TAG, "首次可交互时间: " + firstInteractionDelay + "ms");
        }
        if (mFirstContentTime > 0) {
            long firstContentDelay = mFirstContentTime - mPageStartTime;
            MyLog.i(TAG, "首次内容显示时间: " + firstContentDelay + "ms");
        }

        // ================================ 组件状态信息 ================================
        MyLog.i(TAG, "基础UI就绪: " + (mIsBasicUIReady ? "是" : "否"));
        MyLog.i(TAG, "数据准备完成: " + (mDataPrepared ? "是" : "否"));

        // ================================ 配置参数信息 ================================
        MyLog.i(TAG, "图表强制刷新: " + (PersonalSceneConfig.shouldForceChartRefresh() ? "启用" : "禁用"));
        MyLog.i(TAG, "模糊半径: " + PersonalSceneConfig.getBlurRadius());
        MyLog.i(TAG, "静态内容延迟: " + PersonalSceneConfig.getStaticContentDelay() + "ms");
        MyLog.i(TAG, "用户数据延迟: " + PersonalSceneConfig.getUserDataDelay() + "ms");
        MyLog.i(TAG, "图表数据延迟: " + PersonalSceneConfig.getChartDataDelay() + "ms");

        // ================================ 性能等级评估 ================================
        String performanceLevel = getPerformanceLevel(totalLoadTime);
        MyLog.i(TAG, "性能评估: " + performanceLevel);
        MyLog.i(TAG, "=== 性能报告结束 ===");
    }

    /**
     * 根据加载时间评估性能等级
     * 性能等级标准：
     * - 优秀：<300ms，用户几乎无感知延迟
     * - 良好：300-500ms，用户可接受的延迟
     * - 一般：500-800ms，有明显延迟但可用
     * - 需要优化：>800ms，延迟明显影响体验
     *
     * @param loadTime 总加载时间（毫秒）
     * @return 性能等级描述
     */
    private String getPerformanceLevel(long loadTime) {
        if (loadTime < 300) {
            return "优秀 (<300ms)";
        } else if (loadTime < 500) {
            return "良好 (300-500ms)";
        } else if (loadTime < 800) {
            return "一般 (500-800ms)";
        } else {
            return "需要优化 (>800ms)";
        }
    }


    // ================================ 数据更新方法 ================================

    /**
     * 更新轮播图数据 - 支持ViewStub延迟加载
     * 处理轮播图数据的更新，支持两种情况：
     * 1. ViewStub未inflate：先inflate再延迟设置数据
     * 2. ViewStub已inflate：直接更新数据
     *
     * 设计考虑：
     * - 数据可能比ViewStub inflate更早到达
     * - 需要处理异步inflate的时序问题
     * - 提供详细的日志用于问题排查
     *
     * @param bannerData 轮播图数据列表
     */
    public void updateBannerData(List<PersonalBannerModel.RowsDTO> bannerData) {
        MyLog.d(TAG, "🚀 收到轮播图数据，数量: " + (bannerData != null ? bannerData.size() : 0));

        // ================================ 数据有效性检查 ================================
        if (bannerData == null || bannerData.isEmpty()) {
            MyLog.w(TAG, "轮播图数据为空，跳过更新");
            return;
        }

        // ================================ 数据详情日志 ================================
        // 打印第一个数据的详细信息，用于调试
        PersonalBannerModel.RowsDTO firstItem = bannerData.get(0);
        MyLog.d(TAG, "第一个轮播图数据 - 标题: " + firstItem.getTitle() + ", 图片路径: " + firstItem.getImgPath());

        // ================================ ViewStub状态处理 ================================
        if (!isBannerInflated) {
            // ViewStub还未inflate，需要先inflate再设置数据
            MyLog.d(TAG, "🚀 轮播图ViewStub未inflate，开始inflate");
            inflateBannerIfNeeded();

            // 延迟设置数据，等待ViewStub inflate完成
            Handler mainHandler = new Handler(Looper.getMainLooper());
            mainHandler.postDelayed(() -> {
                if (bannerView != null) {
                    try {
                        bannerView.refreshData(bannerData);
                        MyLog.d(TAG, "🚀 轮播图数据更新完成（延迟设置）");
                    } catch (Exception e) {
                        MyLog.e(TAG, "轮播图数据设置失败（延迟）", e);
                    }
                } else {
                    MyLog.w(TAG, "延迟设置时bannerView仍为null");
                }
            }, 300); // 延迟300ms确保ViewStub完全inflate
        } else if (bannerView != null) {
            // ViewStub已经inflate，直接更新数据
            try {
                bannerView.refreshData(bannerData);
                MyLog.d(TAG, "🚀 轮播图数据更新完成（直接设置）");
            } catch (Exception e) {
                MyLog.e(TAG, "轮播图数据设置失败（直接）", e);
            }
        } else {
            MyLog.w(TAG, "bannerView为null，无法设置数据");
        }
    }

    // ================================ 公共访问方法 ================================

    /**
     * 获取视图绑定对象
     *
     * 提供给其他类访问当前Scene的视图绑定对象，
     * 用于外部组件需要访问UI元素的场景
     *
     * @return ScenePersonalBinding 视图绑定对象
     */
    public ScenePersonalBinding getBinding() {
        return binding;
    }


    /**
     * 加载公共数据 - 无论登录状态都需要的数据
     *
     * 加载不依赖用户登录状态的数据：
     * 1. 轮播图数据 - 用于首页推广展示
     * 2. 其他公共配置数据（如有需要可扩展）
     *
     * 这些数据对所有用户都可见，不需要登录验证
     */
    private void loadCommonData() {
        // ================================ 轮播图数据加载 ================================
        // 获取轮播图数据，用于首页推广内容展示
        getBanner();

        // 注意：如需添加其他公共数据加载，可在此处扩展
        // 例如：公告信息、系统配置等
    }

    /**
     * 观察行程数据变化 - 实时数据监听
     *
     * 监听本地数据库中的行程数据变化，实时更新UI显示：
     * 1. 今日总里程 - 显示当天累计行驶距离
     * 2. 今日总时长 - 显示当天累计行驶时间
     *
     * 使用LiveData观察者模式，数据变化时自动更新UI，
     * 无需手动刷新，提供实时的数据展示体验
     */
    private void observeTripData() {
        // ================================ 今日总里程观察 ================================
        // 监听今日总里程变化，自动更新里程数字显示
        TripRepository.getInstance().getTodayTotalDistance().observe(this, distance -> {
            // 使用DistanceConverter格式化距离显示（如：123.5公里）
            Spans formattedDistance = DistanceConverter.formatDistance(requireActivity(), distance);
            binding.personalContent.tvMileageNumber.setText(formattedDistance);
        });

        // ================================ 今日总时长观察 ================================
        // 监听今日总时长变化，自动更新时长数字显示
        TripRepository.getInstance().getTodayTotalDuration().observe(this, duration -> {
            // 使用TimeFormatter格式化时间显示（如：2小时30分钟）
            Spans formattedTime = TimeFormatter.formatTime(requireActivity(), duration);
            binding.personalContent.tvTimeNumber.setText(formattedTime);
        });
    }

    // ================================ UI更新方法 ================================

    /**
     * 更新用户界面显示 - 用户信息UI更新
     *
     * 当用户登录成功或用户信息发生变化时，更新相关UI显示：
     * 1. 用户昵称显示（中间区域和头部标题）
     * 2. 用户荣誉标签列表（VIP、会员等）
     * 3. 用户头像加载（主头像和标题头像）
     *
     * 数据来源：UserManager中缓存的用户信息
     */
    public void updateUserUI() {
        // ================================ 用户昵称更新 ================================
        String nickName = UserManager.getNickName();
        // 更新中间区域的昵称显示
        binding.middleLayout.fragUcNicknameTv.setText(nickName);
        // 更新头部标题区域的昵称显示
        binding.ucHeadTitle.titleUcTitle.setText(nickName);

        // ================================ 荣誉标签列表设置 ================================
        RecyclerView tagsRecyclerView = binding.middleLayout.groupTagsRecycler;
        // 设置水平布局管理器，标签横向排列
        tagsRecyclerView.setLayoutManager(new LinearLayoutManager(
                getApplicationContext(), LinearLayoutManager.HORIZONTAL, false));

        // 创建荣誉标签适配器
        HonorTagAdapter adapter = new HonorTagAdapter();
        tagsRecyclerView.setAdapter(adapter);

        // ================================ 荣誉标签数据处理 ================================
        // 获取用户组信息，可能包含多个标签（用逗号分隔）
        String[] groups = UserManager.getGroup() != null ? UserManager.getGroup().split(",") : new String[0];
        List<HonorTag> tags = new ArrayList<>();

        // 处理每个用户组标签
        for (String group : groups) {
            String trimmedGroup = group.trim();
            // 过滤掉"普通用户"标签，不显示
            if ("普通用户".equals(trimmedGroup)) {
                continue;
            }
            // 添加有效的荣誉标签
            if (!trimmedGroup.isEmpty()) {
                HonorTag.HonorType honorType = HonorTag.HonorType.fromString(trimmedGroup);
                tags.add(new HonorTag(trimmedGroup, honorType));
            }
        }
        // 提交标签数据到适配器
        adapter.submitList(tags);

        // ================================ 用户头像加载 ================================
        // 加载主头像（中间区域）
        loadAvatar(binding.ucAvater);
        // 加载标题头像（头部区域）
        loadAvatar(binding.ucHeadTitle.titleUcAvater);
    }

    // ================================ 默认图表显示方法 ================================

    /**
     * 显示默认图表和提示信息 - 无数据状态处理
     *
     * 在以下情况下显示默认图表：
     * 1. 用户未登录时
     * 2. 用户已登录但没有消费记录时
     *
     * 功能包括：
     * 1. 设置默认的统计数字显示（全部为0）
     * 2. 创建示例折线图（里程趋势）
     * 3. 创建示例饼图（费用分布）
     * 4. 延迟加载图表避免阻塞UI
     * 5. 显示引导蒙版和提示信息
     */
    private void showDefaultCharts() {
        // ================================ 默认统计数字设置 ================================
        // 设置本月消费统计为0，给用户清晰的无数据状态提示
        binding.personalContent.tvSpentThisMonth.setText("0");        // 本月花费
        binding.personalContent.tvEmittedThisMonth.setText("0");      // 本月排放
        binding.personalContent.tvAvgConsumptionThisMonth.setText("0"); // 本月平均油耗

        // ================================ 默认折线图配置 ================================
        // 创建渐变色配置，营造现代化的视觉效果
        Object[][] stopsArr = {
                {0.00, "#FFFFFF"},  // 顶部白色
                {0.50, "#CFDCFF"},  // 中间浅蓝色
                {1.00, "#2460FE"},  // 底部深蓝色
        };
        Map<String, Object> linearGradientColor = AAGradientColor.linearGradient(
                AALinearGradientDirection.ToTop, stopsArr
        );

        // 创建默认折线图模型，使用示例数据展示效果
        AAChartModel defaultLineChartModel = new AAChartModel()
                .chartType(AAChartType.Areaspline)                    // 区域样条图类型
                .categories(new String[]{"第1天", "第10天", "第20天", "第30天"}) // X轴分类
                .markerRadius(0)                                      // 数据点半径为0
                .yAxisTitle("公里")                                   // Y轴标题
                .yAxisLineWidth(0)                                    // Y轴线宽度为0
                .yAxisGridLineWidth(0)                                // Y轴网格线宽度为0
                .legendEnabled(false)                                 // 禁用图例
                .backgroundColor("#00000000")                         // 透明背景
                .axesTextColor(SkinManager.getInstance().getColorHexString(R.color.title_color)) // 坐标轴文字颜色
                .series(new AASeriesElement[]{
                        new AASeriesElement()
                                .name("每日公里数")                   // 系列名称
                                .lineWidth(0)                        // 线条宽度为0（只显示区域）
                                .color(linearGradientColor)          // 使用渐变色
                                .data(new Object[]{25.5, 42.0, 18.0, 35.2}) // 示例数据
                });

        // ================================ 默认饼图配置 ================================
        // 创建示例饼图数据，展示费用分布效果
        Object[][] defaultPieData = new Object[][]{
                {getString(R.string.refuel_title), 45.0},      // 加油费用 45%
                {getString(R.string.maintenance_title), 25.0}, // 维修费用 25%
                {getString(R.string.insurance_title), 20.0},   // 保险费用 20%
                {getString(R.string.tax_title), 10.0}          // 税费 10%
        };

        // 创建默认饼图模型
        AAChartModel defaultPieChartModel = new AAChartModel()
                .chartType(AAChartType.Pie)                           // 饼图类型
                .axesTextColor(SkinManager.getInstance().getColorHexString(R.color.title_color)) // 坐标轴文字颜色
                .colorsTheme(new String[]{"#A5D63F", "#2A82E4", "#06caf4", "#7dffc0"}) // 颜色主题
                .dataLabelsEnabled(true)                              // 启用数据标签
                .tooltipEnabled(false)                                // 禁用提示框
                .backgroundColor("#00000000")                         // 透明背景
                .series(new AAPie[]{
                        new AAPie()
                                .name("费用分布")                     // 系列名称
                                .innerSize("60%")                     // 内圈大小（环形图）
                                .dataLabels(new AADataLabels()
                                        .enabled(true)                // 启用数据标签
                                        .useHTML(true)                // 使用HTML格式
                                        .distance(5)                  // 标签距离
                                        .color(SkinManager.getInstance().getColorHexString(R.color.title_color)) // 标签颜色
                                        .format("<b>{point.name}</b>: <br> {point.percentage:.1f} %")) // 标签格式
                                .data(defaultPieData)                 // 设置数据
                });

        // ================================ 图表配置处理 ================================
        // 转换饼图模型为AAOptions并应用共享样式
        aaPieChartOptions = defaultPieChartModel.aa_toAAOptions();
        applyCommonStyleToPieChart(aaPieChartOptions);

        // ================================ 延迟图表加载策略 ================================
        // 延迟2秒后再加载图表，避免与页面初始化冲突
        Handler chartHandler = new Handler(Looper.getMainLooper());
        chartHandler.postDelayed(() -> {
            // 使用异步方式加载图表，避免阻塞UI线程
            CompletableFuture.runAsync(() -> {
                // 确保Activity仍然存在
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        // ================================ ViewStub延迟加载 ================================
                        // 延迟加载图表视图，只在需要时才创建
                        inflateLineChartIfNeeded();  // 加载折线图ViewStub
                        inflatePieChartIfNeeded();   // 加载饼图ViewStub

                        // ================================ 图表绘制 ================================
                        // 等待ViewStub inflate完成后再绘制图表
                        chartHandler.postDelayed(() -> {
                            // 绘制折线图
                            if (lineChartView != null) {
                                lineChartView.aa_drawChartWithChartModel(defaultLineChartModel);
                            }
                            // 绘制饼图
                            if (pieChartView != null) {
                                pieChartView.aa_drawChartWithChartOptions(aaPieChartOptions);
                            }

                            // ================================ 兼容性处理 ================================
                            // 只在老旧设备上强制刷新图表，解决渲染问题
                            if (PersonalSceneConfig.shouldForceChartRefresh()) {
                                if (lineChartView != null) {
                                    forceChartRefresh(lineChartView);
                                }
                                if (pieChartView != null) {
                                    forceChartRefresh(pieChartView);
                                }
                            }
                        }, 100); // 等待100ms让ViewStub完成inflate
                    });
                }
            });
        }, 2000); // 延迟2秒加载图表，确保页面基础功能已就绪

        // ================================ 状态标记 ================================
        // 标记数据已准备好，用于皮肤切换时的图表重绘
        mDataPrepared = true;

        // ================================ 显示引导信息 ================================
        // 显示蒙版和提示信息，引导用户登录或添加数据
        showOverlayWithMessage();
    }

 

    /**
     * 应用共享样式到饼图选项 - 统一饼图视觉风格
     *
     * 为饼图应用统一的样式配置：
     * 1. 提示框样式（背景、边框、文字）
     * 2. 图例样式（字体、颜色、大小）
     * 3. 交互事件（自动隐藏提示框）
     *
     * 与折线图样式保持一致，确保整体视觉风格统一
     *
     * @param options 饼图选项对象
     */
    public void applyCommonStyleToPieChart(AAOptions options) {
        // ================================ 提示框样式配置 ================================
        options.tooltip
                .style(mTooltipTextStyle)                                                        // 提示框文字样式
                .backgroundColor(SkinManager.getInstance().getColorHexString(R.color.title_color)) // 提示框背景色
                .borderColor(SkinManager.getInstance().getColorHexString(R.color.title_color));   // 提示框边框色

        // ================================ 图例样式配置 ================================
        options.legend
                .itemStyle(mLegendItemStyle);                                                    // 图例项样式

        // ================================ 交互事件配置 ================================
        // 设置图表加载事件，实现提示框自动隐藏功能
        options.chart
                .events(new AAChartEvents()
                        .load("function() {\n" +
                                "   const chart = this;\n" +
                                "   Highcharts.addEvent(\n" +
                                "       chart.tooltip,\n" +
                                "       'refresh',\n" +
                                "       function () {\n" +
                                "           chart.tooltip.hide(1500);\n" +  // 1.5秒后自动隐藏提示框
                                "   });\n" +
                                "}"));
    }

    // ================================ 蒙版显示方法 ================================

    /**
     * 显示蒙版和提示信息 - 支持ViewStub延迟加载
     *
     * 根据用户登录状态显示相应的蒙版和引导信息：
     * 1. 未登录状态：显示登录引导蒙版
     * 2. 已登录无数据：显示添加数据引导蒙版
     *
     * 蒙版功能：
     * - 模糊背景图表区域
     * - 显示引导文字和按钮
     * - 提供相应的操作入口
     *
     * 性能优化：
     * - 只在需要时才加载ViewStub
     * - 延迟设置蒙版内容避免时序问题
     */
    private void showOverlayWithMessage() {
        // ================================ 设备性能适配 ================================
        // 根据设备性能调整模糊半径，低端设备使用较小的半径
        final float radius = PersonalSceneConfig.getBlurRadius();

        // ================================ 按需加载蒙版 ================================
        // 只在用户未登录时才显示蒙版，已登录且有数据时不显示
        if (!UserManager.isLogin()) {
            // 延迟加载图表模糊蒙版ViewStub
            inflateChartOverlayIfNeeded();

            // 延迟加载VIP模糊蒙版ViewStub
            inflateVipOverlayIfNeeded();

            // ================================ 延迟设置蒙版内容 ================================
            // 延迟100ms设置蒙版内容，确保ViewStub完全inflate
            Handler mainHandler = new Handler(Looper.getMainLooper());
            mainHandler.postDelayed(() -> {
                // 设置消费图表区域蒙版（费用统计区域）
                setupConsumptionOverlay(radius);

                // 设置里程图表区域蒙版（里程统计区域）
                setupOilConsumptionOverlay(radius);
            }, 100); // 等待ViewStub inflate完成
        }
    }

    /**
     * 设置消费记录蒙版 - 费用统计区域引导
     *
     * 在费用统计图表区域设置模糊蒙版和引导信息：
     * 1. 未登录状态：显示登录引导
     * 2. 已登录无数据：显示添加消费记录引导
     *
     * 蒙版组件：
     * - 锁定图标（未登录时显示）
     * - 主要提示文字
     * - 副提示文字
     * - 操作按钮
     *
     * @param blurRadius 模糊半径，根据设备性能调整
     */
    private void setupConsumptionOverlay(float blurRadius) {
        // ================================ ViewStub状态检查 ================================
        // 确保ViewStub已经inflate，否则跳过设置
        if (!isChartOverlayInflated || chartOverlayLayout == null) {
            MyLog.w(TAG, "图表模糊蒙版ViewStub未inflate，跳过设置");
            return;
        }

        // ================================ 获取蒙版UI组件 ================================
        TextView messageText = chartOverlayLayout.findViewById(R.id.chartOverlayMessage);      // 主要提示文字
        TextView subMessageText = chartOverlayLayout.findViewById(R.id.chartOverlaySubMessage); // 副提示文字
        TextView buttonText = chartOverlayLayout.findViewById(R.id.chartOverlayButton);         // 操作按钮
        ImageView lockIcon = chartOverlayLayout.findViewById(R.id.chartLockIcon);               // 锁定图标

        // ================================ 根据登录状态设置内容 ================================
        if (!UserManager.isLogin()) {
            // 未登录状态：显示登录引导
            lockIcon.setVisibility(View.VISIBLE);                    // 显示锁定图标
            messageText.setText("登录后查看详细消费数据");              // 主要提示
            subMessageText.setText("支持在微信小程序中添加");           // 副提示
            buttonText.setText("立即登录");                          // 按钮文字

            // 设置登录按钮点击事件
            buttonText.setOnClickListener(v -> showLoginDialog());
        } else if (expensesList == null || expensesList.isEmpty()) {
            // 已登录但无数据状态：显示添加数据引导
            lockIcon.setVisibility(View.GONE);                       // 隐藏锁定图标
            messageText.setText("暂无消费记录");                      // 主要提示
            subMessageText.setText("记录车辆的各项支出，掌握用车成本");   // 副提示
            buttonText.setText("添加消费记录");                       // 按钮文字

            // 设置添加记录按钮点击事件
            buttonText.setOnClickListener(v -> {
                // 跳转到费用管理页面
                requireNavigationScene(this).push(new ExpenseTabNavigationScene());
            });
        }

        // ================================ 配置模糊蒙版效果 ================================
        // 使用ViewStub inflate后的视图配置模糊效果
        if (chartOverlayLayout instanceof BVConstraintLayout blurLayout) {
            blurLayout.setupWith(binding.personalContent.elCost)      // 设置模糊目标为费用区域
                    .setBlurRadius(blurRadius)                        // 设置模糊半径
                    .setBlurAutoUpdate(true);                         // 启用自动更新
            blurLayout.setVisibility(View.VISIBLE);                   // 显示蒙版
            MyLog.d(TAG, "图表模糊蒙版配置完成");
        } else {
            MyLog.w(TAG, "图表模糊蒙版类型不正确或为null");
        }
    }

    /**
     * 🚀 设置公里数记录蒙版 - 支持ViewStub延迟加载
     */
    private void setupOilConsumptionOverlay(float blurRadius) {
        // 🚀 检查ViewStub是否已经inflate
        if (!isVipOverlayInflated || vipOverlayLayout == null) {
            MyLog.w(TAG, "VIP模糊蒙版ViewStub未inflate，跳过设置");
            return;
        }

        // 获取蒙版UI组件
        TextView messageText = vipOverlayLayout.findViewById(R.id.oilOverlayMessage);
        TextView subMessageText = vipOverlayLayout.findViewById(R.id.oilOverlaySubMessage);
        TextView buttonText = vipOverlayLayout.findViewById(R.id.oilOverlayButton);
        ImageView lockIcon = vipOverlayLayout.findViewById(R.id.oilLockIcon);

        if (!UserManager.isLogin()) {
            // 未登录状态
            lockIcon.setVisibility(View.VISIBLE);
            messageText.setText("登录后查看油耗记录");
            subMessageText.setText("支持在微信小程序中添加");
            buttonText.setText("立即登录");

            // 设置点击事件
            buttonText.setOnClickListener(v -> showLoginDialog());
        } else {
            // 已登录但可能无数据状态
            lockIcon.setVisibility(View.GONE);
            messageText.setText("暂无油耗记录");
            subMessageText.setText("记录每次加油数据，了解您的用车成本");
            buttonText.setText("添加油耗记录");

            // 设置点击事件
            buttonText.setOnClickListener(v -> {
                // 跳转到添加油耗记录页面
                requireNavigationScene(this).push(new ExpenseTabNavigationScene());
            });
        }

        // 🚀 配置蒙版，使用ViewStub inflate后的视图
        if (vipOverlayLayout instanceof BVConstraintLayout) {
            BVConstraintLayout blurLayout = (BVConstraintLayout) vipOverlayLayout;
            blurLayout.setupWith(binding.personalContent.elVip)
                    .setBlurRadius(blurRadius)
                    .setBlurAutoUpdate(true);
            blurLayout.setVisibility(View.VISIBLE);
            MyLog.d(TAG, "VIP模糊蒙版配置完成");
        } else {
            MyLog.w(TAG, "VIP模糊蒙版类型不正确或为null");
        }
    }

    /**
     * 隐藏蒙版
     */
    private void hideOverlay() {
        // 如果没有费用数据，直接返回
        if (expensesList == null || expensesList.isEmpty()) {
            return;
        }

        // 🚀 隐藏消费图表区域蒙版 - 使用ViewStub inflate后的视图
        if (isChartOverlayInflated && chartOverlayLayout != null) {
            chartOverlayLayout.setVisibility(View.GONE);
            MyLog.d(TAG, "隐藏图表模糊蒙版");
        }

        // 🚀 隐藏公里数图表区域蒙版 - 使用ViewStub inflate后的视图
        if (isVipOverlayInflated && vipOverlayLayout != null) {
            // 检查是否有公里数数据 - 使用安全的检查方式
            List<Pair<String, Double>> mileageData = Utilities.Companion.getCurrentMonthDailyMileage();
            boolean hasMileageData = !mileageData.isEmpty();

            if (hasMileageData) {
                vipOverlayLayout.setVisibility(View.GONE);
                MyLog.d(TAG, "隐藏VIP模糊蒙版");
            }
        }
    }

    /**
     * 显示未登录状态的UI
     */
    public void showUnloggedState() {
        binding.middleLayout.fragUcNicknameTv.setText("点击登录");
        binding.middleLayout.groupTagsRecycler.setAdapter(null);

        // 显示默认图表
        showDefaultCharts();

        // 显示蒙版
        showOverlayWithMessage();
    }

    /**
     * 加载费用数据 - 带详细性能监控
     */
    @SuppressLint("CheckResult")
    private void loadExpenseData() {
        long requestStart = System.currentTimeMillis();
        String url = Const.VEHICLE_EXPENSE_LIST;
        MyLog.d(TAG, "网络请求-费用数据开始，地址：" + url);

        RxHttp.get(url)
                .setCacheMode(CacheMode.REQUEST_NETWORK_FAILED_READ_CACHE)
                .toObservable(ExpenseListModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(expenseListModel -> {
                    long responseTime = System.currentTimeMillis() - requestStart;
                    MyLog.d(TAG, "网络请求-费用数据响应: " + responseTime + "ms");

                    if (expenseListModel.getCode() == StatusCodeModel.SUCCESS) {
                        long dataProcessStart = System.currentTimeMillis();

                        // 请求成功
                        MyLog.v(TAG, "费用数据请求成功" + new Gson().toJson(expenseListModel));
                        List<Expense> expenses = expenseListModel.getRows();

                        // 数据处理：排序和转换
                        this.expensesList = expenses.stream()
                                .sorted(Comparator.comparing(Expense::getDate))
                                .collect(Collectors.toUnmodifiableList());

                        MyLog.d(TAG, "费用数据处理: " + (System.currentTimeMillis() - dataProcessStart) + "ms");

                        // 更新费用相关UI
                        long uiUpdateStart = System.currentTimeMillis();
                        updateExpenseUI();
                        MyLog.d(TAG, "费用UI更新启动: " + (System.currentTimeMillis() - uiUpdateStart) + "ms");

                        MyLog.d(TAG, "费用数据总处理时间: " + (System.currentTimeMillis() - requestStart) + "ms");

                        // 标记一个请求完成
                        checkAllRequestsCompleted();
                    } else {
                        MyLog.d(TAG, "费用数据请求失败，状态码: " + expenseListModel.getCode());
                        checkAllRequestsCompleted(); // 即使失败也要计数
                    }
                }, throwable -> {
                    long errorTime = System.currentTimeMillis() - requestStart;
                    MyLog.e(TAG, "费用数据请求失败，耗时: " + errorTime + "ms, 错误: " + throwable.getMessage());
                    // 标记请求完成（即使失败）
                    checkAllRequestsCompleted();
                });
    }

    @SuppressLint("CheckResult")
    private void getDefaultCar() {
        long requestStart = System.currentTimeMillis();
        String url = Const.VEHICLE_CAR_DEFAULT;
        MyLog.d(TAG, "网络请求-车辆信息开始，地址：" + url);

        RxHttp.get(url)
                .setCacheMode(CacheMode.REQUEST_NETWORK_FAILED_READ_CACHE)
                .toObservable(DefaultVehicleDataModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(defaultVehicleDataModel -> {
                    long responseTime = System.currentTimeMillis() - requestStart;
                    MyLog.d(TAG, "网络请求-车辆信息响应: " + responseTime + "ms");

                    if (defaultVehicleDataModel.getCode() == StatusCodeModel.SUCCESS) {
                        long uiUpdateStart = System.currentTimeMillis();

                        // 更新车辆基本信息
                        binding.personalContent.tvBrand.setText(defaultVehicleDataModel.getData().getModel());
                        binding.personalContent.tvLicensePlateNumber.setText(defaultVehicleDataModel.getData().getLicensePlate());
                        binding.personalContent.tvAllMileageNumber.setText(DistanceConverter.formatDistance(requireActivity(), defaultVehicleDataModel.getData().getTotalMileage()));
                        binding.personalContent.tvAllTimeNumber.setText(TimeFormatter.formatTime(requireActivity(), defaultVehicleDataModel.getData().getTotalDrivingTime()));

                        MyLog.d(TAG, "车辆信息文本更新: " + (System.currentTimeMillis() - uiUpdateStart) + "ms");

                        // 加载车辆Logo（可能耗时）
                        long imageLoadStart = System.currentTimeMillis();
                        Glide.with(Objects.requireNonNull(getApplicationContext()))
                                .load(defaultVehicleDataModel.getData().getBrandLogo())
                                .into(binding.personalContent.ivLogoCar);
                        MyLog.d(TAG, "车辆Logo加载启动: " + (System.currentTimeMillis() - imageLoadStart) + "ms");

                        // 保存数据到DataManager
                        long dataSaveStart = System.currentTimeMillis();
                        DataManager.setCarId(Long.valueOf(defaultVehicleDataModel.getData().getCarId()));
                        DataManager.setBrand(defaultVehicleDataModel.getData().getBrand());
                        DataManager.setModel(defaultVehicleDataModel.getData().getModel());
                        DataManager.setLicensePlate(defaultVehicleDataModel.getData().getLicensePlate());
                        MyLog.d(TAG, "车辆数据保存: " + (System.currentTimeMillis() - dataSaveStart) + "ms");

                        MyLog.d(TAG, "车辆信息总处理时间: " + (System.currentTimeMillis() - requestStart) + "ms");

                        // 标记一个请求完成
                        checkAllRequestsCompleted();
                    } else {
                        MyLog.d(TAG, "车辆信息请求失败，状态码: " + defaultVehicleDataModel.getCode());
                        checkAllRequestsCompleted(); // 即使失败也要计数
                    }

                    // 请求成功日志
                    MyLog.v(TAG, "车辆信息请求完成: " + new Gson().toJson(defaultVehicleDataModel));
                }, throwable -> {
                    long errorTime = System.currentTimeMillis() - requestStart;
                    MyLog.e(TAG, "车辆信息请求失败，耗时: " + errorTime + "ms, 错误: " + throwable.getMessage());
                    // 标记请求完成（即使失败）
                    checkAllRequestsCompleted();
                });
    }

    @SuppressLint("CheckResult")
    private void getBanner() {
        String url = Const.CAR_MODEL_LIST;
        MyLog.v(TAG, "🚀 请求轮播图数据：" + url);
        RxHttp.get(url)
                .setCacheMode(CacheMode.REQUEST_NETWORK_FAILED_READ_CACHE)
                .toObservable(PersonalBannerModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(personalBannerModel -> {
                    if (personalBannerModel.getCode() == StatusCodeModel.SUCCESS) {
                        // 请求成功
                        MyLog.v(TAG, "🚀 轮播图数据请求成功，数量: " + personalBannerModel.getRows().size());
                        MyLog.v(TAG, "请求成功" + new Gson().toJson(personalBannerModel));

                        // 🚀 使用新的updateBannerData方法，支持ViewStub
                        updateBannerData(personalBannerModel.getRows());
                    } else {
                        MyLog.w(TAG, "轮播图数据请求失败，状态码: " + personalBannerModel.getCode());
                    }
                    // 请求成功
                    MyLog.v(TAG, "请求成功" + personalBannerModel);
                }, throwable -> {
                    // 请求失败
                    MyLog.e(TAG, "🚀 轮播图数据请求失败: " + throwable.getMessage());
                });

    }


    /**
     * 配置彩色渐变面积图表 - 显示每天公里数
     * 使用现代化的绿色到青色渐变，营造清新活力的视觉效果
     *
     * @param expenses 费用列表（保留参数以保持兼容性，但实际不使用）
     * @return 配置好的AAChartModel对象
     */
    private AAChartModel createGradientAreaChartModel(List<Expense> expenses) {
        // ⚡ 新能源汽车科技纯色方案 - 摆脱渐变，使用品牌纯色

        // 当前方案: 特斯拉深空灰 - 极简科技感
        String solidColor = "#475569";  // 特斯拉深空灰

        // 🚗 新能源汽车品牌纯色方案（可快速切换）：

        // 方案A: 蔚来科技蓝 - 智能电动的代表色
        // String solidColor = "#0EA5E9";  // 蔚来科技蓝

        // 方案B: 小鹏科技银 - 未来金属质感
        // String solidColor = "#64748B";  // 科技银灰

        // 方案C: 理想科技绿 - 新能源环保理念
        // String solidColor = "#059669";  // 理想科技绿

        // 方案D: 极氪科技青 - 电动科技的未来感
        // String solidColor = "#0891B2";  // 极氪科技青

        // 方案E: 比亚迪科技红 - 中国智造的力量感
        // String solidColor = "#DC2626";  // 比亚迪科技红

        // 方案F: 岚图科技紫 - 高端智能的神秘感
        // String solidColor = "#7C3AED";  // 岚图科技紫

        // 方案G: 小米橙 - 年轻活力的科技感
        // String solidColor = "#FF6900";  // 小米橙

        // 方案H: 华为蓝 - 稳重可靠的科技感
        // String solidColor = "#1890FF";  // 华为蓝
        // 获取当前月的每天公里数数据
        List<Pair<String, Double>> dailyMileageData = Utilities.Companion.getCurrentMonthDailyMileage();
        // 将公里数据中的日期转换为字符串数组
        String[] categories = dailyMileageData.stream().map(Pair::getFirst).toArray(String[]::new);
        // 将公里数据中的公里数值转换为Double数组
        Double[] data = dailyMileageData.stream().map(Pair::getSecond).toArray(Double[]::new);

        // 创建并配置AAChartModel对象 - 保持原有样式
        return new AAChartModel()
                .axesTextColor(SkinManager.getInstance().getColorHexString(R.color.title_color)) // 设置坐标轴文字颜色
                .chartType(AAChartType.Areaspline) // 设置图表类型为区域样条图
                .categories(categories) // 设置分类
                .markerRadius(0) // 设置标记点半径为0
                .xAxisVisible(true) // 设置x轴可见
                .subtitle("本月公里数") // 设置副标题
                .yAxisTitle("公里") // 设置y轴标题
                .yAxisLineWidth(0) // 设置y轴线条宽度为0
                .yAxisGridLineWidth(0) // 设置y轴网格线宽度为0
                .legendEnabled(false) // 禁用图例
                .backgroundColor("#00000000") // 设置背景透明
                .series(new AASeriesElement[]{
                                new AASeriesElement()
                                        .name("公里数") // 设置系列名称
                                        .lineWidth(0) // 设置线条宽度为0
                                        .color(solidColor) // 设置新能源科技纯色
                                        .data(data) // 设置数据
                        }
                );
    }

    /**
     * 配置饼状图
     *
     * @param pieData 饼状图数据，二维数组，其中每个子数组包含两个元素：分类名称和对应的数值
     * @return 配置好的AAChartModel对象
     */
    private AAChartModel createPieChartModel(Object[][] pieData) {
        return new AAChartModel()
                .chartType(AAChartType.Pie) // 设置图表类型为饼状图
                .colorsTheme(new String[]{"#A5D63F", "#2A82E4", "#06caf4", "#7dffc0"}) // 设置图表主题颜色
                .dataLabelsEnabled(true) // 是否显示数据标签
                .tooltipEnabled(false) // 是否启用提示框
                .backgroundColor("#00000000") // 设置背景透明
                .series(new AAPie[]{
                        new AAPie()
                                .name("费用分类") // 设置系列名称
                                .innerSize("60%") // 设置内圈大小
                                .dataLabels(new AADataLabels()
                                        .enabled(true) // 是否启用数据标签
                                        .useHTML(true) // 是否使用HTML格式
                                        .distance(5) // 数据标签与数据点的距离
                                        .color(SkinManager.getInstance().getColorHexString(R.color.title_color)) // 数据标签颜色
                                        .format("<b>{point.name}</b>: <br> {point.percentage:.1f} %")) // 数据标签格式
                                .data(pieData) // 设置系列数据
                });
    }

    /**
     * 更新费用相关UI - 带详细性能监控
     */
    private void updateExpenseUI() {
        long uiUpdateStart = System.currentTimeMillis();
        MyLog.d(TAG, "开始更新费用UI");

        if (expensesList == null || expensesList.isEmpty()) {
            // 如果没有费用数据，显示默认图表和蒙版
            long defaultChartsStart = System.currentTimeMillis();
            showDefaultCharts();
            MyLog.d(TAG, "显示默认图表: " + (System.currentTimeMillis() - defaultChartsStart) + "ms");
            return;
        }

        // 隐藏图表区域蒙版
        long hideOverlayStart = System.currentTimeMillis();
        hideOverlay();
        MyLog.d(TAG, "隐藏蒙版: " + (System.currentTimeMillis() - hideOverlayStart) + "ms");

        // 获取本月的费用列表
        long dataProcessStart = System.currentTimeMillis();
        List<Expense> thisMonthExpenses = Utilities.Companion.getThisMonthExpenses(expensesList);
        MyLog.d(TAG, "本月费用数据处理: " + (System.currentTimeMillis() - dataProcessStart) + "ms");

        // 更新统计数字
        long statisticsStart = System.currentTimeMillis();
        updateStatistics(thisMonthExpenses);
        MyLog.d(TAG, "统计数字更新: " + (System.currentTimeMillis() - statisticsStart) + "ms");

        // 更新图表视图
        long chartsStart = System.currentTimeMillis();
        updateCharts(thisMonthExpenses);
        MyLog.d(TAG, "图表更新启动: " + (System.currentTimeMillis() - chartsStart) + "ms");

        MyLog.d(TAG, "费用UI更新总耗时: " + (System.currentTimeMillis() - uiUpdateStart) + "ms");
    }

    /**
     * 更新统计数字
     */
    private void updateStatistics(List<Expense> thisMonthExpenses) {
        // 计算本月总花费
        double spent = Utilities.Companion.getTotalSpent(thisMonthExpenses);

        // 计算本月排放（根据电源类型和排放量）
        double emitted = Utilities.Companion.getEmitted(thisMonthExpenses, DataManager.getPowerType(), DataManager.getEmission());

        // 计算本月平均油耗
        double consumption = Utilities.Companion.getAvgConsumption(thisMonthExpenses);

        // 更新本月花费的显示
        binding.personalContent.tvSpentThisMonth.setText(String.valueOf(spent));
        // 更新本月排放的显示
        binding.personalContent.tvEmittedThisMonth.setText(String.valueOf(emitted));
        // 更新本月平均油耗的显示
        binding.personalContent.tvAvgConsumptionThisMonth.setText(String.valueOf(consumption));
    }

    /**
     * 更新图表视图 - 带详细性能监控
     */
    private void updateCharts(List<Expense> thisMonthExpenses) {
        long chartsUpdateStart = System.currentTimeMillis();
        MyLog.d(TAG, "开始图表数据处理");

        // 使用CompletableFuture在后台线程处理数据
        CompletableFuture.supplyAsync(() -> {
            long backgroundStart = System.currentTimeMillis();
            try {
                // 获取饼图数据
                long pieDataStart = System.currentTimeMillis();
                Object[][] pieData = getPieData();
                MyLog.d(TAG, "饼图数据获取: " + (System.currentTimeMillis() - pieDataStart) + "ms");

                // 创建图表模型
                long modelCreateStart = System.currentTimeMillis();
                AAChartModel pieChartModel = createPieChartModel(pieData);
                AAChartModel lineChartModel = createGradientAreaChartModel(expensesList);
                MyLog.d(TAG, "图表模型创建: " + (System.currentTimeMillis() - modelCreateStart) + "ms");

                // 转换为AAOptions
                long optionsStart = System.currentTimeMillis();
                AAOptions pieOptions = pieChartModel.aa_toAAOptions();
                MyLog.d(TAG, "AAOptions转换: " + (System.currentTimeMillis() - optionsStart) + "ms");

                // 应用样式
                long styleStart = System.currentTimeMillis();
                applyCommonStyleToPieChart(pieOptions);
                MyLog.d(TAG, "样式应用: " + (System.currentTimeMillis() - styleStart) + "ms");

                // 返回处理结果
                Map<String, Object> result = new HashMap<>();
                result.put("pieOptions", pieOptions);
                result.put("lineChartModel", lineChartModel);

                MyLog.d(TAG, "后台图表处理总耗时: " + (System.currentTimeMillis() - backgroundStart) + "ms");
                return result;
            } catch (Exception e) {
                MyLog.e(TAG, "处理图表数据时发生错误", e);
                return null;
            }
        }).thenAccept(result -> {
            // 回到主线程更新UI
            if (getActivity() == null || result == null) return;

            long uiRenderStart = System.currentTimeMillis();
            getActivity().runOnUiThread(() -> {
                MyLog.d(TAG, "开始UI线程图表渲染");

                // 保存配置
                long saveStart = System.currentTimeMillis();
                aaPieChartOptions = (AAOptions) result.get("pieOptions");
                MyLog.d(TAG, "配置保存: " + (System.currentTimeMillis() - saveStart) + "ms");

                // 🚀 使用ViewStub延迟加载并绘制图表
                long drawStart = System.currentTimeMillis();

                // 延迟加载图表视图
                inflatePieChartIfNeeded();
                inflateLineChartIfNeeded();

                // 延迟一下再绘制图表，确保ViewStub完全inflate
                Handler chartHandler = new Handler(Looper.getMainLooper());
                chartHandler.postDelayed(() -> {
                    // 绘制图表
                    if (pieChartView != null) {
                        pieChartView.aa_drawChartWithChartOptions(aaPieChartOptions);
                        MyLog.d(TAG, "🚀 饼图绘制完成");
                    } else {
                        MyLog.w(TAG, "饼图ViewStub未正确inflate");
                    }
                    if (lineChartView != null) {
                        lineChartView.aa_drawChartWithChartModel((AAChartModel) result.get("lineChartModel"));
                        MyLog.d(TAG, "🚀 折线图绘制完成");
                    } else {
                        MyLog.w(TAG, "折线图ViewStub未正确inflate");
                    }
                }, 200); // 等待200ms让ViewStub完成inflate

                MyLog.d(TAG, "图表绘制启动: " + (System.currentTimeMillis() - drawStart) + "ms");

                // 只在必要时强制刷新
                if (PersonalSceneConfig.shouldForceChartRefresh()) {
                    long refreshStart = System.currentTimeMillis();
                    if (lineChartView != null) {
                        forceChartRefresh(lineChartView);
                    }
                    if (pieChartView != null) {
                        forceChartRefresh(pieChartView);
                    }
                    MyLog.d(TAG, "图表强制刷新: " + (System.currentTimeMillis() - refreshStart) + "ms");
                }

                // 标记数据已准备好
                mDataPrepared = true;

                MyLog.d(TAG, "UI线程图表渲染: " + (System.currentTimeMillis() - uiRenderStart) + "ms");
                MyLog.d(TAG, "图表更新总耗时: " + (System.currentTimeMillis() - chartsUpdateStart) + "ms");
            });
        });
    }

    private Object[][] getPieData() {
        List<Expense> thisMonthExpenses = Utilities.Companion.getThisMonthExpenses(expensesList);
        double spentRefuel = 0.0;
        double spentInsurance = 0.0;
        double spentTax = 0.0;
        double spentMaintenance = 0.0;

        for (Expense e : thisMonthExpenses) {
            switch (e.getType()) {
                case Const.FUEL_TYPE_GAS:
                    spentRefuel += e.getSpent();
                    break;
                case Const.FUEL_TYPE_REPAIR:
                    spentMaintenance += e.getSpent();
                    break;
                case Const.FUEL_TYPE_TAXES:
                    spentTax += e.getSpent();
                    break;
                case Const.FUEL_TYPE_INSURANCE:
                    spentInsurance += e.getSpent();
                    break;
                default:
                    spentInsurance += e.getSpent();
                    break;
            }
        }

        double total = spentRefuel + spentMaintenance + spentInsurance + spentTax;
        List<Object[]> pieDataList = new ArrayList<>();
        if (spentRefuel > 0.0) {
            pieDataList.add(new Object[]{getString(R.string.refuel_title), (spentRefuel / total) * 100});
        }
        if (spentMaintenance > 0.0) {
            pieDataList.add(new Object[]{getString(R.string.maintenance_title), (spentMaintenance / total) * 100});
        }
        if (spentInsurance > 0.0) {
            pieDataList.add(new Object[]{getString(R.string.insurance_title), (spentInsurance / total) * 100});
        }
        if (spentTax > 0.0) {
            pieDataList.add(new Object[]{getString(R.string.tax_title), (spentTax / total) * 100});
        }

        // 将List<Object[]>转换为Object[][]
        Object[][] pieData = new Object[pieDataList.size()][2];
        for (int i = 0; i < pieDataList.size(); i++) {
            pieData[i] = pieDataList.get(i);
        }

        return pieData;
    }

    private void loadAvatar(CircleImageView imageView) {
        try {
            Glide.with(requireActivity())
                    .load(Const.DEV_BASEURL + UserManager.getUserAvatar())
                    .into(imageView);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 初始化折叠头部
     */
    private void initHeaderView() {
        // {{ AURA-X: [Fix] - 图标已在initMinimalUI中设置，这里只需要确保状态正确 }}
        // 设置初始状态：完全展开时显示白色图标，透明度为1
        groupChange(1f, 1);

        // 为appbarLayout添加滚动偏移变化的监听器
        binding.appbarLayout.addOnOffsetChangedListener((appBarLayout, verticalOffset) -> {
            // 计算滚动百分比
            float percent = Math.abs(verticalOffset) / (float) appBarLayout.getTotalScrollRange();

            // {{ AURA-X: [Fix] - 只设置中间标题区域的透明度，不影响右侧图标 }}
            // 根据滚动百分比设置中间标题区域的透明度，保持图标可见
            binding.ucHeadTitle.titleUcAvater.setAlpha(percent);
            binding.ucHeadTitle.titleUcTitle.setAlpha(percent);

            // 判断滚动状态并调用groupChange方法更新界面
            if (percent == 0) {
                // 完全展开，显示白色图标
                groupChange(1f, 1);
            } else if (percent == 1) {
                // 完全关闭，隐藏头像并显示黑色图标
                binding.ucAvater.setVisibility(View.GONE);
                groupChange(1f, 2);
            } else {
                // 介于展开和关闭之间，显示头像并调用groupChange方法更新图标颜色
                binding.ucAvater.setVisibility(View.VISIBLE);
                groupChange(percent, 0);
            }
        });

        /* 获取AppBarLayout的OverScrollViewBehavior，并为其添加进度变化的监听器*/
        AppBarLayoutOverScrollViewBehavior myAppBarLayoutBehavior = (AppBarLayoutOverScrollViewBehavior) ((CoordinatorLayout.LayoutParams) binding.appbarLayout.getLayoutParams()).getBehavior();
        if (myAppBarLayoutBehavior != null) {
            myAppBarLayoutBehavior.setOnProgressChangeListener((progress, isRelease) -> {
                // 根据进度更新进度条的显示
                binding.ucHeadTitle.ucProgressbar.setProgress((int) (progress * 360));

                // 如果进度为100%且未旋转且是释放状态，则可能需要刷新ViewPager中的Fragment
                if (progress == 1 && !binding.ucHeadTitle.ucProgressbar.isSpinning && isRelease) {
                    // 刷新数据
                    getBanner();
                    getUserInfo();
                    if (UserManager.isLogin()) {
                        loadExpenseData();
                    }
                }

                // {{ AURA-X: [Fix] - 根据进度和状态设置图标显示，确保设置图标始终可见 }}
                if (progress == 0 && !binding.ucHeadTitle.ucProgressbar.isSpinning) {
                    // 进度为0时，显示两个图标
                    binding.ucHeadTitle.ucMsgIv.setVisibility(View.VISIBLE);
                    binding.ucHeadTitle.ucSettingIv.setVisibility(View.VISIBLE);
                } else if (progress > 0) {
                    // 进度大于0时，隐藏消息图标但保持设置图标可见
                    binding.ucHeadTitle.ucMsgIv.setVisibility(View.INVISIBLE);
                    // {{ AURA-X: [Fix] - 确保设置图标始终保持可见 }}
                    binding.ucHeadTitle.ucSettingIv.setVisibility(View.VISIBLE);
                }
            });
        }
    }
    


    /**
     * 生成数据列表
     *
     * @return 包含符合条件的应用信息的列表
     * @throws JsonSyntaxException 如果解析JSON数据时发生语法错误，将抛出此异常
     */
    private List<AppInfo> generateData() {
        List<AppInfo> mAllAppList;
        try {
            mAllAppList = new Gson().fromJson(DataManager.getAppListData(), new TypeToken<List<AppInfo>>() {
            }.getType());
        } catch (JsonSyntaxException e) {
            return Collections.emptyList();
        }

        if (mAllAppList == null) {
            return Collections.emptyList();
        }

        Set<String> targetAppNames = new HashSet<>(Arrays.asList("智能任务", "闪传", "全面触控", "主题"));
        return mAllAppList.stream()
                .filter(appInfo -> targetAppNames.contains(appInfo.getAppName()))
                .collect(Collectors.toList());
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();

        // 先检查是否需要登录
        if (!UserManager.isLogin() && requiresLogin(id)) {
            showLoginDialog();
            return;
        }

        switch (id) {
            case R.id.uc_avater:
                handleAvatarClick();
                break;
            case R.id.bt_back:
                requireNavigationScene(this).pop();
                break;
            case R.id.el_mycar:
                requireNavigationScene(this).push(new CarTabNavigationScene());
                break;
            case R.id.el_cost:
                requireNavigationScene(this).push(new ExpenseTabNavigationScene());
                break;
            case R.id.el_today:
                requireNavigationScene(this).push(TripTabNavigationScene.newInstance(0));
                break;
            case R.id.el_alldata:
                requireNavigationScene(this).push(TripTabNavigationScene.newInstance(1));
                break;
            case R.id.uc_msg_iv:
                requireNavigationScene(this).push(NoticeTabNavigationScene.newInstance(0));
                break;
            default:
                break;
        }
    }

    /**
     * 判断指定的ID是否需要登录才能操作
     */
    private boolean requiresLogin(int viewId) {
        // 除了返回按钮外，其他功能都需要登录
        return viewId != R.id.bt_back;
    }

    /**
     * 处理头像点击事件
     */
    private void handleAvatarClick() {
        if (UserManager.isLogin()) {
            // 已登录状态，显示退出登录对话框
            showLogoutDialog();
        } else {
            // 未登录状态，显示登录对话框
            showLoginDialog();
        }
    }

    /**
     * 显示登录对话框
     */
    public void showLoginDialog() {
        FragmentActivity activity = (FragmentActivity) requireActivity();
        LoginDialog dialog = LoginDialog.createDialog(activity.getSupportFragmentManager(), requireActivity());
        dialog.setOnLoginSuccessListener(userModel -> {
            // 登录成功后，加载用户数据并更新UI
            loadUserData();

            // 🚀 强制显示图表，确保登录后能看到图表
            Handler mainHandler = new Handler(Looper.getMainLooper());
            mainHandler.postDelayed(() -> {
                MyLog.d(TAG, "🚀 登录成功，强制显示图表");
                forceShowCharts();
            }, 1000); // 等待数据加载完成
        });
        dialog.showDialog();
    }

    /**
     * 显示退出登录对话框
     */
    private void showLogoutDialog() {
        // 获取当前的FragmentActivity实例并进行空值检查
        FragmentActivity activity = (FragmentActivity) requireActivity();

        // 显示一个信息对话框，询问用户是否确定要退出登录
        GeneralDialog.showInfoDialog(
                activity.getSupportFragmentManager(),
                requireActivity(),
                "确定要退出登录吗？",
                new GeneralDialog.DialogClickListener() {
                    @Override
                    public void onPositiveClick() {
                        // 用户点击了确定按钮，执行退出登录操作
                        UserManager.logout();

                        // 清空车辆相关数据
                        DataManager.setCarId(0L);
                        DataManager.setBrand("");
                        DataManager.setModel("");
                        DataManager.setLicensePlate("");
                        DataManager.setPowerType("");
                        DataManager.setEmission("");

                        // 清空费用和油耗数据
                        expensesList = null;

                        // 清空UI显示
                        binding.personalContent.tvBrand.setText("");
                        binding.personalContent.tvLicensePlateNumber.setText("");
                        binding.personalContent.tvAllMileageNumber.setText("");
                        binding.personalContent.tvAllTimeNumber.setText("");

                        // 更新UI显示未登录状态，并显示默认图表
                        showUnloggedState();
                    }

                    @Override
                    public void onNegativeClick() {
                        // 用户点击了取消按钮，不做任何操作
                    }
                }
        );
    }

    /**
     * 获取用户信息 - 带详细性能监控
     */
    @SuppressLint("CheckResult")
    private void getUserInfo() {
        long requestStart = System.currentTimeMillis();
        MyLog.d(TAG, "网络请求-用户信息开始");

        RxHttp.get(Const.USER_INFO)
                .setCacheMode(CacheMode.ONLY_NETWORK)
                .toObservable(UserModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(model -> {
                    long responseTime = System.currentTimeMillis() - requestStart;
                    MyLog.d(TAG, "网络请求-用户信息响应: " + responseTime + "ms");

                    long processStart = System.currentTimeMillis();
                    MyLog.v(TAG, "获取用户信息" + new Gson().toJson(model));
                    if (model.getCode() != StatusCodeModel.SUCCESS) {
                        // 获取失败，显示登录对话框
                        MyLog.d(TAG, "用户信息获取失败，显示登录对话框");
                        showLoginDialog();
                        return;
                    }

                    // 保存用户信息
                    UserModel.DataDTO user = model.getData();
                    UserManager.setUserId(user.getUserId());
                    UserManager.setUserName(user.getUserName());
                    UserManager.setNickName(user.getNickName());
                    UserManager.setLogin(true);
                    UserManager.setUserEmail(user.getEmail());
                    UserManager.setUserPhone(user.getPhonenumber());
                    UserManager.setUserAvatar(user.getAvatar());
                    UserManager.setGroup(model.getPostGroup());

                    long dataProcessTime = System.currentTimeMillis() - processStart;
                    MyLog.d(TAG, "用户信息数据处理: " + dataProcessTime + "ms");

                    // 更新UI显示
                    long uiUpdateStart = System.currentTimeMillis();
                    updateUserUI();
                    MyLog.d(TAG, "用户信息UI更新: " + (System.currentTimeMillis() - uiUpdateStart) + "ms");

                    MyLog.d(TAG, "用户信息总处理时间: " + (System.currentTimeMillis() - requestStart) + "ms");

                    // 标记一个请求完成
                    checkAllRequestsCompleted();
                }, throwable -> {
                    long errorTime = System.currentTimeMillis() - requestStart;
                    MyLog.e(TAG, "获取用户信息失败，耗时: " + errorTime + "ms, 错误: " + throwable.getMessage());
                    // 显示未登录状态
                    showUnloggedState();
                    // 标记请求完成（即使失败）
                    checkAllRequestsCompleted();
                });
    }

    /**
     * 根据给定的透明度和状态来更新头部标题组件的显示状态
     *
     * @param alpha 透明度值，范围在0.0（完全透明）到1.0（完全不透明）之间
     * @param state 状态值，0-正在变化，1-完全展开，2-完全关闭
     */
    public void groupChange(float alpha, int state) {
        // 设置标题栏的消息图标和设置图标的透明度
        binding.ucHeadTitle.ucSettingIv.setAlpha(alpha);
        binding.ucHeadTitle.ucMsgIv.setAlpha(alpha);

        // 根据状态更新图标资源
        switch (state) {
            // 介于展开和关闭之间的状态，显示黑色图标
            case 0:
                // 完全关闭，显示黑色图标
            case 2:
                setIconColor(R.drawable.icon_msg_black, R.drawable.icon_setting);
                break;
            // 完全展开，显示白色图标
            case 1:
                setIconColor(R.drawable.icon_msg_day, R.drawable.icon_setting_day);
                break;
            default:
                // 处理未知状态
                throw new IllegalArgumentException("Invalid state: " + state);
        }
    }

    private void setIconColor(int msgResId, int settingResId) {
        binding.ucHeadTitle.ucMsgIv.setImageResource(msgResId);
        binding.ucHeadTitle.ucSettingIv.setImageResource(settingResId);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        FragmentActivity activity = (FragmentActivity) requireActivity();
        // 确保LoginDialog被正确关闭
        LoginDialog.createDialog(activity.getSupportFragmentManager(), requireActivity()).dismissDialog();


        // 清除引用避免内存泄漏
        aaPieChartOptions = null;
        mTooltipTextStyle = null;
        mLegendItemStyle = null;
        expensesList = null;

        // 🚀 清理ViewStub延迟加载的视图引用
        pieChartView = null;
        lineChartView = null;
        bannerView = null;
        recyclerView = null;
        chartOverlayLayout = null;
        vipOverlayLayout = null;
        isPieChartInflated = false;
        isLineChartInflated = false;
        isBannerInflated = false;
        isRecyclerViewInflated = false;
        isChartOverlayInflated = false;
        isVipOverlayInflated = false;
    }

    @Override
    public void onSkinChange(SkinModel value) {
        // 重新初始化样式
        initCommonStyles();

        // 如果数据已准备好，则重新创建图表配置
        if (mDataPrepared) {
            // 无论是否有数据，都重新创建图表以应用新的皮肤
            if (expensesList == null || expensesList.isEmpty()) {
                // 重新创建默认图表
                showDefaultCharts();
            } else {
                // 重新创建数据图表，确保油耗图表保持渐变蓝色效果
                updateCharts(Utilities.Companion.getThisMonthExpenses(expensesList));
            }
        }
    }

    /**
     * 🚀 延迟加载轮播图视图 - 重型组件优化
     */
    private void inflateBannerIfNeeded() {
        if (!isBannerInflated) {
            long startTime = System.currentTimeMillis();
            MyLog.d(TAG, "🔄 开始延迟加载轮播图视图");

            try {
                ViewStub stubBanner = binding.personalContent.getRoot().findViewById(R.id.stub_banner);
                if (stubBanner != null) {
                    // 异步inflate，避免阻塞UI
                    CompletableFuture.runAsync(() -> {
                        Handler mainHandler = new Handler(Looper.getMainLooper());
                        mainHandler.post(() -> {
                            try {
                                stubBanner.setOnInflateListener((stub, inflated) -> {
                                    bannerView = (BannerViewPager<PersonalBannerModel.RowsDTO>) inflated;

                                    // 初始化轮播图
                                    setupBannerViewDelayed();

                                    MyLog.d(TAG, "✅ 轮播图视图延迟加载完成");
                                });
                                bannerView = (BannerViewPager<PersonalBannerModel.RowsDTO>) stubBanner.inflate();
                                isBannerInflated = true;

                                long inflateTime = System.currentTimeMillis() - startTime;
                                MyLog.d(TAG, "📊 轮播图视图延迟加载耗时: " + inflateTime + "ms");
                            } catch (Exception e) {
                                MyLog.e(TAG, "❌ 延迟加载轮播图视图失败", e);
                            }
                        });
                    });
                }
            } catch (Exception e) {
                MyLog.e(TAG, "❌ 延迟加载轮播图视图失败", e);
            }
        }
    }

    /**
     * 🚀 延迟加载RecyclerView视图 - 重型组件优化
     */
    private void inflateRecyclerViewIfNeeded() {
        if (!isRecyclerViewInflated) {
            long startTime = System.currentTimeMillis();
            MyLog.d(TAG, "🔄 开始延迟加载RecyclerView视图");

            try {
                ViewStub stubRecyclerView = binding.personalContent.getRoot().findViewById(R.id.stub_recyclerview);
                if (stubRecyclerView != null) {
                    // 异步inflate，避免阻塞UI
                    CompletableFuture.runAsync(() -> {
                        Handler mainHandler = new Handler(Looper.getMainLooper());
                        mainHandler.post(() -> {
                            try {
                                stubRecyclerView.setOnInflateListener((stub, inflated) -> {
                                    recyclerView = (RecyclerView) inflated;


                                    // 初始化RecyclerView
                                    initRecyclerViewDelayed();

                                    MyLog.d(TAG, "✅ RecyclerView视图延迟加载完成");
                                });
                                recyclerView = (RecyclerView) stubRecyclerView.inflate();
                                isRecyclerViewInflated = true;

                                long inflateTime = System.currentTimeMillis() - startTime;
                                MyLog.d(TAG, "📊 RecyclerView视图延迟加载耗时: " + inflateTime + "ms");
                            } catch (Exception e) {
                                MyLog.e(TAG, "❌ 延迟加载RecyclerView视图失败", e);
                            }
                        });
                    });
                }
            } catch (Exception e) {
                MyLog.e(TAG, "❌ 延迟加载RecyclerView视图失败", e);
            }
        }
    }

    /**
     * 🚀 延迟初始化轮播图配置
     */
    private void setupBannerViewDelayed() {
        if (bannerView != null) {
            try {
                // 设置指示器滑块之间的间隔
                int indicatorSliderGap = BannerUtils.dp2px(4);
                // 设置指示器滑块的宽度
                int indicatorSliderWidth = BannerUtils.dp2px(15);
                // 设置页面之间的边距
                int pageMargin = BannerUtils.dp2px(15);

                bannerView
                        .setIndicatorSliderGap(indicatorSliderGap)
                        .setIndicatorSliderWidth(indicatorSliderWidth)
                        .setIndicatorGravity(IndicatorGravity.END)
                        .setIndicatorStyle(IndicatorStyle.ROUND_RECT)
                        .setIndicatorSlideMode(SMOOTH)
                        .registerLifecycleObserver(getLifecycle())
                        .setPageMargin(pageMargin)
                        .setIndicatorSliderColor(Color.parseColor("#FFFFFF"), Color.parseColor("#7F7F7F"))
                        .setScrollDuration(800)
                        .setUserInputEnabled(false)
                        .setPageStyle(MULTI_PAGE_SCALE)
                        .setAdapter(new PersonalBannerAdapter())
                        .setOnPageClickListener((view, i) ->
                                Toast.makeText(requireActivity(), "点击了第" + i + "个", Toast.LENGTH_SHORT).show()
                        )
                        .create();

                MyLog.d(TAG, "🚀 轮播图延迟配置完成");

                // 🚀 配置完成后，检查是否需要重新获取数据
                // 如果数据加载比ViewStub inflate更早，需要重新获取
                Handler dataHandler = new Handler(Looper.getMainLooper());
                dataHandler.postDelayed(() -> {
                    MyLog.d(TAG, "🚀 检查轮播图数据状态");
                    getBanner(); // 重新获取数据确保显示
                }, 100);

            } catch (Exception e) {
                MyLog.e(TAG, "轮播图配置失败", e);
            }
        }
    }

    /**
     * 🚀 延迟初始化RecyclerView配置
     */
    private void initRecyclerViewDelayed() {
        if (recyclerView != null) {
            try {
                // 设置RecyclerView的布局管理器
                recyclerView.setLayoutManager(new LinearLayoutManager(requireActivity()));

                // 设置适配器（如果已经预先创建）
                if (helper != null) {
                    recyclerView.setAdapter(helper.getAdapter());
                    MyLog.d(TAG, "RecyclerView适配器设置完成");
                } else {
                    // 如果适配器还没创建，重新创建
                    MyLog.d(TAG, "重新创建RecyclerView适配器");
                    /** 热门应用列表适配器 */
                    DragAndSwipeAdapter mAdapter = new DragAndSwipeAdapter();
                    helper = new QuickAdapterHelper.Builder(mAdapter).build();
                    helper.addBeforeAdapter(new HomeTopHeaderAdapter());
                    mAdapter.submitList(generateData());

                    // 设置项点击监听器
                    mAdapter.setOnItemClickListener((adapter, view, position) -> {
                        AppInfo appInfo = adapter.getItem(position);
                        if (appInfo == null) return;
                        String appName = appInfo.getAppName();
                        if ("主题".equals(appName) || "闪传".equals(appName) || "智能任务".equals(appName) || "全面触控".equals(appName)) {
                            AppUtils.openPackageActivity(requireActivity(), appInfo.getPackageName(), appInfo.getActivityName());
                        }
                    });

                    recyclerView.setAdapter(helper.getAdapter());
                }

                MyLog.d(TAG, "RecyclerView延迟配置完成");
            } catch (Exception e) {
                MyLog.e(TAG, "RecyclerView配置失败", e);
            }
        }
    }

    /**
     * 强制AAChartView进行完整的重绘流程
     * 在API 17设备上特别有用
     *
     * @param chartView 需要刷新的图表视图
     */
    public void forceChartRefresh(com.github.AAChartModel.AAChartCore.AAChartCreator.AAChartView chartView) {
        if (PersonalSceneConfig.shouldForceChartRefresh()) {
            // 使用配置的延迟时间进行异步刷新
            int refreshDelay = PersonalSceneConfig.getChartRefreshDelay();
            int forceDelay = PersonalSceneConfig.getForceRefreshDelay();

            chartView.postDelayed(() -> {
                // 先触发布局变化
                chartView.setVisibility(View.INVISIBLE);
                chartView.postDelayed(() -> {
                    chartView.setVisibility(View.VISIBLE);
                    chartView.invalidate();
                    chartView.requestLayout();

                    // 模拟触摸事件，强制WebView重绘
                    chartView.postDelayed(() -> {
                        try {
                            float x = chartView.getWidth() / 2f;
                            float y = chartView.getHeight() / 2f;

                            // 完整的触摸事件周期：按下和抬起
                            long downTime = SystemClock.uptimeMillis();
                            MotionEvent downEvent = MotionEvent.obtain(
                                    downTime, downTime, MotionEvent.ACTION_DOWN, x, y, 0);
                            chartView.dispatchTouchEvent(downEvent);
                            downEvent.recycle();

                            // 抬起事件
                            long upTime = SystemClock.uptimeMillis() + 100;
                            MotionEvent upEvent = MotionEvent.obtain(
                                    downTime, upTime, MotionEvent.ACTION_UP, x, y, 0);
                            chartView.dispatchTouchEvent(upEvent);
                            upEvent.recycle();

                            // 最后一次强制重绘
                            chartView.invalidate();
                            chartView.requestLayout();
                        } catch (Exception e) {
                            MyLog.e(TAG, "强制刷新图表时发生错误", e);
                        }
                    }, forceDelay);
                }, forceDelay);
            }, refreshDelay);
        }
    }


}