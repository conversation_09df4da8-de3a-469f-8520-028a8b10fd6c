package com.smartcar.easylauncher.modules.keysetting;

import static com.bytedance.scene.navigation.NavigationSceneGetter.requireNavigationScene;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.databinding.SceneFindKeyMappingDetailsBinding;
import com.smartcar.easylauncher.data.database.entity.KeyMapModel;
import com.smartcar.easylauncher.core.manager.KeyMapManager;
import com.smartcar.easylauncher.data.model.system.RecommendKeyMapModel;
import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * 推荐按键映射预览页面
 * 用于展示推荐按键映射的详细信息，并提供添加到我的按键的功能
 * 
 * <AUTHOR>
 * @date 2024/12/18
 */
public class FindKeyMappingDetailsScene extends BaseScene {
    public static final String TAG = FindKeyMappingDetailsScene.class.getSimpleName();
    
    private SceneFindKeyMappingDetailsBinding binding;
    private RecommendKeyMapModel recommendKeyMap;

    /**
     * 创建推荐按键映射预览Scene的Bundle参数
     */
    public static Bundle createArguments(RecommendKeyMapModel recommendKeyMap) {
        Bundle bundle = new Bundle();
        bundle.putSerializable("recommend_key_map", recommendKeyMap);
        return bundle;
    }

    @NonNull
    @Override
    protected ViewGroup onCreateNewView(@NonNull LayoutInflater layoutInflater, @Nullable ViewGroup viewGroup, @Nullable Bundle bundle) {
        binding = SceneFindKeyMappingDetailsBinding.inflate(layoutInflater, viewGroup, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 获取传递的推荐按键映射数据
        Bundle arguments = getArguments();
        if (arguments != null) {
            recommendKeyMap = (RecommendKeyMapModel) arguments.getSerializable("recommend_key_map");
        }
        
        if (recommendKeyMap == null) {
            MyLog.e(TAG, "onViewCreated: recommendKeyMap is null");
            MToast.makeTextShort("数据错误，请重试");
            requireNavigationScene(this).pop();
            return;
        }
        
        initViews();
        setupData();
        setupClickListeners();
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        MyLog.d(TAG, "initViews: 初始化按键映射详情视图");
    }

    /**
     * 设置数据
     */
    private void setupData() {
        if (recommendKeyMap == null) {
            MyLog.e(TAG, "setupData: recommendKeyMap is null");
            return;
        }

        MyLog.d(TAG, "setupData: 设置按键映射数据 - " + recommendKeyMap.getKeyMapName());

        // 设置按键映射基本信息
        binding.tvKeyMapName.setText(recommendKeyMap.getKeyMapName());
        binding.tvKeyMapDescription.setText(recommendKeyMap.getDescription());
        binding.tvKeyMapCategory.setText(recommendKeyMap.getCategory());
        binding.tvKeyName.setText(recommendKeyMap.getKeyName());
        
        // 设置评分和使用人数
        binding.tvRating.setText(recommendKeyMap.getRatingStars());
        binding.tvUsageCount.setText(recommendKeyMap.getUsageCount() + "人使用");
        
        // 设置标签
        if (recommendKeyMap.isHot()) {
            binding.tvHotTag.setVisibility(View.VISIBLE);
            binding.tvHotTag.setText("热门");
        } else if (recommendKeyMap.isNew()) {
            binding.tvHotTag.setVisibility(View.VISIBLE);
            binding.tvHotTag.setText("新品");
        } else {
            binding.tvHotTag.setVisibility(View.GONE);
        }
        
        // 设置动作信息
        binding.tvActionType.setText(getActionTypeDescription(recommendKeyMap.getActionType()));
        binding.tvActionData.setText(getActionDataDescription(recommendKeyMap));
    }

    /**
     * 设置点击监听器
     */
    private void setupClickListeners() {
        // 添加按钮点击事件
        binding.btnAdd.setOnClickListener(v -> addKeyMapToMyKeys());
        
        // 返回按钮点击事件
        binding.btnBack.setOnClickListener(v -> requireNavigationScene(this).pop());
    }

    /**
     * 获取动作类型描述
     */
    private String getActionTypeDescription(int actionType) {
        switch (actionType) {
            case 0: return "发送广播";
            case 1: return "启动应用";
            case 2: return "控制媒体";
            case 3: return "系统操作";
            default: return "未知操作";
        }
    }

    /**
     * 获取动作数据描述
     */
    private String getActionDataDescription(RecommendKeyMapModel keyMap) {
        if (keyMap.getActionData() == null) {
            return "无";
        }
        
        switch (keyMap.getActionType()) {
            case 1: // 启动应用
                return getAppNameFromPackage(keyMap.getActionData());
            case 2: // 控制媒体
                return getMediaControlDescription(keyMap.getActionData());
            case 3: // 系统操作
                return getSystemActionDescription(keyMap.getActionData());
            default:
                return keyMap.getActionData();
        }
    }

    /**
     * 从包名获取应用名称
     */
    private String getAppNameFromPackage(String packageName) {
        switch (packageName) {
            case "com.netease.cloudmusic": return "网易云音乐";
            case "com.autonavi.minimap": return "高德地图";
            case "com.android.dialer": return "电话";
            case "com.tencent.mm": return "微信";
            default: return packageName;
        }
    }

    /**
     * 获取媒体控制描述
     */
    private String getMediaControlDescription(String controlType) {
        switch (controlType) {
            case "PLAY_PAUSE": return "播放/暂停";
            case "NEXT": return "下一曲";
            case "PREVIOUS": return "上一曲";
            case "STOP": return "停止";
            default: return controlType;
        }
    }

    /**
     * 获取系统操作描述
     */
    private String getSystemActionDescription(String actionType) {
        switch (actionType) {
            case "HOME": return "返回主页";
            case "BACK": return "返回";
            case "MENU": return "菜单";
            case "NOTIFICATIONS": return "通知栏";
            default: return actionType;
        }
    }

    /**
     * 添加按键映射到我的按键
     */
    private void addKeyMapToMyKeys() {
        MyLog.d(TAG, "addKeyMapToMyKeys: " + recommendKeyMap.getKeyMapName());
        
        try {
            // 转换为KeyMapModel
            KeyMapModel keyMapModel = convertToKeyMapModel(recommendKeyMap);
            
            // 保存到数据库
            boolean success = KeyMapManager.getInstance(requireActivity()).saveKeyMap(keyMapModel);
            
            if (success) {
                MToast.makeTextShort("按键映射「" + recommendKeyMap.getKeyMapName() + "」添加成功");
                MyLog.d(TAG, "addKeyMapToMyKeys: 添加成功");
                // 返回上一页
                requireNavigationScene(this).pop();
            } else {
                MToast.makeTextShort("添加失败，请重试");
                MyLog.e(TAG, "addKeyMapToMyKeys: 添加失败");
            }
        } catch (Exception e) {
            MyLog.e(TAG, "addKeyMapToMyKeys: 添加异常", e);
            MToast.makeTextShort("添加失败：" + e.getMessage());
        }
    }

    /**
     * 转换推荐按键映射为按键映射模型
     */
    private KeyMapModel convertToKeyMapModel(RecommendKeyMapModel recommendKeyMap) {
        KeyMapModel keyMapModel = new KeyMapModel();
        keyMapModel.setKeyCode(recommendKeyMap.getKeyCode());
        keyMapModel.setKeyName(recommendKeyMap.getKeyName());
        keyMapModel.setActionType(recommendKeyMap.getActionType());
        keyMapModel.setActionData(recommendKeyMap.getActionData());
        keyMapModel.setExtraData(recommendKeyMap.getExtraData());
        keyMapModel.setEnabled(1); // 默认启用
        keyMapModel.setCreateTime(System.currentTimeMillis());
        keyMapModel.setUpdateTime(System.currentTimeMillis());
        return keyMapModel;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        MyLog.d(TAG, "onDestroyView: 清理按键映射详情页面资源");
        binding = null;
        recommendKeyMap = null;
    }
}
