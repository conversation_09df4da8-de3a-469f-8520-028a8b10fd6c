package com.smartcar.easylauncher.modules.onboarding.adapter;

import android.animation.ArgbEvaluator;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.DecelerateInterpolator;

import androidx.annotation.NonNull;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.hjq.shape.layout.ShapeConstraintLayout;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.modules.onboarding.model.DesktopModeItem;

import java.util.List;

/**
 * 桌面模式选择适配器
 * 使用BaseRecyclerViewAdapterHelper4实现
 *
 * <AUTHOR>
 */
public class DesktopModeAdapter extends BaseQuickAdapter<DesktopModeItem, QuickViewHolder> {

    private OnModeSelectedListener listener;
    private int selectedPosition = 0; // 默认选中第一个
    
    // 颜色常量
    private static final int COLOR_NORMAL = Color.parseColor("#FFFFFF");
    private static final int COLOR_SELECTED = Color.parseColor("#DCEAFC");

    public DesktopModeAdapter() {
        // 空构造函数
    }

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder holder, @SuppressLint("RecyclerView") int position, DesktopModeItem item) {
        // 设置模式预览图
        if (item != null) {
            holder.setImageResource(R.id.iv_mode_preview, item.getPreviewResId());
        }

        // 设置模式图标
        if (item != null) {
            holder.setImageResource(R.id.iv_mode_icon, item.getIconResId());
        }

        // 设置模式标题
        if (item != null) {
            holder.setText(R.id.tv_mode_title, item.getTitle());
        }

        // 设置模式描述
        if (item != null) {
            holder.setText(R.id.tv_mode_desc, item.getDescription());
        }

        // 设置模式特点
        List<String> features = null;
        if (item != null) {
            features = item.getFeatures();
        }
        if (features != null && !features.isEmpty()) {
            // 特性1
            boolean hasFeature1 = true;
            holder.setVisible(R.id.iv_feature1_icon, hasFeature1);
            holder.setVisible(R.id.tv_mode_feature1, hasFeature1);
            holder.setText(R.id.tv_mode_feature1, features.get(0));

            // 特性2
            boolean hasFeature2 = features.size() > 1;
            holder.setVisible(R.id.iv_feature2_icon, hasFeature2);
            holder.setVisible(R.id.tv_mode_feature2, hasFeature2);
            if (hasFeature2) {
                holder.setText(R.id.tv_mode_feature2, features.get(1));
            }
        }
        
        // 设置选中状态
        boolean isSelected = position == selectedPosition;
        setSelectedState(holder, isSelected);

        // 设置点击事件
        holder.getView(R.id.cl_mode_item).setOnClickListener(v -> {
            if (selectedPosition != position) {
                // 更新选中状态
                int oldPosition = selectedPosition;
                selectedPosition = position;
                notifyItemChanged(oldPosition);
                notifyItemChanged(position);
                
                // 添加选中动画
                Animation pulseAnim = AnimationUtils.loadAnimation(v.getContext(), R.anim.pulse);
                v.startAnimation(pulseAnim);
                
                if (listener != null) {
                    listener.onModeSelected(item, position);
                }
            }
        });
    }
    
    /**
     * 设置选中状态，带有动画效果
     * 
     * @param holder ViewHolder
     * @param isSelected 是否选中
     */
    private void setSelectedState(@NonNull QuickViewHolder holder, boolean isSelected) {
        // 获取控件
        ShapeConstraintLayout itemLayout = holder.getView(R.id.cl_mode_item);
        View checkIcon = holder.getView(R.id.iv_mode_selected);

        // 设置选中指示图标和指示条的可见性
        checkIcon.setVisibility(View.VISIBLE);

        // 设置透明度初始值
        float targetAlpha = isSelected ? 1.0f : 0.0f;
        
        // 如果当前状态与目标状态不同，则执行动画
        int currentColor = itemLayout.getSolidColor();
        int targetColor = isSelected ? COLOR_SELECTED : COLOR_NORMAL;
        
        if (currentColor != targetColor || checkIcon.getAlpha() != targetAlpha) {
            // 背景色动画
            ValueAnimator colorAnimator = ValueAnimator.ofObject(new ArgbEvaluator(), currentColor, targetColor);
            colorAnimator.setDuration(300);
            colorAnimator.setInterpolator(new DecelerateInterpolator());
            colorAnimator.addUpdateListener(animation -> {
                int color = (int) animation.getAnimatedValue();
                itemLayout.getShapeDrawableBuilder().setSolidColor(color).intoBackground();
            });
            colorAnimator.start();
            
            // 透明度动画
            ValueAnimator alphaAnimator = ValueAnimator.ofFloat(checkIcon.getAlpha(), targetAlpha);
            alphaAnimator.setDuration(300);
            alphaAnimator.setInterpolator(new DecelerateInterpolator());
            alphaAnimator.addUpdateListener(animation -> {
                float value = (float) animation.getAnimatedValue();
                checkIcon.setAlpha(value);

                // 如果动画结束且透明度为0，则设置为不可见
                if (animation.getAnimatedFraction() >= 1.0f && value == 0) {
                    checkIcon.setVisibility(View.INVISIBLE);
                }
            });
            alphaAnimator.start();
        } else {
            // 如果不需要动画，直接设置颜色和透明度
            itemLayout.getShapeDrawableBuilder().setSolidColor(targetColor).intoBackground();
            checkIcon.setAlpha(targetAlpha);

            // 如果透明度为0，则设置为不可见
            if (targetAlpha == 0) {
                checkIcon.setVisibility(View.INVISIBLE);
            }
        }
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_desktop_mode, viewGroup, false);
        return new QuickViewHolder(view);
    }

    /**
     * 设置选中的模式
     *
     * @param position 位置
     */
    public void setSelectedPosition(int position) {
        if (position >= 0 && position < getItems().size() && selectedPosition != position) {
            int oldPosition = selectedPosition;
            selectedPosition = position;
            notifyItemChanged(oldPosition);
            notifyItemChanged(position);
        }
    }

    /**
     * 获取选中的模式
     *
     * @return 选中的模式项
     */
    public DesktopModeItem getSelectedItem() {
        return getItem(selectedPosition);
    }

    /**
     * 设置模式选择监听器
     *
     * @param listener 监听器
     */
    public void setOnModeSelectedListener(OnModeSelectedListener listener) {
        this.listener = listener;
    }

    /**
     * 模式选择监听器接口
     */
    public interface OnModeSelectedListener {
        void onModeSelected(DesktopModeItem item, int position);
    }
} 