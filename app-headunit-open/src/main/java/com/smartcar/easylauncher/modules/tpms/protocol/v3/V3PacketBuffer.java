package com.smartcar.easylauncher.modules.tpms.protocol.v3;

import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.modules.tpms.protocol.base.BasePacketBuffer;
import java.util.ArrayList;
import java.util.List;

public class V3PacketBuffer extends BasePacketBuffer {
    private static final String TAG = "V3PacketBuffer";
    private static volatile V3PacketBuffer instance;
    
    private static final byte FRAME_HEADER_1 = (byte) 0x55;
    private static final byte FRAME_HEADER_2 = (byte) 0xAA;

    public V3PacketBuffer() {}
    
    public static V3PacketBuffer getInstance() {
        if (instance == null) {
            synchronized (V3PacketBuffer.class) {
                if (instance == null) {
                    instance = new V3PacketBuffer();
                }
            }
        }
        return instance;
    }
    
    /**
     * 处理接收到的数据
     * @param data 接收到的数据
     * @param length 数据长度
     * @return 解析出的完整数据包列表
     */
    public List<byte[]> handleBuffer(byte[] data, int length) {
        List<byte[]> packets = new ArrayList<>();
        
        synchronized (mutex) {
            // 记录原始数据
            MyLog.d(TAG, "接收数据: " + bytesToHex(data, length));
            
            // 参数校验
            if (data == null || length <= 0 || currentPosition + length > MAX_PACKET_BUFFER_SIZE) {
                MyLog.e(TAG, "缓冲区溢出或无效参数 - currentPosition: " + currentPosition + ", length: " + length);
                currentPosition = 0;
                return packets;
            }

            try {
                // 复制数据到缓冲区
                System.arraycopy(data, 0, netPacketBuffer, currentPosition, length);
                currentPosition += length;
                MyLog.d(TAG, "当前缓冲区数据: " + bytesToHex(netPacketBuffer, currentPosition));

                // 解析所有完整的数据包
                byte[] remainingData = netPacketBuffer;
                int remainingLength = currentPosition;

                while (remainingLength >= 3) { // 至少包含帧头和长度字段
                    // 查找帧头
                    int frameStart = findFrameHeader(remainingData, remainingLength);
                    MyLog.d(TAG, "查找帧头结果: " + frameStart);
                    
                    if (frameStart == -1) {
                        MyLog.d(TAG, "未找到帧头，退出解析");
                        break;
                    }

                    // 移除帧头前的无效数据
                    if (frameStart > 0) {
                        MyLog.d(TAG, "移除帧头前无效数据: " + frameStart + " 字节");
                        remainingLength -= frameStart;
                        remainingData = eraseData(remainingData, remainingLength + frameStart, frameStart);
                    }

                    // 检查数据长度字段
                    if (remainingLength < 3) {
                        MyLog.d(TAG, "数据不足3字节，等待更多数据");
                        break;
                    }
                    int frameLength = remainingData[2] & 0xFF;
                    MyLog.d(TAG, "数据包长度: " + frameLength);
                    
                    // 检查是否收到完整的帧
                    if (remainingLength < frameLength) {
                        MyLog.d(TAG, "数据不完整，等待更多数据 - 需要: " + frameLength + ", 当前: " + remainingLength);
                        break;
                    }

                    // 提取并验证数据包
                    byte[] frame = new byte[frameLength];
                    System.arraycopy(remainingData, 0, frame, 0, frameLength);
                    MyLog.d(TAG, "提取数据包: " + bytesToHex(frame, frameLength));
                    
                    if (checkChecksum(frame)) {
                        MyLog.d(TAG, "校验和通过，添加到结果列表");
                        packets.add(frame);
                        // 移除已处理的数据
                        remainingLength -= frameLength;
                        remainingData = eraseData(remainingData, remainingLength + frameLength, frameLength);
                    } else {
                        MyLog.w(TAG, "校验和失败，移除1字节后继续");
                        // 校验失败，移除一个字节
                        remainingLength -= 1;
                        remainingData = eraseData(remainingData, remainingLength + 1, 1);
                    }
                }

                // 更新缓冲区
                if (remainingLength > 0 && remainingData != null) {
                    System.arraycopy(remainingData, 0, netPacketBuffer, 0, remainingLength);
                    MyLog.d(TAG, "保留未处理数据: " + bytesToHex(remainingData, remainingLength));
                }
                currentPosition = remainingLength;
                MyLog.d(TAG, "更新缓冲区位置: " + currentPosition);

                MyLog.d(TAG, "解析完成，得到 " + packets.size() + " 个数据包");
                return packets;
            } catch (Exception e) {
                MyLog.e(TAG, "数据处理异常: " + e.getMessage());
                e.printStackTrace();
                currentPosition = 0;
                return packets;
            }
        }
    }
    
    /**
     * 查找帧头
     */
    private int findFrameHeader(byte[] data, int length) {
        if (DEBUG) {
            // 添加调试日志，查看具体的比较过程
            for (int i = 0; i < length - 1; i++) {
                MyLog.v(TAG, String.format("比较位置 %d: %02X %02X vs %02X %02X", 
                    i, data[i], data[i+1], FRAME_HEADER_1, FRAME_HEADER_2));
            }
        }
        
        for (int i = 0; i < length - 1; i++) {
            if (data[i] == FRAME_HEADER_1 && data[i + 1] == FRAME_HEADER_2) {
                return i;
            }
        }
        return -1;
    }
    
    @Override
    protected boolean checkChecksum(byte[] data) {
        if (data == null || data.length < 3) {
            MyLog.w(TAG, "校验和: 数据无效");
            return false;
        }
        
        int length = data[2] & 0xFF;
        if (length == 0 || length > data.length) {
            MyLog.w(TAG, "校验和: 长度字段无效 - length: " + length + ", data.length: " + data.length);
            return false;
        }
        
        // 使用异或运算计算校验和
        byte checksum = data[0];
        for (int i = 1; i < length - 1; i++) {
            checksum ^= data[i];
        }
        
        boolean result = checksum == data[length - 1];
        MyLog.d(TAG, "校验和计算 - 计算值: " + String.format("%02X", checksum) + 
                     ", 期望值: " + String.format("%02X", data[length - 1]) + 
                     ", 结果: " + result);
        return result;
    }
} 