package com.smartcar.easylauncher.modules.theme;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.databinding.SceneThemeHelpBinding;
import com.smartcar.easylauncher.modules.other.WebActivity;
import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * 主题帮助Scene
 * 提供主题相关的帮助信息和常见问题解答
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
public class ThemeHelpScene extends BaseScene {
    
    private static final String TAG = "ThemeHelpScene";
    
    private SceneThemeHelpBinding binding;

    @NonNull
    @Override
    public ViewGroup onCreateNewView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = SceneThemeHelpBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initViews();
        setupHelpContent();
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        // 设置FAQ点击事件
        binding.faqClickMe.setOnClickListener(this::onFaqClick);

        // 设置其他帮助项点击事件 - 更新为新的TextView ID
        binding.tvThemeUsageTitle.setOnClickListener(this::onThemeUsageClick);
        binding.tvThemeDownloadTitle.setOnClickListener(this::onThemeDownloadClick);
        binding.tvThemeCustomTitle.setOnClickListener(this::onThemeCustomClick);
        binding.tvThemeTroubleTitle.setOnClickListener(this::onThemeTroubleClick);

        // 设置简单的返回按钮 - 适合纯信息展示页面
        binding.btBack.setOnClickListener(v -> {
            MyLog.d(TAG, "点击返回按钮");
            getNavigationScene(this).pop();
        });
    }

    /**
     * 设置帮助内容
     */
    private void setupHelpContent() {
        // 设置优化后的标题和描述 - 使用卡片式设计，更加优雅
        binding.tvHelpTitle.setText("主题帮助");
        binding.tvHelpDescription.setText("了解主题功能的使用方法");

        // 设置各个帮助项的内容
        setupHelpItems();
    }

    /**
     * 设置帮助项内容
     */
    private void setupHelpItems() {
        // 主题使用帮助
        binding.tvThemeUsageTitle.setText("如何使用主题");
        binding.tvThemeUsageDesc.setText("学习如何浏览、预览和应用主题");
        
        // 主题下载帮助
        binding.tvThemeDownloadTitle.setText("主题下载管理");
        binding.tvThemeDownloadDesc.setText("了解主题下载、更新和存储管理");
        
        // 自定义主题帮助
        binding.tvThemeCustomTitle.setText("自定义主题");
        binding.tvThemeCustomDesc.setText("创建和导入您自己的个性化主题");
        
        // 故障排除帮助
        binding.tvThemeTroubleTitle.setText("常见问题");
        binding.tvThemeTroubleDesc.setText("解决主题使用中遇到的常见问题");
    }

    /**
     * FAQ点击事件
     */
    private void onFaqClick(View view) {
        MyLog.d(TAG, "点击FAQ");
        openWebPage(Const.THEME_QFA, "主题常见问题");
    }

    /**
     * 主题使用帮助点击事件
     */
    private void onThemeUsageClick(View view) {
        MyLog.d(TAG, "点击主题使用帮助");
        showThemeUsageHelp();
    }

    /**
     * 主题下载帮助点击事件
     */
    private void onThemeDownloadClick(View view) {
        MyLog.d(TAG, "点击主题下载帮助");
        showThemeDownloadHelp();
    }

    /**
     * 自定义主题帮助点击事件
     */
    private void onThemeCustomClick(View view) {
        MyLog.d(TAG, "点击自定义主题帮助");
        showThemeCustomHelp();
    }

    /**
     * 故障排除帮助点击事件
     */
    private void onThemeTroubleClick(View view) {
        MyLog.d(TAG, "点击故障排除帮助");
        showThemeTroubleHelp();
    }

    /**
     * 显示主题使用帮助
     */
    private void showThemeUsageHelp() {
        String helpContent = buildThemeUsageHelpContent();
        showHelpDialog("主题使用帮助", helpContent);
    }

    /**
     * 显示主题下载帮助
     */
    private void showThemeDownloadHelp() {
        String helpContent = buildThemeDownloadHelpContent();
        showHelpDialog("主题下载管理", helpContent);
    }

    /**
     * 显示自定义主题帮助
     */
    private void showThemeCustomHelp() {
        String helpContent = buildThemeCustomHelpContent();
        showHelpDialog("自定义主题", helpContent);
    }

    /**
     * 显示故障排除帮助
     */
    private void showThemeTroubleHelp() {
        String helpContent = buildThemeTroubleHelpContent();
        showHelpDialog("常见问题", helpContent);
    }

    /**
     * 构建主题使用帮助内容
     */
    private String buildThemeUsageHelpContent() {
        return "主题使用指南：\n\n" +
                "1. 浏览主题\n" +
                "   • 在推荐页面查看最新和热门主题\n" +
                "   • 使用分类筛选找到喜欢的主题风格\n\n" +
                "2. 预览主题\n" +
                "   • 点击主题可查看详细预览\n" +
                "   • 支持多角度预览主题效果\n\n" +
                "3. 应用主题\n" +
                "   • 下载后在我的主题中应用\n" +
                "   • 支持白天和夜间主题分别设置\n\n" +
                "4. 主题切换\n" +
                "   • 可设置自动切换模式\n" +
                "   • 支持手动切换主题模式";
    }

    /**
     * 构建主题下载帮助内容
     */
    private String buildThemeDownloadHelpContent() {
        return "主题下载管理：\n\n" +
                "1. 下载主题\n" +
                "   • 点击下载按钮获取主题\n" +
                "   • 下载进度实时显示\n\n" +
                "2. 存储管理\n" +
                "   • 主题文件存储在本地\n" +
                "   • 可查看存储空间使用情况\n\n" +
                "3. 主题更新\n" +
                "   • 自动检测主题更新\n" +
                "   • 支持增量更新节省流量\n\n" +
                "4. 删除主题\n" +
                "   • 长按主题可删除\n" +
                "   • 删除后释放存储空间";
    }

    /**
     * 构建自定义主题帮助内容
     */
    private String buildThemeCustomHelpContent() {
        return "自定义主题指南：\n\n" +
                "1. 主题制作\n" +
                "   • 使用官方主题制作工具\n" +
                "   • 支持自定义颜色和图标\n\n" +
                "2. 主题导入\n" +
                "   • 支持导入.skin格式文件\n" +
                "   • 可从外部存储导入主题\n\n" +
                "3. 主题分享\n" +
                "   • 可将自制主题分享给朋友\n" +
                "   • 支持导出主题文件\n\n" +
                "4. 注意事项\n" +
                "   • 确保主题文件完整性\n" +
                "   • 注意主题兼容性";
    }

    /**
     * 构建故障排除帮助内容
     */
    private String buildThemeTroubleHelpContent() {
        return "常见问题解决：\n\n" +
                "1. 主题无法下载\n" +
                "   • 检查网络连接\n" +
                "   • 确认存储空间充足\n\n" +
                "2. 主题应用失败\n" +
                "   • 重启应用后重试\n" +
                "   • 检查主题文件完整性\n\n" +
                "3. 主题显示异常\n" +
                "   • 清除应用缓存\n" +
                "   • 恢复默认主题后重新应用\n\n" +
                "4. 主题切换卡顿\n" +
                "   • 关闭不必要的后台应用\n" +
                "   • 重启设备释放内存";
    }

    /**
     * 显示帮助对话框
     */
    private void showHelpDialog(String title, String content) {
        // TODO: 实现帮助内容对话框
        MyLog.d(TAG, "显示帮助对话框: " + title);
        MyLog.d(TAG, "内容: " + content);
    }

    /**
     * 打开网页
     */
    private void openWebPage(String url, String title) {
        try {
            Intent intent = new Intent(requireActivity(), WebActivity.class);
            Bundle bundle = new Bundle();
            bundle.putString("ProductUrl", url);
            bundle.putString("title", title);
            intent.putExtras(bundle);
            requireActivity().startActivity(intent);
        } catch (Exception e) {
            MyLog.e(TAG, "打开网页失败", e);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
