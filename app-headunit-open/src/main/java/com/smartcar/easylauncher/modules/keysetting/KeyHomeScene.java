package com.smartcar.easylauncher.modules.keysetting;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bytedance.scene.group.UserVisibleHintGroupScene;
import com.bytedance.scene.interfaces.PushOptions;
import com.smartcar.easylauncher.shared.adapter.setting.KeyMapAdapter;
import com.smartcar.easylauncher.databinding.KeySettingListSceneBinding;
import com.smartcar.easylauncher.data.database.entity.KeyMapModel;
import com.smartcar.easylauncher.core.manager.KeyMapManager;

import java.util.List;

/**
 * 按键映射主页
 * 显示所有按键映射，并提供添加、编辑功能
 * 
 * <AUTHOR>
 * @date 2024/07/09
 */
public class KeyHomeScene extends UserVisibleHintGroupScene implements View.OnClickListener {
    private static final String TAG = KeyHomeScene.class.getSimpleName();
    private KeySettingListSceneBinding binding;
    private KeyMapAdapter keyMapAdapter;
    private KeyMapManager keyMapManager;
    private KeyMapAdapter adapter;
    private static final int REQUEST_CODE_EDIT_KEY_MAPPING = 1001;

    @NonNull
    @Override
    public ViewGroup onCreateView(@NonNull LayoutInflater inflater, @NonNull ViewGroup container, Bundle savedInstanceState) {
        binding = KeySettingListSceneBinding.inflate(getLayoutInflater());
        
        // 初始化按键映射管理器
        keyMapManager = KeyMapManager.getInstance(getActivity());
        
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 初始化视图
        initView();
        
        // 初始化数据
        initData();
    }

    /**
     * 初始化数据，加载按键映射列表
     */
    private void initData() {
        // 确保按键映射管理器已初始化
        keyMapManager.initialize();
        
        // 加载按键映射列表
        refreshKeyMapList();
    }

    /**
     * 刷新按键映射列表
     */
    private void refreshKeyMapList() {
        List<KeyMapModel> keyMapList = keyMapManager.getAllKeyMaps();
        adapter.setData(keyMapList);
        
        // 显示空视图或列表
        if (keyMapList.isEmpty()) {
            binding.llEmpty.setVisibility(View.VISIBLE);
        } else {
            binding.llEmpty.setVisibility(View.GONE);
        }
    }

    /**
     * 初始化视图
     */
    private void initView() {
        // 创建适配器
        adapter = new KeyMapAdapter(getActivity());
        
        // 设置点击事件监听
        adapter.setOnItemClickListener((keyMapModel) -> {
            navigateToKeyMappingEditScene(keyMapModel);
        });
        
        // 设置RecyclerView
        binding.rvKeyMaps.setLayoutManager(new LinearLayoutManager(getActivity()));
        binding.rvKeyMaps.setAdapter(adapter);
        

        // 设置添加按钮点击事件
        binding.btnAdd.setOnClickListener(v -> {
            navigateToKeyMappingEditScene(null);
        });
    }
    
    /**
     * 切换按键映射的启用状态
     * 
     * @param keyMap 要切换状态的按键映射
     */
    private void toggleKeyMapEnabled(KeyMapModel keyMap) {
        // 切换状态
        keyMap.setEnabled(keyMap.getEnabled() == 1 ? 0 : 1);
        
        // 保存更新
        keyMapManager.saveKeyMap(keyMap);
        
        // 刷新列表
        refreshKeyMapList();
    }
    
    /**
     * 跳转到按键映射编辑场景
     * 
     * @param keyMapModel 按键映射模型，如果为null则表示新建
     */
    private void navigateToKeyMappingEditScene(KeyMapModel keyMapModel) {
        Bundle arguments = KeyMappingEditScene.createArguments(keyMapModel);
        PushOptions pushOptions = new PushOptions.Builder()
                .setPushResultCallback(result -> {
                    if (result != null ) {
                        // 编辑或新建成功，刷新列表
                        refreshKeyMapList();
                    }
                })
                .build();
        getNavigationScene(this).push(KeyMappingEditScene.class, arguments, pushOptions);
    }
    


    @Override
    public void onClick(View view) {
        // 处理其他点击事件
    }

    @Override
    public void onResume() {
        super.onResume();
        // 每次恢复时刷新列表
        refreshKeyMapList();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}