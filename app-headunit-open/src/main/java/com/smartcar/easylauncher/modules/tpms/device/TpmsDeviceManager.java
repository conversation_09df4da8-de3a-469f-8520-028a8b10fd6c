package com.smartcar.easylauncher.modules.tpms.device;

import android.content.Context;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;

import androidx.annotation.Nullable;

import com.smartcar.easylauncher.infrastructure.event.scope.device.cody.UsbMessageScopeBus;
import com.smartcar.easylauncher.data.model.system.UsbMessageModel;
import com.smartcar.easylauncher.modules.tpms.core.TpmsManager;
import com.smartcar.easylauncher.modules.tpms.device.usb.UsbTpmsDevice;
import com.smartcar.easylauncher.shared.utils.MyLog;

import cody.bus.ObserverWrapper;

/**
 * TPMS设备管理器
 * <p>
 * 主要职责：
 * 1. 管理TPMS设备的生命周期
 * 2. 处理USB设备的插拔事件
 * 3. 自动检测和启动TPMS设备
 * 4. 管理设备状态和异常处理
 * </p>
 * 
 * <p>
 * 注意事项：
 * 1. 该类采用单例模式，确保全局只有一个实例
 * 2. USB设备的权限处理需要在主线程中进行
 * 3. 设备状态的维护需要考虑并发访问的问题
 * </p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class TpmsDeviceManager {
    
    /**
     * 日志标签
     */
    private static final String TAG = "TpmsDeviceManager";
    
    /**
     * 单例实例
     * volatile关键字确保多线程环境下的可见性
     */
    private static volatile TpmsDeviceManager instance;
    
    /**
     * 应用上下文
     * 使用ApplicationContext避免内存泄漏
     */
    private final Context context;
    


    /**
     * TPMS管理器实例
     * 负责具体的TPMS业务逻辑
     */
    private TpmsManager tpmsManager;
    
    /**
     * 自动启动标志
     * 控制是否在检测到设备时自动启动
     */
    private boolean autoStart = true;
    
    /**
     * USB事件观察者
     * 用于接收USB设备的插拔事件
     */
    private ObserverWrapper<UsbMessageModel> observerWrapper;

    /**
     * 私有构造函数
     * 防止外部直接创建实例
     *
     * @param context 应用上下文
     */
    private TpmsDeviceManager(Context context) {
        this.context = context.getApplicationContext();
        init();
    }

    /**
     * 初始化管理器
     * <p>
     * 主要完成以下工作：
     * 1. 注册USB事件监听
     * 2. 检查已连接的设备
     * </p>
     */
    private void init() {
        // 订阅USB事件，使用observeForever确保生命周期内持续监听
        UsbMessageScopeBus.eventBean().observeForever(observerWrapper = new ObserverWrapper<UsbMessageModel>(true) {
            @Override
            public void onChanged(@Nullable UsbMessageModel value) {
                handleUsbEvent(value);
            }
        });

        // 检查已连接的设备
        checkExistingDevices();
    }

    /**
     * 处理USB事件
     * <p>
     * 处理设备的插入和拔出事件：
     * 1. 设备插入时，如果是TPMS设备且允许自动启动，则启动设备
     * 2. 设备拔出时，如果是TPMS设备，则停止设备
     * </p>
     *
     * @param event USB事件对象
     */
    private void handleUsbEvent(UsbMessageModel event) {
        if (event == null || event.intent == null) {
            MyLog.e(TAG, "收到无效的USB事件");
            return;
        }

        String action = event.intent.getAction();
        UsbDevice device = event.intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
        
        MyLog.d(TAG, "收到USB事件: " + action);
        if (device != null) {
            MyLog.d(TAG, String.format("设备信息: vendorId=0x%04X, productId=0x%04X", 
                device.getVendorId(), device.getProductId()));
        }
        
        if (UsbManager.ACTION_USB_DEVICE_ATTACHED.equals(action)) {
            if (isTpmsDevice(device) && autoStart) {
                MyLog.d(TAG, "检测到TPMS设备插入，准备启动");
                stopTpms(); // 确保清理旧的实例
                startTpms();
            }
        } else if (UsbManager.ACTION_USB_DEVICE_DETACHED.equals(action)) {
            if (isTpmsDevice(device)) {
                MyLog.d(TAG, "检测到TPMS设备拔出，准备停止");
                stopTpms();
            }
        }
    }

    /**
     * 检查已连接的设备
     * <p>
     * 在初始化时检查是否已有TPMS设备连接：
     * 1. 获取系统USB服务
     * 2. 遍历所有已连接的USB设备
     * 3. 找到TPMS设备后启动服务
     * </p>
     * 
     * 注意：此方法需要USB权限，可能需要用户确认
     */
    private void checkExistingDevices() {
        UsbManager usbManager = (UsbManager) context.getSystemService(Context.USB_SERVICE);
        if (usbManager == null) {
            MyLog.e(TAG, "无法获取UsbManager服务");
            return;
        }

        MyLog.d(TAG, "开始检查已连接设备");
        for (UsbDevice device : usbManager.getDeviceList().values()) {
            MyLog.d(TAG, String.format("发现USB设备: vendorId=0x%04X, productId=0x%04X", 
                device.getVendorId(), device.getProductId()));
            if (isTpmsDevice(device) && autoStart) {
                MyLog.d(TAG, "检测到已连接的TPMS设备，准备启动");
                startTpms();
                break;
            }
        }
    }

    /**
     * 判断是否为TPMS设备
     * <p>
     * 通过设备的VID和PID判断是否为支持的TPMS设备
     * </p>
     *
     * @param device USB设备对象
     * @return true:是TPMS设备 false:不是TPMS设备
     */
    private boolean isTpmsDevice(UsbDevice device) {
        if (device == null) return false;
        int vendorId = device.getVendorId();
        int productId = device.getProductId();
        
        for (int[] ids : UsbTpmsDevice.SUPPORTED_DEVICES) {
            if (vendorId == ids[0] && productId == ids[1]) {
                return true;
            }
        }
        return false;
    }

    /**
     * 启动TPMS设备
     * <p>
     * 执行设备启动流程：
     * 1. 检查设备是否已启动
     * 2. 创建设备实例
     * 3. 初始化并启动设备
     * </p>
     * 
     * 可能的异常：
     * 1. 设备初始化失败
     * 2. USB权限获取失败
     * 3. 设备通信异常
     */
    public void startTpms() {
        if (tpmsManager != null && tpmsManager.isRunning()) {
            MyLog.d(TAG, "TPMS设备已启动");
            return;
        }

        try {
            MyLog.d(TAG, "开始启动TPMS设备");
            UsbTpmsDevice device = new UsbTpmsDevice(context);
            tpmsManager = TpmsManager.getInstance().setDevice(device);
            tpmsManager.start();
            MyLog.d(TAG, "TPMS设备启动完成");
        } catch (Exception e) {
            MyLog.e(TAG, "启动TPMS设备失败: " + e.getMessage());
            e.printStackTrace();
            stopTpms(); // 清理可能的部分初始化状态
        }
    }

    /**
     * 停止TPMS设备
     * <p>
     * 执行设备停止流程：
     * 1. 停止设备运行
     * 2. 释放设备资源
     * 3. 清理设备实例
     * </p>
     * 
     * 注意：
     * 1. 即使发生异常也要确保资源被释放
     * 2. 需要在finally中清空设备实例
     */
    public void stopTpms() {
        if (tpmsManager != null) {
            MyLog.d(TAG, "开始停止TPMS设备");
            try {
                tpmsManager.stop();
                tpmsManager.release();
            } catch (Exception e) {
                MyLog.e(TAG, "停止TPMS设备时发生异常: " + e.getMessage());
                e.printStackTrace();
            } finally {
                tpmsManager = null;
                MyLog.d(TAG, "TPMS设备已停止");
            }
        }
    }

    /**
     * 设置自动启动标志
     *
     * @param autoStart true:允许自动启动 false:禁止自动启动
     */
    public void setAutoStart(boolean autoStart) {
        this.autoStart = autoStart;
    }

    /**
     * 获取单例实例
     * <p>
     * 使用双重检查锁定确保线程安全
     * </p>
     *
     * @param context 应用上下文
     * @return TpmsDeviceManager实例
     */
    public static TpmsDeviceManager getInstance(Context context) {
        if (instance == null) {
            synchronized (TpmsDeviceManager.class) {
                if (instance == null) {
                    instance = new TpmsDeviceManager(context);
                }
            }
        }
        return instance;
    }

    /**
     * 释放资源
     * <p>
     * 清理所有资源并重置单例：
     * 1. 清理事件订阅
     * 2. 停止TPMS设备
     * 3. 清空单例实例
     * </p>
     */
    public void release() {
        stopTpms();
        synchronized (TpmsDeviceManager.class) {
            instance = null;
        }
        if(observerWrapper != null){
            UsbMessageScopeBus.eventBean().removeObserver(observerWrapper);
        }
    }

    /**
     * 检查设备是否正在运行
     *
     * @return true:设备正在运行 false:设备未运行
     */
    public boolean isRunning() {
        return tpmsManager != null && tpmsManager.isRunning();
    }
} 