package com.smartcar.easylauncher.modules.touch.model;

import java.util.List;

/**
 * <b>Package:</b> com.zh.touchassistant.model <br>
 * <b>FileName:</b> FloatWindowActionListModel <br>
 * <b>Create Date:</b> 2018/12/7  下午4:04 <br>
 * <b>Author:</b> zihe <br>
 * <b>Description:</b>  <br>
 */

// FloatWindowActionListModel类，继承自BaseDataModel类
public class FloatWindowActionListModel extends BaseDataModel {
    // models变量，用于存储FloatWindowActionModel对象的列表
    private List<FloatWindowActionModel> models;

    // getModels方法，用于获取FloatWindowActionModel对象的列表
    public List<FloatWindowActionModel> getModels() {
        return models;
    }

    // setModels方法，用于设置FloatWindowActionModel对象的列表
    public void setModels(List<FloatWindowActionModel> models) {
        this.models = models;
    }

    // 重写toString方法，返回FloatWindowActionListModel对象的字符串表示形式
    @Override
    public String toString() {
        return "FloatWindowActionListModel{" +
                "models=" + models +
                '}';
    }
}