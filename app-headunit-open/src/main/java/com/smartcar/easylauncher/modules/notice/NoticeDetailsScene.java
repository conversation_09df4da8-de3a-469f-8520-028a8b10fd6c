package com.smartcar.easylauncher.modules.notice;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bytedance.scene.group.UserVisibleHintGroupScene;
import com.smartcar.easylauncher.databinding.SceneNoticeDetailsBinding;
import com.smartcar.easylauncher.data.model.common.NoticeDataModel;
import com.smartcar.easylauncher.shared.widget.spanbuilder.Spans;
import com.zzhoujay.richtext.ImageHolder;
import com.zzhoujay.richtext.RichText;
import com.zzhoujay.richtext.callback.ImageFixCallback;

import java.util.Objects;

/**
 * 公告详细
 *
 * <AUTHOR>
 */
public class NoticeDetailsScene extends UserVisibleHintGroupScene {
    private SceneNoticeDetailsBinding binding;
    public NoticeDataModel.RowsDTO mItem;

    public NoticeDetailsScene(NoticeDataModel.RowsDTO item) {
        this.mItem = item;
    }


    @NonNull
    @Override
    public ViewGroup onCreateView(@NonNull LayoutInflater layoutInflater, @NonNull ViewGroup viewGroup, @Nullable Bundle bundle) {
        binding = SceneNoticeDetailsBinding.inflate(getLayoutInflater());
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        //初始化数据
        initData();
        //初始化view方法
        initView();
    }

    /**
     * 初始化view方法
     */
    private void initView() {
        binding.back.setOnClickListener(v -> Objects.requireNonNull(getNavigationScene(NoticeDetailsScene.this)).pop());

    }

    /**
     * 初始化数据
     */
    private void initData() {
        binding.title.setText(Spans.builder(requireActivity()).text(mItem.getNoticeTitle()).build());
        binding.author.setText(Spans.builder(requireActivity()).text("发布人：").text(mItem.getCreateBy()).build());
        binding.time.setText(Spans.builder(requireActivity()).text("发布时间：").text(mItem.getCreateTime()).build());


        RichText.initCacheDir(requireApplicationContext());
        RichText.debugMode = true;

        RichText.from(mItem.getNoticeContent())
                .autoFix(true) // 是否自动修复，默认true
                .fix(new ImageFixCallback() {
                    @Override
                    public void onInit(ImageHolder imageHolder) {
                        imageHolder.setSize(200, 200);
                        if (imageHolder.getSource().contains("profile/upload")) {
                            imageHolder.setSource("https://www.yxyyds.cn" + imageHolder.getSource());
                        }
                    }

                    @Override
                    public void onLoading(ImageHolder imageHolder) {

                    }

                    @Override
                    public void onSizeReady(ImageHolder imageHolder, int i, int i1, ImageHolder.SizeHolder sizeHolder) {
                        imageHolder.setSize(i, i1);
                    }

                    @Override
                    public void onImageReady(ImageHolder imageHolder, int i, int i1) {
                        imageHolder.setSize(i, i1);
                    }

                    @Override
                    public void onFailure(ImageHolder imageHolder, Exception e) {

                    }
                })
                .urlClick(url -> {
                    if (url.startsWith("code://")) {
                        Toast.makeText(getActivity(), url.replaceFirst("code://", ""), Toast.LENGTH_SHORT).show();
                        return true;
                    }
                    return false;
                })
                .into(binding.text);
    }


}
