package com.smartcar.easylauncher.modules.tpms.device;

import java.nio.ByteBuffer;

/**
 * TPMS设备接口
 * 定义与TPMS硬件设备通信的基本行为
 */
public interface TpmsDevice {
    /**
     * 打开设备
     * @return 是否成功
     */
    boolean open();
    
    /**
     * 关闭设备
     */
    void close();
    
    /**
     * 发送数据
     * @param data 要发送的数据
     * @return 是否发送成功
     */
    boolean send(byte[] data);
    
    /**
     * 设置数据接收回调
     * @param callback 数据接收回调接口
     */
    void setDataCallback(DataCallback callback);
    
    /**
     * 设置设备状态回调
     * @param callback 设备状态回调接口
     */
    void setStateCallback(StateCallback callback);
    
    /**
     * 数据接收回调接口
     */
    interface DataCallback {
        /**
         * 收到数据回调
         * @param buffer 接收到的数据
         */
        void onDataReceived(ByteBuffer buffer);
    }
    
    /**
     * 设备状态回调接口
     */
    interface StateCallback {
        /**
         * 设���连接状态改变
         * @param connected 是否连接
         */
        void onConnectionStateChanged(boolean connected);
        
        /**
         * 设备错误回调
         * @param errorCode 错误码
         * @param message 错误信息
         */
        void onError(int errorCode, String message);
    }
} 