package com.smartcar.easylauncher.modules.tpms.core;

import com.smartcar.easylauncher.modules.tpms.protocol.base.model.TpmsData;
import com.smartcar.easylauncher.modules.tpms.protocol.base.model.TireStatusData;

/**
 * TPMS监听器接口
 */
public interface TpmsListener {
    /**
     * 轮胎状态更新
     */
    void onTireStatus(TireStatusData data);
    
    /**
     * 设备状态更新
     */
    void onDeviceStatus(TpmsData data);
    
    /**
     * 设备连接状态改变
     */
    void onDeviceConnectionChanged(boolean connected);
    
    /**
     * 发生错误
     */
    void onError(String error);
} 