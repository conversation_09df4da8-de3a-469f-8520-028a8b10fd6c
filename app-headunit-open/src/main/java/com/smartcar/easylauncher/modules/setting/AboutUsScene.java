package com.smartcar.easylauncher.modules.setting;


import android.content.Intent;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.bytedance.scene.group.UserVisibleHintGroupScene;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.BuildConfig;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.databinding.FragmentAboutusSettingBinding;
import com.smartcar.easylauncher.shared.dialog.QRCodeDialog;
import com.smartcar.easylauncher.shared.dialog.UpdateChecker;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.modules.other.WebActivity;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.NetworkUtils;
import com.smartcar.easylauncher.shared.utils.system.DeviceUtil;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.schedulers.Schedulers;


/**
 * 关于我们页面
 *
 * <AUTHOR>
 */
public class AboutUsScene extends UserVisibleHintGroupScene {
    public static final String TAG = AboutUsScene.class.getSimpleName();
    private FragmentAboutusSettingBinding binding;
    
    // 用于管理RxJava订阅
    protected final CompositeDisposable disposables = new CompositeDisposable();

    // 连续点击相关常量
    private static final int COUNTS = 5;
    private static final long DURATION = 2000;
    private long mLastClickTime = 0;
    private int mClickCount = 0;

    // 社交媒体类型常量
    private static final int SOCIAL_WEIBO = 1;
    private static final int SOCIAL_BILIBILI = 2;
    private static final int SOCIAL_WECHAT = 3;
    private static final int SOCIAL_QQ_GROUP = 4;
    private FragmentActivity fragmentActivity;

    @Override
    public ViewGroup onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        MyLog.d(TAG, "onCreateView");
        if (binding == null) {
            binding = FragmentAboutusSettingBinding.inflate(inflater, container, false);
        }
        fragmentActivity = (FragmentActivity) requireActivity();
        return (ViewGroup) binding.getRoot();
    }


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        MyLog.d(TAG, "onViewCreated");
        // 初始化基本UI
        initBasicUI();
        // 设置点击事件
        setupClickListeners();
        // 加载开发者模式状态
        loadDeveloperModeStatus();
    }


    private void initBasicUI() {
        // 设置版本信息 - 避免每次都格式化字符串
        String versionInfo = String.format("v %s  (  %s )   %s",
                DeviceUtil.getVersionName(getActivity()),
                BuildConfig.VERSION_TYPE,
                Const.CHANNEL_NAME);
        binding.tvVersion.setText(versionInfo);

        // 初始化开发者按钮（默认隐藏）
        binding.btDeveloper.setVisibility(View.GONE);
    }

    private void setupClickListeners() {
        // 使用更高效的点击监听器设置方式
        View.OnClickListener clickListener = this::handleClick;

        // 为所有按钮设置同一个点击监听器，减少对象创建
        binding.btCheckUpdates.setOnClickListener(clickListener);
        binding.tvFuwu.setOnClickListener(clickListener);
        binding.tvIcp.setOnClickListener(clickListener);
        binding.tvYinsi.setOnClickListener(clickListener);
        binding.tvBangzhu.setOnClickListener(clickListener);
        binding.tvVersion.setOnClickListener(clickListener);
        binding.btDeveloper.setOnClickListener(clickListener);
        binding.ivZhicheLogo.setOnClickListener(clickListener);

        // 添加社交媒体按钮点击事件
        binding.socialMediaGrid.weiboCard.setOnClickListener(clickListener);
        binding.socialMediaGrid.bilibiliCard.setOnClickListener(clickListener);
        binding.socialMediaGrid.wechatCard.setOnClickListener(clickListener);
        binding.socialMediaGrid.qqCard.setOnClickListener(clickListener);
    }

    // 统一处理点击事件，减少匿名内部类创建
    private void handleClick(View view) {
        int id = view.getId();

        if (id == R.id.bt_check_updates) {
            handleCheckUpdates();
        } else if (id == R.id.tv_fuwu) {
            openWebView(Const.USER_PROTOCOL);
        } else if (id == R.id.tv_icp) {
            openWebView(Const.ICP);
        } else if (id == R.id.tv_yinsi) {
            openWebView(Const.PRIVACY_POLICY);
        } else if (id == R.id.tv_bangzhu) {
            showSocialQRCode(SOCIAL_QQ_GROUP);
        } else if (id == R.id.bt_developer) {
            checkForUpdates(true);
        } else if (id == R.id.tv_version || id == R.id.iv_zhiche_logo) {
            handleOptimizedContinuousClick();
        } else if (id == R.id.weibo_card) {
            showSocialQRCode(SOCIAL_WEIBO);
        } else if (id == R.id.bilibili_card) {
            showSocialQRCode(SOCIAL_BILIBILI);
        } else if (id == R.id.wechat_card) {
            showSocialQRCode(SOCIAL_WECHAT);
        } else if (id == R.id.qq_card) {
            showSocialQRCode(SOCIAL_QQ_GROUP);
        }
    }

    /**
     * 显示社交媒体二维码弹窗
     *
     * @param socialType 社交媒体类型
     */
    private void showSocialQRCode(int socialType) {
        String title = "";
        String subtitle = "";
        String description = "";
        int qrCodeResId = 0;

        switch (socialType) {
            case SOCIAL_WEIBO:
                title = getString(R.string.follow_weibo);
                subtitle = getString(R.string.scan_qrcode_subtitle);
                description = getString(R.string.weibo_desc);
                qrCodeResId = R.drawable.icon_qqqun;
                break;

            case SOCIAL_BILIBILI:
                title = getString(R.string.follow_bilibili);
                subtitle = getString(R.string.scan_qrcode_subtitle);
                description = getString(R.string.bilibili_desc);
                qrCodeResId = R.drawable.icon_qqqun;
                break;

            case SOCIAL_WECHAT:
                title = getString(R.string.follow_wechat);
                subtitle = getString(R.string.scan_qrcode_subtitle);
                description = getString(R.string.wechat_desc);
                qrCodeResId = R.drawable.publicnumber;
                break;

            case SOCIAL_QQ_GROUP:
                title = getString(R.string.join_qq_group);
                subtitle = getString(R.string.scan_qrcode_subtitle);
                description = getString(R.string.qq_group_desc);
                qrCodeResId = R.drawable.icon_qqqun;
                break;
        }

        // 加载二维码图片
        if (qrCodeResId != 0) {
            QRCodeDialog.showQRCodeDialog(
                    fragmentActivity.getSupportFragmentManager(),
                    requireActivity(),
                    title,
                    subtitle,
                    description,
                    BitmapFactory.decodeResource(getResources(), qrCodeResId),
                    false,
                    new QRCodeDialog.DialogClickListener() {
                        @Override
                        public void onPositiveClick() {
                            // 点击确定按钮的操作
                        }
                    }
            );
        }
    }

    // 优化后的连续点击处理方法
    private void handleOptimizedContinuousClick() {
        long currentTime = SystemClock.uptimeMillis();
        if (currentTime - mLastClickTime > DURATION) {
            // 超时，重置计数
            mClickCount = 1;
        } else {
            // 增加点击计数
            mClickCount++;
        }

        // 记录最后点击时间
        mLastClickTime = currentTime;

        // 达到目标点击次数
        if (mClickCount >= COUNTS) {
            mClickCount = 0; // 重置计数
            toggleDeveloperMode();

            // 添加日志以便调试
            MyLog.d(TAG, "Developer mode toggle triggered");
        }
    }

    // 切换开发者模式
    private void toggleDeveloperMode() {
        // 添加日志以便调试
        MyLog.d(TAG, "Toggling developer mode");

        disposables.add(Observable.fromCallable(() -> {
                    boolean newDevMode = !SettingsManager.getDeveloperMode();
                    SettingsManager.setDeveloperMode(newDevMode);
                    return newDevMode;
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                        newDevMode -> {
                            // 添加日志以便调试
                            MyLog.d(TAG, "Developer mode set to: " + newDevMode);
                            updateDeveloperMode(newDevMode);

                        },
                        throwable -> MyLog.e(TAG, "Error toggling developer mode", throwable)
                ));
    }

    // 直接加载开发者模式状态
    private void loadDeveloperModeStatus() {
        disposables.add(Observable.fromCallable(SettingsManager::getDeveloperMode)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                        this::updateDeveloperMode,
                        throwable -> MyLog.e(TAG, "Error loading developer mode", throwable)
                ));
    }



    private void handleCheckUpdates() {
        // 网络检查放在UI线程，避免不必要的线程切换
        if (!NetworkUtils.isNetworkConnected(getActivity())) {
            MToast.makeTextShort(getString(R.string.no_network));
            return;
        }
        checkForUpdates(false);
    }

    private void checkForUpdates(boolean isDeveloperMode) {
        // 优化更新检查流程
        disposables.add(Observable.fromCallable(() -> {
                    // 创建更新检查器
                    UpdateChecker updateChecker = createUpdateChecker(isDeveloperMode);

                    // 根据模式选择检查方法
                    if (isDeveloperMode) {
                        updateChecker.checkForUpdatesFromCloud();
                    } else {
                        updateChecker.checkForUpdates();
                    }
                    return true;
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                        result -> {
                        }, // 不需要处理结果
                        throwable -> MyLog.e(TAG, "Error checking for updates", throwable)
                ));
    }

    // 提取创建UpdateChecker的方法，提高代码可读性
    private UpdateChecker createUpdateChecker(boolean isDeveloperMode) {
        return new UpdateChecker.UpdateCheckerBuilder()
                .withContext(getActivity())
                .withFragmentManager(fragmentActivity.getSupportFragmentManager())
                .withDisableBackButton(false)
                .withUpdateListener(new UpdateChecker.UpdateListener() {
                    @Override
                    public void onUpdateSuccess() {
                        MyLog.v("检测更新", "更新成功");
                    }

                    @Override
                    public void onNoUpdate() {
                        if (!isDeveloperMode) {
                            MToast.makeTextShort("没有发现新版本");
                        }
                        MyLog.v("检测更新", "没有更新");
                    }

                    @Override
                    public void onUpdateFailed(Exception e) {
                        MToast.makeTextShort("检测更新失败，请联系开发者");
                        MyLog.v("检测更新", "检测更新失败" + e.getMessage());
                    }
                }).build();
    }

    private void openWebView(String url) {
        // 优化WebView打开流程，减少不必要的线程切换
        Intent intent = new Intent(getActivity(), WebActivity.class);
        Bundle bundle = new Bundle();
        bundle.putString("ProductUrl", url);
        intent.putExtras(bundle);
        requireActivity().startActivity(intent);
    }

    private void updateDeveloperMode(boolean enabled) {
        // 添加日志以便调试
        MyLog.d(TAG, "Updating developer mode UI: " + enabled);

        if (binding != null) {
            binding.btDeveloper.setVisibility(enabled ? View.VISIBLE : View.GONE);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        MyLog.d(TAG, "onDestroyView");
        // 清理资源
        disposables.clear();
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        disposables.dispose();
    }
}
