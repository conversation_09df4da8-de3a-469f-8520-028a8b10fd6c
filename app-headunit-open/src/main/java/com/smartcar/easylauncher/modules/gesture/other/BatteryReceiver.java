package com.smartcar.easylauncher.modules.gesture.other;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.BatteryManager;
import android.os.Build;

import com.smartcar.easylauncher.modules.gesture.util.GlobalState;


public class <PERSON><PERSON><PERSON>eiver extends BroadcastReceiver {
    private boolean powerConnected = false;
    public BatteryReceiver(){}

    public BatteryReceiver(Context context) {
        try {
            BatteryManager batteryManager = (BatteryManager) context.getSystemService(Context.BATTERY_SERVICE);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                GlobalState.batteryCapacity = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY);
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                powerConnected = notCharging(batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_STATUS));
            }
        } catch (Exception ignored) {
        }
    }

    private boolean notCharging(int state) {
        return state != BatteryManager.BATTERY_STATUS_NOT_CHARGING && state != BatteryManager.BATTERY_STATUS_DISCHARGING;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent == null) {
            return;
        }
        String action = intent.getAction();
        if (action == null) {
            return;
        }

        int capacity = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1);
        switch (action) {
            case Intent.ACTION_BATTERY_CHANGED: {
                powerConnected = notCharging(intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1));
                break;
            }
            case Intent.ACTION_POWER_DISCONNECTED: {
                powerConnected = false;
                break;
            }
            case Intent.ACTION_POWER_CONNECTED: {
                powerConnected = true;
                break;
            }
            default:
        }
        if (capacity != GlobalState.batteryCapacity) {
            GlobalState.batteryCapacity = capacity;
            if (GlobalState.updateBar != null) {
                GlobalState.updateBar.run();
            }
        }
    }
}
