package com.smartcar.easylauncher.modules.theme;

import android.annotation.SuppressLint;
import android.os.Bundle;

import androidx.annotation.NonNull;

import com.bytedance.scene.Scene;
import com.bytedance.scene.navigation.NavigationScene;
import com.bytedance.scene.navigation.NavigationSceneOptions;
import com.bytedance.scene.utlity.SceneInstanceUtility;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.widget.navigation.TabNavigationViewScene;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;


/**
 * 主题导航场景
 * 提供主题相关功能的底部导航，包括推荐主题、我的主题和帮助页面
 *
 * <AUTHOR>
 * @date 2024/3/15
 */
public class ThemeTabNavigationScene extends TabNavigationViewScene {

    /**
     * 重写此方法以返回底部导航项的布局ID
     *
     * @return 底部导航项的布局ID
     */
    @Override
    protected int getTabItemLayout() {
        return R.layout.layout_text_view;
    }

    /**
     * 重写此方法以返回底部导航项的标题列表
     *
     * @return 底部导航项的标题列表
     */
    @Override
    protected List<String> getNavigationTitles() {
        List<String> titles = new ArrayList<>();
        // 添加主题相关的导航项标题
        titles.add(getString(R.string.recommend_theme));  // 推荐主题
        titles.add(getString(R.string.my_theme));         // 我的主题
        return titles;
    }

    /**
     * 重写此方法以返回整个导航场景的标题
     *
     * @return 导航场景的标题
     */
    @Override
    protected String getTitleName() {
        return getString(R.string.car_theme);  // 主题
    }

    /**
     * 获取底部导航项与对应场景的映射关系
     *
     * @return 底部导航项与对应场景的映射关系
     */
    @NonNull
    @Override
    @SuppressLint("RestrictedApi")
    public LinkedHashMap<Integer, Scene> getSceneMap() {
        LinkedHashMap<Integer, Scene> linkedHashMap = new LinkedHashMap<>();

        // 为推荐主题创建并配置场景
        NavigationScene navigationScene = (NavigationScene) SceneInstanceUtility.getInstanceFromClass(
                NavigationScene.class,
                new NavigationSceneOptions(FindThemeScene.class, getBundle(0)).toBundle()
        );
        linkedHashMap.put(0, navigationScene);

        // 为我的主题创建并配置场景
        navigationScene = (NavigationScene) SceneInstanceUtility.getInstanceFromClass(
                NavigationScene.class,
                new NavigationSceneOptions(MyThemeScene.class, getBundle(1)).toBundle()
        );
        linkedHashMap.put(1, navigationScene);

        return linkedHashMap;
    }

    /**
     * 创建一个包含索引的Bundle对象
     *
     * @param index 要放入Bundle的索引值
     * @return 包含索引的Bundle
     */
    private Bundle getBundle(int index) {
        Bundle bundle = new Bundle();
        // 将索引值放入Bundle
        bundle.putInt("index", index);
        return bundle;
    }
}

