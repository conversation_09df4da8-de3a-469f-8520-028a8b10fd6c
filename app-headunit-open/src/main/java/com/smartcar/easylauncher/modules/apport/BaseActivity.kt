package com.smartcar.easylauncher.modules.apport

import android.os.Bundle
import android.widget.Button
import androidx.annotation.StringRes
import androidx.viewbinding.ViewBinding
import com.smartcar.easylauncher.R
import com.smartcar.easylauncher.core.base.BaseActivity
import com.smartcar.easylauncher.shared.utils.apportutil.inflateViewBinding

/**
 * <AUTHOR>
 * date 2023/5/12
 * email <EMAIL>
 * desc
 */
abstract class BaseActivity<VB : ViewBinding> : BaseActivity() {
    protected lateinit var binding: VB

    protected val TAG = this.javaClass.simpleName

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = inflateViewBinding(layoutInflater)
        setContentView(binding.root)
        initData()
    }

//    open fun setSystemBarRed(activity: Activity?) {
//        if (Build.VERSION.SDK_INT != Build.VERSION_CODES.KITKAT) {
//            ImmersionBar.with(activity!!)
//                .fullScreen(false) //  .hideBar(BarHide.FLAG_HIDE_BAR)//隐藏状态栏  需要二次点击才能返回
//                .init()
//        }
//    }

    fun setToolbar(@StringRes id: Int) {
        setToolbar(getString(id))
    }

    open fun setToolbar(title: String) {
        findViewById<Button>(R.id.more_btn_back)?.run {
            setOnClickListener {
                finish()
            }
        }

    }


    protected abstract fun initData()
}