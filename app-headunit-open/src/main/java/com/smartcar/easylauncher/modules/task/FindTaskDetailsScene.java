package com.smartcar.easylauncher.modules.task;

import static com.bytedance.scene.navigation.NavigationSceneGetter.requireNavigationScene;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.shared.adapter.task.TaskConditionPreviewAdapter;
import com.smartcar.easylauncher.shared.adapter.task.TaskResultPreviewAdapter;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.databinding.SceneFindTaskDetailsBinding;
import com.smartcar.easylauncher.infrastructure.event.cody.TaskNotificationScopeBus;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.data.model.task.ConditionModel;
import com.smartcar.easylauncher.data.model.task.RecommendTaskModel;
import com.smartcar.easylauncher.data.model.task.ResultModel;
import com.smartcar.easylauncher.data.model.task.TaskModel;
import com.smartcar.easylauncher.data.model.task.TaskNotificationModel;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.util.ArrayList;
import java.util.List;

/**
 * 推荐任务预览页面
 * 用于展示推荐任务的详细信息，并提供添加到我的任务的功能
 * 
 * <AUTHOR>
 */
public class FindTaskDetailsScene extends BaseScene {
    public static final String TAG = FindTaskDetailsScene.class.getSimpleName();
    
    private SceneFindTaskDetailsBinding binding;
    private RecommendTaskModel recommendTask;
    private TaskConditionPreviewAdapter conditionAdapter;
    private TaskResultPreviewAdapter resultAdapter;

    /**
     * 创建推荐任务预览Scene的Bundle参数
     */
    public static Bundle createArguments(RecommendTaskModel recommendTask) {
        Bundle bundle = new Bundle();
        bundle.putSerializable("recommend_task", recommendTask);
        return bundle;
    }

    @NonNull
    @Override
    protected ViewGroup onCreateNewView(@NonNull LayoutInflater layoutInflater, @Nullable ViewGroup viewGroup, @Nullable Bundle bundle) {
        binding = SceneFindTaskDetailsBinding.inflate(layoutInflater, viewGroup, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 获取传入的推荐任务数据
        Bundle arguments = getArguments();
        if (arguments != null) {
            recommendTask = (RecommendTaskModel) arguments.getSerializable("recommend_task");
        }
        
        if (recommendTask == null) {
            MyLog.e(TAG, "推荐任务数据为空");
            MToast.makeTextShort("任务数据错误");
            requireNavigationScene(this).pop();
            return;
        }
        
        initViews();
        setupData();
        setupListeners();
    }

    /**
     * 初始化视图
     */
    private void initViews() {

        // 设置条件列表 - 使用标准LinearLayoutManager
        conditionAdapter = new TaskConditionPreviewAdapter();
        binding.rvConditions.setLayoutManager(new LinearLayoutManager(requireActivity()));
        binding.rvConditions.setAdapter(conditionAdapter);

        // 设置结果列表 - 使用标准LinearLayoutManager
        resultAdapter = new TaskResultPreviewAdapter();
        binding.rvResults.setLayoutManager(new LinearLayoutManager(requireActivity()));
        binding.rvResults.setAdapter(resultAdapter);

        MyLog.d(TAG, "initViews: RecyclerView初始化完成");
    }

    /**
     * 设置数据
     */
    private void setupData() {
        if (recommendTask == null) {
            MyLog.e(TAG, "setupData: recommendTask is null");
            return;
        }

        MyLog.d(TAG, "setupData: 开始设置推荐任务数据");
        MyLog.d(TAG, "setupData: 任务名称=" + recommendTask.getTaskName());
        MyLog.d(TAG, "setupData: 任务ID=" + recommendTask.getId());

        // 设置基本信息
        binding.tvTaskName.setText(recommendTask.getTaskName());
        binding.tvTaskDescription.setText(recommendTask.getDescription());
        binding.tvTaskCategory.setText(recommendTask.getCategory());
        binding.tvTaskRating.setText(recommendTask.getRatingStars());
        binding.tvUsageCount.setText(formatUsageCount(recommendTask.getUsageCount()));

        // 设置可重复标识
        if (recommendTask.isRepeatable()) {
            binding.tvRepeatable.setText("可重复执行");
            binding.tvRepeatable.setVisibility(View.VISIBLE);
        } else {
            binding.tvRepeatable.setText("单次执行");
            binding.tvRepeatable.setVisibility(View.VISIBLE);
        }

        // 设置热门/新品标签
        if (recommendTask.isHot()) {
            binding.tvHotTag.setText("热门推荐");
            binding.tvHotTag.setVisibility(View.VISIBLE);
        } else if (recommendTask.isNew()) {
            binding.tvHotTag.setText("新品推荐");
            binding.tvHotTag.setVisibility(View.VISIBLE);
        } else {
            binding.tvHotTag.setVisibility(View.GONE);
        }

        // 设置标签
        if (recommendTask.getTags() != null && !recommendTask.getTags().isEmpty()) {
            StringBuilder tagsBuilder = new StringBuilder();
            for (int i = 0; i < recommendTask.getTags().size(); i++) {
                if (i > 0) tagsBuilder.append(" • ");
                tagsBuilder.append(recommendTask.getTags().get(i));
            }
            binding.tvTags.setText(tagsBuilder.toString());
            binding.tvTags.setVisibility(View.VISIBLE);
        } else {
            binding.tvTags.setVisibility(View.GONE);
        }

        // 设置条件数据
        if (recommendTask.getConditions() != null && !recommendTask.getConditions().isEmpty()) {
            MyLog.d(TAG, "setupData: 条件数量=" + recommendTask.getConditions().size());
            for (int i = 0; i < recommendTask.getConditions().size(); i++) {
                ConditionModel condition = recommendTask.getConditions().get(i);
                MyLog.d(TAG, "setupData: 条件" + i + " - ID=" + condition.getId() + ", 名称=" + condition.getName());
            }
            conditionAdapter.submitList(recommendTask.getConditions());
            binding.cardConditions.setVisibility(View.VISIBLE);
        } else {
            MyLog.w(TAG, "setupData: 没有条件数据");
            binding.cardConditions.setVisibility(View.GONE);
        }

        // 设置结果数据 - 使用NestedScrollView + ConstraintLayout的优雅方案
        if (recommendTask.getResults() != null && !recommendTask.getResults().isEmpty()) {
            MyLog.d(TAG, "setupData: 结果数量=" + recommendTask.getResults().size());
            for (int i = 0; i < recommendTask.getResults().size(); i++) {
                ResultModel result = recommendTask.getResults().get(i);
                MyLog.d(TAG, "setupData: 结果" + i + " - ID=" + result.getId() +
                        ", 名称=" + result.getName() +
                        ", 类型=" + result.getType() +
                        ", 执行=" + result.getExecute() +
                        ", 延时=" + result.getDelayTime());
                if (result.getAppInfo() != null) {
                    MyLog.d(TAG, "setupData: 结果" + i + " - AppInfo应用名=" + result.getAppInfo().getAppName());
                }
            }

            MyLog.d(TAG, "setupData: 设置结果适配器数据");
            resultAdapter.submitList(recommendTask.getResults());
            binding.cardResults.setVisibility(View.VISIBLE);
            MyLog.d(TAG, "setupData: 结果区域已设置为可见");
        } else {
            MyLog.w(TAG, "setupData: 没有结果数据或结果列表为空");
            if (recommendTask.getResults() == null) {
                MyLog.w(TAG, "setupData: getResults() 返回 null");
            } else {
                MyLog.w(TAG, "setupData: getResults() 返回空列表，大小=" + recommendTask.getResults().size());
            }
            binding.cardResults.setVisibility(View.GONE);
        }

        MyLog.d(TAG, "setupData: 数据设置完成");
    }

    /**
     * 设置监听器
     */
    private void setupListeners() {
        // 添加到我的任务按钮
        binding.btnSave.setOnClickListener(v -> addToMyTasks());
        
        // 取消按钮
        binding.btnCancel.setOnClickListener(v -> requireNavigationScene(this).pop());
    }

    /**
     * 添加到我的任务
     */
    private void addToMyTasks() {
        if (recommendTask == null) {
            MToast.makeTextShort("任务信息无效");
            return;
        }

        try {
            // 转换推荐任务为普通任务
            TaskModel newTask = convertRecommendTaskToTask(recommendTask);

            // 获取当前任务列表
            String taskListJson = DataManager.getTask();
            List<TaskModel> taskList = new ArrayList<>();
            if (taskListJson != null && !taskListJson.isEmpty()) {
                try {
                    taskList = new Gson().fromJson(taskListJson, new TypeToken<List<TaskModel>>() {}.getType());
                } catch (JsonSyntaxException e) {
                    MyLog.e(TAG, "解析任务列表JSON失败: " + e.getMessage());
                    taskList = new ArrayList<>();
                }
            }

            // 检查是否已存在相同任务
            boolean exists = taskList.stream().anyMatch(task ->
                task.getTaskName().equals(newTask.getTaskName()));

            if (exists) {
                MToast.makeTextShort("该任务已存在于我的任务中");
                return;
            }

            // 添加新任务
            taskList.add(newTask);

            // 保存到本地
            DataManager.setTask(new Gson().toJson(taskList));

            // 发送通知
            TaskNotificationScopeBus.eventBean().post(new TaskNotificationModel(1, newTask));

            // 显示成功提示并返回
            MToast.makeTextShort("「" + recommendTask.getTaskName() + "」已添加成功");
            MyLog.d(TAG, "成功添加推荐任务: " + recommendTask.getTaskName());

            // 返回上一页
            requireNavigationScene(this).pop();

        } catch (Exception e) {
            MToast.makeTextShort("添加任务失败，请稍后重试");
            MyLog.e(TAG, "添加推荐任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将推荐任务转换为普通任务
     */
    private TaskModel convertRecommendTaskToTask(RecommendTaskModel recommendTask) {
        TaskModel task = new TaskModel();
        task.setId(String.valueOf(System.currentTimeMillis()));
        task.setSaveTime(String.valueOf(System.currentTimeMillis()));
        task.setStatus(false); // 默认关闭状态
        task.setModel(recommendTask.isRepeatable()); // 根据推荐任务设置是否可重复
        task.setTaskName(recommendTask.getTaskName());
        task.setConditions(recommendTask.getConditions());
        task.setResults(recommendTask.getResults());
        return task;
    }

    /**
     * 格式化使用人数显示
     */
    private String formatUsageCount(int count) {
        if (count >= 10000) {
            return String.format("%.1fw人使用", count / 10000.0);
        } else if (count >= 1000) {
            return String.format("%.1fk人使用", count / 1000.0);
        } else {
            return count + "人使用";
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        MyLog.d(TAG, "onDestroyView: 清理推荐任务预览页面资源");
        
        // 清理资源
        recommendTask = null;
        conditionAdapter = null;
        resultAdapter = null;
        binding = null;
    }
}
