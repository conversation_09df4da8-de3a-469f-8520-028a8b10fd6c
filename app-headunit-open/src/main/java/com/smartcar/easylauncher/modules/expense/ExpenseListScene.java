package com.smartcar.easylauncher.modules.expense;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bytedance.scene.animation.SharedElementSceneTransitionExecutor;
import com.bytedance.scene.animation.interaction.scenetransition.SceneTransition;
import com.bytedance.scene.animation.interaction.scenetransition.visiblity.Fade;
import com.bytedance.scene.group.UserVisibleHintGroupScene;
import com.bytedance.scene.interfaces.PushOptions;
import com.bytedance.scene.interfaces.PushResultCallback;
import com.bytedance.scene.ktx.NavigationSceneExtensionsKt;
import com.chad.library.adapter.base.entity.node.BaseNode;
import com.google.gson.Gson;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.common.NodeSectionAdapter;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.core.constants.StatusCodeModel;
import com.smartcar.easylauncher.databinding.SceneCostListBinding;
import com.smartcar.easylauncher.data.database.dbmager.CostDbManager;
import com.smartcar.easylauncher.data.database.entity.Expense;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.core.manager.UserManager;
import com.smartcar.easylauncher.data.model.common.ItemNode;
import com.smartcar.easylauncher.data.model.common.RootNode;
import com.smartcar.easylauncher.data.model.vehicle.ExpenseListModel;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import rxhttp.RxHttp;

/**
 * 消费列表场景
 * <AUTHOR>
 */
public class ExpenseListScene extends UserVisibleHintGroupScene {
    private static final String TAG = ExpenseListScene.class.getName();
    private SceneCostListBinding binding;

    @NonNull
    @Override
    public ViewGroup onCreateView(@NonNull LayoutInflater layoutInflater, @NonNull ViewGroup viewGroup, @Nullable Bundle bundle) {
        binding = SceneCostListBinding.inflate(layoutInflater, viewGroup, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initDatabase();
        //   fetchExpenses();
        setupUIData();
    }

    /**
     * 初始化数据库和DAO
     */
    @SuppressLint("CheckResult")
    private void initDatabase() {
        String url = Const.VEHICLE_EXPENSE_LIST;
        MyLog.v(TAG, "请求地址：" + url);
        RxHttp.get(url)
                .toObservable(ExpenseListModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(vehicleCarModel -> {
                    if (vehicleCarModel.getCode() == StatusCodeModel.SUCCESS) {
                        // 请求成功
                        MyLog.v(TAG, "请求成功" + new Gson().toJson(vehicleCarModel));
                        List<Expense> rows = vehicleCarModel.getRows();
                        DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                                .appendPattern("yyyy-MM")
                                .appendPattern("-[d][dd]")  // 这允许单位数和双位数的日期
                                .toFormatter();
                        // 使用 stream 来排序和分组
                        Map<String, List<Expense>> expensesByMonth = rows.stream()
                                .sorted(Comparator.comparing(expense -> LocalDate.parse(expense.getDate(), formatter)))
                                .collect(Collectors.groupingBy(
                                        expense -> YearMonth.parse(expense.getDate().substring(0, 7), DateTimeFormatter.ofPattern("yyyy-MM"))
                                                .format(DateTimeFormatter.ofPattern("yyyy-MM")),
                                        Collectors.toList()
                                ));
                        // 将 Map 转换为 List<BaseNode>
                        List<BaseNode> monthNodes = new ArrayList<>();
                        for (Map.Entry<String, List<Expense>> entry : expensesByMonth.entrySet()) {
                            String monthYear = entry.getKey();
                            List<Expense> expensesForMonth = entry.getValue();
                            // 创建月份节点
                            RootNode monthNode = new RootNode(
                                    expensesForMonth.stream()
                                            .map(ItemNode::new) // 假设 ItemNode 有合适的构造函数
                                            .collect(Collectors.toList()),
                                    monthYear
                            );
                            monthNode.setExpanded(monthNodes.isEmpty());
                            monthNodes.add(monthNode);
                        }

                        // 假设你有一个方法来设置列表视图以显示月份节点
                        setupListView(monthNodes);
                    }
                    // 请求成功
                    MyLog.v(TAG, "请求成功" + vehicleCarModel);
                }, throwable -> {
                    // 请求失败

                    MyLog.v(TAG, "请求失败" + throwable.getMessage());
                });
    }


    private void fetchExpenses() {
        CostDbManager.getInstance().getExpensesByCarId(DataManager.getCarId()).subscribe(new Consumer<List<Expense>>() {
            @Override
            public void accept(List<Expense> expenses) throws Exception {
                DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                        .appendPattern("yyyy-MM")
                        .appendPattern("-[d][dd]")  // 这允许单位数和双位数的日期
                        .toFormatter();
                // 使用 stream 来排序和分组
                Map<String, List<Expense>> expensesByMonth = expenses.stream()
                        .sorted(Comparator.comparing(expense -> LocalDate.parse(expense.getDate(), formatter)))
                        .collect(Collectors.groupingBy(
                                expense -> YearMonth.parse(expense.getDate().substring(0, 7), DateTimeFormatter.ofPattern("yyyy-MM"))
                                        .format(DateTimeFormatter.ofPattern("yyyy-MM")),
                                Collectors.toList()
                        ));
                // 将 Map 转换为 List<BaseNode>
                List<BaseNode> monthNodes = new ArrayList<>();
                for (Map.Entry<String, List<Expense>> entry : expensesByMonth.entrySet()) {
                    String monthYear = entry.getKey();
                    List<Expense> expensesForMonth = entry.getValue();
                    // 创建月份节点
                    RootNode monthNode = new RootNode(
                            expensesForMonth.stream()
                                    .map(expense -> new ItemNode(expense)) // 假设 ItemNode 有合适的构造函数
                                    .collect(Collectors.toList()),
                            monthYear
                    );
                    monthNode.setExpanded(monthNodes.isEmpty());
                    monthNodes.add(monthNode);
                }

                // 假设你有一个方法来设置列表视图以显示月份节点
                setupListView(monthNodes);
            }
        });

    }


    /**
     * 在UI线程上设置列表视图
     */
    private void setupListView(List<BaseNode> monthNodes) {
        binding.rvExpensesList.setLayoutManager(new LinearLayoutManager(requireActivity()));
        final NodeSectionAdapter nodeAdapter = new NodeSectionAdapter();
        binding.rvExpensesList.setAdapter(nodeAdapter);
        nodeAdapter.setList(monthNodes);
        nodeAdapter.addChildClickViewIds(R.id.view_all);
        nodeAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            if (view.getId() == R.id.view_all) {
                ItemNode itemNode = (ItemNode) adapter.getItem(position);
//                long expenseId = itemNode.getExpenseId();
//                Bundle bundle = new Bundle();
//                bundle.putLong("expenseId", expenseId);
//                requireNavigationScene(CostListScene.this).push(CostDetailsScene.class, bundle);


                NavigationSceneExtensionsKt.requireNavigationScene(ExpenseListScene.this).disableSupportRestore();

                ArrayMap<String, SceneTransition> map = new ArrayMap<>();
                // map.put(GridDetailScene.VIEW_NAME_HEADER_IMAGE + itemNode.getId(), new AutoSceneTransition());
                //  map.put(GridDetailScene.VIEW_NAME_HEADER_TITLE + itemNode.getId(), new AutoSceneTransition());
                SharedElementSceneTransitionExecutor sharedElementSceneTransitionExecutor = new SharedElementSceneTransitionExecutor(map, new Fade());
                PushOptions build = new PushOptions.Builder().setPushResultCallback(new PushResultCallback() {
                    @Override
                    public void onResult(@Nullable Object o) {
                        if (o != null) {
                            MToast.makeTextShort("跳转成功");
                        }
                    }
                }).setAnimation(sharedElementSceneTransitionExecutor).build();
                NavigationSceneExtensionsKt.requireNavigationScene(ExpenseListScene.this)
                        .push(new ExpenseDetailsScene(itemNode), build);
            }
        });

    }

    /**
     * 初始化UI数据
     */
    private void setupUIData() {
        binding.tvExpensesListWelcomeUser.setText(UserManager.getNickName());
        binding.tvCarNameExpensesList.setText(DataManager.getBrand() + " " + DataManager.getModel());
    }

    /**
     * UI线程运行
     */
    private void runOnUiThread(Runnable runnable) {
        new Handler(Looper.getMainLooper()).post(runnable);
    }
}
