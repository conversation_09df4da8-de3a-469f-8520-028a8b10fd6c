package com.smartcar.easylauncher.modules.tpms.test;

import com.smartcar.easylauncher.modules.tpms.core.TpmsListener;
import com.smartcar.easylauncher.modules.tpms.core.TpmsManager;
import com.smartcar.easylauncher.modules.tpms.device.TpmsDevice;
import com.smartcar.easylauncher.modules.tpms.device.usb.UsbTpmsDevice;
import com.smartcar.easylauncher.modules.tpms.protocol.base.model.TpmsData;
import com.smartcar.easylauncher.modules.tpms.protocol.base.model.TireStatusData;
import com.smartcar.easylauncher.shared.utils.MyLog;

import android.content.Context;

/**
 * TPMS数据显示测试类
 */
public class TpmsDataDisplayTest implements TpmsListener {
    private static final String TAG = "TpmsDataDisplayTest";
    
    private final Context context;
    private final TpmsManager tpmsManager;
    
    public TpmsDataDisplayTest(Context context) {
        this.context = context;
        TpmsDevice device = new UsbTpmsDevice(context);
        this.tpmsManager =  TpmsManager.getInstance().setDevice(device);;
        this.tpmsManager.addListener(this);
    }
    
    /**
     * 开始测试
     */
    public void start() {
        MyLog.d(TAG, "开始TPMS测试...");
        tpmsManager.start();
    }
    
    /**
     * 停止测试
     */
    public void stop() {
        MyLog.d(TAG, "停止TPMS测试");
        tpmsManager.stop();
    }
    
    @Override
    public void onTireStatus(TireStatusData data) {
        StringBuilder sb = new StringBuilder();
        sb.append("\n============ 轮胎状态 ============");
        sb.append("\n位置: ").append(data.getPosition().getValue());
        sb.append("\n压力: ").append(data.getPressure()).append(" kPa");
        sb.append("\n温度: ").append(data.getTemperature()).append(" ℃");
        sb.append("\n电压: ").append(data.getVoltage()).append(" V");
        sb.append("\n电量: ").append(data.getBattery()).append(" %");
        sb.append("\n传感器ID: ").append(data.getSensorId());
        sb.append("\n警告: ").append(data.getDisplayStatus());
        sb.append("\n协议版本: V").append(data.getProtocolVersion());
        sb.append("\n================================");
        
        MyLog.d(TAG, sb.toString());
    }
    
    @Override
    public void onDeviceStatus(TpmsData data) {
        MyLog.d(TAG, "设备状态: " + data.toString());
    }
    
    @Override
    public void onDeviceConnectionChanged(boolean connected) {
        MyLog.d(TAG, "设备连接状态: " + (connected ? "已连接" : "已断开"));
    }
    
    @Override
    public void onError(String error) {
        MyLog.e(TAG, "错误: " + error);
    }
    
    /**
     * 使用示例
     */
    public static void main(String[] args) {
        // 在Activity或Service中使用:
        /*
        TpmsDataDisplayTest test = new TpmsDataDisplayTest(context);
        
        // 开始测试
        test.start();
        
        // ... 运行一段时间后
        
        // 停止测试
        test.stop();
        */
    }
} 