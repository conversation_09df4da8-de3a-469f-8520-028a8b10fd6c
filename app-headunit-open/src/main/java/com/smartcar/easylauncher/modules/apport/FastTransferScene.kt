package com.smartcar.easylauncher.modules.apport

import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.wifi.WifiManager
import android.os.Bundle
import android.os.Looper
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import androidx.annotation.NonNull
import androidx.annotation.Nullable
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.bytedance.scene.ktx.requireNavigationScene
import com.bytedance.scene.navigation.OnBackPressedListener
import com.demon.qfsolution.QFHelper.init
import com.jeremyliao.liveeventbus.LiveEventBus
import com.smartcar.easylauncher.R
import com.smartcar.easylauncher.shared.adapter.common.FilesAdapter
import com.smartcar.easylauncher.core.base.BaseScene
import com.smartcar.easylauncher.core.constants.Const
import com.smartcar.easylauncher.core.constants.Constants
import com.smartcar.easylauncher.databinding.ActivityApportBinding
import com.smartcar.easylauncher.shared.dialog.GeneralDialog
import com.smartcar.easylauncher.shared.dialog.WifiStateDialog
import com.smartcar.easylauncher.model.FastTransferInfoModel
import com.smartcar.easylauncher.infrastructure.system.receiver.WifiReceiver
import com.smartcar.easylauncher.infrastructure.system.service.WebHelper
import com.smartcar.easylauncher.infrastructure.system.service.WebService
import com.smartcar.easylauncher.infrastructure.skin.SkinManager
import com.smartcar.easylauncher.shared.utils.MyLog
import com.smartcar.easylauncher.shared.utils.apportutil.FileUtils
import com.smartcar.easylauncher.shared.utils.apportutil.LogUtils.wtf
import com.smartcar.easylauncher.shared.utils.apportutil.WifiUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 快传Scene - 文件传输功能
 *
 * 功能特性：
 * - WiFi状态监控和管理
 * - 文件列表显示和刷新
 * - 实时传输状态显示
 * - 传输进度监控
 * - 兼容Android API 17+
 * - 高性能优化，适配低端设备
 * - 智能退出检查，防止传输中断
 *
 * 性能优化：
 * - 使用Scene框架替代Activity，减少内存占用
 * - 优化文件列表加载，避免UI阻塞
 * - 智能的传输状态更新机制
 * - 完善的资源释放和生命周期管理
 * - 使用Kotlin协程和现代特性提升性能
 *
 * Kotlin特性：
 * - 使用sealed class定义传输状态
 * - 使用data class封装传输信息
 * - 使用扩展函数简化代码
 * - 使用lazy初始化优化性能
 * - 使用协程处理异步操作
 *
 * <AUTHOR>
 * @version 3.0 - Kotlin优化版本
 */
class FastTransferScene : BaseScene() {

    companion object {
        private const val TAG = "FastTransferScene"

        /** 传输状态检查间隔 */
        private const val TRANSFER_CHECK_INTERVAL = 500L

        /** 文件大小缓存最大容量 */
        private const val FILE_SIZE_CACHE_MAX_SIZE = 1000

        /** 提示框样式配置 */
        private const val USE_SIMPLE_MESSAGE = true // true=极简版本，false=智能版本
    }

    /**
     * 传输状态密封类 - 使用Kotlin特性提升类型安全
     */
    sealed class TransferState {
        /** 空闲状态 */
        object Idle : TransferState()

        /** 传输中状态 */
        data class Transferring(
            val speed: Long,
            val files: Int,
            val totalSize: Long,
            val progress: Float
        ) : TransferState()

        /** 传输完成状态 */
        data class Completed(val totalFiles: Int, val totalSize: Long) : TransferState()

        /** 传输错误状态 */
        data class Error(val message: String, val cause: Throwable? = null) : TransferState()
    }

    /**
     * 文件加载状态密封类
     */
    sealed class LoadingState {
        object Idle : LoadingState()
        object Scanning : LoadingState()
        data class Calculating(val fileCount: Int) : LoadingState()
        data class Processing(val fileCount: Int) : LoadingState()
        object Completed : LoadingState()
        data class Error(val message: String) : LoadingState()
    }

    /** 视图绑定对象 - 使用lateinit延迟初始化 */
    private lateinit var binding: ActivityApportBinding

    /** 文件列表数据 - 使用线程安全的集合 */
    private val fileList: MutableList<FastTransferInfoModel> = mutableListOf()

    /** 文件适配器 - 使用lazy延迟初始化 */
    private val filesAdapter by lazy {
        FilesAdapter(fileList).apply {
            MyLog.d(TAG, "文件适配器初始化完成")
        }
    }

    /** WiFi状态接收器 - 使用lazy延迟初始化 */
    private val wifiReceiver by lazy {
        WifiReceiver().apply {
            MyLog.d(TAG, "WiFi接收器初始化完成")
        }
    }

    /** 广播过滤器 - 使用lazy延迟初始化和扩展函数 */
    private val intentFilter by lazy {
        IntentFilter().apply {
            addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION)
            @Suppress("DEPRECATION")
            addAction(ConnectivityManager.CONNECTIVITY_ACTION)
            MyLog.d(TAG, "广播过滤器初始化完成")
        }
    }

    /** 协程作用域 - 使用SupervisorJob确保异常隔离 */
    private val sceneScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    /** 文件大小缓存 - 使用LRU缓存避免内存泄漏 */
    private val fileSizeCache = object : LinkedHashMap<String, Long>(
        FILE_SIZE_CACHE_MAX_SIZE, 0.75f, true
    ) {
        override fun removeEldestEntry(eldest: MutableMap.MutableEntry<String, Long>?): Boolean {
            return size > FILE_SIZE_CACHE_MAX_SIZE
        }
    }

    /** 当前传输状态 - 使用密封类提升类型安全 */
    private var currentTransferState: TransferState = TransferState.Idle

    /** 当前加载状态 */
    private var currentLoadingState: LoadingState = LoadingState.Idle

    /** 加载动画集合 - 使用lazy延迟初始化 */
    private val loadingAnimations by lazy {
        LoadingAnimations(
            rotate = AnimationUtils.loadAnimation(requireActivity(), R.anim.loading_rotate),
            fadeIn = AnimationUtils.loadAnimation(requireActivity(), R.anim.loading_fade_in),
            fadeOut = AnimationUtils.loadAnimation(requireActivity(), R.anim.loading_fade_out)
        ).also {
            MyLog.d(TAG, "加载动画初始化完成")
        }
    }

    /**
     * 加载动画数据类 - 封装动画资源
     */
    private data class LoadingAnimations(
        val rotate: Animation?,
        val fadeIn: Animation?,
        val fadeOut: Animation?
    )

    @NonNull
    override fun onCreateNewView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): ViewGroup {
        MyLog.d(TAG, "创建快传Scene视图")
        binding = ActivityApportBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(@NonNull view: View, @Nullable savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        MyLog.d(TAG, "快传Scene视图创建完成，开始初始化")

        // 初始化数据和UI
        initializeScene()

        // 设置返回键拦截
        setupBackPressedListener()
    }

    /**
     * 设置返回键拦截监听器 - 检查传输状态
     */
    private fun setupBackPressedListener() {
        MyLog.d(TAG, "设置返回键拦截监听器")

        try {
            requireNavigationScene().addOnBackPressedListener(this, OnBackPressedListener {
                MyLog.d(TAG, "返回键被按下，检查传输状态")
                handleBackPressed()
                true // 拦截返回键事件
            })

            MyLog.d(TAG, "返回键拦截设置完成")
        } catch (e: Exception) {
            MyLog.e(TAG, "设置返回键拦截失败", e)
        }
    }

    /**
     * 处理返回键按下事件 - 智能检查传输状态
     */
    private fun handleBackPressed() {
        // 添加简化的状态日志
        MyLog.d(TAG, "返回键处理: ${currentTransferState.getSimpleDescription()}")

        when (val state = currentTransferState) {
            is TransferState.Idle -> {
                // 空闲状态，直接退出
                MyLog.d(TAG, "当前无传输任务，直接退出")
                exitScene()
            }

            is TransferState.Transferring -> {
                // 传输中，显示确认对话框
                MyLog.d(
                    TAG,
                    "检测到正在传输，显示确认对话框 - 速度:${state.speed}B/s, 进度:${state.progress}%"
                )
                showTransferExitConfirmDialog(state)
            }

            is TransferState.Completed -> {
                // 传输完成，直接退出
                MyLog.d(TAG, "传输已完成，直接退出")
                exitScene()
            }

            is TransferState.Error -> {
                // 传输错误，直接退出
                MyLog.d(TAG, "传输出现错误，直接退出")
                exitScene()
            }
        }
    }

    /**
     * 测试传输状态 - 用于调试
     */
    private fun testTransferState() {
        MyLog.d(TAG, "测试传输状态")

        // 模拟传输状态
        currentTransferState = TransferState.Transferring(
            speed = 1024 * 1024, // 1MB/s
            files = 5,
            totalSize = 100 * 1024 * 1024, // 100MB
            progress = 45.5f
        )

        MyLog.d(TAG, "设置测试传输状态: ${currentTransferState.getDescription()}")
    }

    /**
     * 显示传输退出确认对话框 - 简化版本
     */
    private fun showTransferExitConfirmDialog(transferState: TransferState.Transferring) {
        try {
            val content = generateExitConfirmMessage(transferState)

            val fragmentActivity = requireActivity() as FragmentActivity
            GeneralDialog.showWarningDialog(
                fragmentActivity.supportFragmentManager,
                requireActivity(),
                content,
                object : GeneralDialog.DialogClickListener {
                    override fun onPositiveClick() {
                        MyLog.d(TAG, "用户确认退出，强制停止传输")
                        forceStopTransferAndExit()
                    }

                    override fun onNegativeClick() {
                        MyLog.d(TAG, "用户取消退出，继续传输")
                        // 不做任何操作，继续传输
                    }
                }
            )

            MyLog.d(TAG, "传输退出确认对话框显示成功")
        } catch (e: Exception) {
            MyLog.e(TAG, "显示传输退出确认对话框失败", e)
            // 异常情况下直接退出
            exitScene()
        }
    }

    /**
     * 生成退出确认消息 - 极简版本
     */
    private fun generateExitConfirmMessage(transferState: TransferState.Transferring): String {
        return if (USE_SIMPLE_MESSAGE) {
            // 极简版本 - 使用扩展函数
            transferState.getExitMessage()
        } else {
            // 详细版本 - 包含警告信息
            "${transferState.getExitMessage()}\n\n退出将中断传输！"
        }
    }

    /**
     * 强制停止传输并退出 - 处理用户确认退出的情况
     */
    private fun forceStopTransferAndExit() {
        MyLog.d(TAG, "强制停止传输并退出")

        sceneScope.launch {
            try {
                // 停止Web服务
                WebService.stop(requireActivity())

                // 重置传输状态
                WebHelper.instance.resetTransferStatus()

                // 更新当前状态
                currentTransferState = TransferState.Idle

                // 退出Scene
                exitScene()

                MyLog.d(TAG, "强制停止传输完成")
            } catch (e: Exception) {
                MyLog.e(TAG, "强制停止传输失败", e)
                // 即使失败也要退出
                exitScene()
            }
        }
    }

    /**
     * 安全退出Scene - 统一的退出入口
     */
    private fun exitScene() {
        MyLog.d(TAG, "退出快传Scene")

        try {
            requireNavigationScene().pop()
        } catch (e: Exception) {
            MyLog.e(TAG, "退出Scene失败", e)
        }
    }

    /**
     * 初始化Scene - 重构的初始化方法
     */
    private fun initializeScene() {
        MyLog.d(TAG, "开始初始化快传Scene")

        // 初始化核心组件
        initializeCore()

        // 初始化UI组件
        initializeUI()

        // 初始化事件监听
        initializeListeners()

        // 初始化传输相关
        initializeTransfer()

        // 启动传输状态监控（调试用）
        startTransferStatusMonitoring()

        MyLog.d(TAG, "快传Scene初始化完成")
    }

    /**
     * 初始化核心组件
     */
    private fun initializeCore() {
        wtf(TAG, "初始化核心组件")
        init(requireActivity().applicationContext, Const.showLog, "fileProvider")

        // 注册广播接收器
        requireActivity().registerReceiver(wifiReceiver, intentFilter)

        MyLog.d(TAG, "核心组件初始化完成")
    }

    /**
     * 初始化UI组件
     */
    private fun initializeUI() {
        MyLog.d(TAG, "初始化UI组件")

        // 初始化RecyclerView
        setupRecyclerView()

        // 初始化按钮点击事件
        setupButtonClickListeners()

        // 确保初始状态下传输进度框是隐藏的
        binding.transferStatus.visibility = View.GONE

        // 检查WiFi状态并更新UI
        updateWifiState(WifiUtils.getNetState())

        MyLog.d(TAG, "UI组件初始化完成")
    }

    /**
     * 初始化事件监听
     */
    private fun initializeListeners() {
        MyLog.d(TAG, "初始化事件监听")

        // 注册WiFi状态变化监听
        LiveEventBus.get<Boolean>(Constants.WIFI_CONNECT_CHANGE_EVENT).observe(this) { connected ->
            updateWifiState(connected)
        }

        // 注册文件列表加载事件监听
        setupEventBusListeners()

        MyLog.d(TAG, "事件监听初始化完成")
    }

    /**
     * 初始化传输相关
     */
    private fun initializeTransfer() {
        MyLog.d(TAG, "初始化传输相关")

        // 设置传输状态回调 - 确保只设置一次
        setupTransferStatusCallback()

        // 异步加载文件列表，避免阻塞UI
        loadFileListAsync()

        MyLog.d(TAG, "传输相关初始化完成")
    }

    /**
     * 设置传输状态回调 - 防止重复设置
     */
    private fun setupTransferStatusCallback() {
        try {
            MyLog.d(TAG, "设置传输状态回调")

            WebHelper.instance.setTransferStatusCallback { speed, files, size, progress ->
                // 添加详细日志
                MyLog.v(
                    TAG,
                    "收到传输状态回调: 速度=${speed}B/s, 文件=${files}个, 大小=${size}B, 进度=${progress}%"
                )

                // 确保Scene状态正常时处理
                if (isSceneActive()) {
                    handleTransferStatusUpdate(speed, files, size, progress)
                } else {
                    MyLog.w(TAG, "Scene未激活，忽略传输状态更新")
                }
            }

            MyLog.d(TAG, "传输状态回调设置完成")
        } catch (e: Exception) {
            MyLog.e(TAG, "设置传输状态回调失败", e)
        }
    }

    /**
     * 设置按钮点击事件监听器 - 重构版本
     */
    private fun setupButtonClickListeners() {
        MyLog.d(TAG, "设置按钮点击事件监听器")

        binding.apply {
            // 二维码按钮 - 显示WiFi状态对话框
            mBtnWifiCancel.setOnClickListener {
                MyLog.d(TAG, "二维码按钮被点击")
                showWifiStateDialog()
            }

            // WiFi设置按钮 - 打开系统WiFi设置
            mBtnWifiSettings.setOnClickListener {
                MyLog.d(TAG, "WiFi设置按钮被点击")
                openWifiSettings()
            }

            // 返回按钮 - 使用智能退出检查
            toolbar.moreBtnBack.setOnClickListener {
                MyLog.d(TAG, "返回按钮被点击")
                handleBackPressed() // 使用统一的返回处理逻辑
            }

            // 设置按钮 - 打开快传设置页面
            toolbar.ivSetting.setOnClickListener {
                MyLog.d(TAG, "设置按钮被点击")
                openTransferSettings()
            }

            // 隐藏WiFi图标
            toolbar.ivWifi.visibility = View.GONE
        }

        MyLog.d(TAG, "按钮点击事件监听器设置完成")
    }

    /**
     * 显示WiFi状态对话框 - 扩展函数风格
     */
    private fun showWifiStateDialog() {
        try {
            val fragmentActivity = requireActivity() as FragmentActivity
            WifiStateDialog().showAllowingState(fragmentActivity.supportFragmentManager)
            MyLog.d(TAG, "WiFi状态对话框显示成功")
        } catch (e: Exception) {
            MyLog.e(TAG, "显示WiFi状态对话框失败", e)
        }
    }

    /**
     * 打开WiFi设置 - 安全的Intent启动
     */
    private fun openWifiSettings() {
        try {
            val intent = Intent(Settings.ACTION_WIFI_SETTINGS)
            requireActivity().startActivity(intent)
            MyLog.d(TAG, "WiFi设置页面打开成功")
        } catch (e: Exception) {
            MyLog.e(TAG, "打开WiFi设置页面失败", e)
        }
    }

    /**
     * 打开传输设置 - 安全的Intent启动
     */
    private fun openTransferSettings() {
        try {
            val intent = Intent(requireActivity(), FastTransferSettingActivity::class.java)
            requireActivity().startActivity(intent)
            MyLog.d(TAG, "传输设置页面打开成功")
        } catch (e: Exception) {
            MyLog.e(TAG, "打开传输设置页面失败", e)
        }
    }

    /**
     * WiFi状态变化处理 - 重构版本
     */
    private fun updateWifiState(isConnected: Boolean) {
        MyLog.d(TAG, "WiFi状态变化: ${if (isConnected) "已连接" else "已断开"}")

        when (isConnected) {
            true -> handleWifiConnected()
            false -> handleWifiDisconnected()
        }
    }

    /**
     * 处理WiFi断开连接 - 使用扩展函数风格
     */
    private fun handleWifiDisconnected() {
        MyLog.d(TAG, "处理WiFi断开连接状态")

        binding.apply {
            mTxtTitle.setText(R.string.wlan_disabled)
            mTxtTitle.setTextColor(SkinManager.getInstance().getColor(R.color.field_name_color))
            mTxtSubTitle.visibility = View.VISIBLE
            mImgLanState.setImageResource(R.drawable.ic_task_wifi_switch)
            mTxtStateHint.setText(R.string.fail_to_start_http_service)
            mTxtAddress.visibility = View.GONE
            mBtnWifiSettings.visibility = View.VISIBLE

            // 隐藏传输状态区域
            transferStatus.visibility = View.GONE
        }

        MyLog.d(TAG, "WiFi断开连接状态处理完成")
    }

    /**
     * 处理WiFi连接中状态 - 可选的中间状态
     */
    private fun handleWifiConnecting() {
        MyLog.d(TAG, "处理WiFi连接中状态")

        binding.apply {
            mTxtTitle.setText(R.string.wlan_enabled)
            mTxtTitle.setTextColor(SkinManager.getInstance().getColor(R.color.field_name_color))
            mTxtSubTitle.visibility = View.GONE
            mImgLanState.setImageResource(R.drawable.ic_task_wifi)
            mTxtStateHint.setText(R.string.retrofit_wlan_address)
            mTxtAddress.visibility = View.GONE
            mBtnWifiSettings.visibility = View.GONE
        }

        MyLog.d(TAG, "WiFi连接中状态处理完成")
    }

    /**
     * 处理WiFi已连接状态 - 显示服务地址
     */
    private fun handleWifiConnected() {
        MyLog.d(TAG, "处理WiFi已连接状态")

        val ipAddress = WifiUtils.getIp()
        val serviceUrl =
            String.format(getString(R.string.http_address), ipAddress, Constants.HTTP_PORT)

        binding.apply {
            mTxtTitle.setText(R.string.wlan_enabled)
            mTxtTitle.setTextColor(SkinManager.getInstance().getColor(R.color.field_name_color))
            mTxtSubTitle.visibility = View.GONE
            mImgLanState.setImageResource(R.drawable.shanchuan)
            mTxtStateHint.setText(R.string.pls_input_the_following_address_in_pc_browser)
            mTxtAddress.apply {
                visibility = View.VISIBLE
                text = serviceUrl
            }
            mBtnWifiSettings.visibility = View.GONE
        }

        MyLog.d(TAG, "WiFi已连接状态处理完成，服务地址: $serviceUrl")
    }

    /**
     * Scene恢复时调用 - 增强版本
     */
    override fun onResume() {
        super.onResume()
        MyLog.d(TAG, "Scene恢复，重新设置传输回调")

        // 重新设置传输状态回调，防止回调丢失
        setupTransferStatusCallback()

        // 检查当前传输状态
        checkCurrentTransferStatus()

        LiveEventBus.get<Int>(Constants.LOAD_BOOK_LIST).post(0)
    }

    /**
     * 检查当前传输状态 - 防止状态丢失
     */
    private fun checkCurrentTransferStatus() {
        try {
            // 检查WebHelper是否有正在进行的传输
            if (WebHelper.instance.isConnected()) {
                MyLog.d(TAG, "WebHelper已连接，检查传输状态")
                // 可以添加获取当前状态的逻辑
            }
        } catch (e: Exception) {
            MyLog.e(TAG, "检查传输状态失败", e)
        }
    }

    /**
     * Scene销毁时的资源清理 - 增强版本
     */
    override fun onDestroy() {
        super.onDestroy()
        MyLog.d(TAG, "开始清理快传Scene资源")

        // 清理协程资源
        cleanupCoroutines()

        // 清理广播接收器
        cleanupBroadcastReceiver()

        // 清理Web服务
        cleanupWebService()

        // 清理缓存和状态
        cleanupCacheAndState()

        MyLog.d(TAG, "快传Scene资源清理完成")
    }

    /**
     * 清理协程资源 - 确保所有异步任务被取消
     */
    private fun cleanupCoroutines() {
        try {
            sceneScope.cancel("Scene被销毁")
            MyLog.d(TAG, "协程作用域已取消")
        } catch (e: Exception) {
            MyLog.e(TAG, "取消协程作用域失败", e)
        }
    }

    /**
     * 清理广播接收器 - 防止内存泄漏
     */
    private fun cleanupBroadcastReceiver() {
        try {
            requireActivity().unregisterReceiver(wifiReceiver)
            MyLog.d(TAG, "广播接收器已注销")
        } catch (e: Exception) {
            MyLog.e(TAG, "注销广播接收器失败", e)
        }
    }

    /**
     * 清理Web服务 - 停止传输服务
     */
    private fun cleanupWebService() {
        try {
            // 停止Web服务
            WebService.stop(requireActivity())

            // 重置传输状态
            WebHelper.instance.resetTransferStatus()

            // 释放WebHelper资源
            WebHelper.instance.release()

            MyLog.d(TAG, "Web服务已清理")
        } catch (e: Exception) {
            MyLog.e(TAG, "清理Web服务失败", e)
        }
    }

    /**
     * 清理缓存和状态 - 释放内存
     */
    private fun cleanupCacheAndState() {
        try {
            // 清理文件大小缓存
            synchronized(fileSizeCache) {
                fileSizeCache.clear()
            }

            // 重置状态
            currentTransferState = TransferState.Idle
            currentLoadingState = LoadingState.Idle

            // 清理文件列表
            fileList.clear()

            MyLog.d(TAG, "缓存和状态已清理")
        } catch (e: Exception) {
            MyLog.e(TAG, "清理缓存和状态失败", e)
        }
    }

    /**
     * 设置事件总线监听器 - 重构版本
     */
    private fun setupEventBusListeners() {
        MyLog.d(TAG, "设置事件总线监听器")

        // 监听文件列表加载事件
        LiveEventBus.get<Int>(Constants.LOAD_BOOK_LIST).observe(this) { requestCode ->
            MyLog.i(TAG, "📨 收到文件列表加载事件，请求码: $requestCode")
            handleFileListLoadRequest()
        }

        // 监听增量文件添加事件
        LiveEventBus.get<String>(Constants.ADD_FILE_INCREMENTAL).observe(this) { fileName ->
            MyLog.i(TAG, "📨 收到增量文件添加事件，文件名: $fileName")
            handleIncrementalFileAdd(fileName)
        }

        // 监听WiFi连接状态变化
        LiveEventBus.get<Boolean>(Constants.WIFI_CONNECT_CHANGE_EVENT).observe(this) { connected ->
            MyLog.d(TAG, "收到WiFi连接状态变化事件: $connected")
            handleWifiServiceStateChange(connected)
        }

        MyLog.d(TAG, "事件总线监听器设置完成")
    }

    /**
     * 处理文件列表加载请求 - 优化版本，避免误隐藏传输状态，增加调试日志
     */
    private fun handleFileListLoadRequest() {
        MyLog.i(TAG, "📋 处理文件列表加载请求，当前状态: ${currentTransferState.getSimpleDescription()}")
        MyLog.d(TAG, "📋 传输状态当前可见性: ${binding.transferStatus.visibility}")

        // 只在传输进行中时保持传输状态显示，其他情况都隐藏
        when (currentTransferState) {
            is TransferState.Transferring -> {
                MyLog.d(TAG, "📋 传输进行中，保持传输状态显示")
                // 传输中不做任何UI重置操作
            }
            else -> {
                MyLog.d(TAG, "📋 非传输状态(${currentTransferState.getSimpleDescription()})，隐藏传输显示")
                // 空闲、完成、错误状态都隐藏传输状态
                binding.apply {
                    val oldVisibility = transferStatus.visibility
                    transferStatus.visibility = View.GONE
                    transferSpeed.text = "0 B/s"  // 重置速度显示
                    MyLog.d(TAG, "📋 传输状态可见性: $oldVisibility -> ${transferStatus.visibility}")
                }
            }
        }

        binding.refreshLayout.isRefreshing = false

        // 异步更新文件列表和统计信息
        MyLog.d(TAG, "📋 开始异步加载文件列表")
        loadFileListAsync()
    }

    /**
     * 处理增量文件添加 - 优化用户体验，避免全量刷新，增加调试日志
     */
    private fun handleIncrementalFileAdd(fileName: String) {
        MyLog.i(TAG, "📁 处理增量文件添加: $fileName")
        MyLog.d(TAG, "📁 当前文件列表大小: ${fileList.size}")

        sceneScope.launch {
            try {
                MyLog.d(TAG, "📁 开始在IO线程扫描新文件")
                // 在IO线程扫描新文件
                val newFile = withContext(Dispatchers.IO) {
                    findNewFileByName(fileName)
                }

                if (newFile != null) {
                    MyLog.d(TAG, "📁 找到新文件: ${newFile.name}, 路径: ${newFile.path}")
                    // 在主线程更新UI
                    withContext(Dispatchers.Main) {
                        addFileToListIncremental(newFile)
                    }
                    MyLog.i(TAG, "✅ 增量添加文件成功: ${newFile.name}")
                } else {
                    MyLog.w(TAG, "⚠️ 未找到新文件: $fileName，执行全量刷新")
                    // 如果找不到新文件，执行全量刷新作为备选方案
                    withContext(Dispatchers.Main) {
                        loadFileListAsync()
                    }
                }
            } catch (e: Exception) {
                MyLog.e(TAG, "❌ 增量文件添加失败: $fileName", e)
                // 出错时执行全量刷新作为备选方案
                withContext(Dispatchers.Main) {
                    loadFileListAsync()
                }
            }
        }
    }

    /**
     * 根据文件名查找新文件
     */
    private suspend fun findNewFileByName(fileName: String): FastTransferInfoModel? {
        return try {
            val allFiles = FileUtils.getAllFiles(requireActivity())
            allFiles.find { it.name == fileName || it.path.endsWith(fileName) }
        } catch (e: Exception) {
            MyLog.e(TAG, "查找新文件失败: $fileName", e)
            null
        }
    }

    /**
     * 增量添加文件到列表
     */
    private fun addFileToListIncremental(newFile: FastTransferInfoModel) {
        // 检查文件是否已存在，避免重复添加
        val existingIndex = fileList.indexOfFirst { it.path == newFile.path }

        if (existingIndex >= 0) {
            // 文件已存在，更新现有文件信息
            fileList[existingIndex] = newFile
            filesAdapter.notifyItemChanged(existingIndex)
            MyLog.d(TAG, "更新现有文件: ${newFile.name}")
        } else {
            // 新文件，添加到列表开头（最新文件在顶部）
            fileList.add(0, newFile)
            filesAdapter.notifyItemInserted(0)

            // 滚动到顶部显示新文件
            binding.list.smoothScrollToPosition(0)
            MyLog.d(TAG, "新增文件到列表顶部: ${newFile.name}")
        }

        // 更新统计信息
        updateFileStatistics()
    }

    /**
     * 更新文件统计信息
     */
    private fun updateFileStatistics() {
        val totalSize = fileList.sumOf { it.size.parseFileSize() }

        binding.apply {
            receivedFiles.text = getString(R.string.received_files_format, fileList.size)
            <EMAIL> =
                getString(R.string.total_size_format, totalSize.formatFileSize())
        }

        MyLog.d(TAG, "文件统计信息已更新: ${fileList.size}个文件, ${totalSize.formatFileSize()}")
    }

    /**
     * 处理WiFi服务状态变化
     */
    private fun handleWifiServiceStateChange(isConnected: Boolean) {
        MyLog.d(TAG, "处理WiFi服务状态变化: $isConnected")

        try {
            when (isConnected) {
                true -> {
                    WebService.start(requireActivity())
                    MyLog.d(TAG, "Web服务已启动")
                }

                false -> {
                    WebService.stop(requireActivity())
                    MyLog.d(TAG, "Web服务已停止")
                }
            }
        } catch (e: Exception) {
            MyLog.e(TAG, "处理WiFi服务状态变化失败", e)
        }
    }

    /**
     * 设置RecyclerView - 重构版本
     */
    private fun setupRecyclerView() {
        MyLog.d(TAG, "设置RecyclerView")

        binding.apply {
            // 配置RecyclerView
            list.apply {
                setHasFixedSize(true)
                layoutManager = LinearLayoutManager(requireActivity())
                adapter = filesAdapter
            }

            // 配置下拉刷新
            refreshLayout.apply {
                // 设置下拉刷新颜色主题
                setColorSchemeResources(
                    android.R.color.holo_blue_bright,
                    android.R.color.holo_green_light,
                    android.R.color.holo_orange_light,
                    android.R.color.holo_red_light
                )

                // 设置下拉刷新监听
                setOnRefreshListener {
                    MyLog.d(TAG, "用户触发下拉刷新")
                    loadFileListAsync()
                }
            }
        }

        // 触发初始文件列表加载
        LiveEventBus.get<Int>(Constants.LOAD_BOOK_LIST).post(0)

        MyLog.d(TAG, "RecyclerView设置完成")
    }

    /**
     * 异步加载文件列表 - Kotlin优化版本
     * 使用协程、密封类和扩展函数提升性能和可读性
     */
    private fun loadFileListAsync() {
        // 防止重复加载
        if (currentLoadingState !is LoadingState.Idle) {
            MyLog.d(TAG, "文件列表正在加载中，跳过重复请求")
            return
        }

        MyLog.d(TAG, "开始异步加载文件列表")
        val startTime = System.currentTimeMillis()

        sceneScope.launch {
            try {
                // 更新加载状态为扫描中
                updateLoadingState(LoadingState.Scanning)

                // 在IO线程执行文件扫描和计算
                val result = withContext(Dispatchers.IO) {
                    performFileScanning(startTime)
                }

                // 在主线程更新UI
                updateUIWithFileData(result.fileList, result.totalSize)
                updateLoadingState(LoadingState.Completed)

                val totalTime = System.currentTimeMillis() - startTime
                MyLog.d(TAG, "文件列表加载完成，总耗时: ${totalTime}ms")

            } catch (e: Exception) {
                MyLog.e(TAG, "加载文件列表失败", e)
                updateLoadingState(LoadingState.Error(e.message ?: "未知错误"))
            }
        }
    }

    /**
     * 文件扫描结果数据类
     */
    private data class FileScanResult(
        val fileList: List<FastTransferInfoModel>,
        val totalSize: Long
    )

    /**
     * 执行文件扫描 - 分离的IO操作
     */
    private suspend fun performFileScanning(startTime: Long): FileScanResult {
        val scanStartTime = System.currentTimeMillis()

        // 更新UI状态为扫描中
        withContext(Dispatchers.Main) {
            updateLoadingText("正在扫描文件...")
        }

        // 扫描文件
        val fileList: List<FastTransferInfoModel> = FileUtils.getAllFiles(requireActivity())
        val scanDuration = System.currentTimeMillis() - scanStartTime
        MyLog.d(TAG, "文件扫描完成，耗时: ${scanDuration}ms，文件数量: ${fileList.size}")

        // 更新状态为计算中
        withContext(Dispatchers.Main) {
            updateLoadingState(LoadingState.Calculating(fileList.size))
        }

        // 计算文件大小
        val calcStartTime = System.currentTimeMillis()
        val totalSize = calculateTotalSizeOptimized(fileList)
        val calcDuration = System.currentTimeMillis() - calcStartTime
        MyLog.d(TAG, "大小计算完成，耗时: ${calcDuration}ms")

        return FileScanResult(fileList, totalSize)
    }

    /**
     * 更新加载状态 - 使用密封类
     */
    private suspend fun updateLoadingState(newState: LoadingState) {
        currentLoadingState = newState

        withContext(Dispatchers.Main) {
            when (newState) {
                is LoadingState.Idle -> hideLoadingState()
                is LoadingState.Scanning -> {
                    showLoadingState()
                    updateLoadingText("正在扫描文件...")
                }

                is LoadingState.Calculating -> {
                    updateLoadingText(
                        when {
                            newState.fileCount == 0 -> "未发现文件"
                            newState.fileCount < 10 -> "正在计算文件大小..."
                            newState.fileCount < 100 -> "正在处理 ${newState.fileCount} 个文件..."
                            else -> "正在处理大量文件 (${newState.fileCount} 个)..."
                        }
                    )
                }

                is LoadingState.Processing -> {
                    updateLoadingText("正在处理 ${newState.fileCount} 个文件...")
                }

                is LoadingState.Completed -> {
                    updateLoadingText("加载完成")
                    delay(500) // 短暂显示完成状态
                    hideLoadingState()
                }

                is LoadingState.Error -> {
                    updateLoadingText("加载失败: ${newState.message}")
                    delay(3000) // 显示错误信息3秒
                    hideLoadingState()
                }
            }
        }
    }

    /**
     * 更新加载文本 - 扩展函数风格
     */
    private fun updateLoadingText(text: String) {
        binding.loadingText?.text = text
    }

    /**
     * 优化的文件大小计算 - 使用缓存和Kotlin协程
     */
    private fun calculateTotalSizeOptimized(fileList: List<FastTransferInfoModel>): Long {
        return fileList.sumOf { model ->
            val cacheKey = "${model.name}_${model.size}_${model.hashCode()}"

            // 线程安全的缓存访问
            synchronized(fileSizeCache) {
                fileSizeCache[cacheKey] ?: run {
                    // 缓存未命中，计算并缓存结果
                    val calculatedSize = model.size.parseFileSize()
                    fileSizeCache[cacheKey] = calculatedSize
                    calculatedSize
                }
            }
        }
    }

    /**
     * 文件大小解析扩展函数 - Kotlin风格
     */
    private fun String.parseFileSize(): Long {
        return try {
            // 提取数字部分
            val numberStr = this.replace(Regex("[^\\d.]"), "")
            if (numberStr.isEmpty()) return 0L

            val number = numberStr.toDoubleOrNull() ?: return 0L

            // 根据单位计算大小
            when {
                this.contains(
                    "GB",
                    ignoreCase = true
                ) -> (number * 1_073_741_824).toLong() // 1024^3
                this.contains(
                    "MB",
                    ignoreCase = true
                ) -> (number * 1_048_576).toLong()     // 1024^2
                this.contains("KB", ignoreCase = true) -> (number * 1024).toLong()
                else -> number.toLong()
            }
        } catch (e: Exception) {
            MyLog.w(TAG, "解析文件大小失败: $this${e.message}")
            0L
        }
    }

    /**
     * 更新UI显示文件数据 - 优化版本
     */
    private fun updateUIWithFileData(fileList: List<FastTransferInfoModel>, totalSize: Long) {
        MyLog.d(
            TAG,
            "更新UI文件数据，文件数量: ${fileList.size}，总大小: ${totalSize.formatFileSize()}"
        )

        binding.apply {
            // 更新文件列表数据
            <EMAIL> {
                clear()
                addAll(fileList)
            }

            // 通知适配器数据变化
            filesAdapter.notifyDataSetChanged()

            // 更新统计信息显示
            receivedFiles.text = getString(R.string.received_files_format, fileList.size)
            <EMAIL> =
                getString(R.string.total_size_format, totalSize.formatFileSize())
        }

        MyLog.d(TAG, "UI文件数据更新完成")
    }

    /**
     * 文件大小格式化扩展函数 - Kotlin风格
     */
    private fun Long.formatFileSize(): String {
        return when {
            this >= 1_073_741_824 -> String.format("%.2f GB", this / 1_073_741_824.0) // 1024^3
            this >= 1_048_576 -> String.format("%.2f MB", this / 1_048_576.0)         // 1024^2
            this >= 1024 -> String.format("%.2f KB", this / 1024.0)
            else -> "$this B"
        }
    }

    /**
     * 传输速度格式化扩展函数
     */
    private fun formatTransferSpeed(speed: Long): String {
        return "${speed.formatFileSize()}/s"
    }

    // 移除此方法，因为已经在lazy初始化中处理了动画加载

    /**
     * 显示加载状态 - 优化的动画效果
     */
    private fun showLoadingState() {
        MyLog.d(TAG, "显示加载状态")

        binding.apply {
            // 更新状态文字
            receivedFiles.text = "扫描中..."
            totalSize.text = "计算中..."

            // 显示加载容器
            loadingContainer?.apply {
                visibility = View.VISIBLE
                loadingAnimations.fadeIn?.let { animation ->
                    startAnimation(animation)
                }
            }

            // 启动图标旋转动画
            loadingIcon?.apply {
                loadingAnimations.rotate?.let { animation ->
                    startAnimation(animation)
                }
            }

            // 初始加载文字
            loadingText?.text = "正在初始化..."
        }

        MyLog.d(TAG, "加载状态显示完成")
    }

    /**
     * 隐藏加载状态 - 优化的动画效果
     */
    private fun hideLoadingState() {
        MyLog.d(TAG, "隐藏加载状态")

        binding.apply {
            // 停止下拉刷新
            refreshLayout.isRefreshing = false

            // 停止旋转动画
            loadingIcon?.clearAnimation()

            // 淡出动画
            loadingAnimations.fadeOut?.let { animation ->
                animation.setAnimationListener(object : Animation.AnimationListener {
                    override fun onAnimationStart(animation: Animation?) {}
                    override fun onAnimationRepeat(animation: Animation?) {}
                    override fun onAnimationEnd(animation: Animation?) {
                        binding.loadingContainer?.visibility = View.GONE
                        currentLoadingState = LoadingState.Idle
                    }
                })
                loadingContainer?.startAnimation(animation)
            } ?: run {
                // 如果动画加载失败，直接隐藏
                loadingContainer?.visibility = View.GONE
                currentLoadingState = LoadingState.Idle
            }
        }

        MyLog.d(TAG, "加载状态隐藏完成")
    }

    /**
     * 处理传输状态更新 - 重构版本，增加详细调试日志
     */
    private fun handleTransferStatusUpdate(speed: Long, files: Int, size: Long, progress: Float) {
        // 添加更详细的日志
        MyLog.d(
            TAG,
            "🔄 传输状态更新: 速度=${speed}B/s, 文件=${files}个, 大小=${size}B, 进度=${progress}%"
        )
        MyLog.d(TAG, "🔄 当前传输状态: ${currentTransferState.getSimpleDescription()}")

        // 检查参数有效性
        if (speed < 0 || files < 0 || size < 0 || progress < 0) {
            MyLog.w(TAG, "⚠️ 传输状态参数异常，忽略更新")
            return
        }

        // 更新当前传输状态 - 优化判断逻辑
        val newState = when {
            // 有速度或者有进度且未完成 = 传输中
            speed > 0 || (progress > 0 && progress < 100) -> {
                MyLog.d(TAG, "📤 判断为传输中状态")
                TransferState.Transferring(speed, files, size, progress)
            }
            // 进度达到100% = 完成
            progress >= 100 && files > 0 -> {
                MyLog.d(TAG, "✅ 判断为传输完成状态")
                TransferState.Completed(files, size)
            }
            // 所有参数都为0或负数 = 空闲（传输结束）
            speed == 0L && files == 0 && size == 0L && progress == 0f -> {
                MyLog.d(TAG, "💤 判断为空闲状态（所有参数为0）")
                TransferState.Idle
            }
            // 其他情况保持当前状态
            else -> {
                MyLog.d(TAG, "🔄 保持当前状态: ${currentTransferState.getSimpleDescription()}")
                currentTransferState
            }
        }

        MyLog.d(TAG, "🎯 新状态: ${newState.getSimpleDescription()}")

        // 只在状态真正改变时更新，或者是传输中状态（需要实时更新进度）
        if (newState != currentTransferState || newState is TransferState.Transferring) {
            val oldState = currentTransferState.getSimpleDescription()
            currentTransferState = newState
            MyLog.i(TAG, "🔀 传输状态变更: $oldState -> ${newState.getSimpleDescription()}")

            // 确保在主线程更新UI，并检查Scene状态
            updateUIOnMainThread {
                if (isSceneActive()) {
                    MyLog.d(TAG, "🎨 开始更新传输UI")
                    updateTransferUI(currentTransferState)
                } else {
                    MyLog.w(TAG, "⚠️ Scene未激活，跳过UI更新")
                }
            }
        } else {
            MyLog.v(TAG, "🔄 状态未变化，跳过更新")
        }
    }

    /**
     * 更新传输UI显示 - 使用密封类，增加详细调试日志
     */
    private fun updateTransferUI(state: TransferState) {
        MyLog.d(TAG, "🎨 updateTransferUI 被调用，状态: ${state.getSimpleDescription()}")

        binding.apply {
            when (state) {
                is TransferState.Idle -> {
                    MyLog.d(TAG, "🎨 处理空闲状态 - 隐藏传输UI")
                    MyLog.d(TAG, "🎨 传输状态当前可见性: ${transferStatus.visibility}")
                    transferStatus.visibility = View.GONE
                    MyLog.d(TAG, "✅ 传输状态已设置为GONE")
                }

                is TransferState.Transferring -> {
                    MyLog.d(TAG, "🎨 处理传输中状态 - 显示传输UI")
                    transferStatus.visibility = View.VISIBLE

                    // 更新传输速度
                    transferSpeed.text = getString(
                        R.string.transfer_speed_format,
                        formatTransferSpeed(state.speed)
                    )

                    // 更新文件信息
                    if (state.files > 0 && state.totalSize > 0) {
                        receivedFiles.text = getString(R.string.received_files_format, state.files)
                        totalSize.text =
                            getString(R.string.total_size_format, state.totalSize.formatFileSize())
                    }

                    // 更新进度
                    if (state.progress >= 0) {
                        transferProgress.text =
                            getString(R.string.transfer_progress_format, state.progress)
                        progressBar.progress = state.progress.toInt().coerceIn(0, 100)
                    }

                    MyLog.v(TAG, "🎨 传输UI已更新 - ${state.progress}%")
                }

                is TransferState.Completed -> {
                    MyLog.d(TAG, "🎨 处理传输完成状态 - 隐藏传输UI")
                    MyLog.d(TAG, "🎨 传输状态当前可见性: ${transferStatus.visibility}")
                    // 传输完成时立即隐藏传输状态
                    transferStatus.visibility = View.GONE
                    MyLog.i(TAG, "✅ 传输完成，UI已隐藏 - ${state.totalFiles}个文件")
                }

                is TransferState.Error -> {
                    MyLog.d(TAG, "🎨 处理传输错误状态 - 隐藏传输UI")
                    transferStatus.visibility = View.GONE
                    MyLog.e(TAG, "❌ 传输错误，UI已隐藏 - ${state.message}")

                    // 错误状态也延迟重置
                    sceneScope.launch {
                        delay(2000) // 错误状态延迟2秒重置
                        if (currentTransferState is TransferState.Error) {
                            currentTransferState = TransferState.Idle
                            MyLog.d(TAG, "🔄 传输错误状态已重置为空闲")
                        }
                    }
                }
            }
        }

        MyLog.d(TAG, "🎨 updateTransferUI 完成，最终可见性: ${binding.transferStatus.visibility}")
    }

    // ================================
    // Kotlin扩展函数和工具方法
    // ================================

    /**
     * 安全执行扩展函数 - 统一异常处理
     */
    private inline fun <T> safeExecute(
        operation: String,
        block: () -> T
    ): T? {
        return try {
            block()
        } catch (e: Exception) {
            MyLog.e(TAG, "$operation 执行失败", e)
            null
        }
    }

    /**
     * 协程安全执行扩展函数
     */
    private fun CoroutineScope.safeLaunch(
        operation: String,
        block: suspend CoroutineScope.() -> Unit
    ) {
        launch {
            try {
                block()
            } catch (e: Exception) {
                MyLog.e(TAG, "$operation 协程执行失败", e)
            }
        }
    }

    /**
     * View可见性扩展函数
     */
    private fun View.show() {
        visibility = View.VISIBLE
    }

    private fun View.hide() {
        visibility = View.GONE
    }

    private fun View.invisible() {
        visibility = View.INVISIBLE
    }

    /**
     * 检查传输状态扩展函数
     */
    private fun TransferState.isTransferring(): Boolean {
        return this is TransferState.Transferring
    }

    private fun TransferState.isIdle(): Boolean {
        return this is TransferState.Idle
    }

    /**
     * 获取传输状态描述扩展函数
     */
    private fun TransferState.getDescription(): String {
        return when (this) {
            is TransferState.Idle -> "空闲状态"
            is TransferState.Transferring -> "传输中 - ${String.format("%.1f", progress)}%"
            is TransferState.Completed -> "传输完成 - ${totalFiles}个文件"
            is TransferState.Error -> "传输错误 - $message"
        }
    }

    /**
     * 传输状态简化描述扩展函数 - 用于日志
     */
    private fun TransferState.getSimpleDescription(): String {
        return when (this) {
            is TransferState.Idle -> "空闲"
            is TransferState.Transferring -> "传输中${String.format("%.0f", progress)}%"
            is TransferState.Completed -> "完成"
            is TransferState.Error -> "错误"
        }
    }

    /**
     * 生成简洁的退出提示扩展函数
     */
    private fun TransferState.Transferring.getExitMessage(): String {
        return when {
            progress < 10f -> "传输刚开始，确认退出吗？"
            progress > 90f -> "传输即将完成，确认退出吗？"
            else -> "正在传输文件，确认退出吗？"
        }
    }

    /**
     * 性能监控扩展函数
     */
    private inline fun <T> measureTimeAndLog(
        operation: String,
        block: () -> T
    ): T {
        val startTime = System.currentTimeMillis()
        val result = block()
        val duration = System.currentTimeMillis() - startTime
        MyLog.d(TAG, "$operation 耗时: ${duration}ms")
        return result
    }

    /**
     * 字符串安全性检查扩展函数
     */
    private fun String?.isNotNullOrEmpty(): Boolean {
        return !this.isNullOrEmpty()
    }

    /**
     * 集合安全操作扩展函数
     */
    private fun <T> List<T>.safeGet(index: Int): T? {
        return if (index in 0 until size) this[index] else null
    }

    /**
     * 数值范围限制扩展函数
     */
    private fun Float.clampProgress(): Float {
        return this.coerceIn(0f, 100f)
    }

    private fun Long.clampSize(): Long {
        return this.coerceAtLeast(0L)
    }

    /**
     * 调试信息扩展函数
     */
    private fun TransferState.logState() {
        MyLog.d(TAG, "当前传输状态: ${this.getDescription()}")
    }

    private fun LoadingState.logState() {
        val description = when (this) {
            is LoadingState.Idle -> "空闲"
            is LoadingState.Scanning -> "扫描中"
            is LoadingState.Calculating -> "计算中 - ${fileCount}个文件"
            is LoadingState.Processing -> "处理中 - ${fileCount}个文件"
            is LoadingState.Completed -> "完成"
            is LoadingState.Error -> "错误 - $message"
        }
        MyLog.d(TAG, "当前加载状态: $description")
    }

    /**
     * 检查Scene是否处于活跃状态
     */
    private fun isSceneActive(): Boolean {
        return try {
            // 检查Scene是否已创建视图且未被销毁
            this::binding.isInitialized && view != null
        } catch (e: Exception) {
            MyLog.w(TAG, "检查Scene状态失败${e.message}")
            false
        }
    }

    /**
     * 在主线程更新UI的安全方法
     */
    private fun updateUIOnMainThread(action: () -> Unit) {
        try {
            if (Thread.currentThread() == Looper.getMainLooper().thread) {
                action()
            } else {
                requireActivity().runOnUiThread {
                    action()
                }
            }
        } catch (e: Exception) {
            MyLog.e(TAG, "UI更新失败", e)
        }
    }

    /**
     * 调试方法：手动检查传输状态
     */
    private fun debugCheckTransferStatus() {
        MyLog.d(TAG, "=== 🔍 传输状态调试信息 ===")
        MyLog.d(TAG, "当前传输状态: ${currentTransferState.getDescription()}")
        MyLog.d(TAG, "WebHelper连接状态: ${WebHelper.instance.isConnected()}")
        MyLog.d(TAG, "传输状态UI可见性: ${binding.transferStatus.visibility}")
        MyLog.d(TAG, "Scene活跃状态: ${isSceneActive()}")
        MyLog.d(TAG, "文件列表大小: ${fileList.size}")
        MyLog.d(TAG, "========================")
    }

    /**
     * 启动传输状态监控 - 用于调试
     */
    private fun startTransferStatusMonitoring() {
        sceneScope.launch {
            while (isSceneActive()) {
                MyLog.v(TAG, "🔍 监控 - 传输状态: ${currentTransferState.getSimpleDescription()}, UI可见性: ${binding.transferStatus.visibility}")
                delay(2000) // 每2秒检查一次
            }
        }
    }
}
