package com.smartcar.easylauncher.modules.onboarding.model;

/**
 * 语言项数据模型
 * 
 * <AUTHOR>
 */
public class LanguageItem {
    private String code; // 语言代码，如"zh", "en"
    private String name; // 语言名称
    private String hello; // 对应语言的"你好"
    private boolean selected; // 是否被选中

    public LanguageItem(String code, String name, String hello) {
        this.code = code;
        this.name = name;
        this.hello = hello;
        this.selected = false;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHello() {
        return hello;
    }

    public void setHello(String hello) {
        this.hello = hello;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }
} 