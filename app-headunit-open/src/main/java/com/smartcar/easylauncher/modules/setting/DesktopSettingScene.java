package com.smartcar.easylauncher.modules.setting;

import android.annotation.SuppressLint;
import android.net.Proxy;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;

import com.bytedance.scene.group.UserVisibleHintGroupScene;
import com.google.gson.Gson;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.BuildConfig;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.infrastructure.amap.LocationService;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.core.constants.SettingsConstants;
import com.smartcar.easylauncher.databinding.FragmentDesktopSettingBinding;
import com.smartcar.easylauncher.shared.dialog.GeneralDialog;
import com.smartcar.easylauncher.infrastructure.event.scope.layout.cody.LyoutScopeBus;
import com.smartcar.easylauncher.data.model.common.LayoutUPModel;
import com.smartcar.easylauncher.data.model.navigation.LCGeoPoint;
import com.smartcar.easylauncher.data.model.navigation.LocateInforModel;
import com.smartcar.easylauncher.data.model.user.ActivationcodeModel;
import com.smartcar.easylauncher.modules.onboarding.OnboardingManager;

import static com.bytedance.scene.navigation.NavigationSceneGetter.requireNavigationScene;
import com.smartcar.easylauncher.core.manager.AuthorityManager;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.file.DataCleanUtil;
import com.smartcar.easylauncher.shared.utils.system.DeviceIdUtil;
import com.smartcar.easylauncher.shared.utils.system.DeviceUtil;
import com.smartcar.easylauncher.shared.view.psd.PayDialog;
import com.smartcar.easylauncher.shared.view.selector.SelectorGroup;

import java.util.HashMap;
import java.util.concurrent.TimeUnit;

import cn.bfy.dualsim.DeviceIdentifierUtils;
import cn.bfy.dualsim.TelephonyManagement;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.schedulers.Schedulers;
import rxhttp.RxHttp;

/**
 * 桌面设置
 *
 * <AUTHOR>
 */
public class DesktopSettingScene extends UserVisibleHintGroupScene implements PayDialog.PayInterface {
    public static final String TAG = DesktopSettingScene.class.getSimpleName();
    private FragmentDesktopSettingBinding binding;
    private final SelectorGroup singleGroup = new SelectorGroup();
    private PayDialog payDialog;
    private FragmentActivity fragmentActivity;
    private final CompositeDisposable disposables = new CompositeDisposable();
    private OnboardingManager onboardingManager;

    @Override
    public ViewGroup onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = FragmentDesktopSettingBinding.inflate(inflater, container, false);
        fragmentActivity = (FragmentActivity) requireActivity();
        onboardingManager = OnboardingManager.getInstance();
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        // 1. 初始化基本UI结构 - 只加载必要的UI元素
        initBasicUI();
        // 2. 初始化选择器组 - 这是必要的交互元素
        setupDesktopMode();
        // 3. 初始化语言设置
        setupLanguageSetting();
        // 加载可选数据
        loadOptionalData();
    }

    private void initBasicUI() {
        // 初始化缓存管理UI - 先设置默认文本，避免用户看到空白
        binding.btClean.setText(getString(R.string.clear_cache));
        binding.btClean.setOnClickListener(v -> showClearCacheConfirmDialog());
    }

    private void setupDesktopMode() {
        singleGroup.setChoiceMode(SelectorGroup.MODE_SINGLE_CHOICE);
        singleGroup.setStateListener((groupTag, tag, isSelected) -> {
            if (!isSelected) return;
            updateHomeLayout(tag);
        });

        binding.selectorStartersDuck.setGroup("Personalized", singleGroup);
        binding.selectorStartersPork.setGroup("Personalized", singleGroup);
        binding.selectorStartersHybrid.setGroup("Personalized", singleGroup);
    }

    /**
     * 设置语言设置功能
     */
    private void setupLanguageSetting() {
        // 设置语言设置字段的标题和描述
        binding.languageField.tvFieldTitle.setText(getString(R.string.current_language));
        binding.languageField.tvFieldDesc.setText(getCurrentLanguageName());
        binding.languageField.btnFieldAction.setText(getString(R.string.change_language));

        // 设置点击事件
        binding.languageField.btnFieldAction.setOnClickListener(v -> openLanguageSelection());
    }

    /**
     * 打开语言选择页面
     */
    private void openLanguageSelection() {
        MyLog.v(TAG, "打开语言选择页面");
        LanguageSelectionScene languageScene = new LanguageSelectionScene();
        requireNavigationScene(this).push(languageScene);
    }

    /**
     * 获取当前语言的显示名称
     */
    private String getCurrentLanguageName() {
        String languageCode = onboardingManager.getSelectedLanguage();
        return switch (languageCode) {
            case "zh" -> getString(R.string.language_zh);
            case "zh_tw" -> getString(R.string.language_zh_tw);
            case "en" -> getString(R.string.language_en);
            case "ja" -> getString(R.string.language_ja);
            case "ko" -> getString(R.string.language_ko);
            case "ru" -> getString(R.string.language_ru);
            case "fr" -> getString(R.string.language_fr);
            case "de" -> getString(R.string.language_de);
            case "es" -> getString(R.string.language_es);
            case "it" -> getString(R.string.language_it);
            case "pt" -> getString(R.string.language_pt);
            case "ar" -> getString(R.string.language_ar);
            default -> getString(R.string.language_zh); // 默认中文
        };
    }

    private void loadOptionalData() {
        // 合并多个RxJava操作，减少线程切换开销
        disposables.add(Observable.zip(
                        // 1. 异步加载缓存大小 - 这是耗时操作
                        Observable.fromCallable(() -> {
                            try {
                                // 使用缓存机制，避免重复计算
                                return DataCleanUtil.getTotalCacheSize(requireActivity());
                            } catch (Exception e) {
                                return "未知";
                            }
                        }).subscribeOn(Schedulers.io()),

                        // 2. 异步加载当前桌面模式 - 这是IO操作
                        Observable.fromCallable(() -> {
                            return SettingsManager.getDefaultHome();
                        }).subscribeOn(Schedulers.io()),

                        // 3. 合并结果处理
                        (cacheSize, homeType) -> {
                            return new Object[]{cacheSize, homeType};
                        }
                )
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(results -> {
                    // 更新缓存大小显示
                    String cacheSize = (String) results[0];
                    binding.btClean.setText(String.format("清除缓存 (%s)", cacheSize));

                    // 更新桌面模式选择器
                    int homeType = (int) results[1];
                    if (homeType == SettingsConstants.HOME_CARD_LAYOUT_TYPE) {
                        singleGroup.initSelector(true, binding.selectorStartersDuck);
                    } else if (homeType == SettingsConstants.HOME_MAP_LAYOUT_TYPE) {
                        singleGroup.initSelector(true, binding.selectorStartersPork);
                    } else if (homeType == SettingsConstants.HOME_HYBRID_LAYOUT_TYPE) {
                        singleGroup.initSelector(true, binding.selectorStartersHybrid);
                    }
                }, throwable -> {
                    // 错误处理
                    binding.btClean.setText("清除缓存 (未知)");
                }));
    }

    private void updateHomeLayout(String tag) {
        // 先显示切换提示，提供即时反馈
        String layoutName = getLayoutName(tag);
        MToast.makeTextShort("正在切换至" + layoutName + "...");

        disposables.add(Observable.fromCallable(() -> {
                    if ("card".equals(tag)) {
                        SettingsManager.setDefaultHome(SettingsConstants.HOME_CARD_LAYOUT_TYPE);
                    } else if ("map".equals(tag)) {
                        SettingsManager.setDefaultHome(SettingsConstants.HOME_MAP_LAYOUT_TYPE);
                    } else if ("hybrid".equals(tag)) {
                        SettingsManager.setDefaultHome(SettingsConstants.HOME_HYBRID_LAYOUT_TYPE);
                    }
                    return tag;
                })
                .subscribeOn(Schedulers.io())
                .delay(300, java.util.concurrent.TimeUnit.MILLISECONDS) // 短暂延迟，让用户看到反馈
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(result -> {
                    // 发送布局更新事件
                    LyoutScopeBus.eventBean().post(new LayoutUPModel(
                            SettingsConstants.HOME__TYPE,
                            SettingsManager.getDefaultHome(),
                            SettingsManager.getCardLayout()
                    ));

                    // 显示切换成功提示
                    MToast.makeTextShort("已切换至" + layoutName);
                }, throwable -> {
                    // 错误处理
                    MToast.makeTextShort("切换失败，请重试");
                    MyLog.e(TAG, "桌面布局切换失败", throwable);
                }));
    }

    /**
     * 获取布局名称
     * @param tag 布局标签
     * @return 布局显示名称
     */
    private String getLayoutName(String tag) {
        switch (tag) {
            case "card":
                return "经典桌面模式";
            case "map":
                return "沉浸式桌面模式";
            case "hybrid":
                return "混合桌面模式";
            default:
                return "未知模式";
        }
    }

    private void showClearCacheConfirmDialog() {
        GeneralDialog.showGeneralDialog(
                fragmentActivity.getSupportFragmentManager(),
                requireActivity(),
                getString(R.string.clear_cache_confirm_title),
                getString(R.string.clear_cache_confirm_message),
                getString(R.string.confirm),
                getString(R.string.cancel_button),
                new GeneralDialog.DialogClickListener() {
                    @Override
                    public void onPositiveClick() {
                        clearCache();
                    }

                    @Override
                    public void onNegativeClick() {
                    }
                });
    }

    private void clearCache() {
        // 先更新UI状态，提供即时反馈
        binding.btClean.setText(getString(R.string.clear_cache_processing));

        // 使用RxJava处理耗时操作
        disposables.add(Observable.fromCallable(() -> {
                    try {
                        // 清除缓存 - 耗时操作
                        DataCleanUtil.clearAllCache(requireActivity());
                        // 获取清除后的缓存大小
                        return DataCleanUtil.getTotalCacheSize(requireActivity());
                    } catch (Exception e) {
                        return "未知";
                    }
                })
                .subscribeOn(Schedulers.io())
                // 添加超时处理，避免长时间阻塞
                .timeout(3, TimeUnit.SECONDS, Observable.just("极小"))
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                        cacheSize -> {
                            binding.btClean.setText(String.format(getString(R.string.clear_cache_with_size), cacheSize));
                            MToast.makeTextShort(getString(R.string.clear_cache_success));
                        },
                        throwable -> {
                            binding.btClean.setText(getString(R.string.clear_cache_unknown));
                            MToast.makeTextShort(getString(R.string.clear_cache_failed));
                        }
                ));
    }

    /**
     * 检查是否使用了代理
     */
    private boolean isWifiProxy() {
        if (requireActivity() == null) return false;

        try {
            final boolean IS_ICS_OR_LATER = Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH;
            String proxyAddress;
            int proxyPort;

            if (IS_ICS_OR_LATER) {
                proxyAddress = System.getProperty("http.proxyHost");
                String portStr = System.getProperty("http.proxyPort");
                proxyPort = Integer.parseInt((portStr != null ? portStr : "-1"));
            } else {
                proxyAddress = Proxy.getHost(requireActivity());
                proxyPort = Proxy.getPort(requireActivity());
            }

            return (!TextUtils.isEmpty(proxyAddress)) && (proxyPort != -1);
        } catch (Exception e) {
            MyLog.e(TAG, "Check proxy failed", e);
            return false;
        }
    }

    // PayDialog.PayInterface 实现方法
    @Override
    public void Payfinish(String password) {
        if (isWifiProxy()) {
            MToast.makeTextShort(getString(R.string.network_unsafe));
            return;
        }
        getSongSingerData(password);
    }

    @Override
    public void onSucc() {
        SettingsManager.setDefaultHome(SettingsConstants.HOME_MAP_LAYOUT_TYPE);
        LyoutScopeBus.eventBean().post(new LayoutUPModel(
                SettingsConstants.HOME__TYPE,
                SettingsManager.getDefaultHome(),
                SettingsManager.getCardLayout()
        ));
        if (payDialog != null) {
            payDialog.cancel();
        }
    }

    @Override
    public void onForget() {
        if (payDialog != null && payDialog.payPassView.progress.getVisibility() != View.VISIBLE) {
            MToast.makeTextShort(getString(R.string.contact_admin));
        }
    }

    @SuppressLint("CheckResult")
    private void getSongSingerData(String secretKey) {
        HashMap<String, Object> babyMap = new HashMap<>();
        babyMap.put(Const.AV_KEY_SECRET_KEY, secretKey);
        RxHttp.get(Const.LC_BASEURL + Const.AV_KEY_ACTIVE_CODE)
                .add("where", new Gson().toJson(babyMap))
                .add("keys", Const.AV_KEY_ACTIVE_CODE_STATE)
                .toObservable(ActivationcodeModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(students -> {
                    //请求成功
                    MyLog.v("请求激活码", new Gson().toJson(students));
                    //非空判断
                    if (students.getResults() == null || students.getResults().isEmpty()) {
                        // MToast.makeTextShort("激活码错误");
                        payDialog.setError(getString(R.string.activation_code_error));
                        //记录激活码激活日志方法
                        recordActiveLog(404, getString(R.string.activation_code_error), secretKey);
                        return;
                    }
                    ActivationcodeModel.ResultsDTO resultsDTO = students.getResults().get(0);
                    if (resultsDTO.getState() == 0) {
                        //写一个激活软件的方法
                        activeSoftware(secretKey, resultsDTO.getObjectId());
                    } else if (resultsDTO.getState() == 1) {
                        dataCheck(secretKey);
                    } else if (resultsDTO.getState() == 2) {
                        recordActiveLog(2, getString(R.string.activation_expired), secretKey);
                        payDialog.setError(getString(R.string.activation_expired));
                    } else {
                        payDialog.setError(getString(R.string.unknown_error_contact_admin));
                    }

                }, throwable -> {
                    //请求失败
                    MyLog.v("请求激活码", "失败");
                    payDialog.setError(getString(R.string.network_connection_error));
                });
    }

    /**
     * 记录激活码激活日志方法
     *
     * @param code
     * @param remark
     * @param secretKey
     */
    @SuppressLint("CheckResult")
    private void recordActiveLog(int code, String remark, String secretKey) {
        LocateInforModel lastKnownLocationInfo = LocationService.getInstance().getLastKnownLocationInfo();
        LCGeoPoint lcGeoPoint = new LCGeoPoint();
        lcGeoPoint.set__type("GeoPoint");
        if (lastKnownLocationInfo != null) {
            // 如果 currentLocation 不是 null，则设置经纬度

            lcGeoPoint.setLatitude(lastKnownLocationInfo.getLatitude());
            lcGeoPoint.setLongitude(lastKnownLocationInfo.getLongitude());
        } else {
            // 如果 currentLocation 是 null，则处理这种情况（例如，显示错误消息或尝试重新定位）
            lcGeoPoint.setLatitude(0.0);
            lcGeoPoint.setLongitude(0.0);
        }

        RxHttp.postJson(Const.LC_BASEURL + Const.AV_KEY_ACTIVATION_LOG + "?fetchWhenSave=true")  //发送Json字符串单形式的Post请求
                .add(Const.AV_KEY_ACTIVE_LOG_STATE, code)
                .add(Const.AV_KEY_ACTIVE_CODE_REMARK, remark)
                .add(Const.AV_KEY_ACTIVE_LOG_IMEI, TelephonyManagement.getInstance().getDualSimChip(requireActivity()).getImei(0))
                .add(Const.AV_KEY_ACTIVE_LOG_ANDROID, DeviceIdentifierUtils.getAndroidId(requireActivity()))
                .add(Const.AV_KEY_ACTIVE_LOG_UUID, DeviceIdUtil.getDeviceId(requireActivity()))
                .add(Const.AV_KEY_ACTIVE_SECRETKEY, secretKey)
                .add(Const.AV_KEY_ACTIVE_LOG_LOCATION, lcGeoPoint)
                .add(Const.AV_KEY_ACTIVE_LOG_MAC, DeviceIdentifierUtils.getMacAddressList().get(0))
                .add(Const.AV_KEY_ACTIVE_LOG_SERIAL, DeviceIdentifierUtils.getSerialNumber())
                .add(Const.AV_KEY_ACTIVE_LOG_CITY, lastKnownLocationInfo != null ? lastKnownLocationInfo.getCity() : "未知")
                .add(Const.AV_KEY_ACTIVE_LOG_VERSION_CODE, DeviceUtil.getVersionCode(requireActivity()))
                .add(Const.AV_KEY_ACTIVE_LOG_VERSION_TYPE, BuildConfig.VERSION_TYPE)
                .toObservable(String.class)
                // .asClass(ActivationcodeModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(students -> {
                    //请求成功
                    MyLog.v("记录激活码激活日志", new Gson().toJson(students) + "");
                }, throwable -> {
                    //请求失败
                    MyLog.v("记录激活码激活日志", "失败");
                });
    }

    @SuppressLint("CheckResult")
    private void dataCheck(String secretKey) {
        MyLog.v("设备鉴权查询", "Const.AV_KEY_ACTIVE_CODE_IMEI :   " + TelephonyManagement.getInstance().getDualSimChip(requireActivity()).getImei(0)
                + "    Const.AV_KEY_ACTIVE_CODE_ANDROID :   " + DeviceIdentifierUtils.getAndroidId(requireActivity()) +
                "    Const.AV_KEY_ACTIVE_CODE_UUID :" + DeviceIdUtil.getDeviceId(requireActivity()));

        HashMap<String, Object> babyMap = new HashMap<>();
        babyMap.put(Const.AV_KEY_SECRET_KEY, secretKey);
        babyMap.put(Const.AV_KEY_ACTIVE_CODE_IMEI, TelephonyManagement.getInstance().getDualSimChip(requireActivity()).getImei(0));
        babyMap.put(Const.AV_KEY_ACTIVE_CODE_ANDROID, DeviceIdentifierUtils.getAndroidId(requireActivity()));
        babyMap.put(Const.AV_KEY_ACTIVE_CODE_UUID, DeviceIdUtil.getDeviceId(requireActivity()));
        RxHttp.get(Const.LC_BASEURL + Const.AV_KEY_ACTIVE_CODE)
                .add("where", new Gson().toJson(babyMap))
                .add("keys", Const.AV_KEY_SECRET_KEY)
                .toObservable(ActivationcodeModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(students -> {
                    //请求成功
                    MyLog.v("设备鉴权查询", new Gson().toJson(students));
                    //非空判断
                    if (students.getResults() == null || students.getResults().isEmpty()) {
                        // MToast.makeTextShort("激活码错误");
                        payDialog.setError(getString(R.string.device_auth_failed));
                        recordActiveLog(0, getString(R.string.device_auth_failed), secretKey);
                        return;
                    }
                    recordActiveLog(1, getString(R.string.activation_restored), secretKey);
                    ActivationcodeModel.ResultsDTO resultsDTO = students.getResults().get(0);
                    AuthorityManager.setActivationState(true);
                    AuthorityManager.setActivationCode(resultsDTO.getSecretKey());
                    payDialog.setSucc();
                }, throwable -> {
                    //请求失败
                    MyLog.v("请求激活码", "失败");
                    payDialog.setError(getString(R.string.network_connection_error));
                });
    }

    @SuppressLint("CheckResult")
    private void activeSoftware(String secretKey, String objectId) {

        LocateInforModel lastKnownLocationInfo = LocationService.getInstance().getLastKnownLocationInfo();
        LCGeoPoint lcGeoPoint = new LCGeoPoint();
        lcGeoPoint.set__type("GeoPoint");
        if (lastKnownLocationInfo != null) {
            // 如果 currentLocation 不是 null，则设置经纬度

            lcGeoPoint.setLatitude(lastKnownLocationInfo.getLatitude());
            lcGeoPoint.setLongitude(lastKnownLocationInfo.getLongitude());
        } else {
            // 如果 currentLocation 是 null，则处理这种情况（例如，显示错误消息或尝试重新定位）
            lcGeoPoint.setLatitude(0.0);
            lcGeoPoint.setLongitude(0.0);
        }

        RxHttp.putJson(Const.AV_KEY_ACTIVE_CODE + "/" + objectId + "?" + "fetchWhenSave=false")
                .add(Const.AV_KEY_ACTIVE_CODE_STATE, 1)
                .add(Const.AV_KEY_ACTIVE_CODE_IMEI, TelephonyManagement.getInstance().getDualSimChip(requireActivity()).getImei(0))
                .add(Const.AV_KEY_ACTIVE_CODE_UUID, DeviceIdUtil.getDeviceId(requireActivity()))
                .add(Const.AV_KEY_ACTIVE_CODE_ANDROID, DeviceIdentifierUtils.getAndroidId(requireActivity()))
                .add(Const.AV_KEY_ACTIVE_CODE_MAC, DeviceIdentifierUtils.getMacAddressList().get(0))
                .add(Const.AV_KEY_ACTIVE_CODE_SERIAL, DeviceIdentifierUtils.getSerialNumber())
                .add(Const.AV_KEY_ACTIVE_CODE_LOCATION, lcGeoPoint)
                .add("keys", "updatedAt")
                .toObservable(String.class)
                // .asClass(ActivationcodeModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(students -> {
                    //请求成功
                    MyLog.v("修改激活信息", new Gson().toJson(students));
                    recordActiveLog(1, "成功激活", secretKey);
                    payDialog.setSucc();
                    AuthorityManager.setActivationCode(secretKey);
                    AuthorityManager.setActivationState(true);
                }, throwable -> {
                    //请求失败
                    MyLog.v("修改激活信息", "失败");
                    payDialog.setError(getString(R.string.network_connection_error));
                });
    }

    @Override
    public void onResume() {
        super.onResume();
        // 当从语言选择页面返回时，更新语言显示
        if (binding != null && binding.languageField != null) {
            binding.languageField.tvFieldDesc.setText(getCurrentLanguageName());
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (payDialog != null) {
            payDialog.dismiss();
            payDialog = null;
        }
        disposables.clear();
        binding = null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        disposables.dispose();
    }
}
