package com.smartcar.easylauncher.modules.car;

import static com.bytedance.scene.navigation.NavigationSceneGetter.requireNavigationScene;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bytedance.scene.animation.SharedElementSceneTransitionExecutor;
import com.bytedance.scene.animation.interaction.scenetransition.AutoSceneTransition;
import com.bytedance.scene.animation.interaction.scenetransition.SceneTransition;
import com.bytedance.scene.animation.interaction.scenetransition.visiblity.Fade;
import com.bytedance.scene.group.UserVisibleHintGroupScene;
import com.bytedance.scene.interfaces.PushOptions;
import com.bytedance.scene.ktx.NavigationSceneExtensionsKt;
import com.google.gson.Gson;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.personal.CarListAdapter;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.core.constants.Constants;
import com.smartcar.easylauncher.core.constants.StatusCodeModel;
import com.smartcar.easylauncher.databinding.ActivityCarListSceneBinding;
import com.smartcar.easylauncher.data.database.entity.CarModel;
import com.smartcar.easylauncher.infrastructure.event.scope.notice.cody.GeneralNotificationScopeBus;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.data.model.common.AjaxResult;
import com.smartcar.easylauncher.data.model.common.GeneralNoticeModel;
import com.smartcar.easylauncher.data.model.vehicle.DefaultVehicleDataModel;
import com.smartcar.easylauncher.data.model.vehicle.VehicleCarModel;
import com.smartcar.easylauncher.modules.trip.TripDetailsScene;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.util.List;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import rxhttp.RxHttp;
import rxhttp.wrapper.cache.CacheMode;


/**
 * <AUTHOR>
 * @date 2024/07/04
 */
public class CarListScene extends UserVisibleHintGroupScene implements View.OnClickListener {
    private static final String TAG = "CostStatisticsScene";
    private ActivityCarListSceneBinding binding;
    private CarListAdapter tripListAdapter;

    @NonNull
    @Override
    public ViewGroup onCreateView(@NonNull LayoutInflater inflater, @NonNull ViewGroup container, Bundle savedInstanceState) {
        binding = ActivityCarListSceneBinding.inflate(getLayoutInflater());
        initView();
        //初始化数据
        initData();
        //初始化添加按钮
        initAddButton();
        return binding.getRoot();
    }

    private void initData() {

        getCarData();
//        CarDbManager.INSTANCE.getAll().subscribe(cars -> {
//            tripListAdapter.submitList(cars);
//        });
    }

    @SuppressLint("CheckResult")
    private void getCarData() {
        String url = Const.VEHICLE_CAR_LIST;
        MyLog.v(TAG, "请求地址：" + url);

        RxHttp.get(url)
                .setCacheMode(CacheMode.REQUEST_NETWORK_FAILED_READ_CACHE)
                .toObservable(VehicleCarModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(vehicleCarModel -> {
                    if (vehicleCarModel.getCode() == StatusCodeModel.SUCCESS) {
                        // 请求成功
                        MyLog.v(TAG, "请求成功" + new Gson().toJson(vehicleCarModel));
                        List<CarModel> cars = vehicleCarModel.getRows();
                        tripListAdapter.submitList(cars);
                    }
                    // 请求成功
                    MyLog.v(TAG, "请求成功" + vehicleCarModel);
                }, throwable -> {
                    // 请求失败

                    MyLog.v(TAG, "请求失败" + throwable.getMessage());
                });
    }

    /**
     * 初始化view方法
     */
    private void initView() {
        binding.recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        tripListAdapter = new CarListAdapter();
        tripListAdapter.addOnItemChildClickListener(R.id.mBtnWifiCancel, (baseQuickAdapter, view, i) -> {
            CarModel item = tripListAdapter.getItem(i);
            setCarData(item.getCarId());


        });
        tripListAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            CarModel carModel = tripListAdapter.getItem(i);

            NavigationSceneExtensionsKt.requireNavigationScene(CarListScene.this).disableSupportRestore();

            ArrayMap<String, SceneTransition> map = new ArrayMap<>();
            map.put(TripDetailsScene.VIEW_NAME_HEADER_IMAGE + carModel.getCarId(), new AutoSceneTransition());
            map.put(TripDetailsScene.VIEW_NAME_HEADER_TITLE + carModel.getCarId(), new AutoSceneTransition());
            SharedElementSceneTransitionExecutor sharedElementSceneTransitionExecutor = new SharedElementSceneTransitionExecutor(map, new Fade());
            PushOptions build = new PushOptions.Builder().setAnimation(sharedElementSceneTransitionExecutor).setPushResultCallback(o -> {
                if (o != null) {
                    MyLog.v(TAG, "返回结果：" + o);
                    getCarData();
                }
            }).build();
            requireNavigationScene(CarListScene.this)
                    .push(new AddCarScene(carModel), build);
        });

        binding.recyclerView.setAdapter(tripListAdapter);
    }

    private void getDefaultCar() {
        String url = Const.VEHICLE_CAR_DEFAULT;
        MyLog.v(TAG, "请求地址：" + url);
        RxHttp.get(url)
                .toObservable(DefaultVehicleDataModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(defaultVehicleDataModel -> {
                    if (defaultVehicleDataModel.getCode() == StatusCodeModel.SUCCESS) {
                        // 初始化车辆信息
                        DataManager.setCarId(Long.valueOf(defaultVehicleDataModel.getData().getCarId()));
                        DataManager.setBrand(defaultVehicleDataModel.getData().getBrand());
                        DataManager.setModel(defaultVehicleDataModel.getData().getModel());
                        DataManager.setLicensePlate(defaultVehicleDataModel.getData().getLicensePlate());
                    }
                    // 请求成功
                    MyLog.v(TAG, "请求成功" + new Gson().toJson(defaultVehicleDataModel));
                }, throwable -> {
                    // 请求失败

                    MyLog.v(TAG, "请 求失败" + throwable.getMessage());
                });

    }

    private void setCarData(int id) {
        String url = Const.VEHICLE_CAR_SETDEFAULT + id;
        MyLog.v(TAG, "请求地址：" + url);

        RxHttp.putForm(url)
                .setCacheMode(CacheMode.REQUEST_NETWORK_FAILED_READ_CACHE)
                .toObservable(AjaxResult.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(vehicleCarModel -> {
                    if (vehicleCarModel.getCode() == StatusCodeModel.SUCCESS) {
                        // 请求成功
                        MyLog.v(TAG, "请求成功" + new Gson().toJson(vehicleCarModel));
                        getCarData();
                        // 发送车辆变更通知
                        GeneralNotificationScopeBus.eventBean().post(
                            new GeneralNoticeModel(Constants.NoticeType.VEHICLE_CHANGED, String.valueOf(id), true)
                        );
                    }
                    // 请求成功
                    MyLog.v(TAG, "请求成功" + vehicleCarModel);
                }, throwable -> {
                    // 请求失败
                    MyLog.v(TAG, "请求失败" + throwable.getMessage());
                });
    }

    @Override
    public void onClick(View view) {

    }

    private void initAddButton() {

        binding.btnAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (tripListAdapter.getItems().size() > 0) {
                    MToast.makeTextShort("已存在车辆");
                    return;
                }
                PushOptions pushOptions = new PushOptions.Builder().setAnimation(requireActivity(), R.anim.slide_in_from_right, R.anim.slide_out_to_left).build();
                PushOptions build = new PushOptions.Builder().setPushResultCallback(o -> {
                    if (o != null) {
                        MyLog.v(TAG, "返回结果：" + o);
                        getCarData();
                    }
                }).build();
                requireNavigationScene(CarListScene.this).push(new AddCarScene(null), build);
                //  getNavigationScene(CarListScene.this).push(new MyCarScene(null), build);
            }
        });
    }

    /**
     * 创建一个包含索引的Bundle对象
     *
     * @param index 要放入Bundle的索引值
     * @return 包含索引的Bundle
     */
    private Bundle getBundle(int index) {
        Bundle bundle = new Bundle();
        bundle.putInt(Const.FUEL_TYPE, index); // 将索引值放入Bundle
        return bundle;
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

}