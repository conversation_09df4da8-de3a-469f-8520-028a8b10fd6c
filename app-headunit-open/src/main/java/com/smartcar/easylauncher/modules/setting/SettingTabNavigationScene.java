package com.smartcar.easylauncher.modules.setting;

import android.annotation.SuppressLint;
import android.os.Bundle;

import androidx.annotation.NonNull;

import com.bytedance.scene.Scene;
import com.bytedance.scene.navigation.NavigationScene;
import com.bytedance.scene.navigation.NavigationSceneOptions;
import com.bytedance.scene.utlity.SceneInstanceUtility;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.widget.navigation.TabNavigationViewScene;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;


/**
 * 消费统计底部导航场景
 * <AUTHOR>
 * @date 2024/3/15
 */
public class SettingTabNavigationScene extends TabNavigationViewScene {

    /**
     * 重写此方法以返回底部导航项的布局ID
     *
     * @return 底部导航项的布局ID
     */
    @Override
    protected int getTabItemLayout() {
        return R.layout.layout_text_view;
    }

    /**
     * 重写此方法以返回底部导航项的标题列表
     *
     * @return 底部导航项的标题列表
     */
    @Override
    protected List<String> getNavigationTitles() {
        List<String> titles = new ArrayList<>();
        // 添加第一个底部导航项的标题
        titles.add(getString(R.string.access_control_settings));
        titles.add(getString(R.string.desktop_settings));
        titles.add(getString(R.string.custom_settings));
        titles.add(getString(R.string.system_settings));
        titles.add(getString(R.string.about));
        return titles;
    }

    /**
     * 重写此方法以返回整个导航场景的标题
     *
     * @return 导航场景的标题
     */
    @Override
    protected String getTitleName() {
        return getString(R.string.settings_title);
    }

    /**
     * 获取底部导航项与对应场景的映射关系
     *
     * @return 底部导航项与对应场景的映射关系
     */
    @NonNull
    @Override
    @SuppressLint("RestrictedApi")
    public LinkedHashMap<Integer, Scene> getSceneMap() {
        LinkedHashMap<Integer, Scene> linkedHashMap = new LinkedHashMap<>();

        // 为第一个底部导航项创建并配置场景
        NavigationScene navigationScene = (NavigationScene) SceneInstanceUtility.getInstanceFromClass(
                NavigationScene.class,
                new NavigationSceneOptions(BaseSettingScene.class, getBundle(0)).toBundle()
        );
        // 将第一个场景与索引0关联
        linkedHashMap.put(0, navigationScene);

        // 为第二个底部导航项创建并配置场景
        navigationScene = (NavigationScene) SceneInstanceUtility.getInstanceFromClass(
                NavigationScene.class,
                new NavigationSceneOptions(DesktopSettingScene.class, getBundle(1)).toBundle()
        );
        // 将第二个场景与索引1关联
        linkedHashMap.put(1, navigationScene);

        // 为第二个底部导航项创建并配置场景
        navigationScene = (NavigationScene) SceneInstanceUtility.getInstanceFromClass(
                NavigationScene.class,
                new NavigationSceneOptions(PersonalizedSettingScene.class, getBundle(1)).toBundle()
        );
        // 将第二个场景与索引1关联
        linkedHashMap.put(2, navigationScene);

        // 为第二个底部导航项创建并配置场景
        navigationScene = (NavigationScene) SceneInstanceUtility.getInstanceFromClass(
                NavigationScene.class,
                new NavigationSceneOptions(SystemSettingsScene.class, getBundle(1)).toBundle()
        );
        // 将第二个场景与索引1关联
        linkedHashMap.put(3, navigationScene);

        // 为第二个底部导航项创建并配置场景
        navigationScene = (NavigationScene) SceneInstanceUtility.getInstanceFromClass(
                NavigationScene.class,
                new NavigationSceneOptions(AboutUsScene.class, getBundle(1)).toBundle()
        );
        // 将第二个场景与索引1关联
        linkedHashMap.put(4, navigationScene);

        return linkedHashMap;
    }

    /**
     * 创建一个包含索引的Bundle对象
     *
     * @param index 要放入Bundle的索引值
     * @return 包含索引的Bundle
     */
    private Bundle getBundle(int index) {
        Bundle bundle = new Bundle();
        // 将索引值放入Bundle
        bundle.putInt("index", index);
        return bundle;
    }
}

