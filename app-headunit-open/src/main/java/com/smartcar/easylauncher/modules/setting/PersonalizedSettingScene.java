package com.smartcar.easylauncher.modules.setting;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.bytedance.scene.group.UserVisibleHintGroupScene;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.SettingsConstants;
import com.smartcar.easylauncher.databinding.FragmentPersonalizedSettingBinding;
import com.smartcar.easylauncher.shared.dialog.GeneralDialog;
import com.smartcar.easylauncher.infrastructure.event.scope.layout.cody.LyoutScopeBus;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.core.manager.ThemeHelper;
import com.smartcar.easylauncher.data.model.common.LayoutUPModel;
import com.smartcar.easylauncher.infrastructure.skin.SkinManager;
import com.smartcar.easylauncher.shared.utils.ui.ScreenUtils;
import com.smartcar.easylauncher.shared.view.VerticalSeekBar;
import com.smartcar.easylauncher.shared.view.segmentcontrlo.SegmentedControlItem;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * 个性化设置
 * <AUTHOR>
 */
public class PersonalizedSettingScene extends UserVisibleHintGroupScene {
    public  final String TAG = "PersonalizedSettingFragment";
    private FragmentPersonalizedSettingBinding binding;
    private boolean isDataInitialized = false;
    private final CompositeDisposable disposables = new CompositeDisposable();
    private FragmentActivity fragmentActivity;
    // 缓存列表，避免重复创建
    private final List<SegmentedControlItem> patternItems = new ArrayList<>();
    private final List<SegmentedControlItem> modelItems = new ArrayList<>();

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 不启用预加载，因为这不是首页
    }

    @Override
    public ViewGroup onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = FragmentPersonalizedSettingBinding.inflate(inflater, container, false);
        int cardSize = SettingsConstants.DEFAULT_MAP_LAYOUT_3.equals(SettingsManager.getMapLayout()) ||
                SettingsConstants.DEFAULT_MAP_LAYOUT_5.equals(SettingsManager.getMapLayout()) ?
                SettingsManager.getMapCardHeight() : SettingsManager.getMapCardWidth();
        binding.seekbar.setProgress(cardSize);
        fragmentActivity = (FragmentActivity) requireActivity();
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        // 1. 初始化列表数据 - 这是必要的UI数据
        initListData();
        // 2. 初始化基本UI结构
        initBasicUI();
        // 3. 加载可选数据
        loadOptionalData();
    }

    private void initListData() {
        // 初始化模式列表
        if (patternItems.isEmpty()) {
            patternItems.add(new SegmentedControlItem(getString(R.string.theme_auto)));
            patternItems.add(new SegmentedControlItem(getString(R.string.theme_day)));
            patternItems.add(new SegmentedControlItem(getString(R.string.theme_night)));
            patternItems.add(new SegmentedControlItem(getString(R.string.theme_amap)));
        }

        // 初始化数量列表
        if (modelItems.isEmpty()) {
            modelItems.add(new SegmentedControlItem(getString(R.string.card_count_1)));
            modelItems.add(new SegmentedControlItem(getString(R.string.card_count_2)));
            modelItems.add(new SegmentedControlItem(getString(R.string.card_count_3)));
            modelItems.add(new SegmentedControlItem(getString(R.string.card_count_4)));
        }
    }

    private void initBasicUI() {
        // 添加items到控件
        binding.scv2.addItems(patternItems);
        binding.scv3.addItems(modelItems);

        // 设置屏幕类型显示和控制
        setupScreenTypeField();

        // 设置恢复默认按钮
        setupResetField();

        // 设置seekbar初始值
        setupSeekBar();
    }

    private void setupSeekBar() {

        // 初始化seekbar监听器
        binding.seekbar.setOnStateChangeListener(new VerticalSeekBar.OnStateChangeListener() {
            @Override
            public void onStartTouch(View view) {}

            @Override
            public void onStateChangeListener(View view, float progress, float indicatorOffset) {}

            @Override
            public void onStopTrackingTouch(View view, float progress) {
                updateCardSize((int) progress);
            }
        });
    }

    private void loadOptionalData() {
        // 1. 加载当前设置
        disposables.add(Observable.fromCallable(() -> {
                Map<String, Integer> settings = new HashMap<>();
                settings.put("themeMode", SettingsManager.getThemeMode());
                settings.put("mapListCount", SettingsManager.getMapListCount());
                return settings;
            })
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(settings -> {
                // 设置主题选择
                int themeMode = settings.get("themeMode");
                if (themeMode >= 0 && themeMode < patternItems.size()) {
                    binding.scv2.setSelectedItem(themeMode);
                }

                // 设置卡片数量
                int currentCount = settings.get("mapListCount");
                if (currentCount >= 1 && currentCount <= modelItems.size()) {
                    binding.scv3.setSelectedItem(currentCount - 1);
                }

                // 设置选择监听器
                setupThemeSelection();
                setupCardCount();
            }));
    }

    private void setupThemeSelection() {
        binding.scv2.setOnSegItemClickListener((item, position) -> {
            updateThemeMode(position);
        });
    }

    private void setupCardCount() {
        binding.scv3.setOnSegItemClickListener((item, position) -> {
            updateCardCount(position + 1);
        });
    }

    private void updateThemeMode(int position) {
        disposables.add(Observable.fromCallable(() -> {
                SettingsManager.setThemeMode(position);
                return position;
            })
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(pos -> {
                switch (pos) {
                    case 0:
                        ThemeHelper.manualDetection();
                        break;
                    case 1:
                        setDayTheme();
                        break;
                    case 2:
                        setNightTheme();
                        break;
                    case 3:
                        requestTopicStatus();
                        break;
                }
            }));
    }

    private void updateCardCount(int count) {
        disposables.add(Observable.fromCallable(() -> {
                SettingsManager.setMapListCount(count);
                return count;
            })
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(newCount -> 
                LyoutScopeBus.eventBean().post(new LayoutUPModel(
                    SettingsConstants.LAYOUT_TYPE,
                    SettingsManager.getDefaultHome(),
                    SettingsManager.getMapLayout()
                ))
            ));
    }

    private void updateCardSize(int progress) {
        disposables.add(Observable.fromCallable(() -> {
                if (SettingsConstants.DEFAULT_MAP_LAYOUT_3.equals(SettingsManager.getMapLayout()) ||
                    SettingsConstants.DEFAULT_MAP_LAYOUT_5.equals(SettingsManager.getMapLayout())) {
                    SettingsManager.setMapCardHeight(progress);
                } else {
                    SettingsManager.setMapCardWidth(progress);
                }
                return progress;
            })
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(result -> 
                LyoutScopeBus.eventBean().post(new LayoutUPModel(
                    SettingsConstants.LAYOUT_TYPE,
                    SettingsManager.getDefaultHome(),
                    SettingsManager.getMapLayout()
                ))
            ));
    }

    private void setupResetField() {
        binding.resetField.tvFieldTitle.setText(getString(R.string.reset_default_title));
        binding.resetField.btnFieldAction.setText(getString(R.string.reset_default_button));
        binding.resetField.tvFieldDesc.setText(getString(R.string.reset_default_desc));
        binding.resetField.btnFieldAction.setOnClickListener(v -> showResetConfirmDialog());
    }

    /**
     * 设置屏幕类型显示和控制
     */
    private void setupScreenTypeField() {
        // 设置标题和按钮
        binding.screenTypeField.tvFieldTitle.setText(getString(R.string.screen_type_detection));
        binding.screenTypeField.btnFieldAction.setText(getString(R.string.screen_type_redetect));
        binding.screenTypeField.btnFieldAction.setOnClickListener(v -> forceDetectScreenType());

        // 显示当前屏幕类型
        updateScreenTypeDisplay();
    }

    /**
     * 更新屏幕类型显示
     */
    private void updateScreenTypeDisplay() {
        // 获取当前屏幕尺寸和类型
        int windowWidth = ScreenUtils.getWindowWidth(requireActivity());
        int windowHeight = ScreenUtils.getWindowHeigh(requireActivity());
        SettingsManager.setScreenType(-1);
        int currentScreenType = ScreenUtils.getScreenOrientation(windowWidth, windowHeight);

        String screenTypeName = getScreenTypeName(currentScreenType);
        String screenTypeDesc = getScreenTypeDescription(currentScreenType);

        // 更新UI显示
        String displayText = screenTypeName + " (" + windowWidth + "×" + windowHeight + ") - " + screenTypeDesc;
        binding.screenTypeField.tvFieldDesc.setText(displayText);

        Log.i(TAG, "屏幕类型更新: " + displayText);
    }

    /**
     * 强制重新检测屏幕类型
     */
    private void forceDetectScreenType() {
        disposables.add(Observable.fromCallable(() -> {
                // 强制重新检测屏幕类型
                int newScreenType = ScreenUtils.forceDetectScreenType(requireActivity());
                return newScreenType;
            })
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(newScreenType -> {
                // 更新显示
                updateScreenTypeDisplay();

                // 发送布局更新事件
                LyoutScopeBus.eventBean().post(new LayoutUPModel(
                    SettingsConstants.LAYOUT_TYPE,
                    SettingsManager.getDefaultHome(),
                    getLayoutValueByScreenType(newScreenType)
                ));

                // 显示检测结果
                String screenTypeName = getScreenTypeName(newScreenType);
                android.widget.Toast.makeText(requireActivity(),
                    getString(R.string.screen_type_detection_success) + ": " + screenTypeName,
                    android.widget.Toast.LENGTH_SHORT).show();
            }));
    }

    /**
     * 获取屏幕类型名称
     */
    private String getScreenTypeName(int screenType) {
        switch (screenType) {
            case ScreenUtils.ORIENTATION_PORTRAIT:
                return getString(R.string.screen_type_portrait);
            case ScreenUtils.ORIENTATION_LANDSCAPE:
                return getString(R.string.screen_type_landscape);
            case ScreenUtils.ORIENTATION_LONG_LANDSCAPE:
                return getString(R.string.screen_type_long_landscape);
            default:
                return getString(R.string.screen_type_unknown);
        }
    }

    /**
     * 获取屏幕类型描述
     */
    private String getScreenTypeDescription(int screenType) {
        switch (screenType) {
            case ScreenUtils.ORIENTATION_PORTRAIT:
                return getString(R.string.screen_type_portrait_desc);
            case ScreenUtils.ORIENTATION_LANDSCAPE:
                return getString(R.string.screen_type_landscape_desc);
            case ScreenUtils.ORIENTATION_LONG_LANDSCAPE:
                return getString(R.string.screen_type_long_landscape_desc);
            default:
                return getString(R.string.screen_type_unknown_desc);
        }
    }

    /**
     * 根据屏幕类型获取对应的布局值
     */
    private String getLayoutValueByScreenType(int screenType) {
        int homeType = SettingsManager.getDefaultHome();
        switch (homeType) {
            case SettingsConstants.HOME_CARD_LAYOUT_TYPE:
                return SettingsManager.getCardLayout();
            case SettingsConstants.HOME_MAP_LAYOUT_TYPE:
                return SettingsManager.getMapLayout();
            case SettingsConstants.HOME_PIP_LAYOUT_TYPE:
                return SettingsManager.getCardLayout();
            default:
                return SettingsManager.getCardLayout();
        }
    }

    private void showResetConfirmDialog() {
        GeneralDialog.showGeneralDialog(
                fragmentActivity.getSupportFragmentManager(),
            requireActivity(),
            getString(R.string.reset_confirm_title),
            getString(R.string.reset_confirm_message),
            getString(R.string.confirm),
            getString(R.string.cancel),
            new GeneralDialog.DialogClickListener() {
                @Override
                public void onPositiveClick() {
                    resetSettings();
                }

                @Override
                public void onNegativeClick() {}
            });
    }

    private void resetSettings() {
        disposables.add(Observable.fromCallable(() -> {
                DataManager.setFirstApp("");
                DataManager.setSecondApp("");
                DataManager.setThirdApp("");
                DataManager.setAppListData("");
                return true;
            })
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe());
    }

    /**
     * 设置白天主题
     */
    public void setDayTheme() {
        if (SettingsManager.getDayTheme().isEmpty()) {
            SkinManager.getInstance().restoreDefaultTheme();
        } else {
            SkinManager.getInstance().loadSkin(SettingsManager.getDayTheme());
        }
        Intent intent = new Intent();
        intent.setAction("AUTONAVI_STANDARD_BROADCAST_RECV");
        intent.putExtra("KEY_TYPE", 10017);
        intent.putExtra("EXTRA_HEADLIGHT_STATE", 1);
        getActivity().sendBroadcast(intent);
    }

    /**
     * 设置夜间主题
     */
    public void setNightTheme() {
        SkinManager.getInstance().loadSkin(SettingsManager.getNightTheme());
        Intent intent = new Intent();
        intent.setAction("AUTONAVI_STANDARD_BROADCAST_RECV");
        intent.putExtra("KEY_TYPE", 10017);
        intent.putExtra("EXTRA_HEADLIGHT_STATE", 0);
        getActivity().sendBroadcast(intent);
    }

    private void requestTopicStatus() {
        // 实现主题状态请求逻辑
    }

    @Override
    public void onResume() {
        super.onResume();

    }

    @Override
    public void onPause() {
        super.onPause();

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 清理所有订阅，防止内存泄漏
        disposables.clear();
        binding = null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        patternItems.clear();
        modelItems.clear();
        disposables.dispose();
    }
}
