package com.smartcar.easylauncher.modules.onboarding;

import android.app.AlertDialog;
import android.os.Bundle;
import android.text.method.LinkMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.annotation.NonNull;

import com.airbnb.lottie.LottieCompositionFactory;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.databinding.SceneOnboardingPrivacyBinding;
import com.smartcar.easylauncher.core.manager.AuthorityManager;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.ui.HyperlinkText;

/**
 * 引导流程 - 隐私协议场景
 * 展示隐私协议并获取用户同意
 *
 * <AUTHOR>
 */
public class OnboardingPrivacyScene extends BaseOnboardingScene<SceneOnboardingPrivacyBinding> {

    @Override
    protected SceneOnboardingPrivacyBinding getViewBinding(LayoutInflater inflater, ViewGroup container) {
        return SceneOnboardingPrivacyBinding.inflate(inflater, container, false);
    }

    @Override
    protected void initView() {
        MyLog.v(TAG, "隐私政策场景初始化");

        // 设置进度指示器状态
        if (binding != null) {
            binding.progressIndicator.setCurrentStep(2);
        }

        // 设置返回按钮可见性
        if (binding != null) {
            binding.ivBack.setVisibility(View.VISIBLE);
        }

        // 设置隐私协议超链接文本
        setupPrivacyLinks();
    }

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // 在视图创建完成后设置动画和Lottie
        setupLottieAnimation();
        setupAnimations();
    }

    /**
     * 设置动画效果
     */
    private void setupAnimations() {
        if (binding == null || getActivity() == null) return;

        // 添加淡入动画
        Animation fadeIn = AnimationUtils.loadAnimation(getActivity(), android.R.anim.fade_in);
        binding.tvTitle.startAnimation(fadeIn);
        binding.tvSubtitle.startAnimation(fadeIn);
        binding.ivBack.startAnimation(fadeIn);

        // 添加卡片动画
        Animation slideIn = AnimationUtils.loadAnimation(getActivity(), R.anim.slide_in_right);
        slideIn.setStartOffset(200);
        binding.clPrivacyContent.startAnimation(slideIn);

        // 添加按钮动画
        Animation fadeInDelayed = AnimationUtils.loadAnimation(getActivity(), android.R.anim.fade_in);
        fadeInDelayed.setStartOffset(400);
        binding.btnAgree.startAnimation(fadeInDelayed);
        binding.btnDisagree.startAnimation(fadeInDelayed);

        // 设置权限卡片动画
        animatePermissionCards();
    }

    /**
     * 设置权限卡片动画
     */
    private void animatePermissionCards() {
        if (binding == null || getActivity() == null) return;

        // 创建动画
        Animation fadeInSlide1 = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in_slide_up);
        Animation fadeInSlide2 = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in_slide_up);
        Animation fadeInSlide3 = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in_slide_up);
        Animation fadeInSlide4 = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in_slide_up);
        Animation fadeInSlide5 = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in_slide_up);
        Animation fadeInSlide6 = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in_slide_up);

        // 设置延迟
        fadeInSlide1.setStartOffset(300);
        fadeInSlide2.setStartOffset(400);
        fadeInSlide3.setStartOffset(500);
        fadeInSlide4.setStartOffset(600);
        fadeInSlide5.setStartOffset(700);
        fadeInSlide6.setStartOffset(800);

        // 应用动画
        binding.cardStoragePermission.startAnimation(fadeInSlide1);
        binding.cardLocationPermission.startAnimation(fadeInSlide2);
        binding.cardDeviceIdPermission.startAnimation(fadeInSlide3);
        binding.cardBluetoothPermission.startAnimation(fadeInSlide4);
        binding.cardAudioPermission.startAnimation(fadeInSlide5);
        binding.cardNetworkPermission.startAnimation(fadeInSlide6);
    }

    /**
     * 设置隐私协议超链接文本
     */
    private void setupPrivacyLinks() {
        if (binding == null || getActivity() == null) return;

        try {
            // 获取字符串资源并记录
            String privacyText = getString(R.string.user_and_privacy);
            MyLog.v(TAG, "隐私协议文本: " + privacyText);

            // 检查文本是否包含关键词
            boolean hasUserProtocol = privacyText.contains("用户协议");
            boolean hasPrivacyPolicy = privacyText.contains("隐私政策");
            MyLog.v(TAG, "包含用户协议: " + hasUserProtocol + ", 包含隐私政策: " + hasPrivacyPolicy);

            // 设置隐私协议摘要中的超链接
            binding.tvPrivacySummary.setText(HyperlinkText.getClickableSpan(getActivity(), privacyText));
            binding.tvPrivacySummary.setMovementMethod(LinkMovementMethod.getInstance());
        } catch (Exception e) {
            // 发生异常时记录错误并使用普通文本
            MyLog.e(TAG, "设置隐私协议超链接失败: " + e.getMessage());
            if (binding != null) {
                binding.tvPrivacySummary.setText(getString(R.string.user_and_privacy));
            }
        }
    }

    /**
     * 设置Lottie动画
     */
    private void setupLottieAnimation() {
        if (binding == null || getActivity() == null) return;

        try {
            // 使用LottieCompositionFactory加载动画
            LottieCompositionFactory.fromAsset(getActivity(), "privacy_animation.json")
                    .addListener(composition -> {
                        if (composition != null && binding != null) {
                            // 设置动画
                            binding.lottieAnimation.setComposition(composition);
                            // 设置重复次数
                            binding.lottieAnimation.setRepeatCount(0);
                            // 设置动画速度
                            binding.lottieAnimation.setSpeed(0.8f);
                            // 播放动画
                            binding.lottieAnimation.playAnimation();
                        }
                    })
                    .addFailureListener(exception -> {
                        MyLog.e(TAG, "Lottie动画加载失败: " + exception.getMessage());
                        // 加载失败时设置备用图标
                        if (binding != null) {
                            binding.lottieAnimation.setImageResource(R.drawable.ic_privacy_shield);
                        }
                    });
        } catch (Exception e) {
            MyLog.e(TAG, "Lottie动画设置异常: " + e.getMessage());
            // 发生异常时设置备用图标
            if (binding != null) {
                binding.lottieAnimation.setImageResource(R.drawable.ic_privacy_shield);
            }
        }
    }



    @Override
    protected void setupListeners() {
        if (binding == null) return;

        // 设置返回按钮点击事件
        binding.ivBack.setOnClickListener(v -> navigateBack());

        // 设置同意按钮点击事件
        binding.btnAgree.setOnClickListener(v -> {
            // 保存用户同意了隐私政策
            AuthorityManager.setUseLicense(true);
            // 导航到桌面模式选择页面
            navigateToNext(new OnboardingPermissionScene());
        });

        // 设置不同意按钮点击事件
        binding.btnDisagree.setOnClickListener(v -> {
            // 显示提示对话框
            showExitConfirmationDialog();
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        // 页面重新可见时，如果动画没有在播放，则重新播放
        if (binding != null && !binding.lottieAnimation.isAnimating()) {
            binding.lottieAnimation.playAnimation();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        // 页面不可见时，取消动画
        if (binding != null && binding.lottieAnimation.isAnimating()) {
            binding.lottieAnimation.cancelAnimation();
        }
    }

    /**
     * 显示不同意确认对话框
     */
    private void showExitConfirmationDialog() {
        if (getActivity() == null) return;

        new AlertDialog.Builder(getActivity())
                .setTitle(getString(R.string.confirm))
                .setMessage(getString(R.string.privacy_policy_warning))
                .setPositiveButton(getString(R.string.view_agreement), (dialog, which) -> dialog.dismiss())
                .setNegativeButton(getString(R.string.exit), (dialog, which) -> {
                    dialog.dismiss();
                    // 设置不同意隐私协议的状态
                    AuthorityManager.setUseLicense(false);
                    // 退出应用
                    if (getActivity() != null) {
                        getActivity().moveTaskToBack(true);
                    }
                })
                .setCancelable(false)
                .show();
    }
} 