package com.smartcar.easylauncher.modules.touch.util;

import android.content.Context;
import android.content.Intent;
import android.os.Build;

import com.smartcar.easylauncher.modules.touch.FloatViewLiveData;
import com.smartcar.easylauncher.modules.touch.service.CoreAccessibilityService;
import com.smartcar.easylauncher.modules.touch.service.FloatWindowService;
import com.smartcar.easylauncher.modules.touch.setting.FloatWindowSetting;


/**
 * <b>Package:</b> com.zh.touchassistant.util <br>
 * <b>FileName:</b> FloatServiceUtil <br>
 * <b>Create Date:</b> 2018/12/14  下午11:23 <br>
 * <b>Author:</b> zihe <br>
 * <b>Description:</b>  <br>
 */
public class FloatServiceUtil {
    private FloatServiceUtil() {
    }

//    public static int getFloatButtonX(Context context) {
//        return Property.getDefault().getProperty(AccessibilityConstant.Config.KEY_FLOAT_BUTTON_X,
//                ScreenUtil.getPointFromScreenWidthRatio(context.getApplicationContext(), 0.8f));
//    }
//
//    public static int getFloatButtonY(Context context) {
//        return Property.getDefault().getProperty(AccessibilityConstant.Config.KEY_FLOAT_BUTTON_Y,
//                ScreenUtil.getPointFromScreenHeightRatio(context.getApplicationContext(), 0.3f));
//    }
//
//    public static int getFloatPanelX() {
//        return Property.getDefault().getProperty(AccessibilityConstant.Config.KEY_FLOAT_PANEL_X, 0);
//    }
//
//    public static int getFloatPanelY() {
//        return Property.getDefault().getProperty(AccessibilityConstant.Config.KEY_FLOAT_PANEL_Y, 0);
//    }

    public static void openFloatButton() {
        FloatWindowSetting.getFloatViewLiveData().setValue(true);
//        LauncherApp assistantApp = (LauncherApp) ContextProvider.get().getApplication();
//        assistantApp.getFloatViewLiveData().setValue(true);
    }

    public static void closeFloatButton() {
//        LauncherApp assistantApp = (LauncherApp) ContextProvider.get().getApplication();
//        assistantApp.getFloatViewLiveData().setValue(false);
        FloatWindowSetting.getFloatViewLiveData().setValue(false);
    }

    public static boolean isFloatButtonOpen() {
//        LauncherApp assistantApp = (LauncherApp) ContextProvider.get().getApplication();
//        FloatViewLiveData floatViewLiveData = assistantApp.getFloatViewLiveData();
        FloatViewLiveData floatViewLiveData = FloatWindowSetting.getFloatViewLiveData();
        return floatViewLiveData.isOpen();
    }

    public static void toggleFloatButton() {
        if (isFloatButtonOpen()) {
            closeFloatButton();
        } else {
            openFloatButton();
        }
    }

    /**
     * 显示悬浮球
     */
    public static void showFloatWindow(Context context) {
        setEnableFloatWindow(context, true);
    }

    /**
     * 隐藏悬浮球
     */
    public static void hideFloatWindow(Context context) {
        setEnableFloatWindow(context, false);
    }

    /**
     * 开启循环检测
     */
    public static void startLoopCheck(Context context) {
        setEnableLoopCheck(context, true);
    }

    /**
     * 停止循环检测
     */
    public static void stopLoopCheck(Context context) {
        setEnableLoopCheck(context, false);
    }

    /**
     * 开关悬浮窗
     */
    public static void setEnableFloatWindow(Context context, boolean isEnable) {
        Intent intent = new Intent(context, FloatWindowService.class);
        if (isEnable) {
            intent.setAction(FloatWindowService.Action.ACTION_SHOW_FLOATING_WINDOW);
        } else {
            intent.setAction(FloatWindowService.Action.ACTION_HIDE_FLOATING_WINDOW);
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }
    }

    /**
     * 开关循环检测
     */
    public static void setEnableLoopCheck(Context context, boolean isEnable) {
        Intent intent = new Intent(context, CoreAccessibilityService.class);
        if (isEnable) {
            intent.setAction(CoreAccessibilityService.Action.ACTION_START_LOOP_CHECK);
        } else {
            intent.setAction(CoreAccessibilityService.Action.ACTION_STOP_LOOP_CHECK);
        }
        context.startService(intent);
    }
}