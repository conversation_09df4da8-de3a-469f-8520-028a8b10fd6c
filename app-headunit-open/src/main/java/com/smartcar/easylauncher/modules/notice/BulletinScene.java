package com.smartcar.easylauncher.modules.notice;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bytedance.scene.group.UserVisibleHintGroupScene;
import com.bytedance.scene.interfaces.PushOptions;
import com.bytedance.scene.ktx.NavigationSceneExtensionsKt;
import com.google.gson.Gson;
import com.smartcar.easylauncher.shared.adapter.personal.NoticeListAdapter;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.core.constants.StatusCodeModel;
import com.smartcar.easylauncher.databinding.SceneTodayTripBinding;
import com.smartcar.easylauncher.data.model.common.NoticeDataModel;
import com.smartcar.easylauncher.shared.utils.MyLog;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import rxhttp.RxHttp;

/**
 * 公告
 * <AUTHOR>
 */
public class BulletinScene extends UserVisibleHintGroupScene {
    private static final String TAG = "BulletinScene";
    private SceneTodayTripBinding binding;

    private NoticeListAdapter tripListAdapter;

    @NonNull
    @Override
    public ViewGroup onCreateView(@NonNull LayoutInflater layoutInflater, @NonNull ViewGroup viewGroup, @Nullable Bundle bundle) {
        binding = SceneTodayTripBinding.inflate(getLayoutInflater());
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        //初始化view方法
        initView();
        //初始化数据
        initData();
    }

    /**
     * 初始化view方法
     */
    private void initView() {
        binding.recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        tripListAdapter = new NoticeListAdapter();
        tripListAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            NoticeDataModel.RowsDTO tripModel = tripListAdapter.getItem(i);
            Bundle bundle = new Bundle();
            bundle.putSerializable("Trip", tripModel);


            NavigationSceneExtensionsKt.requireNavigationScene(BulletinScene.this).disableSupportRestore();

//                ArrayMap<String, SceneTransition> map = new ArrayMap<>();
//                map.put(com.smartcar.easylauncher.ui.personal.trip.TripDetailsScene.VIEW_NAME_HEADER_IMAGE + tripModel.getId(), new AutoSceneTransition());
//                map.put(com.smartcar.easylauncher.ui.personal.trip.TripDetailsScene.VIEW_NAME_HEADER_TITLE + tripModel.getId(), new AutoSceneTransition());
//                map.put(com.smartcar.easylauncher.ui.personal.trip.TripDetailsScene.TOTAL_DISTANCE_NUM + tripModel.getId(), new AutoSceneTransition());
//                map.put(com.smartcar.easylauncher.ui.personal.trip.TripDetailsScene.TOTAL_TIME_NUM + tripModel.getId(), new AutoSceneTransition());
//                map.put(com.smartcar.easylauncher.ui.personal.trip.TripDetailsScene.DURATION_NUM + tripModel.getId(), new AutoSceneTransition());
//                SharedElementSceneTransitionExecutor sharedElementSceneTransitionExecutor = new SharedElementSceneTransitionExecutor(map, new Fade());
            NavigationSceneExtensionsKt.requireNavigationScene(BulletinScene.this)
                    .push(new NoticeDetailsScene(tripModel), new PushOptions.Builder().build());
        });
        binding.recyclerView.setAdapter(tripListAdapter);
    }

    /**
     * 初始化数据
     */
    private void initData() {
        getNoticeData();
    }

    @SuppressLint("CheckResult")
    private void getNoticeData() {
        String url = Const.NOTICE;
        MyLog.v(TAG, "请求地址：" + url);
        RxHttp.get(url)
                .add("noticeType", "1")
                .add("status", "0")
                .toObservable(NoticeDataModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(noticeDataModel -> {
                    if (noticeDataModel.getCode() == StatusCodeModel.SUCCESS) {
                        // 请求成功
                        MyLog.v(TAG, "请求成功" + new Gson().toJson(noticeDataModel));
                        tripListAdapter.submitList(noticeDataModel.getRows());
                    }
                    // 请求成功
                    MyLog.v(TAG, "请求成功" + noticeDataModel);
                }, throwable -> {
                    // 请求失败

                    MyLog.v(TAG, "请求失败" + throwable.getMessage());
                });
    }

}
