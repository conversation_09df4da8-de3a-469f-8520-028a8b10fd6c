package com.smartcar.easylauncher.modules.trip;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bytedance.scene.animation.SharedElementSceneTransitionExecutor;
import com.bytedance.scene.animation.interaction.scenetransition.AutoSceneTransition;
import com.bytedance.scene.animation.interaction.scenetransition.SceneTransition;
import com.bytedance.scene.animation.interaction.scenetransition.visiblity.Fade;
import com.bytedance.scene.group.UserVisibleHintGroupScene;
import com.bytedance.scene.interfaces.PushOptions;
import com.bytedance.scene.ktx.NavigationSceneExtensionsKt;
import com.smartcar.easylauncher.shared.adapter.personal.TripLIstAdapter;
import com.smartcar.easylauncher.databinding.SceneTodayTripBinding;
import com.smartcar.easylauncher.data.database.entity.TripModel;
import com.smartcar.easylauncher.data.repository.TripRepository;


/**
 * 总行程场景
 *
 * <AUTHOR>
 */
public class TotalTripScene extends UserVisibleHintGroupScene {
    private SceneTodayTripBinding binding;
    private TripLIstAdapter tripListAdapter;

    @NonNull
    @Override
    public ViewGroup onCreateView(@NonNull LayoutInflater layoutInflater, @NonNull ViewGroup viewGroup, @Nullable Bundle bundle) {
        binding = SceneTodayTripBinding.inflate(getLayoutInflater());
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        //初始化view方法
        initView();
        //初始化数据
        initData();
    }

    /**
     * 初始化view方法
     */
    private void initView() {
        binding.recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        tripListAdapter = new TripLIstAdapter();
        tripListAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            TripModel tripModel = tripListAdapter.getItem(i);
            Bundle bundle = new Bundle();
            bundle.putSerializable("Trip", tripModel);


            NavigationSceneExtensionsKt.requireNavigationScene(TotalTripScene.this).disableSupportRestore();

            ArrayMap<String, SceneTransition> map = new ArrayMap<>();
            assert tripModel != null;
            map.put(TripDetailsScene.VIEW_NAME_HEADER_IMAGE + tripModel.getId(), new AutoSceneTransition());
            map.put(TripDetailsScene.VIEW_NAME_HEADER_TITLE + tripModel.getId(), new AutoSceneTransition());
            map.put(TripDetailsScene.TOTAL_DISTANCE_NUM + tripModel.getId(), new AutoSceneTransition());
            map.put(TripDetailsScene.TOTAL_TIME_NUM + tripModel.getId(), new AutoSceneTransition());
            map.put(TripDetailsScene.DURATION_NUM + tripModel.getId(), new AutoSceneTransition());
            SharedElementSceneTransitionExecutor sharedElementSceneTransitionExecutor = new SharedElementSceneTransitionExecutor(map, new Fade());
            NavigationSceneExtensionsKt.requireNavigationScene(TotalTripScene.this)
                    .push(new TripDetailsScene(tripModel), new PushOptions.Builder().setAnimation(sharedElementSceneTransitionExecutor).build());
        });
        binding.recyclerView.setAdapter(tripListAdapter);
    }

    /**
     * 初始化数据
     */
    private void initData() {
        TripRepository.getInstance().getAllTrips().observe(this, tripModels -> {
            tripListAdapter.submitList(tripModels);
        });
//        //获取Room数据库行程数据
//        TripDbManager instance = TripDbManager.getInstance();
//        instance.getAllTrips().subscribe(new Observer<>() {
//            @Override
//            public void onSubscribe(Disposable d) {
//                MyLog.e("TAG", "onSubscribe: ");
//            }
//
//            @Override
//            public void onNext(List<TripModel> tripModels) {
//                MyLog.e("TAG", "onNext: " + tripModels.size());
//                tripListAdapter.submitList(tripModels);
//            }
//
//            @Override
//            public void onError(Throwable e) {
//                MyLog.e("TAG", "onError: " + e.getMessage());
//            }
//
//            @Override
//            public void onComplete() {
//                MyLog.e("TAG", "onComplete: ");
//            }
//        });

    }
}
