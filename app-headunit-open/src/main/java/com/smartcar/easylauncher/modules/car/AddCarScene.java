package com.smartcar.easylauncher.modules.car;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;
import androidx.fragment.app.FragmentActivity;

import com.bumptech.glide.Glide;
import com.bytedance.scene.group.UserVisibleHintGroupScene;
import com.google.gson.Gson;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.core.constants.StatusCodeModel;
import com.smartcar.easylauncher.databinding.SceneMyCarBinding;
import com.smartcar.easylauncher.data.database.entity.CarModel;
import com.smartcar.easylauncher.shared.dialog.CarSelectionDialog;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.core.manager.UserManager;
import com.smartcar.easylauncher.data.model.common.ResponseModel;
import com.smartcar.easylauncher.data.model.vehicle.CarSelectModel;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.view.segmentcontrlo.SegmentedControlItem;
import com.smartcar.easylauncher.shared.view.segmentcontrlo.SegmentedControlView;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import rxhttp.RxHttp;

/**
 * <AUTHOR>
 */
public class AddCarScene extends UserVisibleHintGroupScene {
    private static final String TAG = AddCarScene.class.getSimpleName();
    private SceneMyCarBinding binding;
    public static CarModel mCarModel;
    private CarSelectModel mBrandSelect;

    public static final String VIEW_NAME_HEADER_IMAGE = "detail:header:image";
    public static final String VIEW_NAME_HEADER_TITLE = "detail:header:title";

    public AddCarScene(CarModel carModel) {
        mCarModel = carModel;
    }

    @NonNull
    @Override
    public ViewGroup onCreateView(@NonNull LayoutInflater layoutInflater, @NonNull ViewGroup viewGroup, @Nullable Bundle bundle) {
        binding = SceneMyCarBinding.inflate(getLayoutInflater());
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        //初始化数据
        initData();
        //初始化view方法
        initView();
    }

    @Override
    public void onActivityCreated(@Nullable @org.jetbrains.annotations.Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        if (mCarModel != null) {
            ViewCompat.setTransitionName(binding.ivCar, VIEW_NAME_HEADER_IMAGE + mCarModel.getCarId());
            ViewCompat.setTransitionName(binding.inputView, VIEW_NAME_HEADER_TITLE + mCarModel.getCarId());
        }
        //设置图片
        Glide.with(requireApplicationContext()).load(R.drawable.add_car_bg1).into(binding.ivCar);
    }

    private void initData() {
        List<SegmentedControlItem> days = createDays();
        List<SegmentedControlItem> pattern = pattern();
        List<SegmentedControlItem> model = model();
        binding.scv1.addItems(days);
        binding.scv2.addItems(pattern);
        binding.scv3.addItems(model);
        // requireNavigationScene(MyCarScene.this).setResult(MyCarScene.this, "hahahah");

        if (mCarModel == null) {
            binding.btDelete.setVisibility(View.GONE);
            return;
        }
        binding.inputView.setPlateNum(DataManager.getLicensePlate());
        //动力类型处理方法
        powerType();
        //排放级别处理方法
        emissionLevel();
        //etc处理逻辑
        etc();
        //品牌车型处理方法
        brandModel();

    }

    private void brandModel() {
        if (mCarModel.getBrand() != null && mCarModel.getModel() != null) {
            CarSelectModel carSelectModel = new CarSelectModel();
            carSelectModel.setBrand(mCarModel.getBrand());
            carSelectModel.setModel(mCarModel.getModel());
            mBrandSelect = carSelectModel;
            binding.tvRedianText.setText(mCarModel.getBrand() + " " + mCarModel.getModel());
            //里程数
            binding.etTotalMileage.setText(String.valueOf(mCarModel.getTotalMileage()));
        }
    }

    private void etc() {
        if ("Y".equals(mCarModel.getHasEtc())) {
            binding.scv3.setSelectedItem(0);
        } else {
            binding.scv3.setSelectedItem(1);
        }
    }

    private void emissionLevel() {
        switch (mCarModel.getEuroCategory()) {
            case "2":
                binding.scv2.setSelectedItem(0);
                break;
            case "3":
                binding.scv2.setSelectedItem(1);
                break;
            case "4":
                binding.scv2.setSelectedItem(2);
                break;
            case "5":
                binding.scv2.setSelectedItem(3);
                break;
            case "6":
                binding.scv2.setSelectedItem(4);
                break;
            default:
        }
    }

    private void powerType() {
        switch (mCarModel.getFuelType()) {
            case "0":
                binding.scv1.setSelectedItem(0);
                break;
            case "1":
                binding.scv1.setSelectedItem(1);
                break;
            case "2":
                binding.scv1.setSelectedItem(2);
                break;
            default:
        }
    }

    private void initView() {
        binding.btDelete.setOnClickListener(v -> deleteData(mCarModel));
        //车牌号处理逻辑
        binding.inputView.setmPlateNumViewTextWatcher(number -> {
            if (binding.inputView.getPlateNum().length() >= 7) {
                Log.d("PlateNumView", "onTextChanged: " + binding.inputView.getPlateNum());
                DataManager.setLicensePlate(binding.inputView.getPlateNum());
            }

        });
        //动力类型
        binding.scv1.setOnSegItemClickListener((item, position) -> DataManager.setPowerType(String.valueOf(item.getCode())));
        //排放级别
        binding.scv2.setOnSegItemClickListener((item, position) -> DataManager.setEmission(String.valueOf(item.getCode())));
        //ETC处理逻辑
        binding.scv3.setOnSegItemClickListener(new SegmentedControlView.OnSegItemClickListener() {
            @Override
            public void onItemClick(SegmentedControlItem item, int position) {
                switch (position) {
                    case 0:
                        DataManager.setHasETC("Y");
                        break;
                    case 1:
                        DataManager.setHasETC("N");
                        break;
                    default:
                }
            }
        });
        //车型选择处理逻辑
        binding.redian.setOnClickListener(v -> {
            FragmentActivity activity = (FragmentActivity) requireActivity();
            CarSelectionDialog.showGeneralDialog(activity.getSupportFragmentManager(), getApplicationContext(), new CarSelectionDialog.DialogClickListener() {
                @Override
                public void onPositiveClick(CarSelectModel brandSelect) {
                    mBrandSelect = brandSelect;
                    binding.tvRedianText.setText(brandSelect.getBrand() + " " + brandSelect.getModel());
                }

            });
        });
        binding.btback.setOnClickListener(v -> getNavigationScene(AddCarScene.this).pop());
        //保存数据
        binding.btSave.setOnClickListener(v -> {
            if (binding.etTotalMileage.getText().toString().trim().isEmpty()) {
                MToast.makeTextShort("请输入里程总数");
                return;
            }

            if (binding.inputView.getPlateNum().length() < 7) {
                MToast.makeTextShort("请输入车牌号");
                return;
            }

            if (mBrandSelect == null) {
                MToast.makeTextShort("请先选择车型");
                return;
            }
            DataManager.setBrand(mBrandSelect.getBrand());
            DataManager.setModel(mBrandSelect.getModel());
            if (mCarModel == null) {
                mCarModel = new CarModel();
            }
            mCarModel.setBrand(DataManager.getBrand());
            mCarModel.setModel(DataManager.getModel());
            mCarModel.setFuelType(DataManager.getPowerType());
            mCarModel.setEuroCategory(DataManager.getEmission());
            mCarModel.setLicensePlate(DataManager.getLicensePlate());
            mCarModel.setTotalMileage(Integer.parseInt(binding.etTotalMileage.getText().toString().trim()));
            mCarModel.setHasEtc(DataManager.getHasETC());
            mCarModel.setUserId(Long.valueOf(UserManager.getUserId()));
            if (mCarModel.getCarId() == 0) {
                //插入数据
                insertData(mCarModel);
            } else {
                //更新数据
                updateData(mCarModel);
            }

//                CarModel carModel = new CarModel();
//                carModel.setBrand(DataManager.getBrand());
//                carModel.setModel(DataManager.getModel());
//                carModel.setFuelType(DataManager.getPowerType());
//                carModel.setEuroCategory(DataManager.getEmission());
//                carModel.setLicensePlate(DataManager.getLicensePlate());
//                carModel.setTotalMileage(Integer.parseInt(binding.etTotalMileage.getText().toString().trim()));
//                carModel.setHasEtc(DataManager.getHasETC());
//                carModel.setSubtotalMileage(0);
//                carModel.setCarModelImage("");
//                CarDbManager.INSTANCE.insert(carModel).subscribe(new Observer<Long>() {
//                    @Override
//                    public void onSubscribe(Disposable d) {
//                        MyLog.e("TAG", "onSubscribe: ");
//                    }
//
//                    @Override
//                    public void onNext(Long aLong) {
//                        MToast.makeTextShort("保存成功");
//                        getNavigationScene(MyCarScene.this).pop();
//                    }
//
//                    @Override
//                    public void onError(Throwable e) {
//                        MToast.makeTextShort("保存失败" + e.getMessage());
//                        MyLog.e("TAG", "onError: " + e.getMessage());
//                    }
//
//                    @Override
//                    public void onComplete() {
//                        MyLog.e("TAG", "onComplete: ");
//                    }
//                });
        });

    }

    @SuppressLint("CheckResult")
    private void deleteData(CarModel mCarModel) {
        RxHttp.deleteJson(Const.VEHICLE_CAR + "/" + mCarModel.getCarId())
                .toObservable(ResponseModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(responseModel -> {
                    MyLog.v(TAG, new Gson().toJson(responseModel));
                    if (responseModel.getCode() == StatusCodeModel.SUCCESS) {
                        getNavigationScene(AddCarScene.this).setResult(AddCarScene.this, "hahahah");
                        getNavigationScene(AddCarScene.this).pop();
                        MToast.makeTextShort("已删除");
                    } else {
                        MToast.makeTextShort(responseModel.getMsg());
                    }
                }, throwable -> {
                    MToast.makeTextShort("网络连接失败，请检查网络设置。");
                    MyLog.e("TAG", "onError: " + throwable.getMessage());
                });
    }

    @SuppressLint("CheckResult")
    private void updateData(CarModel mCarModel) {
        RxHttp.putJson(Const.VEHICLE_CAR)
                .addAll(new Gson().toJson(mCarModel))
                .toObservable(ResponseModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(responseModel -> {
                    MyLog.v(TAG, new Gson().toJson(responseModel));
                    if (responseModel.getCode() == StatusCodeModel.SUCCESS) {
                        getNavigationScene(AddCarScene.this).setResult(AddCarScene.this, "hahahah");
                        getNavigationScene(AddCarScene.this).pop();
                        MToast.makeTextShort("已修改");
                    } else {
                        MToast.makeTextShort(responseModel.getMsg());
                    }
                }, throwable -> {
                    MToast.makeTextShort("网络连接失败，请检查网络设置。");
                    MyLog.e("TAG", "onError: " + throwable.getMessage());
                });

    }

    @SuppressLint("CheckResult")
    private void insertData(CarModel mCarModel) {
        RxHttp.postJson(Const.VEHICLE_CAR)
                .addAll(new Gson().toJson(mCarModel))
                .toObservable(ResponseModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(responseModel -> {
                    MyLog.v(TAG, new Gson().toJson(responseModel));
                    if (responseModel.getCode() == StatusCodeModel.SUCCESS) {
                        getNavigationScene(AddCarScene.this).setResult(AddCarScene.this, "hahahah");
                        getNavigationScene(AddCarScene.this).pop();
                        MToast.makeTextShort("已添加");
                    } else {
                        MToast.makeTextShort(responseModel.getMsg());
                    }
                }, throwable -> {
                    MToast.makeTextShort("网络连接失败，请检查网络设置。");
                    MyLog.e("TAG", "onError: " + throwable.getMessage());
                });

    }


    private List<SegmentedControlItem> createDays() {
        List<SegmentedControlItem> items = new ArrayList<>();
        items.add(new SegmentedControlItem(0, "汽油"));
        items.add(new SegmentedControlItem(1, "柴油"));
        items.add(new SegmentedControlItem(2, "电动"));
        items.add(new SegmentedControlItem(3, "混动"));
        return items;
    }

    private List<SegmentedControlItem> pattern() {
        List<SegmentedControlItem> items = new ArrayList<>();
        items.add(new SegmentedControlItem(2, "国二"));
        items.add(new SegmentedControlItem(3, "国三"));
        items.add(new SegmentedControlItem(4, "国四"));
        items.add(new SegmentedControlItem(5, "国五"));
        items.add(new SegmentedControlItem(6, "国六"));
        return items;
    }

    private List<SegmentedControlItem> model() {
        List<SegmentedControlItem> items = new ArrayList<>();
        items.add(new SegmentedControlItem("有ETC"));
        items.add(new SegmentedControlItem("无ETC"));
        return items;
    }
}
