package com.smartcar.easylauncher.modules.expense;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;

import android.annotation.SuppressLint;
import android.app.DatePickerDialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bytedance.scene.Scene;
import com.github.gzuliyujiang.wheelpicker.DatePicker;
import com.github.gzuliyujiang.wheelpicker.annotation.DateMode;
import com.github.gzuliyujiang.wheelpicker.contract.OnDatePickedListener;
import com.github.gzuliyujiang.wheelpicker.entity.DateEntity;
import com.github.gzuliyujiang.wheelpicker.impl.UnitDateFormatter;
import com.github.gzuliyujiang.wheelpicker.widget.DateWheelLayout;
import com.google.gson.Gson;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.core.constants.StatusCodeModel;
import com.smartcar.easylauncher.databinding.SceneAddCostBinding;
import com.smartcar.easylauncher.data.database.dbmager.CostDbManager;
import com.smartcar.easylauncher.data.database.entity.Expense;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.core.manager.UserManager;
import com.smartcar.easylauncher.data.model.common.ResponseModel;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observer;
import io.reactivex.rxjava3.disposables.Disposable;
import rxhttp.RxHttp;

/**
 * 定义一个继承自Scene的类，用于添加费用
 */
public class AddExpenseScene extends Scene {
    private static final String TAG = AddExpenseScene.class.getName();
    /**
     * 绑定视图，用于访问布局中的元素
     */
    private SceneAddCostBinding binding;
    /**
     * 费用类型
     */
    private int feeType;
    /**
     * 费用ID，默认为0
     */
    private long expenseId = 0L;

    /**
     * 传递的参数
     */
    private Bundle arguments;

    // 静态方法来创建Fragment实例并设置参数
    public static AddExpenseScene addInstance(int index) {
        AddExpenseScene scene = new AddExpenseScene();
        Bundle bundle = new Bundle();
        bundle.putInt(Const.FUEL_TYPE, index);
        scene.setArguments(bundle);
        return scene;
    }

    /**
     * 静态方法来创建Fragment实例并设置参数编辑实例参数
     */
    public static AddExpenseScene editInstance(long id) {
        AddExpenseScene scene = new AddExpenseScene();
        Bundle bundle = new Bundle();
        bundle.putLong("expenseId", id);
        scene.setArguments(bundle);
        return scene;
    }

    /**
     * 初始化视图
     */
    @NonNull
    @Override
    public View onCreateView(@NonNull LayoutInflater layoutInflater, @NonNull ViewGroup viewGroup, @Nullable Bundle bundle) {
        // 绑定布局
        binding = SceneAddCostBinding.inflate(getLayoutInflater());
        // 返回绑定视图的根
        return binding.getRoot();
    }

    /**
     * 视图创建后的初始化
     */
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        // 获取传递的参数
        arguments = getArguments();
        // 从参数中获取费用类型，默认为加油
        if (arguments != null) {
            feeType = arguments.getInt(Const.FUEL_TYPE, Const.FUEL_TYPE_GAS);
        }
        // 初始化视图
        initView();
        // 设置视图
        setupViews();

    }

    /**
     * 初始化视图组件
     */
    private void initView() {
        // 将EditText转换为日期选择器
        //transformIntoDatePicker(binding.etRefDate, "yyyy-MM-dd", new Date());
        // 设置EditText为非焦点模式，但可点击
        binding.etRefDate.setFocusableInTouchMode(false);
        binding.etRefDate.setClickable(true);
        binding.etRefDate.setFocusable(false);
        binding.btCancel.setOnClickListener(v -> {
            getNavigationScene(AddExpenseScene.this).pop();
        });
        // 设置注册按钮的点击事件
        binding.btnRegister.setOnClickListener(v -> {
            registerExpense();

        });
        binding.etRefDate.setOnClickListener(v -> {
            onYearMonthDay();
        });
    }


    public void onYearMonthDay() {

        DatePicker picker = new DatePicker(getActivity());
        DateWheelLayout wheelLayout = picker.getWheelLayout();
        wheelLayout.setDateMode(DateMode.YEAR_MONTH_DAY);
        wheelLayout.setDateFormatter(new UnitDateFormatter());
        wheelLayout.setTextSize(DensityUtils.dp2px(getActivity(), 16));
        wheelLayout.setSelectedTextSize(DensityUtils.dp2px(getActivity(), 19));
        wheelLayout.setRange(DateEntity.monthOnFuture(-3), DateEntity.today(), DateEntity.today());
        wheelLayout.setCurtainEnabled(true);

        wheelLayout.setResetWhenLinkage(false);
        picker.setWidth(DensityUtils.dp2px(getActivity(), 420));
        picker.setHeight(DensityUtils.dp2px(getActivity(), 260));
        picker.getTitleView().setVisibility(View.GONE);
        picker.setOnDatePickedListener(new OnDatePickedListener() {
            @Override
            public void onDatePicked(int year, int month, int day) {
                // 使用 String.format 来格式化月份和日期，确保它们总是两位数
                // %02d 表示如果整数不足两位，则前面补零
                String formattedDate = String.format("%d-%02d-%02d", year, month, day);
                binding.etRefDate.setText(formattedDate);
            }
        });
        picker.getWheelLayout().setResetWhenLinkage(false);
        picker.show();
    }

    /**
     * 根据费用类型设置视图的可见性
     */
    private void setupViews() {
        // 根据费用类型隐藏或显示特定的容器
        if (!(feeType == Const.FUEL_TYPE_GAS)) {
            // 非加油类型隐藏油价和总公里数容器
            binding.etPricePerLiterContainer.setVisibility(View.GONE);
            binding.etTotalKmContainer.setVisibility(View.GONE);
            // 显示可选描述容器
            binding.etOptionalDescriptionContainer.setVisibility(View.VISIBLE);
        }

        // 根据费用类型设置不同容器的可见性
        switch (feeType) {
            case Const.FUEL_TYPE_GAS:
                break;
            case Const.FUEL_TYPE_TAXES:
                binding.etTaxTitleContainer.setVisibility(View.VISIBLE);
                break;
            case Const.FUEL_TYPE_REPAIR:
                binding.etMaintenanceTitleContainer.setVisibility(View.VISIBLE);
                break;
            default:
                binding.etYearContainer.setVisibility(View.VISIBLE);
                break;
        }
        if (arguments != null) {
            long expenseIdExtra = arguments.getLong("expenseId", -1L);
            // 检查是否修改现有费用
            long selectedCarId = DataManager.getCarId();
            if (expenseIdExtra != -1L) {
                // 修改现有费用
                expenseId = expenseIdExtra;
                // 设置标题为更新
                binding.addExpenseTitleLabel.setText(getStringForUpdate(feeType));
                CostDbManager.getInstance().getExpenseByCarIdAndExpenseId(selectedCarId, expenseIdExtra).subscribe(new Observer<Expense>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onNext(Expense expense) {
                        populateFields(expense);
                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onComplete() {

                    }
                });
                // 在后台线程中获取费用详情并更新UI
//                Executors.newSingleThreadExecutor().execute(() -> {
//                    Expense expense = expenseDao.getExpenseFromId(selectedCarId, expenseIdExtra);
//                    requireActivity().runOnUiThread(() -> populateFields(expense));
//                });
            } else {
                // 添加新费用
                binding.addExpenseTitleLabel.setText(getStringForAdd(feeType));
            }
        }

    }

    /**
     * 根据费用类型获取更新标题的字符串
     */
    private String getStringForUpdate(int type) {
        // 根据类型返回不同的更新标题
        switch (type) {
            case Const.FUEL_TYPE_GAS:
                return getString(R.string.update_refuel);
            case Const.FUEL_TYPE_TAXES:
                return getString(R.string.update_tax);
            case Const.FUEL_TYPE_REPAIR:
                return getString(R.string.update_maintenance);
            case Const.FUEL_TYPE_INSURANCE:
                return getString(R.string.update_insurance);
            default:
                return getString(R.string.update_insurance);
        }
    }

    /**
     * 根据费用详情填充表单字段
     */
    private void populateFields(Expense expense) {
        // 填充日期、总花费和描述
        binding.etRefDate.setText(expense.getDate());
        binding.etRefTotalSpent.setText(String.valueOf(expense.getSpent()));
        binding.etOptionalDescription.setText(expense.getDescription());

        // 根据费用类型填充其他字段
        switch (expense.getType()) {
            case Const.FUEL_TYPE_GAS:
                // 加油类型，填充油价和总公里数
                binding.etRefPricePerLiter.setText(String.valueOf(expense.getPricePerLiter()));
                binding.etRefTotalKm.setText(String.valueOf(expense.getTotalMeters()/1000));
                break;
            case Const.FUEL_TYPE_TAXES:
                // 税款类型，填充标题
                binding.etTaxTitle.setText(expense.getTitle());
                break;
            case Const.FUEL_TYPE_REPAIR:
                // 保养类型，填充标题
                binding.etMaintenanceTitle.setText(expense.getTitle());
                break;
            default:
                // 保险或其他类型，提取年份并设置
                String[] titleParts = expense.getTitle().split(" ");
                if (titleParts.length > 1) {
                    binding.etYear.setText(titleParts[1]);
                }
                break;
        }
    }

    /**
     * 根据费用类型获取添加标题的字符串
     */
    private String getStringForAdd(int type) {
        // 根据类型返回不同的添加标题
        switch (type) {
            case Const.FUEL_TYPE_GAS:
                return getString(R.string.label_add_refuel);
            case Const.FUEL_TYPE_TAXES:
                return getString(R.string.label_add_tax);
            case Const.FUEL_TYPE_REPAIR:
                return getString(R.string.label_add_maintenance);
            case Const.FUEL_TYPE_INSURANCE:
                return getString(R.string.label_add_insurance);
            default:
                return getString(R.string.label_add_insurance);
        }
    }

    // 注册费用
    private void registerExpense() {
        Expense newExpense;
        boolean errorFlag = false;
        long selectedCarId = DataManager.getCarId();
        selectedCarId = 5;
        // 获取日期、总花费和描述
        String dateString = binding.etRefDate.getText().toString();
        if (dateString.isEmpty()) {
            // 如果日期为空，则使用当前日期
            dateString = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(new Date());
        }

        double totalSpent;
        try {
            // 尝试解析总花费
            totalSpent = Double.parseDouble(binding.etRefTotalSpent.getText().toString());
        } catch (NumberFormatException e) {
            // 如果解析失败，则总花费设为-1
            totalSpent = -1.0;
        }

        String description = binding.etOptionalDescription.getText().toString();

        // 根据费用类型处理费用
        switch (feeType) {
            case Const.FUEL_TYPE_GAS:
                newExpense = handleRefuelExpense(dateString, totalSpent, selectedCarId);
                break;
            case Const.FUEL_TYPE_REPAIR:
                newExpense = handleMaintenanceExpense(dateString, totalSpent, description, selectedCarId);
                break;
            case Const.FUEL_TYPE_TAXES:
                newExpense = handleTaxExpense(dateString, totalSpent, description, selectedCarId);
                break;
            default:
                newExpense = handleInsuranceExpense(dateString, totalSpent, selectedCarId);
                break;
        }
        insertData(newExpense);
        // 如果没有错误，则插入费用并关闭当前页面
//        if (!errorFlag) {
//            CostDbManager.getInstance().insertExpense(newExpense);
//        }
    }

    @SuppressLint("CheckResult")
    private void insertData(Expense newExpense) {
        RxHttp.postJson(Const.VEHICLE_EXPENSE)
                .addAll(new Gson().toJson(newExpense))
                .toObservable(ResponseModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(responseModel -> {
                    MyLog.v(TAG, new Gson().toJson(responseModel));
                    if (responseModel.getCode() == StatusCodeModel.SUCCESS) {
                        getNavigationScene(AddExpenseScene.this).setResult(AddExpenseScene.this, "hahahah");
                        getNavigationScene(AddExpenseScene.this).pop();
                        MToast.makeTextShort("已添加");
                    } else {
                        MToast.makeTextShort(responseModel.getMsg());
                    }

                }, throwable -> {
                    MyLog.e("TAG", "onError: " + throwable.getMessage());
                });
    }

    /**
     * 处理加油费用
     */
    private Expense handleRefuelExpense(String dateString, double totalSpent, long selectedCarId) {
        double pricePerLiter; // 每升油价
        int totalKm; // 总公里数
        boolean errorFlag = false; // 错误标志

        // 尝试从EditText获取每升油价并转换为double类型
        try {
            pricePerLiter = Double.parseDouble(binding.etRefPricePerLiter.getText().toString());
        } catch (NumberFormatException e) {
            pricePerLiter = -1.0; // 如果转换失败，则设置为-1.0
        }

        // 尝试从EditText获取总公里数并转换为int类型
        try {
            totalKm = Integer.parseInt(binding.etRefTotalKm.getText().toString());
        } catch (NumberFormatException e) {
            totalKm = -1; // 如果转换失败，则设置为-1
        }

        // 检查是否所有必要数据都已正确输入
        if (totalSpent < 0 || totalKm < 0 || pricePerLiter <= 0) {
            // 如果总花费、总公里数或每升油价无效，则显示警告并设置错误标志
            Toast.makeText(requireActivity(), getString(R.string.insert_all_data_warning), Toast.LENGTH_SHORT).show();
            errorFlag = true;
        } else {
            // 如果数据有效，则创建并返回Expense对象
            return new Expense(expenseId, getString(R.string.refuel_title), feeType, dateString, totalSpent, null, pricePerLiter, totalKm, selectedCarId, UserManager.getUserId());
        }
        // 如果发生错误，则返回null
        return null;
    }

    /**
     * 处理保养费用
     */
    private Expense handleMaintenanceExpense(String dateString, double totalSpent, String description, long selectedCarId) {
        String title = binding.etMaintenanceTitle.getText().toString(); // 保养标题
        boolean errorFlag = false; // 错误标志

        // 检查是否所有必要数据都已正确输入
        if (totalSpent < 0 || title.isEmpty()) {
            // 如果总花费或标题无效，则显示警告并设置错误标志
            Toast.makeText(requireActivity(), getString(R.string.insert_all_data_warning), Toast.LENGTH_SHORT).show();
            errorFlag = true;
        } else {
            // 如果数据有效，则创建并返回Expense对象
            return new Expense(expenseId, title, feeType, dateString, totalSpent, description, null, null, selectedCarId, UserManager.getUserId());
        }
        // 如果发生错误，则返回null
        return null;
    }

    // 处理税款费用
    private Expense handleTaxExpense(String dateString, double totalSpent, String description, long selectedCarId) {
        String title = binding.etTaxTitle.getText().toString(); // 税款标题
        boolean errorFlag = false; // 错误标志

        // 检查是否所有必要数据都已正确输入
        if (totalSpent < 0 || title.isEmpty()) {
            // 如果总花费或标题无效，则显示警告并设置错误标志
            Toast.makeText(requireActivity(), getString(R.string.insert_all_data_warning), Toast.LENGTH_SHORT).show();
            errorFlag = true;
        } else {
            // 如果数据有效，则创建并返回Expense对象
            return new Expense(expenseId, title, feeType, dateString, totalSpent, description, null, null, selectedCarId, UserManager.getUserId());
        }
        // 如果发生错误，则返回null
        return null;
    }


    /**
     * 处理保险费用
     */
    private Expense handleInsuranceExpense(String dateString, double totalSpent, long selectedCarId) {
        int year; // 保险年份
        boolean errorFlag = false; // 错误标志

        // 尝试从EditText获取年份并转换为int类型
        try {
            year = Integer.parseInt(binding.etYear.getText().toString());
        } catch (NumberFormatException e) {
            year = -1; // 如果转换失败，则年份设置为-1
        }

        // 检查是否所有必要数据都已正确输入
        if (year < 1900 || totalSpent < 0) {
            // 如果年份无效（小于1900）或总花费无效（小于0），则显示警告并设置错误标志
            Toast.makeText(requireActivity(), getString(R.string.insert_all_data_warning), Toast.LENGTH_SHORT).show();
            errorFlag = true;
        } else {
            // 如果数据有效，则构造保险标题并创建Expense对象
            String title = getString(R.string.insurance_title) + " " + year; // 拼接保险标题和年份
            // 创建并返回Expense对象
            return new Expense(expenseId, title, feeType, dateString, totalSpent, null, null, null, selectedCarId, UserManager.getUserId());
        }
        // 如果发生错误，则返回null
        return null;
    }

    /**
     * 将EditText转换为日期选择器
     */
    private void transformIntoDatePicker(EditText editText, String format, Date maxDate) {
        // 设置EditText为非焦点模式，但可点击
        editText.setFocusableInTouchMode(false);
        editText.setClickable(true);
        editText.setFocusable(false);

        // 初始化日历实例
        final Calendar myCalendar = Calendar.getInstance();

        // 设置日期选择监听器
        DatePickerDialog.OnDateSetListener datePickerOnDataSetListener = (view, year, monthOfYear, dayOfMonth) -> {
            // 更新日历实例的日期
            myCalendar.set(Calendar.YEAR, year);
            myCalendar.set(Calendar.MONTH, monthOfYear);
            myCalendar.set(Calendar.DAY_OF_MONTH, dayOfMonth);

            // 使用指定的格式将日历日期格式化为字符串，并设置到EditText中
            SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.getDefault());
            editText.setText(sdf.format(myCalendar.getTime()));
        };

        // 设置EditText的点击事件监听器，以显示日期选择器对话框
        editText.setOnClickListener(v -> {
            // 创建DatePickerDialog实例
            DatePickerDialog datePickerDialog = new DatePickerDialog(
                    requireActivity(), // 上下文为当前Fragment所在的Activity
                    datePickerOnDataSetListener, // 日期选择监听器
                    myCalendar.get(Calendar.YEAR), // 初始年份
                    myCalendar.get(Calendar.MONTH), // 初始月份（注意月份是从0开始的）
                    myCalendar.get(Calendar.DAY_OF_MONTH) // 初始日
            );

            // 如果有最大日期限制，则设置到DatePickerDialog中
            if (maxDate != null) {
                datePickerDialog.getDatePicker().setMaxDate(maxDate.getTime());
            }

            // 显示日期选择器对话框
            datePickerDialog.show();
        });
    }

}
