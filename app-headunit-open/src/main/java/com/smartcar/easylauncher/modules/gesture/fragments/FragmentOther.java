package com.smartcar.easylauncher.modules.gesture.fragments;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.ComponentName;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Checkable;
import android.widget.CompoundButton;
import android.widget.Toast;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.databinding.GestureSettingsOtherBinding;
import com.smartcar.easylauncher.modules.gesture.other.AdbProcessExtractor;
import com.smartcar.easylauncher.modules.gesture.other.DialogAppSwitchExclusive;
import com.smartcar.easylauncher.modules.gesture.other.Gesture;
import com.smartcar.easylauncher.modules.gesture.other.GestureSettingActivity;
import com.smartcar.easylauncher.modules.gesture.other.SpfConfig;
import com.smartcar.easylauncher.modules.gesture.shell.KeepShellPublic;

public class FragmentOther extends FragmentSettingsBase {
    private GestureSettingsOtherBinding binding;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = GestureSettingsOtherBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onResume() {
        super.onResume();
        final Activity activity = getActivity();
        if (activity == null) return;

        final PackageManager p = activity.getPackageManager();
        final ComponentName startActivity = new ComponentName(activity.getApplicationContext(), GestureSettingActivity.class);
        
        binding.hideStartIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    if (binding.hideStartIcon.isChecked()) {
                        p.setComponentEnabledSetting(startActivity, PackageManager.COMPONENT_ENABLED_STATE_DISABLED, PackageManager.DONT_KILL_APP);
                    } else {
                        p.setComponentEnabledSetting(startActivity, PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
                    }
                } catch (Exception ex) {
                    Toast.makeText(v.getContext(), ex.getMessage(), Toast.LENGTH_SHORT).show();
                }
            }
        });

        int activityState = p.getComponentEnabledSetting(startActivity);
        binding.hideStartIcon.setChecked(activityState != PackageManager.COMPONENT_ENABLED_STATE_ENABLED && activityState != PackageManager.COMPONENT_ENABLED_STATE_DEFAULT);

        bindCheckable(binding.gameOptimization, SpfConfig.GAME_OPTIMIZATION, SpfConfig.GAME_OPTIMIZATION_DEFAULT);
        bindCheckable(binding.lowPower, SpfConfig.LOW_POWER_MODE, SpfConfig.LOW_POWER_MODE_DEFAULT);
        
        binding.windowWatch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (!isChecked) {
                    try {
                        Intent intent = new Intent(getString(R.string.app_switch_changed));
                        activity.sendBroadcast(intent);
                    } catch (Exception ignored) {
                    }
                }
            }
        });
        bindCheckable(binding.windowWatch, SpfConfig.WINDOW_WATCH, SpfConfig.WINDOW_WATCH_DEFAULT);

        binding.backHomeAnimation.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                homeAnimationPicker(SpfConfig.BACK_HOME_ANIMATION);
            }
        });
        
        binding.appSwitchAnimation.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                homeAnimationPicker(SpfConfig.APP_SWITCH_ANIMATION);
            }
        });

        setHomeAnimationText();

        // 使用ROOT获取最近任务
        binding.rootGetRecents.setChecked(config.getBoolean(SpfConfig.ROOT, SpfConfig.ROOT_DEFAULT));
        binding.rootGetRecents.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Checkable ele = (Checkable) v;
                if (ele.isChecked()) {
                    if (KeepShellPublic.checkRoot()) {
                        config.edit().putBoolean(SpfConfig.ROOT, true).apply();
                        new AdbProcessExtractor().updateAdbProcessState(getActivity(), true);
                        restartService();
                    } else {
                        ele.setChecked(false);
                        Gesture.toast(getString(R.string.no_root), Toast.LENGTH_SHORT);
                    }
                } else {
                    config.edit().putBoolean(SpfConfig.ROOT, false).apply();
                    restartService();
                }
            }
        });

        // 跳过切换
        binding.appSwitchExclusive.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                new DialogAppSwitchExclusive().openDialog(getActivity());
            }
        });

        updateView();
    }

    private void updateView() {
        setHomeAnimationText();
    }

    @Override
    protected void restartService() {
        updateView();
        super.restartService();
    }

    private void homeAnimationPicker(final String Key) {
        String[] options = new String[]{getString(R.string.animation_mode_default), getString(R.string.animation_mode_basic), getString(R.string.animation_mode_custom)};
        new AlertDialog.Builder(getActivity()).setTitle(R.string.animation_mode)
                .setSingleChoiceItems(options,
                        config.getInt(Key, SpfConfig.ANIMATION_DEFAULT),
                        new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                config.edit().putInt(Key, which).apply();
                                restartService();
                                dialog.dismiss();
                            }
                        })
                .create()
                .show();
    }

    private String animationName(int value) {
        switch (value) {
            case SpfConfig.ANIMATION_DEFAULT: {
                return getString(R.string.animation_mode_default);
            }
            case SpfConfig.ANIMATION_BASIC: {
                return getString(R.string.animation_mode_basic);
            }
            case SpfConfig.ANIMATION_CUSTOM: {
                return getString(R.string.animation_mode_custom);
            }
            default:
        }
        return "";
    }

    private void setHomeAnimationText() {
        binding.backHomeAnimation.setText(animationName(config.getInt(SpfConfig.BACK_HOME_ANIMATION, SpfConfig.ANIMATION_DEFAULT)));
        binding.appSwitchAnimation.setText(animationName(config.getInt(SpfConfig.APP_SWITCH_ANIMATION, SpfConfig.ANIMATION_DEFAULT)));
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
