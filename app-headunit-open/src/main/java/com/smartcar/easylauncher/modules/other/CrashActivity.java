package com.smartcar.easylauncher.modules.other;

import android.Manifest;
import android.app.ActivityManager;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.BatteryManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.StatFs;
import android.provider.Settings;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.UnderlineSpan;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.Nullable;
import androidx.core.view.GravityCompat;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.base.BaseActivity;
import com.smartcar.easylauncher.infrastructure.interfaces.BundleAction;
import com.smartcar.easylauncher.infrastructure.interfaces.HandlerAction;
import com.smartcar.easylauncher.databinding.CrashActivityBinding;
import com.smartcar.easylauncher.core.manager.AuthorityManager;
import com.smartcar.easylauncher.core.manager.MapManager;
import com.smartcar.easylauncher.core.manager.ThreadPoolManager;
import com.smartcar.easylauncher.modules.touch.floating.WindowPermissionUtil;
import com.smartcar.easylauncher.shared.utils.AppConfig;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.tencent.bugly.crashreport.CrashReport;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class CrashActivity extends BaseActivity implements View.OnClickListener, HandlerAction, BundleAction {

    private static final String INTENT_KEY_IN_THROWABLE = "throwable";

    private CrashActivityBinding binding;
    private static final String[] SYSTEM_PACKAGE_PREFIX_LIST = new String[]
            {"android", "com.android", "androidx", "com.google.android", "java", "javax", "dalvik", "kotlin"};

    /**
     * 报错代码行数正则表达式
     */
    private static final Pattern CODE_REGEX = Pattern.compile("\\(\\w+\\.\\w+:\\d+\\)");
    private String mStackTrace;


    public static void start(Application application, Throwable throwable) {
        if (throwable == null) {
            return;
        }
        Intent intent = new Intent(application, CrashActivity.class);
        intent.putExtra(INTENT_KEY_IN_THROWABLE, throwable);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        application.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = CrashActivityBinding.inflate(getLayoutInflater());
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON, WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        setContentView(binding.getRoot());
        initView();
        initDate();
    }

    @Override
    public void initImmersionBar() {
        super.initImmersionBar();
    }

    private void initDate() {
        MapManager.getInstance(this).homeHideMap();
        Throwable throwable = getSerializable(INTENT_KEY_IN_THROWABLE);

        if (throwable == null) {
            return;
        }

        binding.tvCrashTitle.setText(throwable.getClass().getSimpleName());
        try {


            StringWriter stringWriter = new StringWriter();
            PrintWriter printWriter = new PrintWriter(stringWriter);
            throwable.printStackTrace(printWriter);
            Throwable cause = throwable.getCause();

            if (cause != null) {
                cause.printStackTrace(printWriter);
            }

            mStackTrace = stringWriter.toString();

            Matcher matcher = CODE_REGEX.matcher(mStackTrace);
            SpannableStringBuilder spannable = new SpannableStringBuilder(mStackTrace);

            if (spannable.length() > 0) {
                while (matcher.find()) {
                    // 不包含左括号（
                    int start = matcher.start() + "(".length();
                    // 不包含右括号 ）
                    int end = matcher.end() - ")".length();

                    // 代码信息颜色
                    int codeColor = 0xFF999999;
                    int lineIndex = mStackTrace.lastIndexOf("at ", start);
                    if (lineIndex != -1) {
                        String lineData = spannable.subSequence(lineIndex, start).toString();
                        if (TextUtils.isEmpty(lineData)) {
                            continue;
                        }
                        // 是否高亮代码行数
                        boolean highlight = true;
                        for (String packagePrefix : SYSTEM_PACKAGE_PREFIX_LIST) {
                            if (lineData.startsWith("at " + packagePrefix)) {
                                highlight = false;
                                break;
                            }
                        }
                        if (highlight) {
                            codeColor = 0xFF287BDE;
                        }
                    }

                    // 设置前景
                    spannable.setSpan(new ForegroundColorSpan(codeColor), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                    // 设置下划线
                    spannable.setSpan(new UnderlineSpan(), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                }
                binding.tvCrashMessage.setText(spannable);
            }

            DisplayMetrics displayMetrics = getResources().getDisplayMetrics();
            int screenWidth = displayMetrics.widthPixels;
            int screenHeight = displayMetrics.heightPixels;
            float smallestWidth = Math.min(screenWidth, screenHeight) / displayMetrics.density;

            String targetResource;
            if (displayMetrics.densityDpi > 480) {
                targetResource = "xxxhdpi";
            } else if (displayMetrics.densityDpi > 320) {
                targetResource = "xxhdpi";
            } else if (displayMetrics.densityDpi > 240) {
                targetResource = "xhdpi";
            } else if (displayMetrics.densityDpi > 160) {
                targetResource = "hdpi";
            } else if (displayMetrics.densityDpi > 120) {
                targetResource = "mdpi";
            } else {
                targetResource = "ldpi";
            }

            StringBuilder builder = new StringBuilder();
            // 获取操作系统版本
            String osVersion = System.getProperty("os.version");
            builder.append("\n操作系统版本：\t").append(osVersion);

            // 获取设备硬件名称
            String hardware = Build.HARDWARE;
            builder.append("\n设备硬件名称：\t").append(hardware);

            // 获取设备制造商
            String manufacturer = Build.MANUFACTURER;
            builder.append("\n设备制造商：\t").append(manufacturer);

            // 获取设备产品名称
            String product = Build.PRODUCT;
            builder.append("\n设备产品名称：\t").append(product);

            // 获取设备的唯一标识符
            String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
            builder.append("\n设备唯一标识符：\t").append(androidId);
            builder.append("\n设备品牌：\t").append(Build.BRAND)
                    .append("\n设备型号：\t").append(Build.MODEL)
                    .append("\n设备类型：\t").append(isTablet() ? "平板" : "手机");

            builder.append("\n屏幕宽高：\t").append(screenWidth).append(" x ").append(screenHeight)
                    .append("\n屏幕密度：\t").append(displayMetrics.densityDpi)
                    .append("\n密度像素：\t").append(displayMetrics.density)
                    .append("\n目标资源：\t").append(targetResource)
                    .append("\n最小宽度：\t").append((int) smallestWidth);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                builder.append("\n安卓版本：\t").append(Build.VERSION.RELEASE)
                        .append("\nAPI 版本：\t").append(Build.VERSION.SDK_INT)
                        .append("\nCPU 架构：\t").append(Build.SUPPORTED_ABIS[0]);
            } else {
                builder.append("\n安卓版本：\t").append(Build.VERSION.RELEASE)
                        .append("\nAPI 版本：\t").append(Build.VERSION.SDK_INT)
                        .append("\nCPU 架构：\t").append(Build.CPU_ABI);
            }
            // 获取CPU信息
            String cpuInfo = "";
            try {
                FileReader fr = new FileReader("/proc/cpuinfo");
                BufferedReader br = new BufferedReader(fr);
                String text = br.readLine();
                String[] array = text.split(":\\s+", 2);
                for (int i = 0; i < array.length; i++) {
                    cpuInfo += array[i] + " ";
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            builder.append("\nCPU信息：\t").append(cpuInfo);

            // 获取内存信息
            ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
            ActivityManager activityManager = (ActivityManager) getSystemService(ACTIVITY_SERVICE);
            activityManager.getMemoryInfo(memoryInfo);
            builder.append("\n总内存：\t").append(memoryInfo.totalMem / 1024 / 1024 + "MB");
            builder.append("\n可用内存：\t").append(memoryInfo.availMem / 1024 / 1024 + "MB");

            // 获取电池信息
            IntentFilter ifilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
            Intent batteryStatus = registerReceiver(null, ifilter);
            int level = batteryStatus.getIntExtra(BatteryManager.EXTRA_LEVEL, -1);
            int scale = batteryStatus.getIntExtra(BatteryManager.EXTRA_SCALE, -1);
            float batteryPct = level / (float) scale;
            builder.append("\n电池电量：\t").append(batteryPct * 100 + "%");
            // 获取存储信息
            StatFs statFs = new StatFs(Environment.getExternalStorageDirectory().getAbsolutePath());
            long total = ((long) statFs.getBlockCount()) * statFs.getBlockSize();
            long available = ((long) statFs.getAvailableBlocks()) * statFs.getBlockSize();
            builder.append("\n总存储：\t").append(total / 1024 / 1024 + "MB");
            builder.append("\n可用存储：\t").append(available / 1024 / 1024 + "MB");

            builder.append("\n应用版本：\t").append(AppConfig.getVersionName())
                    .append("\n版本代码：\t").append(AppConfig.getVersionCode());

            try {
                SimpleDateFormat dateFormat = new SimpleDateFormat("MM-dd HH:mm", Locale.getDefault());
                PackageInfo packageInfo = getPackageManager().getPackageInfo(getPackageName(), PackageManager.GET_PERMISSIONS);
                builder.append("\n首次安装：\t").append(dateFormat.format(new Date(packageInfo.firstInstallTime)))
                        .append("\n最近安装：\t").append(dateFormat.format(new Date(packageInfo.lastUpdateTime)))
                        .append("\n崩溃时间：\t").append(dateFormat.format(new Date()));

                List<String> permissions = Arrays.asList(packageInfo.requestedPermissions);

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    if (permissions.contains(Manifest.permission.READ_EXTERNAL_STORAGE) || permissions.contains(Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
                        builder.append("\n存储权限：\t").append(checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED ? "已获得" : "未获得");
                    }

                    if (permissions.contains(Manifest.permission.ACCESS_FINE_LOCATION) || permissions.contains(Manifest.permission.ACCESS_COARSE_LOCATION)) {
                        builder.append("\n定位权限：\t");
                        if (checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED && checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                            builder.append("精确、粗略");
                        } else {
                            if (checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                                builder.append("精确");
                            } else if (checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                                builder.append("粗略");
                            } else {
                                builder.append("未获得");
                            }
                        }
                    }

                    if (permissions.contains(Manifest.permission.CAMERA)) {
                        builder.append("\n相机权限：\t").append(checkSelfPermission(Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED ? "已获得" : "未获得");
                    }

                    if (permissions.contains(Manifest.permission.RECORD_AUDIO)) {
                        builder.append("\n录音权限：\t").append(checkSelfPermission(Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED ? "已获得" : "未获得");
                    }

                    if (permissions.contains(Manifest.permission.SYSTEM_ALERT_WINDOW)) {
                        builder.append("\n悬浮窗权限：\t").append(WindowPermissionUtil.hasPermission(CrashActivity.this) ? "已获得" : "未获得");
                    }

                    if (permissions.contains(Manifest.permission.REQUEST_INSTALL_PACKAGES)) {
                        builder.append("\n安装包权限：\t").append(checkSelfPermission(Manifest.permission.REQUEST_INSTALL_PACKAGES) == PackageManager.PERMISSION_GRANTED ? "已获得" : "未获得");
                    }
                } else {
                    // 对于API 23以下的版本，只需要在Manifest文件中声明权限即可，无需动态请求
                    builder.append("\n存储权限：\t已获得");
                    builder.append("\n定位权限：\t已获得");
                    builder.append("\n相机权限：\t已获得");
                    builder.append("\n录音权限：\t已获得");
                    builder.append("\n悬浮窗权限：\t已获得");
                    builder.append("\n安装包权限：\t已获得");
                }
                // 获取网络信息
                ConnectivityManager cm = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
                NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
                boolean isConnected = activeNetwork != null && activeNetwork.isConnectedOrConnecting();
                builder.append("\n网络连接：\t").append(isConnected ? "已连接" : "未连接");
                if (isConnected) {
                    builder.append("\n网络类型：\t").append(activeNetwork.getTypeName());
                }

                builder.append("\n激活状态：\t").append(AuthorityManager.getActivationState() ? "已激活" : "未激活");
                if (AuthorityManager.getActivationState()) {
                    builder.append("\n使用许可：\t").append(AuthorityManager.getUseLicense() ? "已通过" : "未通过");
                    builder.append("\n激活码：\t").append(AuthorityManager.getActivationCode());
                }
                if (permissions.contains(Manifest.permission.INTERNET)) {
                    builder.append("\n当前网络访问：\t");

                    ThreadPoolManager.getInstance().execute(() -> {
                        try {
                            InetAddress.getByName("www.baidu.com");
                            builder.append("正常");
                        } catch (UnknownHostException ignored) {
                            builder.append("异常");
                        }
                        post(() -> binding.tvCrashInfo.setText(builder));
                    });

                } else {
                    binding.tvCrashInfo.setText(builder);
                }

            } catch (PackageManager.NameNotFoundException e) {
                CrashReport.postCatchedException(e);
            }
        } catch (Throwable throwable1) {
            MyLog.e("initView()", throwable1.getMessage());
        }
    }

    private void initView() {
        binding.ivCrashInfo.setOnClickListener(this);
        binding.ivCrashShare.setOnClickListener(this);
        binding.ivCrashRestart.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.iv_crash_info:
                binding.dlCrashDrawer.openDrawer(GravityCompat.START);
                break;
            case R.id.iv_crash_share:
                // 分享文本
                Intent intent = new Intent(Intent.ACTION_SEND);
                intent.setType("text/plain");
                intent.putExtra(Intent.EXTRA_TEXT, mStackTrace);
                startActivity(Intent.createChooser(intent, ""));
                break;
            case R.id.iv_crash_restart:
                // 重启应用
                RestartActivity.restart(this);
                finish();
                break;
            default:
        }
    }


    /**
     * 判断当前设备是否是平板
     */
    public boolean isTablet() {
        return (getResources().getConfiguration().screenLayout
                & Configuration.SCREENLAYOUT_SIZE_MASK)
                >= Configuration.SCREENLAYOUT_SIZE_LARGE;
    }

    @Nullable
    @Override
    public Bundle getBundle() {
        return getIntent().getExtras();
    }
}