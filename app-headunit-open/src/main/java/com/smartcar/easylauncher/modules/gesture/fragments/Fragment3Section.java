package com.smartcar.easylauncher.modules.gesture.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.databinding.GestureSettings3sectionBinding;
import com.smartcar.easylauncher.modules.gesture.other.SpfConfig;

public class Fragment3Section extends FragmentSettingsBase {
    private GestureSettings3sectionBinding binding;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = GestureSettings3sectionBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onResume() {
        super.onResume();

        bindCheckable(binding.allowThreeSectionLandscape, SpfConfig.THREE_SECTION_LANDSCAPE, SpfConfig.THREE_SECTION_LANDSCAPE_DEFAULT);
        bindCheckable(binding.allowThreeSectionPortrait, SpfConfig.THREE_SECTION_PORTRAIT, SpfConfig.THREE_SECTION_PORTRAIT_DEFAULT);
        bindSeekBar(binding.barWidthThreeSection, SpfConfig.THREE_SECTION_WIDTH, SpfConfig.THREE_SECTION_WIDTH_DEFAULT, true);
        bindSeekBar(binding.threeSectionHeight, SpfConfig.THREE_SECTION_HEIGHT, SpfConfig.THREE_SECTION_HEIGHT_DEFAULT, true);

        bindHandlerPicker(binding.threeSectionLeftSlide, SpfConfig.THREE_SECTION_LEFT_SLIDE, SpfConfig.THREE_SECTION_LEFT_SLIDE_DEFAULT);
        bindHandlerPicker(binding.threeSectionCenterSlide, SpfConfig.THREE_SECTION_CENTER_SLIDE, SpfConfig.THREE_SECTION_CENTER_SLIDE_DEFAULT);
        bindHandlerPicker(binding.threeSectionRightSlide, SpfConfig.THREE_SECTION_RIGHT_SLIDE, SpfConfig.THREE_SECTION_RIGHT_SLIDE_DEFAULT);
        bindHandlerPicker(binding.threeSectionLeftHover, SpfConfig.THREE_SECTION_LEFT_HOVER, SpfConfig.THREE_SECTION_LEFT_HOVER_DEFAULT);
        bindHandlerPicker(binding.threeSectionCenterHover, SpfConfig.THREE_SECTION_CENTER_HOVER, SpfConfig.THREE_SECTION_CENTER_HOVER_DEFAULT);
        bindHandlerPicker(binding.threeSectionRightHover, SpfConfig.THREE_SECTION_RIGHT_HOVER, SpfConfig.THREE_SECTION_RIGHT_HOVER_DEFAULT);

        bindColorPicker(binding.barColorThreeSection, SpfConfig.THREE_SECTION_COLOR, SpfConfig.THREE_SECTION_COLOR_DEFAULT, getString(R.string.feedback_color));

        updateView();
    }

    private void updateView() {
        setViewBackground(binding.barColorThreeSection, config.getInt(SpfConfig.THREE_SECTION_COLOR, SpfConfig.THREE_SECTION_COLOR_DEFAULT));

        updateActionText(binding.threeSectionLeftSlide, SpfConfig.THREE_SECTION_LEFT_SLIDE, SpfConfig.THREE_SECTION_LEFT_SLIDE_DEFAULT);
        updateActionText(binding.threeSectionCenterSlide, SpfConfig.THREE_SECTION_CENTER_SLIDE, SpfConfig.THREE_SECTION_CENTER_SLIDE_DEFAULT);
        updateActionText(binding.threeSectionRightSlide, SpfConfig.THREE_SECTION_RIGHT_SLIDE, SpfConfig.THREE_SECTION_RIGHT_SLIDE_DEFAULT);
        updateActionText(binding.threeSectionLeftHover, SpfConfig.THREE_SECTION_LEFT_HOVER, SpfConfig.THREE_SECTION_LEFT_HOVER_DEFAULT);
        updateActionText(binding.threeSectionCenterHover, SpfConfig.THREE_SECTION_CENTER_HOVER, SpfConfig.THREE_SECTION_CENTER_HOVER_DEFAULT);
        updateActionText(binding.threeSectionRightHover, SpfConfig.THREE_SECTION_RIGHT_HOVER, SpfConfig.THREE_SECTION_RIGHT_HOVER_DEFAULT);
    }

    @Override
    protected void restartService() {
        updateView();
        super.restartService();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
