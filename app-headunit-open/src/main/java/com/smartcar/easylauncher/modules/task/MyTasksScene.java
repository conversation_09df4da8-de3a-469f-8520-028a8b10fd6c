
package com.smartcar.easylauncher.modules.task;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;
import static com.bytedance.scene.navigation.NavigationSceneGetter.requireNavigationScene;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.bytedance.scene.interfaces.PushOptions;
import com.bytedance.scene.interfaces.PushResultCallback;
import com.chad.library.adapter4.layoutmanager.QuickGridLayoutManager;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.task.TaskAdapter;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.databinding.SceneMyTasksBinding;
import com.smartcar.easylauncher.shared.dialog.GeneralDialog;
import com.smartcar.easylauncher.infrastructure.event.cody.TaskNotificationScopeBus;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.data.model.task.TaskModel;
import com.smartcar.easylauncher.data.model.task.TaskNotificationModel;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.util.ArrayList;
import java.util.List;

/**
 * 我的任务Scene - 优化版本
 * 展示用户创建的所有任务，支持任务管理和操作
 * <p>
 * 优化内容：
 * 1. 使用BaseQuickAdapter4的空视图功能
 * 2. 优化代码结构和命名规范
 * 3. 添加详细的中文注释
 * 4. 优化数据加载和错误处理逻辑
 *
 * <AUTHOR>
 * @date 2024/3/27
 */
public class MyTasksScene extends BaseScene {

    private static final String TAG = "MyTasksScene";

    private SceneMyTasksBinding mBinding;
    private TaskAdapter mTaskAdapter;
    private List<TaskModel> mTaskDataList;
    private PushOptions mPushOptions;
    private FragmentActivity mActivity;


    @NonNull
    @Override
    protected ViewGroup onCreateNewView(@NonNull LayoutInflater layoutInflater, @Nullable ViewGroup viewGroup, @Nullable Bundle bundle) {
        mBinding = SceneMyTasksBinding.inflate(layoutInflater, viewGroup, false);
        return mBinding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initializeComponents();
        setupViews();
        loadTaskData();
    }

    /**
     * 初始化组件
     */
    private void initializeComponents() {
        mActivity = (FragmentActivity) requireActivity();
        mTaskDataList = new ArrayList<>();

        // 配置页面跳转选项
        mPushOptions = new PushOptions.Builder()
                //.setAnimation(requireActivity(), R.anim.slide_in_from_right, R.anim.slide_out_to_left)
                .setPushResultCallback(new PushResultCallback() {
                    @Override
                    public void onResult(@Nullable Object result) {
                        if (result != null) {
                            MyLog.v(TAG, "新任务创建返回结果：" + result);
                            // 重新加载任务数据
                            loadTaskData();
                        }
                    }
                }).build();
    }

    /**
     * 设置视图组件
     */
    private void setupViews() {
        setupRecyclerView();
        setupAdapter();
        setupCreateTaskButton();
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerView() {
        // 设置网格布局 - 2列显示，与推荐任务保持一致
        QuickGridLayoutManager gridLayoutManager = new QuickGridLayoutManager(requireActivity(), 2);
        mBinding.rvTheme.setLayoutManager(gridLayoutManager);

        // RecyclerView性能优化配置
        mBinding.rvTheme.setHasFixedSize(true);
        mBinding.rvTheme.setItemViewCacheSize(20);
    }

    /**
     * 设置适配器和空视图
     */
    private void setupAdapter() {
        mTaskAdapter = new TaskAdapter();

        // 设置任务项点击事件 - 进入任务编辑页面
        mTaskAdapter.setOnItemClickListener((adapter, view, position) -> {
            TaskModel taskModel = mTaskAdapter.getItem(position);
            if (taskModel != null) {
                Bundle arguments = NewTaskScene.createArguments(taskModel);
                NewTaskScene newTaskScene = new NewTaskScene();
                newTaskScene.setArguments(arguments);
                requireNavigationScene(this).push(newTaskScene, mPushOptions);
            }
        });

        // 设置任务项长按事件 - 删除任务
        mTaskAdapter.setOnItemLongClickListener((adapter, view, position) -> {
            TaskModel taskModel = mTaskAdapter.getItem(position);
            if (taskModel != null) {
                showDeleteTaskDialog(taskModel);
            }
            return true;
        });

        // 配置空视图 - 初始状态禁用，避免在加载时显示
        mTaskAdapter.setStateViewEnable(false);
        mTaskAdapter.setUseStateViewSize(true);
        View emptyView = LayoutInflater.from(requireActivity())
                .inflate(R.layout.task_empty_view, mBinding.rvTheme, false);
        mTaskAdapter.setStateView(emptyView);

        mBinding.rvTheme.setAdapter(mTaskAdapter);
    }

    /**
     * 设置创建任务按钮
     */
    private void setupCreateTaskButton() {
        mBinding.btNewtask.setOnClickListener(v -> {
            MyLog.v(TAG, "点击创建新任务按钮");
            getNavigationScene(MyTasksScene.this).push(new NewTaskScene(), mPushOptions);
        });
    }


    /**
     * 加载任务数据
     */
    private void loadTaskData() {
        MyLog.v(TAG, "开始加载任务数据");

        mTaskDataList.clear();
        String taskDataJson = DataManager.getTask();

        if (taskDataJson != null && !taskDataJson.isEmpty()) {
            try {
                // 解析JSON数据
                List<TaskModel> taskList = new Gson().fromJson(taskDataJson,
                        new TypeToken<List<TaskModel>>() {
                        }.getType());

                if (taskList != null && !taskList.isEmpty()) {
                    mTaskDataList.addAll(taskList);
                    MyLog.v(TAG, "任务数据加载成功，数量: " + taskList.size());
                    updateAdapterData();
                } else {
                    MyLog.v(TAG, "任务数据为空");
                    showEmptyState();
                }
            } catch (JsonSyntaxException e) {
                // JSON解析错误处理
                MyLog.e(TAG, "任务数据JSON解析错误: " + e.getMessage());
                MToast.makeTextShort("任务数据加载失败");
                showEmptyState();
            }
        } else {
            MyLog.v(TAG, "本地无任务数据");
            showEmptyState();
        }
    }

    /**
     * 更新适配器数据
     */
    private void updateAdapterData() {
        MyLog.v(TAG, "更新适配器数据，任务数量: " + mTaskDataList.size());

        // 有数据时禁用空视图，确保正常显示数据
        mTaskAdapter.setStateViewEnable(false);

        // 创建新的列表副本，确保数据更新能被适配器检测到
        List<TaskModel> newList = new ArrayList<>(mTaskDataList);
        mTaskAdapter.submitList(newList);

        MyLog.v(TAG, "适配器数据更新完成");
    }

    /**
     * 显示空状态
     */
    private void showEmptyState() {
        MyLog.v(TAG, "显示空状态视图");

        // 启用空视图并清空数据列表
        mTaskAdapter.setStateViewEnable(true);
        mTaskAdapter.submitList(new ArrayList<>());
    }


    /**
     * 显示删除任务确认对话框
     */
    private void showDeleteTaskDialog(TaskModel taskModel) {
        GeneralDialog.showGeneralDialog(
                mActivity.getSupportFragmentManager(),
                getActivity(),
                "删除任务",
                "是否删除该任务？删除后无法恢复",
                "删除",
                "取消",
                new GeneralDialog.DialogClickListener() {
                    @Override
                    public void onPositiveClick() {
                        deleteTask(taskModel);
                    }

                    @Override
                    public void onNegativeClick() {
                        MyLog.v(TAG, "用户取消删除任务");
                    }
                }
        );
    }

    /**
     * 删除任务数据
     */
    private void deleteTask(TaskModel taskModel) {
        MyLog.v(TAG, "开始删除任务: " + taskModel.getTaskName());

        // 从数据列表中移除任务
        boolean removed = false;
        for (int i = 0; i < mTaskDataList.size(); i++) {
            if (mTaskDataList.get(i).getId().equals(taskModel.getId())) {
                mTaskDataList.remove(i);
                removed = true;
                MyLog.v(TAG, "任务已从列表中移除，位置: " + i);
                break;
            }
        }

        if (removed) {
            // 保存更新后的数据到本地
            saveTaskDataToLocal();

            // 发送任务删除通知
            TaskNotificationScopeBus.eventBean().post(new TaskNotificationModel(2, taskModel));
            MToast.makeTextShort("任务已删除");

            // 刷新适配器数据 - 这是关键修复
            if (mTaskDataList.isEmpty()) {
                showEmptyState();
            } else {
                updateAdapterData();
            }
        } else {
            MyLog.e(TAG, "未找到要删除的任务: " + taskModel.getId());
            MToast.makeTextShort("删除失败，任务不存在");
        }
    }

    /**
     * 保存任务数据到本地存储
     */
    private void saveTaskDataToLocal() {
        String jsonData;
        if (mTaskDataList.isEmpty()) {
            jsonData = "";
        } else {
            jsonData = new Gson().toJson(mTaskDataList);
        }
        DataManager.setTask(jsonData);
        MyLog.v(TAG, "任务数据已保存到本地");
    }

    @Override
    public void onResume() {
        super.onResume();
        // 页面恢复时重新加载数据，确保数据同步
        loadTaskData();
    }

}
