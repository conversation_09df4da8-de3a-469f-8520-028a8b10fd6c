package com.smartcar.easylauncher.modules.tpms.core;

import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.app.App;
import com.smartcar.easylauncher.data.database.entity.TpmsEntity;
import com.smartcar.easylauncher.shared.dialog.TirePressureDialog;
import com.smartcar.easylauncher.shared.dialog.flexible.FlexibleDialog;
import com.smartcar.easylauncher.infrastructure.event.scope.device.cody.TirePressureScopeBus;
import com.smartcar.easylauncher.infrastructure.event.scope.notice.cody.GeneralNotificationScopeBus;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.data.repository.TpmsRepository;
import com.smartcar.easylauncher.modules.tpms.device.TpmsDevice;
import com.smartcar.easylauncher.modules.tpms.protocol.base.Protocol;
import com.smartcar.easylauncher.modules.tpms.protocol.base.model.TpmsData;
import com.smartcar.easylauncher.modules.tpms.protocol.base.model.TireStatusData;
import com.smartcar.easylauncher.modules.tpms.protocol.ProtocolDetector;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.TpmsUtlis;
import com.smartcar.easylauncher.shared.utils.thread.ThreadPoolUtil;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicBoolean;

import android.os.Handler;
import android.os.Looper;

/**
 * TPMS管理器
 * 负责管理胎压监测系统的核心功能，包括：
 * 1. 设备连接和协议管理
 * 2. 数据接收和处理
 * 3. 告警状态管理
 * 4. 轮胎操作（学习、交换等）
 * 5. 状态监控和错误处理
 */
public class TpmsManager {
    private static final String TAG = "TpmsManager";

    // ============== 单例管理 ==============
    private static volatile TpmsManager instance;
    private TpmsDevice device;           // TPMS设备对象
    private Protocol protocol;                 // 协议对象
    private final List<TpmsListener> listeners = new ArrayList<>(); // 监听器列表
    private boolean isRunning = false;         // 运行状态标志
    private final Handler mainHandler = new Handler(Looper.getMainLooper()); // 主线程Handler
    private final AtomicBoolean isStarting = new AtomicBoolean(false); // 启动状态标志
    private final ThreadPoolExecutor executor = ThreadPoolUtil.getThreadPoolExecutor(); // 线程池

    // 警告弹窗管理
    private long lastWarningTime = 0; // 上次显示警告的时间
    private int warningInterval = SettingsManager.getWarningInterval() * 60 * 1000; // 从SettingsManager获取警告间隔
    private final Map<Integer, TireStatusData> warningTires = new HashMap<>(); // 使用Map存储警告轮胎，key为position
    private final Handler warningHandler = new Handler(Looper.getMainLooper()); // 用于延迟显示警告
    private static final long DATA_COLLECTION_DELAY = 1000; // 数据收集延迟时间（毫秒）
    private boolean isDialogShowing = false; // 弹窗显示状态

    // 数据保存控制
    private static final long DATA_SAVE_INTERVAL = 60 * 1000; // 数据保存间隔（1分钟）
    private final Map<Integer, Long> lastSaveTimeMap = new HashMap<>(); // 记录每个轮胎的最后保存时间

    // ============== 告警状态常量 ==============
    private static final int WARNING_SIGNAL_LOST = 0x20;    // 0010 0000 信号丢失
    private static final int WARNING_HIGH_TEMP = 0x10;      // 0001 0000 高温
    private static final int WARNING_HIGH_PRESSURE = 0x08;  // 0000 1000 高压
    private static final int WARNING_LOW_PRESSURE = 0x04;   // 0000 0100 低压
    private static final int WARNING_LEAKAGE = 0x02;        // 0000 0010 漏气
    private static final int WARNING_LOW_BATTERY = 0x01;    // 0000 0001 低电量

    // ============== 状态码常量 ==============
    private static final int STATUS_NORMAL = 0;             // 正常状态
    private static final int STATUS_PRESSURE_LOW = 1;       // 低压状态
    private static final int STATUS_PRESSURE_HIGH = 2;      // 高压状态
    private static final int STATUS_LEAKAGE = 3;            // 漏气状态
    private static final int STATUS_BATTERY_LOW = 4;        // 低电量状态
    private static final int STATUS_TEMPERATURE_HIGH = 5;   // 高温状态

    // ============== 构造方法 ==============
    private TpmsManager() {
    }

    /**
     * 获取TpmsManager实例
     *
     * @return TpmsManager实例
     */
    public static TpmsManager getInstance() {
        if (instance == null) {
            synchronized (TpmsManager.class) {
                if (instance == null) {
                    instance = new TpmsManager();
                }
            }
        }
        return instance;
    }

    /**
     * 设置TPMS设备
     *
     * @param device TPMS设备对象
     * @return TpmsManager实例，支持链式调用
     */
    public TpmsManager setDevice(TpmsDevice device) {
        this.device = device;
        return this;
    }

    /**
     * 检查设备是否已设置
     *
     * @throws IllegalStateException 如果设备未设置
     */
    private void checkDevice() {
        if (device == null) {
            MToast.makeTextLong("请先链接胎压硬件设备");
            return;
        }
    }

    // ============== 生命周期管理 ==============

    /**
     * 启动TPMS服务
     * 1. 检查运行状态
     * 2. 打开设备连接
     * 3. 检测协议
     * 4. 初始化协议
     * 5. 启动数据接收
     */
    public void start() {
        checkDevice();
        if (isRunning || !isStarting.compareAndSet(false, true)) {
            return;
        }

        executor.execute(() -> {
            try {
                updateState(TpmsState.CONNECTING);

                // 1. 打开设备连接
                if (!device.open()) {
                    setError(TpmsError.DEVICE_BUSY);
                    return;
                }

                // 2. 检测协议
                updateState(TpmsState.DETECTING);
                protocol = ProtocolDetector.detect(device);
                if (protocol == null) {
                    setError(TpmsError.PROTOCOL_DETECT_FAILED);
                    return;
                }

                // 3. 初始化协议
                updateState(TpmsState.INITIALIZING);
                setupDeviceCallbacks();
                protocol.setCallback(new Protocol.Callback() {
                    @Override
                    public void onData(TpmsData data) {
                        handleTpmsData(data);
                    }
                });
                protocol.start();

                // 4. 启动完成
                updateState(TpmsState.RUNNING);
                isRunning = true;

            } catch (Exception e) {
                MyLog.e(TAG, "启动失败", e);
                setError(TpmsError.fromException(e));
            } finally {
                isStarting.set(false);
            }
        });
    }

    /**
     * 停止TPMS服务
     * 1. 停止协议
     * 2. 关闭设备
     * 3. 清理资源
     */
    public void stop() {
        if (!isRunning) {
            return;
        }
        checkDevice();
        executor.execute(() -> {
            try {
                if (protocol != null) {
                    protocol.stop();
                    protocol = null;
                }
                device.close();
            } catch (Exception e) {
                MyLog.e(TAG, "停止失败", e);
            } finally {
                isRunning = false;
                updateState(TpmsState.DISCONNECTED);
            }
        });
    }

    /**
     * 释放资源
     * 1. 停止服务
     * 2. 清理单例实例
     */
    public void release() {
        stop();
        synchronized (TpmsManager.class) {
            instance = null;
        }
    }

    // ============== 轮胎操作 ==============

    /**
     * 查询轮胎ID
     */
    public void queryTireIds() {
        checkDevice();
        if (protocol != null && isRunning) {
            protocol.queryTireIds();
        }
    }

    /**
     * 交换轮胎位置
     *
     * @param pos1 轮胎1位置
     * @param pos2 轮胎2位置
     */
    public void exchangeTires(byte pos1, byte pos2) {
        checkDevice();
        if (protocol != null && isRunning) {
            protocol.exchangeTires(pos1, pos2);
        }
    }

    /**
     * 学习新轮胎
     *
     * @param key 学习密钥
     */
    public void learn(byte key) {
        checkDevice();
        if (protocol != null && isRunning) {
            protocol.learn(key);
        }
    }

    /**
     * 取消当前操作
     */
    public void cancel() {
        if (protocol != null && isRunning) {
            protocol.cancel();
        }
    }

    // ============== 设备回调设置 ==============

    /**
     * 设置设备回调
     * 1. 数据接收回调
     * 2. 状态变化回调
     */
    private void setupDeviceCallbacks() {
        // 设置数据回调
        device.setDataCallback(new TpmsDevice.DataCallback() {
            @Override
            public void onDataReceived(ByteBuffer buffer) {
                if (protocol != null) {
                    byte[] data = new byte[buffer.remaining()];
                    buffer.get(data);
                    protocol.handleData(data);
                }
            }
        });

        // 设置状态回调
        device.setStateCallback(new TpmsDevice.StateCallback() {
            @Override
            public void onConnectionStateChanged(boolean connected) {
                notifyDeviceConnectionChanged(connected);
            }

            @Override
            public void onError(int errorCode, String message) {
                notifyError("设备错误: " + message);
            }
        });
    }

    // ============== 数据处理 ==============

    /**
     * 处理TPMS数据
     * 1. 根据数据类型分发处理
     * 2. 进行单位转换
     * 3. 处理告警状态
     * 4. 发送事件通知
     */
    private void handleTpmsData(TpmsData data) {
        executor.execute(() -> {
            try {
                switch (data.getType()) {
                    case TIRE_STATUS:
                        TireStatusData tireStatusData = (TireStatusData) data;
                        // 创建新的状态数据对象，进行单位转换和格式化
                        TireStatusData convertedData = new TireStatusData.Builder()
                                .position(tireStatusData.getPosition())
                                .pressure(TpmsUtlis.getInstance().convertFromKpa(tireStatusData.getPressure()))
                                .temperature(convertTemperature(tireStatusData.getTemperature()))
                                .voltage(tireStatusData.getVoltage())
                                .battery(tireStatusData.getBattery())
                                .sensorId(tireStatusData.getSensorId())
                                .displayStatus(processWarningStatus(tireStatusData))
                                .protocolVersion(tireStatusData.getProtocolVersion())
                                .pressureUnit(SettingsManager.getPressureUnit())
                                .temperatureUnit(SettingsManager.getTempUnit())
                                .build();

                        // 检查是否需要保存数据
                        int position = convertedData.getPosition().getValue();
                        long currentTime = System.currentTimeMillis();
                        Long lastSaveTime = lastSaveTimeMap.get(position);

                        // 如果是第一次保存或者距离上次保存超过1分钟，则保存数据
                        if (lastSaveTime == null || (currentTime - lastSaveTime >= DATA_SAVE_INTERVAL)) {
                            // 保存数据到数据库
                            TpmsEntity tpmsEntity = new TpmsEntity(
                                    0, // id will be auto-generated
                                    position,
                                    convertedData.getPressure(),
                                    convertedData.getTemperature(),
                                    convertedData.getBattery(),
                                    convertedData.getSensorId(),
                                    convertedData.getDisplayStatus(),
                                    convertedData.getProtocolVersion(),
                                    convertedData.getPressureUnit(),
                                    convertedData.getTemperatureUnit(),
                                    currentTime
                            );

                            // 使用异步方法保存数据
                            TpmsRepository.getInstance()
                                    .saveTpmsDataAsync(tpmsEntity)
                                    .whenComplete((id, throwable) -> {
                                        if (throwable == null) {
                                            // 保存成功，更新最后保存时间
                                            lastSaveTimeMap.put(position, currentTime);
                                            MyLog.d(TAG, String.format("保存轮胎数据 - 位置: %s, 时间: %d",
                                                    getTirePositionString(position), currentTime));
                                        } else {
                                            // 保存失败
                                            MyLog.e(TAG, "保存轮胎数据失败", throwable);
                                        }
                                    });
                        } else {
                            MyLog.d(TAG, String.format("跳过保存轮胎数据 - 位置: %s, 距离上次保存: %dms",
                                    getTirePositionString(position), currentTime - lastSaveTime));
                        }

                        // 更新警告信息
                        updateWarningInfo(convertedData);

                        // 在主线程发送事件通知
                      //  mainHandler.post(() -> {
                            TirePressureScopeBus.eventTireStatusDataResponse().post(convertedData);
                            notifyTireStatus(convertedData);
                    //    });
                        break;
                    case DEVICE_STATUS:
                        mainHandler.post(() -> notifyDeviceStatus(data));
                        break;
                    case ERROR:
                        mainHandler.post(() -> notifyError(data.toString()));
                        break;
                    default:
                        break;
                }
            } catch (Exception e) {
                MyLog.e(TAG, "处理数据异常", e);
            }
        });
    }

    /**
     * 转换温度值
     * 根据当前设置的温度单位进行转换：
     * 1. 如果是摄氏度，保持原值
     * 2. 如果是华氏度，进行转换
     * 3. 确保温度在有效范围内
     *
     * @param celsius 摄氏度温度值
     * @return 转换后的温度值
     */
    private int convertTemperature(int celsius) {
        // 如果是摄氏度，直接返回
        if ("℃".equals(SettingsManager.getTempUnit())) {
            return celsius;
        }

        // 转换为华氏度
        return (int) (celsius * 1.8 + 32);
    }

    /**
     * 处理告警状态
     * 1. 检查传感器原始告警状态
     * 2. 根据设置的阈值计算告警状态
     * 3. 转换为显示状态码
     *
     * @param tireData 轮胎状态数据
     * @return 显示状态码
     */
    private int processWarningStatus(TireStatusData tireData) {
        int warningStatus = tireData.getWarningStatus();

        // 1. 检查漏气和信号丢失（传感器直接报告的状态）
        if ((warningStatus & TireStatusData.WARNING_LEAKAGE) != 0) {
            MyLog.w(TAG, "漏气告警");
            return TireStatusData.STATUS_LEAKAGE;
        }

        if ((warningStatus & TireStatusData.WARNING_LOW_BATTERY) != 0) {
            MyLog.w(TAG, "低电量告警");
            return TireStatusData.STATUS_BATTERY_LOW;
        }

        // 2. 根据设置的阈值判断告警状态
        float pressure = tireData.getPressure();
        float highPressureThreshold = SettingsManager.getHighPressure();
        float lowPressureThreshold = SettingsManager.getLowPressure();

        // 检查压力
        if (pressure > highPressureThreshold) {
            MyLog.w(TAG, String.format("高压告警: 当前压力=%.1f, 阈值=%.1f",
                    pressure, highPressureThreshold));
            return TireStatusData.STATUS_PRESSURE_HIGH;
        }

        if (pressure < lowPressureThreshold) {
            MyLog.w(TAG, String.format("低压告警: 当前压力=%.1f, 阈值=%.1f",
                    pressure, lowPressureThreshold));
            return TireStatusData.STATUS_PRESSURE_LOW;
        }

        // 检查温度
        int temperature = tireData.getTemperature();
        int highTempThreshold = SettingsManager.getHighTemp();
        if (temperature > highTempThreshold) {
            MyLog.w(TAG, String.format("高温告警: 当前温度=%d, 阈值=%d",
                    temperature, highTempThreshold));
            return TireStatusData.STATUS_TEMPERATURE_HIGH;
        }

        // 记录信号丢失，但不影响显示状态
        if ((warningStatus & TireStatusData.WARNING_SIGNAL_LOST) != 0) {
            MyLog.w(TAG, "信号丢失告警");
        }

        return TireStatusData.STATUS_NORMAL;
    }

    /**
     * 重置警告弹窗状态
     * 在车机唤醒时调用，用于重置警告间隔计时
     */
    public void resetWarningState() {
        MyLog.d(TAG, "重置警告弹窗状态");
        lastWarningTime = 0; // 重置上次警告时间
        warningTires.clear(); // 清空警告列表
        warningHandler.removeCallbacksAndMessages(null); // 取消所有延迟任务
        isDialogShowing = false; // 重置弹窗显示状态
    }

    /**
     * 更新警告信息
     */
    private void updateWarningInfo(TireStatusData tireData) {
        int position = tireData.getPosition().getValue();
        MyLog.d(TAG, String.format("更新轮胎警告信息 - 位置: %s, 状态: %d, 压力: %.1f%s, 温度: %d%s",
                getTirePositionString(position),
                tireData.getDisplayStatus(),
                tireData.getPressure(),
                tireData.getPressureUnit(),
                tireData.getTemperature(),
                tireData.getTemperatureUnit()));

        // 检查是否有警告
        if (tireData.getDisplayStatus() == STATUS_NORMAL) {
            // 如果轮胎状态正常，从警告列表中移除
            warningTires.remove(position);
            MyLog.d(TAG, String.format("轮胎状态正常，从警告列表中移除 - 位置: %s", getTirePositionString(position)));
            return;
        }

        // 更新或添加警告轮胎
        warningTires.put(position, tireData);
        MyLog.d(TAG, String.format("更新警告列表 - 当前警告轮胎数量: %d", warningTires.size()));

        // 取消之前的延迟任务
        warningHandler.removeCallbacksAndMessages(null);
        MyLog.d(TAG, "取消之前的延迟显示任务");

        // 设置新的延迟任务
        warningHandler.postDelayed(() -> {
            // 检查是否需要显示警告
            long currentTime = System.currentTimeMillis();
            long timeSinceLastWarning = currentTime - lastWarningTime;
            MyLog.d(TAG, String.format("检查是否需要显示警告 - 距离上次警告时间: %dms, 警告间隔: %dms, 设置值: %d分钟",
                    timeSinceLastWarning, warningInterval, SettingsManager.getWarningInterval()));

            // 如果是"本次"模式，只显示一次
            if (SettingsManager.getWarningInterval() == SettingsManager.WARNING_INTERVAL_ONCE) {
                if (lastWarningTime == 0) {
                    MyLog.d(TAG, "本次模式 - 首次显示警告");
                    showWarningDialog();
                    lastWarningTime = currentTime;
                } else {
                    MyLog.d(TAG, "本次模式 - 已显示过警告，不再显示");
                }
            } else if (timeSinceLastWarning >= warningInterval) {
                MyLog.d(TAG, "满足显示警告条件，准备显示警告弹窗");
                showWarningDialog();
                lastWarningTime = currentTime;
            } else {
                MyLog.d(TAG, "不满足显示警告条件，跳过显示");
            }
        }, DATA_COLLECTION_DELAY);
        MyLog.d(TAG, String.format("设置新的延迟显示任务 - 延迟时间: %dms", DATA_COLLECTION_DELAY));
    }

    /**
     * 显示警告弹窗
     */
    private void showWarningDialog() {
        mainHandler.post(() -> {
            if (warningTires.isEmpty()) {
                MyLog.d(TAG, "警告列表为空，不显示警告弹窗");
                return;
            }

            // 如果已经有弹窗在显示，则不再显示新的弹窗
            if (isDialogShowing) {
                MyLog.d(TAG, "已有警告弹窗在显示，跳过新的弹窗显示");
                return;
            }

            MyLog.d(TAG, String.format("开始显示警告弹窗 - 警告轮胎数量: %d", warningTires.size()));

            // 构建警告信息
            String title;
            String description;

            // 按位置排序显示警告
            List<Map.Entry<Integer, TireStatusData>> sortedWarnings = new ArrayList<>(warningTires.entrySet());
            sortedWarnings.sort(Map.Entry.comparingByKey());

            if (warningTires.size() > 1) {
                // 多个轮胎警告时，显示统计信息
                title = String.format("轮胎存在%d个异常", warningTires.size());

                // 统计不同类型的警告
                int lowPressureCount = 0;
                int highPressureCount = 0;
                int leakageCount = 0;
                int lowBatteryCount = 0;
                int highTempCount = 0;

                for (TireStatusData tire : warningTires.values()) {
                    switch (tire.getDisplayStatus()) {
                        case STATUS_PRESSURE_LOW:
                            lowPressureCount++;
                            break;
                        case STATUS_PRESSURE_HIGH:
                            highPressureCount++;
                            break;
                        case STATUS_LEAKAGE:
                            leakageCount++;
                            break;
                        case STATUS_BATTERY_LOW:
                            lowBatteryCount++;
                            break;
                        case STATUS_TEMPERATURE_HIGH:
                            highTempCount++;
                            break;
                    }
                }

                // 构建简洁的警告摘要
                StringBuilder summaryBuilder = new StringBuilder();
                summaryBuilder.append("检测到轮胎异常情况，建议您：\n\n");

                // 添加常见建议
                if (leakageCount > 0) {
                    summaryBuilder.append("• 立即停车检查漏气轮胎\n");
                }
                if (lowPressureCount > 0 || highPressureCount > 0) {
                    summaryBuilder.append("• 尽快调整异常胎压\n");
                }
                if (highTempCount > 0) {
                    summaryBuilder.append("• 适当休息，让轮胎温度降低\n");
                }
                if (lowBatteryCount > 0) {
                    summaryBuilder.append("• 安排更换传感器电池\n");
                }

                // 添加通用建议
                summaryBuilder.append("• 定期检查轮胎状态");

                description = summaryBuilder.toString().trim();
                MyLog.d(TAG, "显示多个轮胎警告的通用建议");
            } else {
                // 单个轮胎警告时，显示详细信息
                TireStatusData tire = sortedWarnings.get(0).getValue();
                title = "轮胎异常警告";
                String position = getTirePositionString(tire.getPosition().getValue());
                String warning = getWarningMessage(tire);

                StringBuilder detailBuilder = new StringBuilder();
                detailBuilder.append("• 位置: ").append(position).append("\n");
                detailBuilder.append("• 警告: ").append(warning).append("\n");

                // 添加当前值信息
                if (tire.getDisplayStatus() != STATUS_LEAKAGE && tire.getDisplayStatus() != STATUS_BATTERY_LOW) {
                    detailBuilder.append("• 当前");
                    if (tire.getDisplayStatus() == STATUS_TEMPERATURE_HIGH) {
                        detailBuilder.append("温度: ").append(tire.getTemperature()).append(tire.getTemperatureUnit());
                    } else {
                        detailBuilder.append("胎压: ").append(String.format("%.1f", tire.getPressure())).append(tire.getPressureUnit());
                    }
                    detailBuilder.append("\n");
                }

                // 添加建议
                detailBuilder.append("• 建议: ");
                switch (tire.getDisplayStatus()) {
                    case STATUS_PRESSURE_LOW:
                        detailBuilder.append("请检查轮胎并及时充气");
                        break;
                    case STATUS_PRESSURE_HIGH:
                        detailBuilder.append("请检查轮胎并适当放气");
                        break;
                    case STATUS_LEAKAGE:
                        detailBuilder.append("请立即停车检查轮胎");
                        break;
                    case STATUS_BATTERY_LOW:
                        detailBuilder.append("请尽快更换传感器电池");
                        break;
                    case STATUS_TEMPERATURE_HIGH:
                        detailBuilder.append("请停车让轮胎降温");
                        break;
                    default:
                        detailBuilder.append("请检查轮胎状态");
                        break;
                }

                description = detailBuilder.toString().trim();
                MyLog.d(TAG, String.format("显示单个轮胎警告 - 位置: %s, 警告: %s", position, warning));
            }

            // 显示警告弹窗
            MyLog.d(TAG, "显示警告弹窗");
            isDialogShowing = true;
            TirePressureDialog.showTirePressureDialog(
                    App.getContextInstance(),
                    title,
                    null,
                    null,
                    description,
                    true,
                    "查看",
                    "取消",
                    true,
                    true,
                    new TirePressureDialog.OnDialogClickListener() {
                        @Override
                        public void onOkButtonClick(FlexibleDialog dialog) {
                            MyLog.d(TAG, "用户点击查看按钮");
                            isDialogShowing = false;
                            // 发送卡片操作事件
                            GeneralNotificationScopeBus.cardOperationEvent().post("tpms");
                        }

                        @Override
                        public void onCancelButtonClick(FlexibleDialog dialog) {
                            MyLog.d(TAG, "用户点击取消按钮");
                            isDialogShowing = false;
                            // 不清空警告列表，让用户继续看到警告
                        }
                    }
            );
        });
    }

    /**
     * 获取警告信息
     */
    private String getWarningMessage(TireStatusData tire) {
        String message;
        switch (tire.getDisplayStatus()) {
            case STATUS_PRESSURE_LOW:
                message = "胎压过低，请及时充气";
                break;
            case STATUS_PRESSURE_HIGH:
                message = "胎压过高，请检查并放气";
                break;
            case STATUS_LEAKAGE:
                message = "轮胎漏气，请立即停车检查";
                break;
            case STATUS_BATTERY_LOW:
                message = "传感器电量低，请更换电池";
                break;
            case STATUS_TEMPERATURE_HIGH:
                message = "温度过高，请停车降温";
                break;
            default:
                message = "状态异常，请检查";
                break;
        }
        MyLog.d(TAG, String.format("生成警告信息 - 位置: %s, 状态: %d, 信息: %s",
                getTirePositionString(tire.getPosition().getValue()),
                tire.getDisplayStatus(),
                message));
        return message;
    }

    /**
     * 获取轮胎位置字符串
     */
    private String getTirePositionString(int position) {
        switch (position) {
            case 0:
                return "左前轮";
            case 1:
                return "右前轮";
            case 16:
                return "左后轮";
            case 17:
                return "右后轮";
            case 5:
                return "备胎";
            default:
                return "未知位置";
        }
    }

    // ============== 监听器管理 ==============

    /**
     * 添加监听器
     *
     * @param listener 监听器对象
     */
    public void addListener(TpmsListener listener) {
        if (!listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    /**
     * 移除监听器
     *
     * @param listener 监听器对象
     */
    public void removeListener(TpmsListener listener) {
        listeners.remove(listener);
    }

    // ============== 通知方法 ==============
    private void notifyTireStatus(TireStatusData data) {
        for (TpmsListener listener : listeners) {
            listener.onTireStatus(data);
        }
    }

    private void notifyDeviceStatus(TpmsData data) {
        for (TpmsListener listener : listeners) {
            listener.onDeviceStatus(data);
        }
    }

    private void notifyDeviceConnectionChanged(boolean connected) {
        for (TpmsListener listener : listeners) {
            listener.onDeviceConnectionChanged(connected);
        }
    }

    private void notifyError(String error) {
        MyLog.e(TAG, error);
        for (TpmsListener listener : listeners) {
            listener.onError(error);
        }
    }

    // ============== 状态管理 ==============
    private void updateState(TpmsState newState) {
        TpmsStateEvent event = new TpmsStateEvent(newState, null);
        TirePressureScopeBus.eventTpmsState().post(event);
        MyLog.d(TAG, "TPMS状态更新: " + newState.getDescription());
    }

    private void setError(TpmsError error) {
        TpmsStateEvent event = new TpmsStateEvent(TpmsState.ERROR, error);
        TirePressureScopeBus.eventTpmsState().post(event);
        MyLog.e(TAG, "TPMS错误: " + error.getTitle() + " - " + error.getSolution());
    }

    // ============== 工具方法 ==============

    /**
     * 获取运行状态
     *
     * @return 是否正在运行
     */
    public boolean isRunning() {
        return isRunning;
    }

    /**
     * 设置警告弹窗显示间隔
     *
     * @param minutes 间隔时间(分钟)
     */
    public void setWarningInterval(int minutes) {
        warningInterval = minutes * 60 * 1000;
        MyLog.d(TAG, String.format("更新警告间隔 - 新间隔: %d分钟 (%dms)", minutes, warningInterval));
    }
}