package com.smartcar.easylauncher.modules.other;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;

import com.smartcar.easylauncher.core.base.BaseActivity;
import com.smartcar.easylauncher.home.main.HomeActivity;

import me.jessyan.autosize.AutoSizeCompat;
import me.jessyan.autosize.AutoSizeConfig;

/**
 * desc   : 重启界面
 *
 * <AUTHOR>
 */
public class RestartActivity extends BaseActivity {

    public static void start(Context context) {
        Intent intent = new Intent(context, RestartActivity.class);
        if (!(context instanceof Activity)) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initDate();
    }


    @Override
    public void initImmersionBar() {
        super.initImmersionBar();
    }

    private void initDate() {
        restart(this);
        finish();
    }

    public static void restart(Context context) {
        Intent intent;
//        if (true) {
//            // 如果是未登录的情况下跳转到闪屏页
//            intent = new Intent(context, SplashActivity.class);
//        } else {
        // 如果是已登录的情况下跳转到首页
        intent = new Intent(context, HomeActivity.class);
        //   }

        if (!(context instanceof Activity)) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        context.startActivity(intent);
    }


    @Override
    public Resources getResources() {
        //需要升级到 v1.1.2 及以上版本才能使用 AutoSizeCompat
        int width = AutoSizeConfig.getInstance().getScreenWidth();
        int height = AutoSizeConfig.getInstance().getScreenHeight();
        AutoSizeCompat.autoConvertDensity(super.getResources(), 900, width > height);
        return super.getResources();
    }

    /**
     * 判断当前设备是否是平板
     */
    public boolean isTablet() {
        return (getResources().getConfiguration().screenLayout
                & Configuration.SCREENLAYOUT_SIZE_MASK)
                >= Configuration.SCREENLAYOUT_SIZE_LARGE;
    }

}