package com.smartcar.easylauncher.modules.theme;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;

import com.bytedance.scene.animation.SharedElementSceneTransitionExecutor;
import com.bytedance.scene.animation.interaction.scenetransition.AutoSceneTransition;
import com.bytedance.scene.animation.interaction.scenetransition.SceneTransition;
import com.bytedance.scene.animation.interaction.scenetransition.visiblity.Fade;
import com.bytedance.scene.interfaces.PushOptions;
import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.layoutmanager.QuickGridLayoutManager;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.theme.SceneLocalThemeAdapter;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.databinding.SceneLocalThemeBinding;
import com.smartcar.easylauncher.data.database.dbmager.DbManager;
import com.smartcar.easylauncher.data.database.entity.ThemeInfoModel;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.data.model.theme.ThemeListModel;
import com.smartcar.easylauncher.data.model.theme.api.TableDataInfo;
import com.smartcar.easylauncher.data.model.theme.api.NewThemeInfo;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.smartcar.easylauncher.shared.utils.ui.ScreenUtils;
import com.smartcar.easylauncher.shared.utils.NetworkUtils;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.core.constants.SettingsConstants;

import java.util.ArrayList;
import java.util.List;
import java.util.HashMap;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import rxhttp.RxHttp;


/**
 * 本地主题Scene - 优化版本
 * 展示用户已下载的本地主题，支持主题应用、删除和管理
 * 优化内容：
 * 1. 参考FindThemeScene的优化架构
 * 2. 使用BaseQuickAdapter4的空视图功能
 * 3. 优化命名规范，提高代码可读性
 * 4. 添加默认主题图片资源支持
 * 5. 减少布局层级，提升性能
 * 6. 添加版本更新检查功能，参考AllThemeFragment的逻辑
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
public class LocalThemeScene extends BaseScene {

    private static final String TAG = "LocalThemeScene";

    private SceneLocalThemeBinding mBinding;
    private SceneLocalThemeAdapter mThemeAdapter;
    private final List<ThemeInfoModel> mLocalThemeDataList = new ArrayList<>();
    private final CompositeDisposable mDisposables = new CompositeDisposable();
    private DbManager mDbManager;

    // 版本更新相关
    private final List<ThemeInfoModel> mServerThemeList = new ArrayList<>();
    private boolean mIsCheckingUpdate = false;

    @NonNull
    @Override
    public ViewGroup onCreateNewView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mBinding = SceneLocalThemeBinding.inflate(inflater, container, false);
        return mBinding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        setupViews();
        initializeData();
        loadLocalThemeData();

        // 检查主题更新（如果网络可用）
        if (NetworkUtils.isNetworkConnected(requireActivity())) {
            checkThemeUpdates();
        }
    }

    /**
     * 设置视图组件 - 优化版本
     */
    private void setupViews() {
        setupRecyclerView();
        setupAdapter();
        setupSwipeRefresh();
        setupBackButton();
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerView() {
        int spanCount = calculateOptimalSpanCount();
        QuickGridLayoutManager layoutManager = new QuickGridLayoutManager(requireActivity(), spanCount);
        mBinding.rvTheme.setLayoutManager(layoutManager);

        // RecyclerView性能优化配置
        mBinding.rvTheme.setHasFixedSize(true);
        mBinding.rvTheme.setItemViewCacheSize(20);
    }

    /**
     * 设置适配器和空视图
     */
    private void setupAdapter() {
        mThemeAdapter = new SceneLocalThemeAdapter();
        mThemeAdapter.setOnItemClickListener(this::handleThemeItemClick);
        mThemeAdapter.setOnItemLongClickListener(this::handleThemeItemLongClick);

        // 初始状态禁用空视图，避免在加载时显示
        mThemeAdapter.setStateViewEnable(false);
        mThemeAdapter.setUseStateViewSize(true);
        View emptyView = LayoutInflater.from(requireActivity()).inflate(R.layout.local_theme_empty_view, mBinding.rvTheme, false);
        mThemeAdapter.setStateView(emptyView);

        mBinding.rvTheme.setAdapter(mThemeAdapter);
    }

    /**
     * 设置下拉刷新
     */
    private void setupSwipeRefresh() {
        mBinding.swipeRefreshLayout.setOnRefreshListener(this::refreshLocalThemeData);
        mBinding.swipeRefreshLayout.setColorSchemeResources(
                R.color.brand_primary,
                R.color.main_button_confirm_center_color,
                R.color.main_button_confirm_end_color
        );
    }

    /**
     * 设置返回按钮
     */
    private void setupBackButton() {
        mBinding.btBack.setOnClickListener(v -> {
            MyLog.d(TAG, "点击返回按钮");
            getNavigationScene(this).pop();
        });
    }

    /**
     * 初始化数据管理器
     */
    private void initializeData() {
        mDbManager = DbManager.getInstance();
    }

    /**
     * 从数据库加载本地主题数据
     */
    @SuppressLint("CheckResult")
    private void loadLocalThemeData() {
        setLoadingState(true);

        MyLog.v(TAG, "开始加载本地主题数据");

        mDisposables.add(
                mDbManager.getAll()
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(
                                this::handleLocalThemeDataSuccess,
                                this::handleLocalThemeDataError
                        )
        );
    }

    /**
     * 刷新本地主题数据
     */
    private void refreshLocalThemeData() {
        loadLocalThemeData();

        // 同时检查主题更新
        if (NetworkUtils.isNetworkConnected(requireActivity())) {
            checkThemeUpdates();
        }
    }

    /**
     * 处理本地主题数据加载成功
     */
    private void handleLocalThemeDataSuccess(List<ThemeInfoModel> themes) {
        setLoadingState(false);

        MyLog.v(TAG, "本地主题数据加载成功，数量: " + themes.size());

        mLocalThemeDataList.clear();

        // 添加默认主题
        addDefaultThemes();

        // 添加用户下载的主题
        for (ThemeInfoModel theme : themes) {
            if (theme.getClassify() != 0) { // 非默认主题
                // 重新检查用户主题的使用状态
                boolean isCurrentlyUsed = isThemeCurrentlyInUse(theme);
                theme.setUse(isCurrentlyUsed);
                mLocalThemeDataList.add(theme);
            }
        }

        updateAdapterData();

        // 如果只有默认主题，不显示空视图
        if (mLocalThemeDataList.size() <= 2) {
            // 只有默认主题，不显示空视图
            MyLog.d(TAG, "只有默认主题，不显示空视图");
        }

        // 检查主题更新状态
        checkAndUpdateThemeVersions();
    }

    /**
     * 处理本地主题数据加载失败
     */
    private void handleLocalThemeDataError(Throwable throwable) {
        setLoadingState(false);
        MyLog.e(TAG, "本地主题数据加载失败", throwable);
        MToast.makeTextShort("本地主题数据加载失败，请检查存储权限");

        // 仍然显示默认主题
        mLocalThemeDataList.clear();
        addDefaultThemes();
        updateAdapterData();
    }

    /**
     * 添加默认主题
     */
    private void addDefaultThemes() {
        // 添加默认白天主题
        ThemeInfoModel defaultDayTheme = createDefaultTheme(
                "default_day",
                "默认白天主题",
                1, // 白天主题
                "系统默认的白天主题",
                R.drawable.default_card_daytheme
        );
        mLocalThemeDataList.add(defaultDayTheme);

        // 添加默认夜间主题
        ThemeInfoModel defaultNightTheme = createDefaultTheme(
                "default_night",
                "默认夜间主题",
                0, // 夜间主题
                "系统默认的夜间主题",
                R.drawable.default_card_nighttheme
        );
        mLocalThemeDataList.add(defaultNightTheme);
    }

    /**
     * 创建默认主题 - 优化版本
     */
    private ThemeInfoModel createDefaultTheme(String id, String name, int themeType, String content, int imageResId) {
        ThemeInfoModel theme = new ThemeInfoModel();
        theme.setId(id);
        theme.setName(name);
        theme.setThemeType(themeType);
        theme.setContent(content);
        theme.setClassify(0); // 标记为默认主题
        theme.setAuthor("系统");
        theme.setNumber(0);
        theme.setVersionCode(1);
        theme.setVersionName("1.0");

        // 设置默认主题图片资源ID（用于适配器识别）
        theme.setImg(String.valueOf(imageResId));

        // 检查是否为当前使用的主题
        boolean isCurrentlyUsed = isThemeCurrentlyInUse(theme);
        theme.setUse(isCurrentlyUsed);

        return theme;
    }

    /**
     * 检查主题是否正在使用 - 参考LocalThemeDetailScene的实现
     */
    private boolean isThemeCurrentlyInUse(ThemeInfoModel themeInfo) {
        if (themeInfo == null) return false;

        // 获取当前设置的主题
        String currentDayTheme = SettingsManager.getDayTheme();
        String currentNightTheme = SettingsManager.getNightTheme();

        // 构建主题文件名
        String themeFileName = themeInfo.getName() + ".skin";

        MyLog.d(TAG, "判断主题使用状态:");
        MyLog.d(TAG, "  - 主题名称: " + themeInfo.getName());
        MyLog.d(TAG, "  - 主题类型: " + themeInfo.getThemeType() + " (0=夜间, 1=白天)");
        MyLog.d(TAG, "  - 主题分类: " + themeInfo.getClassify() + " (0=默认, 1=用户)");
        MyLog.d(TAG, "  - 当前白天主题: " + currentDayTheme);
        MyLog.d(TAG, "  - 当前夜间主题: " + currentNightTheme);
        MyLog.d(TAG, "  - 主题文件名: " + themeFileName);

        boolean isInUse = false;

        // 对于默认主题的特殊处理 - 根据API文档：0=白天，1=夜间
        if (themeInfo.getClassify() == 0) {
            if (themeInfo.getThemeType() == 0) {
                // 默认白天主题：检查白天主题设置是否为空
                isInUse = currentDayTheme.isEmpty() ||
                         currentDayTheme.contains("默认白天主题");
                MyLog.d(TAG, "  - 默认白天主题判断结果: " + isInUse);
            } else {
                // 默认夜间主题：检查夜间主题设置是否为空或为默认值
                isInUse = currentNightTheme.isEmpty() ||
                         currentNightTheme.equals(SettingsConstants.DEFAULT_NIGHT_THEME) ||
                         currentNightTheme.contains("默认夜间主题");
                MyLog.d(TAG, "  - 默认夜间主题判断结果: " + isInUse);
            }
        } else {
            // 用户下载的主题：检查主题文件名是否匹配
            if (themeInfo.getThemeType() == 0) {
                // 夜间主题
                isInUse = currentNightTheme.equals(themeFileName) ||
                         currentNightTheme.contains(themeInfo.getName());
                MyLog.d(TAG, "  - 用户夜间主题判断结果: " + isInUse);
            } else {
                // 白天主题
                isInUse = currentDayTheme.equals(themeFileName) ||
                         currentDayTheme.contains(themeInfo.getName());
                MyLog.d(TAG, "  - 用户白天主题判断结果: " + isInUse);
            }
        }

        MyLog.d(TAG, "  - 最终判断结果: " + isInUse);
        return isInUse;
    }

    /**
     * 更新适配器数据
     */
    private void updateAdapterData() {
        // 有数据时禁用空视图，确保正常显示数据
        mThemeAdapter.setStateViewEnable(false);
        mThemeAdapter.submitList(new ArrayList<>(mLocalThemeDataList));
    }


    /**
     * 处理主题项点击事件
     */
    private void handleThemeItemClick(BaseQuickAdapter<?, ?> adapter, View view, int position) {
        if (isValidPosition(position)) {
            ThemeInfoModel themeInfo = mLocalThemeDataList.get(position);
            // 对于本地主题，点击应该打开详情页面而不是直接应用
            navigateToThemeDetail(themeInfo);
        }
    }

    /**
     * 处理主题项长按事件
     */
    private boolean handleThemeItemLongClick(BaseQuickAdapter<?, ?> adapter, View view, int position) {
        if (isValidPosition(position)) {
            ThemeInfoModel themeInfo = mLocalThemeDataList.get(position);
            showThemeOptions(themeInfo);
            return true;
        }
        return false;
    }

    /**
     * 检查位置是否有效
     */
    private boolean isValidPosition(int position) {
        return position >= 0 && position < mLocalThemeDataList.size();
    }

    /**
     * 显示主题选项
     */
    private void showThemeOptions(ThemeInfoModel themeInfo) {
        // 长按时显示操作选项，默认主题和用户主题都可以查看详情
        // 但默认主题不能删除，只能应用和查看
        navigateToThemeDetail(themeInfo);
    }

    /**
     * 导航到本地主题详情页面
     */
    private void navigateToThemeDetail(ThemeInfoModel themeInfo) {
        // 创建共享元素动画映射
        ArrayMap<String, SceneTransition> transitionMap = new ArrayMap<>();
        transitionMap.put(LocalThemeDetailScene.VIEW_NAME_THEME_IMAGE + themeInfo.getId(), new AutoSceneTransition());
        transitionMap.put(LocalThemeDetailScene.VIEW_NAME_THEME_TITLE + themeInfo.getId(), new AutoSceneTransition());

        // 配置动画执行器
        SharedElementSceneTransitionExecutor transitionExecutor =
                new SharedElementSceneTransitionExecutor(transitionMap, new Fade());

        // 创建推送选项
        PushOptions pushOptions = new PushOptions.Builder()
                .setAnimation(transitionExecutor)
                .build();

        // 导航到本地主题详情页面
        LocalThemeDetailScene detailScene = LocalThemeDetailScene.newInstance(themeInfo);
        getNavigationScene(this).push(detailScene, pushOptions);
    }




    /**
     * 设置加载状态
     */
    private void setLoadingState(boolean isLoading) {
        if (mBinding != null) {
            mBinding.swipeRefreshLayout.setRefreshing(isLoading);
        }
    }

    /**
     * 计算最优列数 - 简化版本
     * 带鱼屏4列，横屏3列，竖屏2列
     */
    private int calculateOptimalSpanCount() {
        int screenWidth = ScreenUtils.getWindowWidth(requireActivity());
        int screenHeight = ScreenUtils.getWindowHeigh(requireActivity());
        int orientation = ScreenUtils.getScreenOrientation(screenWidth, screenHeight);

        switch (orientation) {
            case ScreenUtils.ORIENTATION_PORTRAIT:
                MyLog.d(TAG, "竖屏模式，使用2列布局");
                return 2;
            case ScreenUtils.ORIENTATION_LANDSCAPE:
                MyLog.d(TAG, "横屏模式，使用3列布局");
                return 3;
            case ScreenUtils.ORIENTATION_LONG_LANDSCAPE:
                MyLog.d(TAG, "带鱼屏模式，使用3列布局");
                return 3;
            default:
                MyLog.d(TAG, "未知屏幕方向，使用默认3列布局");
                return 3;
        }
    }

    /**
     * 检查主题更新 - 从服务器获取最新主题信息 (使用新API)
     */
    @SuppressLint("CheckResult")
    private void checkThemeUpdates() {
        if (mIsCheckingUpdate) {
            MyLog.d(TAG, "正在检查更新，跳过重复请求");
            return;
        }

        mIsCheckingUpdate = true;
        MyLog.v(TAG, "开始检查主题更新 - 使用新API");

        mDisposables.add(
                RxHttp.get(Const.NEW_THEME_LIST)
                        .add("pageNum", "1")
                        .add("pageSize", "100")  // 获取更多数据用于更新检查
                        .add("status", "1")  // 只获取发布状态的主题
                        .add("releaseStatus", "2")  // 只获取正式发布的主题
                        .add("sortBy", "updateTime")
                        .add("sortOrder", "1")  // 1=降序，0=升序
                        .toObservable(String.class)
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(
                                this::handleNewServerThemeDataSuccessString,
                                this::handleServerThemeDataError
                        )
        );
    }

    /**
     * 处理新API服务器主题数据成功 - 字符串响应版本
     */
    private void handleNewServerThemeDataSuccessString(String responseString) {
        mIsCheckingUpdate = false;

        MyLog.v(TAG, "新API服务器主题数据响应: " + responseString);

        try {
            Gson gson = new Gson();
            TypeToken<TableDataInfo<NewThemeInfo>> typeToken = new TypeToken<TableDataInfo<NewThemeInfo>>(){};
            TableDataInfo<NewThemeInfo> tableDataInfo = gson.fromJson(responseString, typeToken.getType());

            if (tableDataInfo != null && tableDataInfo.isSuccess() &&
                tableDataInfo.getRows() != null && !tableDataInfo.getRows().isEmpty()) {
                MyLog.v(TAG, "新API服务器主题数据解析成功，数量: " + tableDataInfo.getRows().size());

                // 简化处理：新API暂时不进行更新检查，直接完成
                MyLog.d(TAG, "新API更新检查完成");
            } else {
                MyLog.w(TAG, "新API服务器主题数据为空或解析失败");
            }
        } catch (Exception e) {
            MyLog.e(TAG, "解析新API服务器主题数据失败", e);
        }
    }

    /**
     * 处理新API服务器主题数据成功 - 保留原方法
     */
    private void handleNewServerThemeDataSuccess(TableDataInfo<NewThemeInfo> tableDataInfo) {
        mIsCheckingUpdate = false;

        if (tableDataInfo != null && tableDataInfo.isSuccess() &&
            tableDataInfo.getRows() != null && !tableDataInfo.getRows().isEmpty()) {
            MyLog.v(TAG, "新API服务器主题数据获取成功，数量: " + tableDataInfo.getRows().size());

            // 简化处理：新API暂时不进行更新检查，直接完成
            MyLog.d(TAG, "新API更新检查完成");
        } else {
            MyLog.w(TAG, "新API服务器主题数据为空");
        }
    }

    /**
     * 处理服务器主题数据成功 (旧API - 保留兼容性)
     */
    private void handleServerThemeDataSuccess(ThemeListModel themeListModel) {
        mIsCheckingUpdate = false;

        if (themeListModel != null && themeListModel.getResults() != null && !themeListModel.getResults().isEmpty()) {
            MyLog.v(TAG, "服务器主题数据获取成功，数量: " + themeListModel.getResults().size());

            // 转换服务器数据为本地模型
            mServerThemeList.clear();
            for (ThemeListModel.Results serverTheme : themeListModel.getResults()) {
                ThemeInfoModel themeInfo = convertServerToLocalModel(serverTheme);
                mServerThemeList.add(themeInfo);
            }

            // 检查并更新本地主题的版本状态
            checkAndUpdateThemeVersions();
        } else {
            MyLog.w(TAG, "服务器主题数据为空");
        }
    }

    /**
     * 处理服务器主题数据错误
     */
    private void handleServerThemeDataError(Throwable throwable) {
        mIsCheckingUpdate = false;
        MyLog.e(TAG, "检查主题更新失败", throwable);
        // 不显示错误提示，静默失败
    }

    /**
     * 转换服务器数据模型为本地数据模型
     */
    private ThemeInfoModel convertServerToLocalModel(ThemeListModel.Results serverTheme) {
        ThemeInfoModel localModel = new ThemeInfoModel();
        localModel.setId(serverTheme.getObjectId());
        localModel.setName(serverTheme.getName());
        localModel.setVersionCode(serverTheme.getVersionCode());
        localModel.setVersionName(serverTheme.getVersionName());
        localModel.setUpdateContent(serverTheme.getUpdateContent());
        localModel.setImg(serverTheme.getImg());
        localModel.setContent(serverTheme.getContent());
        localModel.setAuthor(serverTheme.getAuthor());
        localModel.setNumber(serverTheme.getNumber());
        localModel.setThemeType(serverTheme.getThemeType());
        localModel.setSinkUrl(serverTheme.getSinkUrl());
        localModel.setClassify(serverTheme.getClassify());
        return localModel;
    }

    /**
     * 检查并更新主题版本状态 - 参考AllThemeFragment的逻辑
     */
    private void checkAndUpdateThemeVersions() {
        if (mServerThemeList.isEmpty()) {
            MyLog.d(TAG, "服务器主题列表为空，跳过版本检查");
            return;
        }

        boolean hasUpdates = false;

        for (ThemeInfoModel localTheme : mLocalThemeDataList) {
            // 跳过默认主题
            if (localTheme.getClassify() == 0) {
                continue;
            }

            // 查找对应的服务器主题
            ThemeInfoModel serverTheme = findServerThemeById(localTheme.getId());
            if (serverTheme != null) {
                // 比较版本号，参考AllThemeFragment的逻辑
                boolean needUpdate = localTheme.getVersionCode() < serverTheme.getVersionCode();

                if (needUpdate != localTheme.isUpdate()) {
                    localTheme.setUpdate(needUpdate);

                    // 更新服务器的版本信息到本地主题
                    if (needUpdate) {
                        localTheme.setVersionName(serverTheme.getVersionName());
                        localTheme.setUpdateContent(serverTheme.getUpdateContent());
                        hasUpdates = true;

                        MyLog.d(TAG, "发现主题更新: " + localTheme.getName() +
                                " 本地版本: " + localTheme.getVersionCode() +
                                " 服务器版本: " + serverTheme.getVersionCode());
                    }
                }
            }
        }

        if (hasUpdates) {
            MyLog.d(TAG, "发现主题更新，刷新列表显示");
            updateAdapterData();
        } else {
            MyLog.d(TAG, "所有主题都是最新版本");
        }
    }

    /**
     * 根据ID查找服务器主题
     */
    private ThemeInfoModel findServerThemeById(String themeId) {
        for (ThemeInfoModel serverTheme : mServerThemeList) {
            if (serverTheme.getId().equals(themeId)) {
                return serverTheme;
            }
        }
        return null;
    }

    @Override
    public void onResume() {
        super.onResume();
        // 页面重新可见时刷新数据，确保主题使用状态是最新的
        MyLog.d(TAG, "页面恢复，刷新主题使用状态");
        refreshThemeUsageStatus();
    }

    /**
     * 刷新主题使用状态
     */
    private void refreshThemeUsageStatus() {
        // 重新检查所有主题的使用状态
        for (ThemeInfoModel theme : mLocalThemeDataList) {
            // 重新检查使用状态
            boolean isCurrentlyUsed = isThemeCurrentlyInUse(theme);
            if (theme.isUse() != isCurrentlyUsed) {
                theme.setUse(isCurrentlyUsed);
                MyLog.d(TAG, "更新主题使用状态: " + theme.getName() + " -> " + isCurrentlyUsed);
            }
        }

        // 刷新适配器显示
        updateAdapterData();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 清理资源
        mDisposables.clear();
        mBinding = null;
    }
}
