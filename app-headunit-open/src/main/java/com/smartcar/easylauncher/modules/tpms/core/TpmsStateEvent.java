package com.smartcar.easylauncher.modules.tpms.core;

/**
 * TPMS状态事件
 * 用于向UI层传递状态变化
 */
public class TpmsStateEvent {
    private final TpmsState state;
    private final TpmsError error;

    public TpmsStateEvent(TpmsState state, TpmsError error) {
        this.state = state;
        this.error = error;
    }

    public TpmsState getState() {
        return state;
    }

    public TpmsError getError() {
        return error;
    }

    public boolean canRetry() {
        return error != null && error.isRetryable();
    }

    public String getDescription() {
        if (state == TpmsState.ERROR && error != null) {
            return error.getTitle();
        }
        return state.getDescription();
    }

    public String getErrorSolution() {
        return error != null ? error.getSolution() : null;
    }
} 