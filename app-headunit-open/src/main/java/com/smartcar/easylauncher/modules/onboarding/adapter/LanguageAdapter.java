package com.smartcar.easylauncher.modules.onboarding.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.annotation.NonNull;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.modules.onboarding.model.LanguageItem;

import java.util.Objects;

/**
 * 语言选择列表适配器
 * 使用BaseRecyclerViewAdapterHelper4实现
 * 
 * <AUTHOR>
 */
public class LanguageAdapter extends BaseQuickAdapter<LanguageItem, QuickViewHolder> {

    private OnLanguageSelectedListener listener;
    private int selectedPosition = -1;

    public LanguageAdapter() {
            }

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder holder, int position, LanguageItem item) {
        // 设置语言名称和问候语
        if (item != null) {
            holder.setText(R.id.tv_language_name, item.getName());
        }
        if (item != null) {
            holder.setText(R.id.tv_hello, item.getHello());
        }

        // 设置选中状态
        if (item != null) {
            holder.setVisible(R.id.iv_check, item.isSelected());
        }

        // 设置点击事件
        holder.getView(R.id.cl_language).setOnClickListener(v -> {
            // 更新之前选中的项
            if (selectedPosition != -1 && selectedPosition < getItems().size()) {
                Objects.requireNonNull(getItem(selectedPosition)).setSelected(false);
                notifyItemChanged(selectedPosition);
            }
            
            // 更新当前选中的项
            selectedPosition = holder.getBindingAdapterPosition();
            if (item != null) {
                item.setSelected(true);
            }
            holder.setVisible(R.id.iv_check, true);
            
            // 添加选中动画
            Animation scaleDown = AnimationUtils.loadAnimation(v.getContext(), R.anim.scale_down);
            Animation scaleUp = AnimationUtils.loadAnimation(v.getContext(), R.anim.scale_up);
            
            v.startAnimation(scaleDown);
            scaleDown.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {}
                
                @Override
                public void onAnimationEnd(Animation animation) {
                    v.startAnimation(scaleUp);
                    // 通知监听器
                    if (listener != null) {
                        listener.onLanguageSelected(item);
                    }
                }
                
                @Override
                public void onAnimationRepeat(Animation animation) {}
            });
        });
    }

    public void setOnLanguageSelectedListener(OnLanguageSelectedListener listener) {
        this.listener = listener;
    }

    /**
     * 设置当前选中的语言
     * @param languageCode 语言代码
     */
    public void setSelectedLanguage(String languageCode) {
        for (int i = 0; i < getItems().size(); i++) {
            LanguageItem item = getItem(i);
            if (item != null && item.getCode().equals(languageCode)) {
                // 清除之前的选中状态
                if (selectedPosition != -1 && selectedPosition < getItems().size()) {
                    Objects.requireNonNull(getItem(selectedPosition)).setSelected(false);
                }

                // 设置新的选中状态
                item.setSelected(true);
                selectedPosition = i;
                notifyDataSetChanged();
                break;
            }
        }
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(LayoutInflater.from(context).inflate(R.layout.item_language, viewGroup, false));
    }

    /**
     * 语言选择监听器
     */
    public interface OnLanguageSelectedListener {
        void onLanguageSelected(LanguageItem languageItem);
    }
} 