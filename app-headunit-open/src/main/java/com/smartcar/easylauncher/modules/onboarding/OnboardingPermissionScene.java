package com.smartcar.easylauncher.modules.onboarding;

import android.Manifest;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.annotation.NonNull;

import com.smart.easy.permissions.EasyPermission;
import com.smart.easy.permissions.PermissionCallback;
import com.smart.easy.permissions.PermissionItem;
import com.smartcar.easylauncher.app.App;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.Constants;
import com.smartcar.easylauncher.databinding.SceneOnboardingPermissionBinding;
import com.smartcar.easylauncher.infrastructure.event.scope.notice.cody.GeneralNotificationScopeBus;
import com.smartcar.easylauncher.data.model.common.GeneralNoticeModel;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.util.ArrayList;
import java.util.List;

/**
 * 引导流程 - 权限请求场景
 * 请求必要的应用权限
 *
 * <AUTHOR>
 */
public class OnboardingPermissionScene extends BaseOnboardingScene<SceneOnboardingPermissionBinding> {

    @Override
    protected SceneOnboardingPermissionBinding getViewBinding(LayoutInflater inflater, ViewGroup container) {
        return SceneOnboardingPermissionBinding.inflate(inflater, container, false);
    }

    @Override
    protected void initView() {
        MyLog.v(TAG, "权限请求场景初始化");
        
        // 设置进度指示器状态
        if (binding != null) {
            binding.progressIndicator.setCurrentStep(3);
        
            // 显示返回按钮
            binding.ivBack.setVisibility(View.VISIBLE);
            
            // 设置Lottie动画
            binding.lottieAnimation.setAnimation("permission_animation.json");
            binding.lottieAnimation.playAnimation();
        }

    }
    

    


    @Override
    public void onViewCreated(@NonNull View view, android.os.Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 在视图创建完成后设置动画
        setupAnimations();
    }
    
    /**
     * 设置动画效果
     */
    private void setupAnimations() {
        if (binding == null || getActivity() == null) return;
        
        // 添加淡入动画
        Animation fadeIn = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in);
        binding.tvTitle.startAnimation(fadeIn);
        binding.tvSubtitle.startAnimation(fadeIn);
        binding.ivBack.startAnimation(fadeIn);
        
        // 添加卡片动画
        Animation slideIn = AnimationUtils.loadAnimation(getActivity(), R.anim.slide_in_right);
        slideIn.setStartOffset(200);
        binding.cvPermissions.startAnimation(slideIn);
        
        // 添加按钮动画
        Animation fadeInDelayed = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in);
        fadeInDelayed.setStartOffset(400);
        binding.btnGrantPermissions.startAnimation(fadeInDelayed);
        
        // 设置权限卡片动画
        animatePermissionCards();
    }
    
    /**
     * 设置权限卡片动画
     */
    private void animatePermissionCards() {
        if (binding == null || getActivity() == null) return;
        
        // 创建动画
        Animation fadeInSlide1 = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in_slide_up);
        Animation fadeInSlide2 = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in_slide_up);
        Animation fadeInSlide3 = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in_slide_up);
        Animation fadeInSlide4 = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in_slide_up);
        Animation fadeInSlide5 = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in_slide_up);
        
        // 设置延迟
        fadeInSlide1.setStartOffset(300);
        fadeInSlide2.setStartOffset(400);
        fadeInSlide3.setStartOffset(500);
        fadeInSlide4.setStartOffset(600);
        fadeInSlide5.setStartOffset(700);
        
        // 应用动画
        binding.cardStoragePermission.startAnimation(fadeInSlide1);
        binding.cardLocationPermission.startAnimation(fadeInSlide2);
        binding.cardBluetoothPermission.startAnimation(fadeInSlide3);
        binding.cardPhonePermission.startAnimation(fadeInSlide4);
    }

    @Override
    protected void setupListeners() {
        if (binding == null) return;
        
        // 返回按钮点击事件
        binding.ivBack.setOnClickListener(v -> {
            MyLog.v(TAG, "用户点击返回按钮");
            navigateBack();
        });
        
        // 授权按钮点击事件
        binding.btnGrantPermissions.setOnClickListener(v -> {
            MyLog.v(TAG, "用户点击授予权限按钮");
            requestPermissions();
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        // 页面重新可见时，如果动画没有在播放，则重新播放
        if (binding != null && !binding.lottieAnimation.isAnimating()) {
            binding.lottieAnimation.playAnimation();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        // 页面不可见时，取消动画
        if (binding != null && binding.lottieAnimation.isAnimating()) {
            binding.lottieAnimation.cancelAnimation();
        }
    }
    
    /**
     * 请求必要的应用权限
     */
    private void requestPermissions() {
        if (getActivity() == null) return;
        
        List<PermissionItem> permissionItems = new ArrayList<>();
        permissionItems.add(new PermissionItem(Manifest.permission.WRITE_EXTERNAL_STORAGE, getString(R.string.writ_in), com.smart.easy.permissions.R.drawable.permission_ic_storage_write));
        permissionItems.add(new PermissionItem(Manifest.permission.READ_EXTERNAL_STORAGE, getString(R.string.read), com.smart.easy.permissions.R.drawable.permission_ic_storage_read));
        permissionItems.add(new PermissionItem(Manifest.permission.ACCESS_FINE_LOCATION, getString(R.string.position), com.smart.easy.permissions.R.drawable.permission_ic_location));
        permissionItems.add(new PermissionItem(Manifest.permission.ACCESS_COARSE_LOCATION, getString(R.string.position), com.smart.easy.permissions.R.drawable.permission_ic_location));
        
        // Android 12及以上需要蓝牙权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            permissionItems.add(new PermissionItem(Manifest.permission.BLUETOOTH_CONNECT, getString(R.string.ble), com.smart.easy.permissions.R.drawable.permission_ic_ble));
            permissionItems.add(new PermissionItem(Manifest.permission.BLUETOOTH_SCAN, getString(R.string.ble), com.smart.easy.permissions.R.drawable.permission_ic_ble));
            permissionItems.add(new PermissionItem(Manifest.permission.BLUETOOTH_ADVERTISE, getString(R.string.ble), com.smart.easy.permissions.R.drawable.permission_ic_ble));
        }
        
        permissionItems.add(new PermissionItem(Manifest.permission.READ_PHONE_STATE, getString(R.string.state), com.smart.easy.permissions.R.drawable.permission_ic_phone));
        
        EasyPermission.create(getActivity())
                .permissions(permissionItems)
                .title(getString(R.string.required_permissions))
                .msg(getString(R.string.onboarding_permission_subtitle))
                .animStyle(com.smart.easy.permissions.R.style.PermissionAnimFade)
                .filterColor(R.color.confirm_button_center_color)
                .style(com.smart.easy.permissions.R.style.PermissionSmartCarStyle)
                .checkMutiPermission(new PermissionCallback() {
                    @Override
                    public void onClose() {
                        // 用户关闭权限对话框，重新请求
                        requestPermissions();
                    }

                    @Override
                    public void onFinish() {
                        // 权限请求完成
                        MyLog.v(TAG, "权限授予完成");
                        
                        // 发送权限授予完成的通知
                        GeneralNotificationScopeBus.eventBean().post(new GeneralNoticeModel(Constants.NoticeType.PERMISSION_GRANTED, "", true));

                        // 初始化应用
                        App.initialise();
                        
                        // 延迟一下再跳转，让动画效果完成
                        new Handler(Looper.getMainLooper()).postDelayed(() -> {
                            // 导航到桌面模式选择页面
                            navigateToNext(new OnboardingDesktopModeScene());
                        }, 500);
                    }

                    @Override
                    public void onDeny(String permission, int position) {
                        MyLog.v(TAG, "权限被拒绝: " + permission);
                    }

                    @Override
                    public void onGuarantee(String permission, int position) {
                        MyLog.v(TAG, "权限被授予: " + permission);
                    }
                });
    }
} 