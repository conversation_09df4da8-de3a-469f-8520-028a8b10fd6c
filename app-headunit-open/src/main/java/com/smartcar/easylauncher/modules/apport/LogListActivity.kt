package com.smartcar.easylauncher.modules.apport

import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.smartcar.easylauncher.R
import com.smartcar.easylauncher.databinding.ActivityLogListBinding
import com.smartcar.easylauncher.shared.utils.apportutil.LogUtils
import java.io.File

/**
 * <AUTHOR>
 * date 2023/5/12
 * email <EMAIL>
 * desc
 */
class LogListActivity : BaseActivity<ActivityLogListBinding>() {

    private var list = mutableListOf<String>()

    private val adapter by lazy {
        LogAdapter()
    }


    override fun initData() {
        setToolbar("日志")
        list.addAll(LogUtils.getLogFiles(this))
        binding.run {
            rvData.adapter = adapter
            toolbar.ivSetting.visibility = View.GONE
            toolbar.ivWifi.visibility = View.GONE
        }

    }


    inner class LogAdapter : RecyclerView.Adapter<LogHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LogHolder {
            return LogHolder(LayoutInflater.from(parent.context).inflate(R.layout.item_log, parent, false))
        }

        override fun onBindViewHolder(holder: LogHolder, position: Int) {
            val file = File(list[position])
            holder.tvText.text = file.name
            holder.itemView.setOnClickListener {
                val intent = Intent(it.context, LogActivity::class.java)
                intent.putExtra("path", list[position])
                it.context.startActivity(intent)
            }
        }

        override fun getItemCount(): Int = list.size
    }


    class LogHolder(view: View) : RecyclerView.ViewHolder(view) {
        var tvText: TextView = view.findViewById(R.id.tvText)
    }
}