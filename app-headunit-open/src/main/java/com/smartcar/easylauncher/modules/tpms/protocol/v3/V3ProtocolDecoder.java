package com.smartcar.easylauncher.modules.tpms.protocol.v3;

import android.util.Log;

import com.smartcar.easylauncher.data.model.vehicle.TireCodeQueryModel;
import com.smartcar.easylauncher.data.model.vehicle.TireOperateResultModel;
import com.smartcar.easylauncher.modules.tpms.protocol.base.model.TireStatusData;
import com.smartcar.easylauncher.modules.tpms.protocol.base.model.TpmsData;
import com.smartcar.easylauncher.modules.tpms.utils.Tools;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.TpmsUtlis;

import java.text.DecimalFormat;

/**
 * V3协议解码器
 */
public class V3ProtocolDecoder {
    private static final String TAG = "V3ProtocolDecoder";

    // 协议帧头
    private static final byte FRAME_HEADER_1 = (byte) 0x55;
    private static final byte FRAME_HEADER_2 = (byte) 0xAA;

    /**
     * 轮胎一
     */
    public byte CMD_TIRE_ONE = -1;
    /**
     * 轮胎二
     */
    public byte CMD_TIRE_TWO = -1;

    // 轮胎位置
    private static final byte POS_LEFT_FRONT = 0x00;   // 左前轮
    private static final byte POS_RIGHT_FRONT = 0x01;  // 右前轮
    private static final byte POS_LEFT_REAR = 0x10;    // 左后轮
    private static final byte POS_RIGHT_REAR = 0x11;   // 右后轮
    private static final byte POS_SPARE = 0x05;        // 备胎
    static DecimalFormat df = new DecimalFormat(".##");

    /**
     * 电池电压阈值常量（单位：V）
     */
    private static final float BATTERY_VOLTAGE_MAX = 3.3f;    // 电池满电电压
    private static final float BATTERY_VOLTAGE_HIGH = 3.0f;   // 电池高电量阈值
    private static final float BATTERY_VOLTAGE_MEDIUM = 2.7f; // 电池中等电量阈值
    private static final float BATTERY_VOLTAGE_LOW = 2.5f;    // 电池低电量阈值
    private static final float BATTERY_VOLTAGE_MIN = 2.2f;    // 电池最低工作电压

    /**
     * 根据电池电压计算电量百分比
     * <p>
     * 电压与电量的对应关系：
     * - 3.3V 及以上：100%
     * - 3.0V-3.3V：71-99%
     * - 2.7V-3.0V：31-70%
     * - 2.5V-2.7V：1-30%
     * - 2.2V 以下：0%
     * </p>
     *
     * @param voltage 电池电压值
     * @return 电量百分比 (0-100)
     */
    public int calculateBatteryPercentage(float voltage) {
        if (voltage >= BATTERY_VOLTAGE_MAX) {
            return 100;
        } else if (voltage <= BATTERY_VOLTAGE_MIN) {
            return 0;
        }

        // 根据电压范围计算百分比
        if (voltage >= BATTERY_VOLTAGE_HIGH) {
            // 3.0V-3.3V 映射到 71-99%
            return (int) (71 + (voltage - BATTERY_VOLTAGE_HIGH) / (BATTERY_VOLTAGE_MAX - BATTERY_VOLTAGE_HIGH) * 28);
        } else if (voltage >= BATTERY_VOLTAGE_MEDIUM) {
            // 2.7V-3.0V 映射到 31-70%
            return (int) (31 + (voltage - BATTERY_VOLTAGE_MEDIUM) / (BATTERY_VOLTAGE_HIGH - BATTERY_VOLTAGE_MEDIUM) * 39);
        } else {
            // 2.5V-2.7V 映射到 1-30%
            return Math.max(1, (int) ((voltage - BATTERY_VOLTAGE_MIN) / (BATTERY_VOLTAGE_MEDIUM - BATTERY_VOLTAGE_MIN) * 30));
        }
    }

    /**
     * 解码TPMS数据
     */
    public TpmsData decode(byte[] data) {
        try {
            // 验证数据有效性
            if (data == null || data.length < 8) {
                return null;
            }

            // 获取轮胎位置
            byte position = data[3];
            MyLog.v("轮胎位置 ", "位置: " + position);

            // 解析压力 (kPa)
            // 确保使用float类型进行计算
            float pressure = ((float)(data[4] & 0xFF)) * 3.44f;
            MyLog.v("轮胎压力 ", "气压: " + TpmsUtlis.getInstance().convertFromKpa(pressure));

            // 解析温度 (℃)
            int temperature = (data[5] & 0xFF) - 50;
            MyLog.v("轮胎温度 ", "温度: " + TpmsUtlis.getInstance().getTempUnitString(temperature));

            // 解析告警状态
            byte warningStatus = data[6];
            if ((warningStatus & 32) != 0) {
                MyLog.v(TAG, "信号丢失了");
            }
            if ((warningStatus & 8) != 0) {
                MyLog.v(TAG, "漏气");
            }
            if ((warningStatus & 16) != 0) {
                MyLog.v(TAG, "低电");
            }

            // 解析电池状态
            byte batteryStatus = data[7];

            // 解析电池电压 - 修改电压计算方式
            float voltage = ((float)(batteryStatus & 0xFF)) * 0.1f;
            
            // 计算电池电量百分比
            int batteryPercentage = calculateBatteryPercentage(voltage);

            // 创建数据对象
            return new TireStatusData.Builder()
                    .position(TireStatusData.Position.fromValue(position))
                    .pressure(pressure)
                    .temperature(temperature)
                    .voltage(String.format("%.1f", voltage))
                    .battery(batteryPercentage)
                    .sensorId("A1B2C3D4")
                    .warningStatus(warningStatus)
                    .protocolVersion(3)
                    .build();

        } catch (Exception e) {
            MyLog.e(TAG, "解码数据异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 解码轮胎ID数据
     */
    public TireCodeQueryModel decodeTireId(byte[] data) {
        // 获取轮胎位置（第4个字节）
        int position = data[3] & 0xFF;
        String tireId = "";

        // 将4字节ID数据转换为16进制字符串
        tireId = Tools.byteToHexString(data[4]) +
                Tools.byteToHexString(data[5]) +
                Tools.byteToHexString(data[6]) +
                Tools.byteToHexString(data[7]);
        String positionName = getTirePositionName(position);

        Log.v("轮胎 ID 读取", getTirePositionName(position) + "  " + tireId);

        // 根据轮胎位置更新UI和TpmsServer（这里假设TpmsServer是一个单例或全局对象）
        switch (position) {
            // 左前轮
            case 1:
                return new TireCodeQueryModel(1, 1, positionName, tireId);
            // 右前轮
            case 2:
                return new TireCodeQueryModel(2, 2, positionName, tireId);
            // 左后轮
            case 3:
                return new TireCodeQueryModel(3, 3, positionName, tireId);
            // 右后轮
            case 4:
                return new TireCodeQueryModel(4, 4, positionName, tireId);
            // 备胎
            case 5:
                return new TireCodeQueryModel(5, 5, positionName, tireId);
            default:
                Log.e("轮胎 ID 读取", "未知的轮胎位置");
                break;
        }
        // 如果没有匹配到任何轮胎位置，则返回null
        return null;
    }


    /**
     * 验证握手响应
     * 格式: 55 AA 06 A5 CS
     */
    public boolean validateHandshakeResponse(byte[] data) {
        try {
            // 验证数据有效性
            if (data == null || data.length < 5) {
                return false;
            }

            // 验证帧头
            if (data[0] != FRAME_HEADER_1 || data[1] != FRAME_HEADER_2) {
                return false;
            }

            // 验证握手响应
            if (data[3] != (byte) 0xA5) {
                return false;
            }

            // 验证校验和
            byte checksum = calculateChecksum(data);

            if (checksum != data[data.length - 1]) {
                MyLog.w(TAG, "握手响应校验和错误");
                return false;
            }

            return true;

        } catch (Exception e) {
            MyLog.e(TAG, "验证握手响应异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 计算校验和
     */
    private byte calculateChecksum(byte[] data) {
        byte checksum = data[0];
        for (int i = 1; i < data.length - 1; i++) {
            checksum ^= data[i];
        }
        return checksum;
    }

    /**
     * 解码轮胎交换结果
     *
     * @param packet
     */
    public int decodeTireExchangeResult(byte[] packet) {
        //验证响应数据的正确性：buff[3]=48表示成功，buff[4]和buff[5]需要与发送的轮胎位置参数匹配
        if (packet[3] == 48 && packet[4] == CMD_TIRE_ONE && packet[5] == CMD_TIRE_TWO) {
            // 轮胎交换成功，更新UI和TpmsServer（这里假设TpmsServer是一个单例或全局对象）
            Log.v("轮胎 ID 读取", "轮胎ID交换成功");
            return 0;
        } else {
            // 轮胎交换失败，处理错误情况
            Log.e("轮胎 ID 读取", "轮胎ID交换失败");
            return 1;
        }
    }

    public void setTireExchangeParams(byte pos1, byte pos2) {
        // 缓存轮胎参数
        CMD_TIRE_ONE = pos1;
        CMD_TIRE_TWO = pos2;
    }

    /**
     * 解码胎压学习结果
     *
     * @param packet
     */
    public TireOperateResultModel tireLearn(byte[] packet) {
        //学习状态
        byte Identifier = packet[3];
        //学习轮胎位置
        byte position = packet[4];
        if (Identifier == 16) {
            Log.v("胎压学习", "开始学习");
            return new TireOperateResultModel(1, 3, "开始学习", getTirePosition(position));
        } else if (Identifier == 24) {
            Log.v("胎压学习", "学习成功");
            return new TireOperateResultModel(1, 0, "学习成功", getTirePosition(position));
        } else if (Identifier == 22) {
            Log.v("胎压学习", "学习成功");
            return new TireOperateResultModel(1, 3, "未知状态", getTirePosition(position));
        } else {
            Log.v("胎压学习", "学习失败");
            return new TireOperateResultModel(1, 2, "学习失败", getTirePosition(position));
        }
    }

    /**
     * 辅助方法，用于获取轮胎位置的名称
     */
    private String getTirePositionName(int position) {
        return switch (position) {
            case 1 -> "左前轮";
            case 2 -> "右前轮";
            case 3 -> "左后轮";
            case 4 -> "右后轮";
            case 5 -> "备胎";
            default -> "未知位置";
        };
    }

    /**
     * 辅助方法，用于byte 数据获取轮胎位置
     */
    private int getTirePosition(byte position) {
        return switch (position) {
            case 0 -> 1;
            case 1 -> 2;
            case 16 -> 3;
            case 17 -> 4;
            case 5 -> 5;
            default -> -1;
        };
    }
}