package com.smartcar.easylauncher.modules.appstore.setting;

import android.os.Bundle;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.viewbinding.ViewBinding;
import com.smartcar.easylauncher.core.base.BaseDialogFragment;
import com.smartcar.easylauncher.databinding.FragmentSettingMoreBinding;
import com.smartcar.easylauncher.infrastructure.event.cody.MoreSettingScopeBus;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.data.model.system.MoreSettingModel;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.view.VerticalSeekBar;
import com.smartcar.easylauncher.shared.view.segmentcontrlo.SegmentedControlItem;
import java.util.ArrayList;
import java.util.List;

/**
 * 更多设置对话框Fragment
 * 功能：
 * 1. 设置应用滑动方向（左右/上下）
 * 2. 设置应用图标行数（3-6行）
 * 3. 设置应用图标列数（3-7列）
 * 4. 设置应用图标大小
 *
 * <AUTHOR>
 */
public class MoreSettingDialogFragment extends BaseDialogFragment {
    private static final String TAG = MoreSettingDialogFragment.class.getSimpleName();

    /** 行数最小值 */
    private static final int MIN_ROWS = 3;
    /** 行数最大值 */
    private static final int MAX_ROWS = 6;
    /** 列数最小值 */
    private static final int MIN_COLUMNS = 3;
    /** 列数最大值 */
    private static final int MAX_COLUMNS = 7;

    /** ViewBinding对象 */
    private FragmentSettingMoreBinding binding;

    @Override
    protected ViewBinding getLayoutBinding() {
        return FragmentSettingMoreBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView(View rootView) {
        binding = FragmentSettingMoreBinding.bind(rootView);
    }

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        // 设置返回按钮
        initBackButton();
        // 初始化滑动方向设置
        initSlidingModeControl();
        // 初始化行数设置
        initRowsControl();
        // 初始化列数设置
        initColumnsControl();
        // 初始化图标大小设置
        initIconSizeControl();
    }

    /**
     * 初始化返回按钮
     */
    private void initBackButton() {
        binding.btBack.setOnClickListener(v -> dismiss());
    }

    /**
     * 初始化滑动方向控制
     */
    private void initSlidingModeControl() {
        List<SegmentedControlItem> directions = createDirectionItems();
        binding.scTime.addItems(directions);
        // 设置当前选中项
        binding.scTime.setSelectedItem(SettingsManager.getSlidingMode());

        binding.scTime.setOnSegItemClickListener((item, position) -> {
            SettingsManager.setSlidingMode(position);
            notifySettingsChanged(0);
        });
    }

    /**
     * 初始化行数控制
     */
    private void initRowsControl() {
        List<SegmentedControlItem> rows = createRows();
        binding.scNumberRows.addItems(rows);
        setRows();
        binding.scNumberRows.setOnSegItemClickListener((item, position) -> {
            int rowNumber = Integer.parseInt(item.getName());
            if (isValidRowNumber(rowNumber)) {
                SettingsManager.setRowNumber(rowNumber);
                notifySettingsChanged(1);
            }
        });
    }

    /**
     * 初始化列数控制
     */
    private void initColumnsControl() {
        List<SegmentedControlItem> columns = createColumns();
        binding.scNumberArrange.addItems(columns);
        setColumns();
        binding.scNumberArrange.setOnSegItemClickListener((item, position) -> {
            int columnNumber = Integer.parseInt(item.getName());
            if (isValidColumnNumber(columnNumber)) {
                SettingsManager.setColumnNumber(columnNumber);
                notifySettingsChanged(2);
            }
        });
    }

    /**
     * 初始化图标大小控制
     */
    private void initIconSizeControl() {
        binding.seekbar.setProgress(SettingsManager.getIconSize());
        binding.seekbar.setOnStateChangeListener(new VerticalSeekBar.OnStateChangeListener() {
            @Override
            public void onStartTouch(View view) {
                MyLog.v(TAG, "开始触摸图标大小调节器");
            }

            @Override
            public void onStateChangeListener(View view, float progress, float indicatorOffset) {
                MyLog.v(TAG, "图标大小调节中: " + progress);
            }

            @Override
            public void onStopTrackingTouch(View view, float progress) {
                MyLog.v(TAG, "结束图标大小调节: " + progress);
                SettingsManager.setIconSize((int) progress);
                notifySettingsChanged(3);
            }
        });
    }

    /**
     * 创建方向选项列表
     */
    private List<SegmentedControlItem> createDirectionItems() {
        List<SegmentedControlItem> items = new ArrayList<>(2);
        items.add(new SegmentedControlItem("左右"));
        items.add(new SegmentedControlItem("上下"));
        return items;
    }

    /**
     * 创建行数选项列表
     */
    private List<SegmentedControlItem> createRows() {
        List<SegmentedControlItem> items = new ArrayList<>(MAX_ROWS - MIN_ROWS + 1);
        for (int i = MIN_ROWS; i <= MAX_ROWS; i++) {
            items.add(new SegmentedControlItem(String.valueOf(i)));
        }
        return items;
    }

    /**
     * 创建列数选项列表
     */
    private List<SegmentedControlItem> createColumns() {
        List<SegmentedControlItem> items = new ArrayList<>(MAX_COLUMNS - MIN_COLUMNS + 1);
        for (int i = MIN_COLUMNS; i <= MAX_COLUMNS; i++) {
            items.add(new SegmentedControlItem(String.valueOf(i)));
        }
        return items;
    }

    /**
     * 设置行数选中状态
     */
    private void setRows() {
        int rowNumber = SettingsManager.getRowNumber();
        if (isValidRowNumber(rowNumber)) {
            binding.scNumberRows.setSelectedItem(rowNumber - MIN_ROWS);
        }
    }

    /**
     * 设置列数选中状态
     */
    private void setColumns() {
        int columnNumber = SettingsManager.getColumnNumber();
        if (isValidColumnNumber(columnNumber)) {
            binding.scNumberArrange.setSelectedItem(columnNumber - MIN_COLUMNS);
        }
    }

    /**
     * 验证行数是否有效
     * @param rowNumber 行数
     * @return 是否有效
     */
    private boolean isValidRowNumber(int rowNumber) {
        return rowNumber >= MIN_ROWS && rowNumber <= MAX_ROWS;
    }

    /**
     * 验证列数是否有效
     * @param columnNumber 列数
     * @return 是否有效
     */
    private boolean isValidColumnNumber(int columnNumber) {
        return columnNumber >= MIN_COLUMNS && columnNumber <= MAX_COLUMNS;
    }

    /**
     * 通知设置变更
     * @param changeType 变更类型：0-滑动方向，1-行数，2-列数，3-图标大小
     */
    private void notifySettingsChanged(int changeType) {
        MoreSettingScopeBus.eventBean().post(new MoreSettingModel(
                SettingsManager.getSlidingMode(),
                SettingsManager.getRowNumber(),
                SettingsManager.getColumnNumber(),
                changeType
        ));
    }
}