package com.smartcar.easylauncher.modules.tpms.protocol.base;

import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * 数据包缓冲区基类
 * <p>
 * 该类负责处理网络数据包的缓存、校验和过滤等基础功能。
 * 继承该类可以实现特定协议的数据包处理逻辑。
 * </p>
 */
public abstract class BasePacketBuffer {
    private static final String TAG = "BasePacketBuffer";
    
    /**
     * 最大网络包大小（64KB）
     */
    protected static final int MAX_PACKET_BUFFER_SIZE = 65536;
    
    /**
     * 是否开启调试模式
     */
    protected static final boolean DEBUG = false;
    
    /**
     * 网络包缓存
     * 用于存储接收到的网络数据包
     */
    protected final byte[] netPacketBuffer = new byte[MAX_PACKET_BUFFER_SIZE];
    
    /**
     * 当前网络包缓存位置
     * 指示当前缓冲区写入位置
     */
    protected int currentPosition = 0;
    
    /**
     * 互斥锁
     * 用于保护共享资源的并发访问
     */
    protected final Object mutex = new Object();
    
    /**
     * 重置缓冲区位置
     * 将当前位置重置为0，清空缓冲区
     */
    public void resetBufferPosition() {
        synchronized (mutex) {
            currentPosition = 0;
        }
    }
    
    /**
     * 从字节数组中删除指定长度的数据
     *
     * @param data 源数据数组
     * @param totalLength 数组总长度
     * @param deleteLength 要删除的长度
     * @return 删除指定数据后的新数组，如果参数无效则返回null
     */
    protected byte[] eraseData(byte[] data, int totalLength, int deleteLength) {
        if (data == null || totalLength <= deleteLength) {
            return null;
        }
        
        if (DEBUG) {
            MyLog.v(TAG, "删除长度: " + deleteLength);
            MyLog.d(TAG, "原始数据: " + bytesToHex(data, totalLength));
        }
        
        int newLength = totalLength - deleteLength;
        byte[] result = new byte[newLength];
        System.arraycopy(data, deleteLength, result, 0, newLength);
        
        if (DEBUG) {
            MyLog.d(TAG, "处理后数据: " + bytesToHex(result, newLength));
        }
        
        return result;
    }
    
    /**
     * 创建新的字节数组副本
     *
     * @param data 源数据
     * @param length 要复制的长度
     * @return 新的字节数组
     * @throws IllegalArgumentException 如果参数无效
     */
    protected byte[] createBufferCopy(byte[] data, int length) {
        if (data == null || length < 0 || length > data.length) {
            throw new IllegalArgumentException("无效的参数");
        }
        
        byte[] copy = new byte[length];
        System.arraycopy(data, 0, copy, 0, length);
        return copy;
    }
    
    /**
     * 校验数据包的校验和
     * 由子类实现具体的校验逻辑
     *
     * @param data 待校验的数据包
     * @return 校验结果，true表示校验通过，false表示校验失败
     */
    protected abstract boolean checkChecksum(byte[] data);
    
    /**
     * 清空缓冲区
     */
    public void clear() {
        synchronized (mutex) {
            currentPosition = 0;
        }
    }
    
    /**
     * 将字节数组转换为十六进制字符串
     * 用于调试输出
     *
     * @param data 字节数组
     * @param length 要转换的长度
     * @return 十六进制字符串
     */
    protected String bytesToHex(byte[] data, int length) {
        if (data == null || length <= 0) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(String.format("%02X ", data[i]));
        }
        return sb.toString().trim();
    }
} 