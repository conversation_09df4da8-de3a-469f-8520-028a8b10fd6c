package com.smartcar.easylauncher.modules.onboarding;

import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.lifecycle.LifecycleObserver;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.airbnb.lottie.LottieCompositionFactory;
import com.bytedance.scene.Scene;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.databinding.SceneOnboardingLanguageBinding;
import com.smartcar.easylauncher.modules.onboarding.adapter.LanguageAdapter;
import com.smartcar.easylauncher.modules.onboarding.model.LanguageItem;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.util.ArrayList;
import java.util.List;

/**
 * 引导流程 - 语言选择场景
 * 允许用户选择应用程序的语言
 *
 * <AUTHOR>
 */
public class OnboardingLanguageScene extends BaseOnboardingScene<SceneOnboardingLanguageBinding> implements LifecycleObserver {

    private LanguageAdapter languageAdapter;
    private List<LanguageItem> languageItems;
    private String selectedLanguageCode;

    public OnboardingLanguageScene() {
        // 第一个页面不允许返回
        allowBack = false;
    }

    @Override
    protected SceneOnboardingLanguageBinding getViewBinding(LayoutInflater inflater, ViewGroup container) {
        return SceneOnboardingLanguageBinding.inflate(inflater, container, false);
    }

    @Override
    protected void initView() {
        MyLog.v(TAG, "语言选择场景初始化");

        // 设置进度指示器状态 - 新方式
        binding.progressIndicator.setCurrentStep(1);
        
        // 设置标题和副标题
        binding.tvTitle.setText(getString(R.string.select_language));
        binding.tvSubtitle.setText(getString(R.string.choose_your_language));

        // 准备语言数据
        prepareLanguageData();

        // 设置RecyclerView
        binding.rvLanguages.setLayoutManager(new LinearLayoutManager(getActivity()));
        languageAdapter = new LanguageAdapter();
        languageAdapter.submitList(languageItems);
        binding.rvLanguages.setAdapter(languageAdapter);

        // 设置Lottie动画
        setupLottieAnimation();

        // 添加淡入动画
        Animation fadeIn = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in);
        binding.tvTitle.startAnimation(fadeIn);
        binding.tvSubtitle.startAnimation(fadeIn);

        // 添加列表动画，延迟一点开始
        Animation slideIn = AnimationUtils.loadAnimation(getActivity(), R.anim.slide_in_right);
        slideIn.setStartOffset(300);
        binding.rvLanguages.startAnimation(slideIn);
        updateProgressIndicator();
    }

    /**
     * 设置Lottie动画
     */
    private void setupLottieAnimation() {
        try {
            // 使用LottieCompositionFactory加载动画
            LottieCompositionFactory.fromAsset(requireActivity(), "language_animation.json")
                    .addListener(composition -> {
                        if (composition != null && binding != null) {
                            // 设置动画
                            binding.lottieAnimation.setComposition(composition);
                            // 设置重复次数
                            binding.lottieAnimation.setRepeatCount(0);
                            // 设置动画速度
                            binding.lottieAnimation.setSpeed(0.8f);
                            // 播放动画
                            binding.lottieAnimation.playAnimation();
                        }
                    })
                    .addFailureListener(exception -> {
                        MyLog.e(TAG, "Lottie动画加载失败: " + exception.getMessage());
                        // 加载失败时显示静态图片
                        if (binding != null) {
                            binding.lottieAnimation.setImageResource(R.drawable.ic_globe);
                        }
                    });
        } catch (Exception e) {
            MyLog.e(TAG, "Lottie动画设置异常: " + e.getMessage());
            // 发生异常时显示静态图片
            if (binding != null) {
                binding.lottieAnimation.setImageResource(R.drawable.ic_globe);
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // 页面重新可见时，如果动画没有在播放，则重新播放
        if (binding != null && !binding.lottieAnimation.isAnimating()) {
            binding.lottieAnimation.playAnimation();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        // 页面不可见时，取消动画
        if (binding != null && binding.lottieAnimation.isAnimating()) {
            binding.lottieAnimation.cancelAnimation();
        }
    }


    /**
     * 更新语言资源
     * 当语言改变时，此方法会被调用以更新UI元素
     * @param language 当前选择的语言代码
     */
    public void updateLanguage(String language) {
        MyLog.v(TAG, "语言选择场景更新语言: " + language);
        
        if (binding == null) return;
        
        // 更新标题和副标题
        binding.tvTitle.setText(getString(R.string.select_language));
        binding.tvSubtitle.setText(getString(R.string.choose_your_language));

    }

    @Override
    protected void setupListeners() {
        // 设置语言选择监听器
        languageAdapter.setOnLanguageSelectedListener(languageItem -> {
            MyLog.v(TAG, "用户选择语言: " + languageItem.getCode());
            selectedLanguageCode = languageItem.getCode();

            // 保存并应用语言设置，不会重启Activity
            onboardingManager.saveSelectedLanguage(selectedLanguageCode);
            onboardingManager.applyLanguage(selectedLanguageCode);
            
            // 在应用语言后立即更新当前页面UI
            updateLanguage(selectedLanguageCode);
            
            // 延迟一下再跳转，让动画效果完成
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                // 导航到下一个场景
                Scene nextScene = new OnboardingPrivacyScene();
                navigateToNext(nextScene);
            }, 500);
        });
    }

    /**
     * 准备语言数据
     */
    private void prepareLanguageData() {
        languageItems = new ArrayList<>();

        // 添加中文
        languageItems.add(new LanguageItem(
                "zh",
                getString(R.string.language_zh),
                getString(R.string.language_sample_zh)
        ));

        languageItems.add(new LanguageItem(
                "zh_tw",
                getString(R.string.language_zh_tw),
                getString(R.string.language_sample_zh_tw)
        ));

        // 添加英文
        languageItems.add(new LanguageItem(
                "en",
                getString(R.string.language_en),
                getString(R.string.language_sample_en)
        ));

        // 可以根据需要添加更多语言
        languageItems.add(new LanguageItem(
                "ja",
                getString(R.string.language_ja),
                getString(R.string.language_sample_ja)
        ));
        // 添加韩语
        languageItems.add(new LanguageItem(
                "ko",
                getString(R.string.language_ko),
                getString(R.string.language_sample_ko)
        ));
        // 添加俄语
        languageItems.add(new LanguageItem(
                "ru",
                getString(R.string.language_ru),
                getString(R.string.language_sample_ru)
        ));
        // 添加法语
        languageItems.add(new LanguageItem(
                "fr",
                getString(R.string.language_fr),
                getString(R.string.language_sample_fr)
        ));
        // 添加德语
        languageItems.add(new LanguageItem(
                "de",
                getString(R.string.language_de),
                getString(R.string.language_sample_de)
        ));
        // 添加西班牙语
        languageItems.add(new LanguageItem(
                "es",
                getString(R.string.language_es),
                getString(R.string.language_sample_es)
        ));
        // 添加意大利语
        languageItems.add(new LanguageItem(
                "it",
                getString(R.string.language_it),
                getString(R.string.language_sample_it)
        ));
        // 添加葡萄牙语
        languageItems.add(new LanguageItem(
                "pt",
                getString(R.string.language_pt),
                getString(R.string.language_sample_pt)
        ));
        // 添加阿拉伯语
        languageItems.add(new LanguageItem(
                "ar",
                getString(R.string.language_ar),
                getString(R.string.language_sample_ar)
        ));
    }

    /**
     * 更新进度指示器状态
     */
    private void updateProgressIndicator() {
        // 使用新的进度指示器直接设置当前步骤
        if (binding != null) {
            binding.progressIndicator.setCurrentStep(1);
        }
    }

} 