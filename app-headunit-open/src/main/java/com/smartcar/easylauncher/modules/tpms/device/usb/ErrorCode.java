package com.smartcar.easylauncher.modules.tpms.device.usb;

/**
 * USB设备错误码定义
 */
public class ErrorCode {
    /**
     * 设备未找到
     */
    public static final int DEVICE_NOT_FOUND = 100;
    
    /**
     * USB权限被拒绝
     */
    public static final int PERMISSION_DENIED = 1002;
    
    /**
     * 设备连接失败
     */
    public static final int CONNECTION_FAILED = 1003;
    
    /**
     * 数据发送错误
     */
    public static final int SEND_ERROR = 1004;
    
    /**
     * 数据接收错误
     */
    public static final int RECEIVE_ERROR = 1005;
    
    /**
     * 端点未找到
     */
    public static final int ENDPOINT_NOT_FOUND = 1006;
    
    /**
     * 传感器错误
     */
    public static final int TIRE_SENSOR_ERROR = 400;
    
    /**
     * 配对失败
     */
    public static final int PAIRING_FAILED = 401;
    
    /**
     * 交换失败
     */
    public static final int EXCHANGE_FAILED = 402;
    
    /**
     * 协议错误 (300-399)
     */
    public static final int PROTOCOL_ERROR = 300;
    public static final int COMMAND_TIMEOUT = 301;
    public static final int HEARTBEAT_TIMEOUT = 302;


} 