package com.smartcar.easylauncher.modules.gesture.fragments;

import android.accessibilityservice.AccessibilityServiceInfo;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityManager;
import android.widget.Toast;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.databinding.GestureSettingsBasicBinding;
import com.smartcar.easylauncher.modules.gesture.other.AdbProcessExtractor;
import com.smartcar.easylauncher.modules.gesture.other.EnhancedModeGuide;
import com.smartcar.easylauncher.modules.gesture.other.Gesture;
import com.smartcar.easylauncher.modules.gesture.other.SpfConfig;
import com.smartcar.easylauncher.modules.gesture.remote.RemoteAPI;
import com.smartcar.easylauncher.modules.gesture.util.GlobalState;
import com.smartcar.easylauncher.modules.other.WebActivity;

import java.util.List;

public class FragmentBasic extends FragmentSettingsBase {
    private GestureSettingsBasicBinding binding;

    private BroadcastReceiver broadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            updateView();
            if (GlobalState.enhancedMode) {
                setResultCode(0);
                setResultData("EnhancedMode √");
            } else {
                setResultCode(5);
                setResultData("EnhancedMode ×");
            }
        }
    };

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = GestureSettingsBasicBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onResume() {
        super.onResume();
        final Activity activity = getActivity();
        if (activity == null) return;

        binding.switchOptions.enableService.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (serviceRunning()) {
                    try {
                        Intent intent = new Intent(getString(R.string.action_service_disable));
                        getActivity().sendBroadcast(intent);
                        v.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                restartService();
                            }
                        }, 1000);
                    } catch (Exception ignored) {
                    }
                } else {
                    try {
                        Intent intent = new Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS);
                        startActivity(intent);
                    } catch (Exception ignored) {
                    }
                    String msg = getString(R.string.service_active_desc) + getString(R.string.app_name_long);
                    Gesture.toast(msg, Toast.LENGTH_LONG);
                }
            }
        });

        bindSeekBar(binding.barHoverTime, SpfConfig.CONFIG_HOVER_TIME, SpfConfig.CONFIG_HOVER_TIME_DEFAULT, true);
        bindSeekBar(binding.vibratorTime, SpfConfig.VIBRATOR_TIME, SpfConfig.VIBRATOR_TIME_DEFAULT, true);
        bindSeekBar(binding.vibratorAmplitude, SpfConfig.VIBRATOR_AMPLITUDE, SpfConfig.VIBRATOR_AMPLITUDE_DEFAULT, true);
        bindSeekBar(binding.vibratorTimeLong, SpfConfig.VIBRATOR_TIME_LONG, SpfConfig.VIBRATOR_TIME_LONG_DEFAULT, true);
        bindSeekBar(binding.vibratorAmplitudeLong, SpfConfig.VIBRATOR_AMPLITUDE_LONG, SpfConfig.VIBRATOR_AMPLITUDE_LONG_DEFAULT, true);
        bindCheckable(binding.vibratorQuickSlide, SpfConfig.VIBRATOR_QUICK_SLIDE, SpfConfig.VIBRATOR_QUICK_SLIDE_DEFAULT);

        binding.vibratorUseSystem.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                config.edit().putBoolean(SpfConfig.VIBRATOR_USE_SYSTEM, binding.vibratorUseSystem.isChecked()).apply();
                binding.vibratorCustom.setVisibility(binding.vibratorUseSystem.isChecked() ? View.GONE : View.VISIBLE);
            }
        });
        binding.vibratorUseSystem.setChecked(config.getBoolean(SpfConfig.VIBRATOR_USE_SYSTEM, SpfConfig.VIBRATOR_USE_SYSTEM_DEFAULT));
        binding.vibratorCustom.setVisibility(binding.vibratorUseSystem.isChecked() ? View.GONE : View.VISIBLE);

        binding.faqClickMe.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), WebActivity.class);
                Bundle bundle = new Bundle();
                bundle.putString("ProductUrl", Const.QFA);
                intent.putExtras(bundle);
                startActivity(intent);
            }
        });

        binding.enhancedMode.stepsClickMe.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(getActivity(), WebActivity.class);
                Bundle bundle = new Bundle();
                bundle.putString("ProductUrl", Const.ENHANCEDMODE);
                intent.putExtras(bundle);
                startActivity(intent);
            }
        });

        binding.enhancedMode.enhancedMode.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                new AdbProcessExtractor().updateAdbProcessState(getActivity(), true);

                GlobalState.enhancedMode = RemoteAPI.isOnline();
                if (GlobalState.enhancedMode) {
                    Gesture.toast("别点啦！增强模式已经好了", Toast.LENGTH_SHORT);
                    updateView();
                } else {
                    String shell = new AdbProcessExtractor().extract(activity);
                    if (shell != null) {
                        new EnhancedModeGuide().show(activity, shell);
                    } else {
                        Gesture.toast("无法提取外接程序文件", Toast.LENGTH_SHORT);
                    }
                }
            }
        });

        updateView();
        activity.registerReceiver(broadcastReceiver, new IntentFilter(getString(R.string.action_adb_process)));
    }

    private void updateView() {
        Activity activity = getActivity();
        if (activity == null) return;

        binding.switchOptions.enableService.setChecked(serviceRunning());

        GlobalState.enhancedMode = RemoteAPI.isOnline();
        if (GlobalState.enhancedMode) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                binding.enhancedMode.enhancedMode.setImageDrawable(activity.getDrawable(R.drawable.adb_on));
            }
        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                binding.enhancedMode.enhancedMode.setImageDrawable(activity.getDrawable(R.drawable.adb_off));
            }
        }
    }

    @Override
    protected void restartService() {
        updateView();
        super.restartService();
    }

    private boolean serviceRunning(Context context, String serviceName) {
        AccessibilityManager m = (AccessibilityManager) context.getSystemService(Context.ACCESSIBILITY_SERVICE);
        List<AccessibilityServiceInfo> serviceInfos = m.getEnabledAccessibilityServiceList(AccessibilityServiceInfo.FEEDBACK_ALL_MASK);
        for (AccessibilityServiceInfo serviceInfo : serviceInfos) {
            if (serviceInfo.getId().endsWith(serviceName)) {
                return true;
            }
        }
        return false;
    }

    private boolean serviceRunning() {
        return serviceRunning(getActivity(), "AccessibilityServiceGesture");
    }

    @Override
    public void onPause() {
        if (getActivity() != null) {
            getActivity().unregisterReceiver(broadcastReceiver);
        }
        super.onPause();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
