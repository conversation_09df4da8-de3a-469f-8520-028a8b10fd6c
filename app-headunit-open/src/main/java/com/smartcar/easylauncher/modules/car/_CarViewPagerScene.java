package com.smartcar.easylauncher.modules.car;

import static com.bytedance.scene.navigation.NavigationSceneGetter.requireNavigationScene;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.angcyo.tablayout.delegate.ViewPager1Delegate;
import com.bytedance.scene.group.GroupScene;
import com.bytedance.scene.group.UserVisibleHintGroupScene;
import com.bytedance.scene.ui.GroupSceneUIUtility;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.databinding.SceneCarViewPagerBinding;
import com.smartcar.easylauncher.infrastructure.skin.SkinManager;
import com.smartcar.easylauncher.modules.expense.ExpenseListScene;

import java.util.LinkedHashMap;

public class _CarViewPagerScene extends GroupScene {
    private SceneCarViewPagerBinding binding;

    @NonNull
    @Override
    public ViewGroup onCreateView(@NonNull LayoutInflater layoutInflater, @NonNull ViewGroup viewGroup, @Nullable Bundle bundle) {
        binding = SceneCarViewPagerBinding.inflate(getLayoutInflater());
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initTabLayout();
        ViewPager1Delegate.Companion.install(binding.myViewPager, binding.tabLayout, true);
        initTab(savedInstanceState == null);
    }

    private void initTabLayout() {
        binding.tabLayout.getTabIndicator().setIndicatorDrawable(SkinManager.getInstance().getDrawable(R.drawable.indicator_bottom_line));
        binding.tabLayout.invalidate();
        assert binding.tabLayout.getTabLayoutConfig() != null;
        binding.tabLayout.getTabLayoutConfig().setTabSelectColor(SkinManager.getInstance().getColor(R.color.tab_select_color));
        binding.tabLayout.getTabLayoutConfig().setTabDeselectColor(SkinManager.getInstance().getColor(R.color.tab_deselect_color));
        binding.tabLayout.getDslSelector().updateStyle();
        binding.btBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                requireNavigationScene(_CarViewPagerScene.this).pop();
            }
        });
    }

    private void initTab(boolean state) {
        binding.tabLayout.configTabLayoutConfig(dslTabLayoutConfig -> null);
        LinkedHashMap<String, UserVisibleHintGroupScene> list = new LinkedHashMap<>();

        list.put("MyCarScene", new AddCarScene(null));
        list.put("CostListScene", new ExpenseListScene());

        GroupSceneUIUtility.setupWithViewPager(binding.myViewPager, this, list);
        binding.myViewPager.setCurrentItem(0);
        binding.myViewPager.setOffscreenPageLimit(1);
    }
}
