package com.smartcar.easylauncher.modules.onboarding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.SettingsConstants;
import com.smartcar.easylauncher.databinding.SceneOnboardingDesktopModeBinding;
import com.smartcar.easylauncher.infrastructure.event.scope.layout.cody.LyoutScopeBus;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.data.model.common.LayoutUPModel;
import com.smartcar.easylauncher.modules.onboarding.adapter.DesktopModeAdapter;
import com.smartcar.easylauncher.modules.onboarding.model.DesktopModeItem;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.util.ArrayList;
import java.util.List;

/**
 * 引导流程 - 桌面模式选择场景
 * 允许用户选择桌面显示模式（卡片模式、沉浸模式、混合模式）
 *
 * <AUTHOR>
 */
public class OnboardingDesktopModeScene extends BaseOnboardingScene<SceneOnboardingDesktopModeBinding> {
    // 当前选择的模式
    private int selectedMode = SettingsConstants.HOME_MAP_LAYOUT_TYPE; // 默认选择卡片模式


    // 模式数据列表
    private List<DesktopModeItem> modeList;

    @Override
    protected SceneOnboardingDesktopModeBinding getViewBinding(LayoutInflater inflater, ViewGroup container) {
        return SceneOnboardingDesktopModeBinding.inflate(inflater, container, false);
    }

    @Override
    protected void initView() {
        MyLog.v(TAG, "桌面模式选择场景初始化");

        // 设置进度指示器状态
        updateProgressIndicator();

        // 设置返回按钮可见性
        binding.ivBack.setVisibility(allowBack ? View.VISIBLE : View.INVISIBLE);


        // 设置Lottie动画
        binding.lottieAnimation.setAnimation("desktop_mode_animation.json");
        binding.lottieAnimation.playAnimation();

        // 初始化模式数据
        initModeData();

        // 初始化RecyclerView
        initRecyclerView();

        // 添加动画
        Animation fadeIn = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in);
        binding.tvTitle.startAnimation(fadeIn);
        binding.tvSubtitle.startAnimation(fadeIn);
        binding.ivBack.startAnimation(fadeIn);

        Animation slideIn = AnimationUtils.loadAnimation(getActivity(), R.anim.slide_in_right);
        slideIn.setStartOffset(300);
        binding.cvModesContainer.startAnimation(slideIn);

    }

    @Override
    public void onResume() {
        super.onResume();
        // 页面重新可见时，如果动画没有在播放，则重新播放
        if (binding != null && !binding.lottieAnimation.isAnimating()) {
            binding.lottieAnimation.playAnimation();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        // 页面不可见时，取消动画
        if (binding != null && binding.lottieAnimation.isAnimating()) {
            binding.lottieAnimation.cancelAnimation();
        }
    }

    /**
     * 初始化模式数据
     */
    private void initModeData() {
        modeList = new ArrayList<>();

        // 卡片模式
        DesktopModeItem cardMode = new DesktopModeItem(
                SettingsConstants.HOME_CARD_LAYOUT_TYPE,
                R.drawable.card_layou_one,
                R.drawable.ic_mode_card,
                getString(R.string.desktop_mode_card_title),
                getString(R.string.desktop_mode_card_description)
        );
        cardMode.addFeature(getString(R.string.desktop_mode_card_feature1));
        cardMode.addFeature(getString(R.string.desktop_mode_card_feature2));
        modeList.add(cardMode);

        // 沉浸模式
        DesktopModeItem immersiveMode = new DesktopModeItem(
                SettingsConstants.HOME_MAP_LAYOUT_TYPE,
                R.drawable.map_layou_one,
                R.drawable.ic_mode_immersive,
                getString(R.string.desktop_mode_immersive_title),
                getString(R.string.desktop_mode_immersive_description)
        );
        immersiveMode.addFeature(getString(R.string.desktop_mode_immersive_feature1));
        immersiveMode.addFeature(getString(R.string.desktop_mode_immersive_feature2));
        modeList.add(immersiveMode);

        // 混合模式
        DesktopModeItem hybridMode = new DesktopModeItem(
                SettingsConstants.HOME_HYBRID_LAYOUT_TYPE,
                R.drawable.map_layou_one,
                R.drawable.ic_mode_hybrid,
                getString(R.string.desktop_mode_hybrid_title),
                getString(R.string.desktop_mode_hybrid_description)
        );
        hybridMode.addFeature(getString(R.string.desktop_mode_hybrid_feature1));
        hybridMode.addFeature(getString(R.string.desktop_mode_hybrid_feature2));
        modeList.add(hybridMode);
    }

    /**
     * 初始化RecyclerView
     */
    private void initRecyclerView() {
        // 创建适配器
        // 模式适配器
        DesktopModeAdapter modeAdapter = new DesktopModeAdapter();
        modeAdapter.submitList(modeList);

        // 设置布局管理器
        binding.rvModes.setLayoutManager(new LinearLayoutManager(getActivity()));

        // 设置适配器
        binding.rvModes.setAdapter(modeAdapter);

        // 设置选中监听器
        modeAdapter.setOnModeSelectedListener((item, position) -> selectedMode = item.getMode());

        // 默认选中第一个
        modeAdapter.setSelectedPosition(0);
    }


    @Override
    protected void setupListeners() {
        // 设置返回按钮点击事件
        binding.ivBack.setOnClickListener(v -> navigateBack());

        // 设置继续按钮点击事件
        binding.btnContinue.setOnClickListener(v -> {
            // 保存选择的桌面模式
            SettingsManager.setDefaultHome(selectedMode);
            completeOnboarding();
            LyoutScopeBus.eventBean().post(new LayoutUPModel(
                    SettingsConstants.HOME__TYPE,
                    SettingsManager.getDefaultHome(),
                    SettingsManager.getCardLayout()
            ));

        });
    }

    /**
     * 更新进度指示器状态
     */
    private void updateProgressIndicator() {
        // 使用新的进度指示器直接设置当前步骤
        if (binding != null) {
            binding.progressIndicator.setCurrentStep(4);
        }
    }
} 