package com.smartcar.easylauncher.modules.gesture.fragments;

import android.app.Activity;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Checkable;
import android.widget.Toast;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.databinding.GestureSettingsWhiteBarBinding;
import com.smartcar.easylauncher.modules.gesture.other.AdbProcessExtractor;
import com.smartcar.easylauncher.modules.gesture.other.SpfConfig;
import com.smartcar.easylauncher.modules.gesture.util.GlobalState;
import com.smartcar.easylauncher.shared.utils.MyLog;

public class FragmentWhiteBar extends FragmentSettingsBase {
    private static final String TAG = "FragmentWhiteBar";
    private GestureSettingsWhiteBarBinding binding;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = GestureSettingsWhiteBarBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onResume() {
        super.onResume();

        bindCheckable(binding.landscapeIosBar, SpfConfig.LANDSCAPE_IOS_BAR, SpfConfig.LANDSCAPE_IOS_BAR_DEFAULT);
        bindCheckable(binding.portraitIosBar, SpfConfig.PORTRAIT_IOS_BAR, SpfConfig.PORTRAIT_IOS_BAR_DEFAULT);

        bindVisibility(binding.portraitIosBar, binding.iosOptionsPortrait);
        bindVisibility(binding.landscapeIosBar, binding.iosOptionsLandscape);

        bindHandlerPicker(binding.iosBarSlideLeft, SpfConfig.IOS_BAR_SLIDE_LEFT, SpfConfig.IOS_BAR_SLIDE_LEFT_DEFAULT);
        bindHandlerPicker(binding.iosBarSlideRight, SpfConfig.IOS_BAR_SLIDE_RIGHT, SpfConfig.IOS_BAR_SLIDE_RIGHT_DEFAULT);
        bindHandlerPicker(binding.iosBarSlideUp, SpfConfig.IOS_BAR_SLIDE_UP, SpfConfig.IOS_BAR_SLIDE_UP_DEFAULT);
        bindHandlerPicker(binding.iosBarSlideUpHover, SpfConfig.IOS_BAR_SLIDE_UP_HOVER, SpfConfig.IOS_BAR_SLIDE_UP_HOVER_DEFAULT);
        bindHandlerPicker(binding.iosBarTouch, SpfConfig.IOS_BAR_TOUCH, SpfConfig.IOS_BAR_TOUCH_DEFAULT);
        bindHandlerPicker(binding.iosBarPress, SpfConfig.IOS_BAR_PRESS, SpfConfig.IOS_BAR_PRESS_DEFAULT);

        bindColorPicker(binding.iosBarColorShadow, SpfConfig.IOS_BAR_COLOR_SHADOW, SpfConfig.IOS_BAR_COLOR_SHADOW_DEFAULT, getString(R.string.white_bar_shadow_color));
        bindColorPicker(binding.iosBarColorStroke, SpfConfig.IOS_BAR_COLOR_STROKE, SpfConfig.IOS_BAR_COLOR_STROKE_DEFAULT, getString(R.string.white_bar_stroke_color));

        bindSeekBar(binding.iosBarWidthLandscape, SpfConfig.IOS_BAR_WIDTH_LANDSCAPE, SpfConfig.IOS_BAR_WIDTH_DEFAULT_LANDSCAPE, true);
        bindSeekBar(binding.iosBarWidthPortrait, SpfConfig.IOS_BAR_WIDTH_PORTRAIT, SpfConfig.IOS_BAR_WIDTH_DEFAULT_PORTRAIT, true);
        bindSeekBar(binding.iosBarAlphaFadeoutPortrait, SpfConfig.IOS_BAR_ALPHA_FADEOUT_PORTRAIT, SpfConfig.IOS_BAR_ALPHA_FADEOUT_PORTRAIT_DEFAULT, true);
        bindSeekBar(binding.iosBarAlphaFadeoutLandscape, SpfConfig.IOS_BAR_ALPHA_FADEOUT_LANDSCAPE, SpfConfig.IOS_BAR_ALPHA_FADEOUT_LANDSCAPE_DEFAULT, true);
        bindColorPicker(binding.iosBarColorLandscape, SpfConfig.IOS_BAR_COLOR_LANDSCAPE, SpfConfig.IOS_BAR_COLOR_LANDSCAPE_DEFAULT, getString(R.string.white_bar_landscape_color));
        bindColorPicker(binding.iosBarColorPortrait, SpfConfig.IOS_BAR_COLOR_PORTRAIT, SpfConfig.IOS_BAR_COLOR_PORTRAIT_DEFAULT, getString(R.string.white_bar_portrait_color));
        bindSeekBar(binding.iosBarSizeShadow, SpfConfig.IOS_BAR_SHADOW_SIZE, SpfConfig.IOS_BAR_SHADOW_SIZE_DEFAULT, true);
        bindSeekBar(binding.iosBarSizeStroke, SpfConfig.IOS_BAR_STROKE_SIZE, SpfConfig.IOS_BAR_STROKE_SIZE_DEFAULT, true);
        bindSeekBar(binding.iosBarMarginBottomLandscape, SpfConfig.IOS_BAR_MARGIN_BOTTOM_LANDSCAPE, SpfConfig.IOS_BAR_MARGIN_BOTTOM_LANDSCAPE_DEFAULT, true);
        bindSeekBar(binding.iosBarMarginBottomPortrait, SpfConfig.IOS_BAR_MARGIN_BOTTOM_PORTRAIT, SpfConfig.IOS_BAR_MARGIN_BOTTOM_PORTRAIT_DEFAULT, true);
        bindSeekBar(binding.iosBarHeight, SpfConfig.IOS_BAR_HEIGHT, SpfConfig.IOS_BAR_HEIGHT_DEFAULT, true);
        bindCheckable(binding.iosBarLockHide, SpfConfig.IOS_BAR_LOCK_HIDE, SpfConfig.IOS_BAR_LOCK_HIDE_DEFAULT);
        bindCheckable(binding.iosBarPopBattery, SpfConfig.IOS_BAR_POP_BATTERY, SpfConfig.IOS_BAR_POP_BATTERY_DEFAULT);
        bindCheckable(binding.iosBarConsecutive, SpfConfig.IOS_BAR_CONSECUTIVE, SpfConfig.IOS_BAR_CONSECUTIVE_DEFAULT);

        bindCheckable(binding.iosInputAvoid, SpfConfig.INPUT_METHOD_AVOID, SpfConfig.INPUT_METHOD_AVOID_DEFAULT);

        setViewBackground(binding.iosBarColorFadeoutPortrait, config.getInt(SpfConfig.IOS_BAR_COLOR_PORTRAIT, SpfConfig.IOS_BAR_COLOR_PORTRAIT_DEFAULT));
        setViewBackground(binding.iosBarColorFadeoutLandscape, config.getInt(SpfConfig.IOS_BAR_COLOR_LANDSCAPE, SpfConfig.IOS_BAR_COLOR_LANDSCAPE_DEFAULT));

        binding.iosBarAutoColorRoot.setChecked(config.getBoolean(SpfConfig.IOS_BAR_AUTO_COLOR, SpfConfig.IOS_BAR_AUTO_COLOR_DEFAULT));
        binding.iosBarAutoColorRoot.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Checkable ele = (Checkable) v;
                if (ele.isChecked()) {
                    if (GlobalState.enhancedMode || config.getBoolean(SpfConfig.ROOT, SpfConfig.ROOT_DEFAULT)) {
                        config.edit().putBoolean(SpfConfig.IOS_BAR_AUTO_COLOR, true).apply();
                        new AdbProcessExtractor().updateAdbProcessState(getActivity(), true);
                        restartService();
                    } else {
                        ele.setChecked(false);
                        Toast.makeText(getActivity().getApplicationContext(), getString(R.string.need_root_mode), Toast.LENGTH_SHORT).show();
                    }
                } else {
                    config.edit().putBoolean(SpfConfig.IOS_BAR_AUTO_COLOR, false).apply();
                    restartService();
                }
            }
        });

        updateView();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) {
            MyLog.v(TAG, "显示到最前端中visible");
        } else {
            MyLog.v(TAG, "不在最前端界面显示invisible");
        }
    }

    private void updateView() {
        Activity context = getActivity();
        if (context == null) return;

        setViewBackground(binding.iosBarColorLandscape, config.getInt(SpfConfig.IOS_BAR_COLOR_LANDSCAPE, SpfConfig.IOS_BAR_COLOR_LANDSCAPE_DEFAULT));
        setViewBackground(binding.iosBarColorPortrait, config.getInt(SpfConfig.IOS_BAR_COLOR_PORTRAIT, SpfConfig.IOS_BAR_COLOR_PORTRAIT_DEFAULT));
        setViewBackground(binding.iosBarColorShadow, config.getInt(SpfConfig.IOS_BAR_COLOR_SHADOW, SpfConfig.IOS_BAR_COLOR_SHADOW_DEFAULT));
        setViewBackground(binding.iosBarColorStroke, config.getInt(SpfConfig.IOS_BAR_COLOR_STROKE, SpfConfig.IOS_BAR_COLOR_STROKE_DEFAULT));
        setViewBackground(binding.iosBarColorFadeoutPortrait, config.getInt(SpfConfig.IOS_BAR_COLOR_PORTRAIT, SpfConfig.IOS_BAR_COLOR_PORTRAIT_DEFAULT));
        setViewBackground(binding.iosBarColorFadeoutLandscape, config.getInt(SpfConfig.IOS_BAR_COLOR_LANDSCAPE, SpfConfig.IOS_BAR_COLOR_LANDSCAPE_DEFAULT));

        binding.iosBarColorFadeoutPortrait.setAlpha(config.getInt(SpfConfig.IOS_BAR_ALPHA_FADEOUT_PORTRAIT, SpfConfig.IOS_BAR_ALPHA_FADEOUT_PORTRAIT_DEFAULT) / 100f);
        binding.iosBarColorFadeoutLandscape.setAlpha(config.getInt(SpfConfig.IOS_BAR_ALPHA_FADEOUT_LANDSCAPE, SpfConfig.IOS_BAR_ALPHA_FADEOUT_LANDSCAPE_DEFAULT) / 100f);

        updateActionText(binding.iosBarSlideLeft, SpfConfig.IOS_BAR_SLIDE_LEFT, SpfConfig.IOS_BAR_SLIDE_LEFT_DEFAULT);
        updateActionText(binding.iosBarSlideRight, SpfConfig.IOS_BAR_SLIDE_RIGHT, SpfConfig.IOS_BAR_SLIDE_RIGHT_DEFAULT);
        updateActionText(binding.iosBarSlideUp, SpfConfig.IOS_BAR_SLIDE_UP, SpfConfig.IOS_BAR_SLIDE_UP_DEFAULT);
        updateActionText(binding.iosBarSlideUpHover, SpfConfig.IOS_BAR_SLIDE_UP_HOVER, SpfConfig.IOS_BAR_SLIDE_UP_HOVER_DEFAULT);
        updateActionText(binding.iosBarTouch, SpfConfig.IOS_BAR_TOUCH, SpfConfig.IOS_BAR_TOUCH_DEFAULT);
        updateActionText(binding.iosBarPress, SpfConfig.IOS_BAR_PRESS, SpfConfig.IOS_BAR_PRESS_DEFAULT);
    }

    @Override
    protected void restartService() {
        updateView();
        super.restartService();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
