package com.smartcar.easylauncher.modules.setting;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bytedance.scene.group.UserVisibleHintGroupScene;
import com.smartcar.easylauncher.databinding.FragmentBaseSettingBinding;
import com.smartcar.easylauncher.core.manager.DeviceInfoManager;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.PerformanceMonitor;

import java.util.Map;

/**
 * 基础设置页面 - API 17+ 优化版本
 * 移除RxJava依赖，使用轻量级DeviceInfoManager
 *
 * <AUTHOR>
 */
public class BaseSettingScene extends UserVisibleHintGroupScene implements DeviceInfoManager.DataLoadCallback {
    public static final String TAG = BaseSettingScene.class.getSimpleName();
    private FragmentBaseSettingBinding binding;
    private DeviceInfoManager deviceInfoManager;
    private PerformanceMonitor performanceMonitor;
    private Handler mainHandler = new Handler(Looper.getMainLooper());

    // 加载状态标记
    private boolean isBasicInfoDisplayed = false;
    private boolean isExtendedInfoDisplayed = false;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        deviceInfoManager = DeviceInfoManager.getInstance();
        performanceMonitor = PerformanceMonitor.getInstance();
    }

    @Override
    public ViewGroup onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        performanceMonitor.startTiming("页面创建");
        binding = FragmentBaseSettingBinding.inflate(inflater, container, false);
        performanceMonitor.endTiming("页面创建");
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        performanceMonitor.startTiming("总体加载时间");
        performanceMonitor.logMemoryUsage("页面初始化前");

        // 初始化UI
        performanceMonitor.startTiming("UI初始化");

        performanceMonitor.endTiming("UI初始化");

        // 异步加载设备信息
        performanceMonitor.startTiming("设备信息加载");
        loadDeviceInfo();

        performanceMonitor.logMemoryUsage("页面初始化后");
    }


    /**
     * 加载设备信息
     */
    private void loadDeviceInfo() {
        deviceInfoManager.loadDeviceInfoAsync(requireActivity(), this);
    }

    @Override
    public void onBasicInfoLoaded(Map<String, String> basicInfo) {
        if (binding == null || isBasicInfoDisplayed) return;

        performanceMonitor.endTiming("设备信息加载");
        performanceMonitor.startTiming("基本信息UI更新");

        MyLog.d(TAG, "基本信息加载完成");

        // 更新头部信息区
        updateHeaderInfo(basicInfo);

        // 更新基本设备信息
        updateBasicDeviceInfo(basicInfo);

        isBasicInfoDisplayed = true;

        performanceMonitor.endTiming("基本信息UI更新");
        performanceMonitor.logMemoryUsage("基本信息加载后");
    }

    @Override
    public void onExtendedInfoLoaded(Map<String, String> extendedInfo) {
        if (binding == null || isExtendedInfoDisplayed) return;

        performanceMonitor.startTiming("扩展信息UI更新");

        MyLog.d(TAG, "扩展信息加载完成");

        // 更新所有信息
        updateAllInfo(extendedInfo);

        isExtendedInfoDisplayed = true;

        performanceMonitor.endTiming("扩展信息UI更新");
        performanceMonitor.endTiming("总体加载时间");
        performanceMonitor.logMemoryUsage("全部信息加载后");

        // 打印性能报告
        performanceMonitor.printReport();
    }

    @Override
    public void onError(Exception error) {
        MyLog.e(TAG, "加载设备信息失败", error);

        performanceMonitor.endTiming("设备信息加载");
        performanceMonitor.endTiming("总体加载时间");

        if (binding != null) {
            // 显示错误信息
            showErrorInfo();
        }

        // 即使出错也打印性能报告
        performanceMonitor.printReport();
    }

    /**
     * 更新头部信息区
     */
    private void updateHeaderInfo(Map<String, String> info) {
        if (binding == null) return;

        // 设备型号和品牌
        String model = info.get(DeviceInfoManager.KEY_MODEL);
        String brand = info.get(DeviceInfoManager.KEY_BRAND);

        binding.tvDeviceModel.setText(formatHeaderValue(model));
        binding.tvDeviceBrand.setText(formatHeaderValue(brand));

        // Android版本信息
        String androidVersion = info.get(DeviceInfoManager.KEY_ANDROID_VERSION);
        String apiLevel = info.get(DeviceInfoManager.KEY_API_LEVEL);

        binding.tvAndroidVersion.setText("Android " + formatHeaderValue(androidVersion));

        // 应用版本信息
        String appVersion = info.get(DeviceInfoManager.KEY_APP_VERSION);
        String appVersionCode = info.get(DeviceInfoManager.KEY_APP_VERSION_CODE);

        binding.tvAppVersion.setText(formatHeaderValue(appVersion));
        binding.tvAppVersionCode.setText(formatHeaderValue(appVersionCode));

        // API级别信息 - 添加null安全检查
        String androidApiText = "Android " + formatHeaderValue(androidVersion) + " (API " + formatHeaderValue(apiLevel) + ")";
        if (binding.apiLevelField != null) {
            binding.apiLevelField.setValue(androidApiText);
        } else {
            MyLog.w(TAG, "apiLevelField is null, skipping API level update");
        }
    }

    /**
     * 更新基本设备信息
     */
    private void updateBasicDeviceInfo(Map<String, String> info) {
        if (binding == null) return;

        // 安全补丁信息 - 添加null安全检查
        String securityPatch = info.get(DeviceInfoManager.KEY_SECURITY_PATCH);
        if (binding.securityPatchField != null) {
            binding.securityPatchField.setValue(securityPatch);
        } else {
            MyLog.w(TAG, "securityPatchField is null, skipping security patch update");
        }
    }

    /**
     * 更新所有信息
     */
    private void updateAllInfo(Map<String, String> info) {
        if (binding == null) return;

        // 更新头部序列号
        String serialNumber = info.get(DeviceInfoManager.KEY_SERIAL_NUMBER);
        binding.tvDeviceSerial.setText("SN: " + formatHeaderValue(serialNumber));

        // 设备信息卡片 - 添加null安全检查
        if (binding.cpuInfoField != null) {
            binding.cpuInfoField.setValue(info.get(DeviceInfoManager.KEY_CPU));
        }
        if (binding.ramInfoField != null) {
            binding.ramInfoField.setValue(info.get(DeviceInfoManager.KEY_RAM));
            binding.ramInfoField.setRemark("实际可用内存可能小于显示值");
        }
        if (binding.storageInfoField != null) {
            binding.storageInfoField.setValue(info.get(DeviceInfoManager.KEY_STORAGE));
            binding.storageInfoField.setRemark("已用空间/总空间");
        }
        if (binding.kernelInfoField != null) {
            binding.kernelInfoField.setValue(info.get(DeviceInfoManager.KEY_KERNEL));
        }

        // 显示信息卡片 - 添加null安全检查
        if (binding.physicalResolutionField != null) {
            binding.physicalResolutionField.setValue(info.get(DeviceInfoManager.KEY_PHYSICAL_RESOLUTION));
        }
        if (binding.appResolutionField != null) {
            binding.appResolutionField.setValue(info.get(DeviceInfoManager.KEY_APP_RESOLUTION));
        }
        if (binding.densityField != null) {
            binding.densityField.setValue(info.get(DeviceInfoManager.KEY_DENSITY));
        }
        if (binding.refreshRateField != null) {
            binding.refreshRateField.setValue(info.get(DeviceInfoManager.KEY_REFRESH_RATE));
        }

        // 网络信息卡片 - 添加null安全检查
        if (binding.ipAddressField != null) {
            binding.ipAddressField.setValue(info.get(DeviceInfoManager.KEY_IP));
        }
        if (binding.macAddressField != null) {
            binding.macAddressField.setValue(info.get(DeviceInfoManager.KEY_MAC));
        }
        if (binding.bluetoothInfoField != null) {
            binding.bluetoothInfoField.setValue(info.get(DeviceInfoManager.KEY_BLUETOOTH));
        }

        // 设备标识卡片 - 添加null安全检查
        if (binding.imeiField != null) {
            binding.imeiField.setValue(info.get(DeviceInfoManager.KEY_DEVICE_ID));
        }
        if (binding.androidIdField != null) {
            binding.androidIdField.setValue(info.get(DeviceInfoManager.KEY_ANDROID_ID));
        }
        if (binding.serialNumberField != null) {
            binding.serialNumberField.setValue(info.get(DeviceInfoManager.KEY_SERIAL_NUMBER));
        }
    }

    /**
     * 显示错误信息
     */
    private void showErrorInfo() {
        if (binding == null) return;

        String errorText = "获取失败";

        // 如果基本信息还没显示，显示错误
        if (!isBasicInfoDisplayed) {
            binding.tvDeviceModel.setText(errorText);
            binding.tvDeviceBrand.setText(errorText);
            binding.tvAndroidVersion.setText(errorText);
            binding.tvAppVersion.setText(errorText);
            binding.tvAppVersionCode.setText(errorText);
        }

        // 设置其他字段为错误状态 - 添加null安全检查
        if (binding.cpuInfoField != null) binding.cpuInfoField.setValue(errorText);
        if (binding.ramInfoField != null) binding.ramInfoField.setValue(errorText);
        if (binding.storageInfoField != null) binding.storageInfoField.setValue(errorText);
        if (binding.kernelInfoField != null) binding.kernelInfoField.setValue(errorText);
        if (binding.physicalResolutionField != null) binding.physicalResolutionField.setValue(errorText);
        if (binding.appResolutionField != null) binding.appResolutionField.setValue(errorText);
        if (binding.densityField != null) binding.densityField.setValue(errorText);
        if (binding.refreshRateField != null) binding.refreshRateField.setValue(errorText);
        if (binding.ipAddressField != null) binding.ipAddressField.setValue(errorText);
        if (binding.macAddressField != null) binding.macAddressField.setValue(errorText);
        if (binding.bluetoothInfoField != null) binding.bluetoothInfoField.setValue(errorText);
        if (binding.imeiField != null) binding.imeiField.setValue(errorText);
        if (binding.androidIdField != null) binding.androidIdField.setValue(errorText);
        if (binding.serialNumberField != null) binding.serialNumberField.setValue(errorText);
        if (binding.apiLevelField != null) binding.apiLevelField.setValue(errorText);
        if (binding.securityPatchField != null) binding.securityPatchField.setValue(errorText);
    }

    /**
     * 格式化头部显示值
     */
    private String formatHeaderValue(String value) {
        if (value == null || value.trim().isEmpty() || value.equals("未知")) {
            return "--";
        }
        String v = value.trim();
        return v.substring(0, 1).toUpperCase() + v.substring(1);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        // 移除所有回调，防止内存泄漏
        if (mainHandler != null) {
            mainHandler.removeCallbacksAndMessages(null);
        }

        // 清理性能监控数据
        if (performanceMonitor != null) {
            performanceMonitor.clear();
        }
    }
}

