
package com.smartcar.easylauncher.modules.task;

import static com.bytedance.scene.navigation.NavigationSceneGetter.requireNavigationScene;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.chad.library.adapter4.QuickAdapterHelper;
import com.chad.library.adapter4.layoutmanager.QuickGridLayoutManager;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.task.FindTaskTopHeaderAdapter;
import com.smartcar.easylauncher.shared.adapter.task.RecommendTaskAdapter;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.databinding.SceneFindTasksCoordinatorBinding;
import com.smartcar.easylauncher.shared.dialog.GeneralDialog;
import com.smartcar.easylauncher.infrastructure.event.cody.TaskNotificationScopeBus;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.data.model.task.RecommendTaskModel;
import com.smartcar.easylauncher.data.model.task.TaskModel;
import com.smartcar.easylauncher.data.model.task.TaskNotificationModel;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.RecommendTaskDataGenerator;

import java.util.ArrayList;
import java.util.List;

/**
 * 发现推荐任务页面
 * data 2024/3/27
 *
 * <AUTHOR>
 */
public class FindTasksScene extends BaseScene {
    public static final String TAG = FindTasksScene.class.getSimpleName();

    private RecommendTaskAdapter mRecommendTaskAdapter;
    private FindTaskTopHeaderAdapter mHeaderAdapter;
    private List<RecommendTaskModel> mRecommendTaskList;
    private SceneFindTasksCoordinatorBinding mBinding;
    private Handler mMainHandler;


    @NonNull
    @Override
    protected ViewGroup onCreateNewView(@NonNull LayoutInflater layoutInflater, @Nullable ViewGroup viewGroup, @Nullable Bundle bundle) {
        mBinding = SceneFindTasksCoordinatorBinding.inflate(layoutInflater, viewGroup, false);
        return mBinding.getRoot();
    }


    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mMainHandler = new Handler(Looper.getMainLooper());

        setupViews();
        loadRecommendTasks();
    }

    /**
     * 设置视图组件 - 优化版本
     */
    private void setupViews() {
        setupRecyclerView();
        setupAdapter();
        setupSwipeRefresh();
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerView() {
        // 设置网格布局 - 2列显示，与主题页面保持一致
        QuickGridLayoutManager layoutManager = new QuickGridLayoutManager(requireActivity(), 2);
        mBinding.rvTheme.setLayoutManager(layoutManager);

        // RecyclerView性能优化配置
        mBinding.rvTheme.setHasFixedSize(true);
        mBinding.rvTheme.setItemViewCacheSize(20);
    }

    /**
     * 设置适配器和空视图
     */
    private void setupAdapter() {
        mRecommendTaskAdapter = new RecommendTaskAdapter();
        mHeaderAdapter = new FindTaskTopHeaderAdapter();
        QuickAdapterHelper helper = new QuickAdapterHelper.Builder(mRecommendTaskAdapter).build();
        helper.addBeforeAdapter(mHeaderAdapter);
        // 设置点击事件 - 显示任务详情预览
        mRecommendTaskAdapter.setOnItemClickListener((adapter, view, position) -> {
            RecommendTaskModel recommendTask = adapter.getItem(position);
            showTaskPreviewDialog(recommendTask);
        });

        // 设置长按事件 - 快速添加任务
        mRecommendTaskAdapter.setOnItemLongClickListener((adapter, view, position) -> {
            RecommendTaskModel recommendTask = adapter.getItem(position);
            showQuickAddConfirmation(recommendTask);
            return true;
        });

        // 设置添加按钮点击事件 - 快速添加任务
        mRecommendTaskAdapter.addOnItemChildClickListener(R.id.tv_add_button, (adapter, view, position) -> {
            RecommendTaskModel recommendTask = adapter.getItem(position);
            showQuickAddConfirmation(recommendTask);
        });

        // 初始状态禁用空视图，避免在加载时显示
        mRecommendTaskAdapter.setStateViewEnable(false);
        mRecommendTaskAdapter.setUseStateViewSize(true);
        View emptyView = LayoutInflater.from(requireActivity()).inflate(R.layout.find_tasks_empty_view, mBinding.rvTheme, false);
        mRecommendTaskAdapter.setStateView(emptyView);

        mBinding.rvTheme.setAdapter(helper.getAdapter());
    }

    /**
     * 设置下拉刷新
     */
    private void setupSwipeRefresh() {
        mBinding.swipeRefreshLayout.setOnRefreshListener(this::loadRecommendTasks);
        mBinding.swipeRefreshLayout.setColorSchemeResources(
                R.color.bluetooth_connect_color,
                R.color.compass_circle_left_bottom_color
        );
    }


    /**
     * 加载推荐任务数据
     */
    private void loadRecommendTasks() {
        // 显示加载状态
        if (!mBinding.swipeRefreshLayout.isRefreshing()) {
            mBinding.swipeRefreshLayout.setRefreshing(true);
        }

        // 更新头部适配器显示加载状态
        if (mHeaderAdapter != null) {
            mHeaderAdapter.updateTaskCount(0, true);
        }

        // 使用更优雅的异步加载机制
        mMainHandler.postDelayed(() -> {
            try {
                // 生成推荐任务数据
                List<RecommendTaskModel> tasks = RecommendTaskDataGenerator.generateRecommendTasks();

                // 处理数据加载成功
                handleTaskDataSuccess(tasks);

            } catch (Exception e) {
                // 处理数据加载失败
                handleTaskDataError(e);
            }
        }, 800); // 减少延迟时间，提升响应速度
    }

    /**
     * 处理任务数据加载成功
     */
    private void handleTaskDataSuccess(List<RecommendTaskModel> tasks) {
        mBinding.swipeRefreshLayout.setRefreshing(false);

        MyLog.d(TAG, "推荐任务数据加载成功，数量: " +
                (tasks != null ? tasks.size() : 0));

        if (tasks != null && !tasks.isEmpty()) {
            mRecommendTaskList = tasks;
            updateAdapterData();
            updateTaskCount(tasks.size());

            // 显示更友好的提示信息
            MToast.makeTextShort("为您推荐了 " + tasks.size() + " 个智能任务");
        } else {
            // 数据为空时，启用空视图并显示
            showEmptyState();
            updateTaskCount(0);
            MToast.makeTextShort("暂无推荐任务，请稍后再试");
        }
    }

    /**
     * 处理任务数据加载失败
     */
    private void handleTaskDataError(Throwable throwable) {
        mBinding.swipeRefreshLayout.setRefreshing(false);
        MyLog.e(TAG, "推荐任务数据加载失败", throwable);
        MToast.makeTextShort("加载推荐任务失败，请检查网络连接");

        // 加载失败时显示空视图
        showEmptyState();
        updateTaskCount(0);
    }


    /**
     * 更新适配器数据
     */
    private void updateAdapterData() {
        // 有数据时禁用空视图，确保正常显示数据
        mRecommendTaskAdapter.setStateViewEnable(false);
        mRecommendTaskAdapter.submitList(new ArrayList<>(mRecommendTaskList));
        MyLog.d(TAG, "适配器数据更新完成，任务数量: " + mRecommendTaskList.size());
    }

    /**
     * 显示空状态
     */
    private void showEmptyState() {
        // 启用空视图并清空数据列表
        mRecommendTaskAdapter.setStateViewEnable(true);
        mRecommendTaskAdapter.submitList(new ArrayList<>());
        MyLog.d(TAG, "显示空状态视图");
    }


    /**
     * 显示任务预览页面
     */
    private void showTaskPreviewDialog(RecommendTaskModel recommendTask) {
        if (recommendTask == null) {
            MyLog.e(TAG, "推荐任务为空，无法预览");
            return;
        }

        try {
            // 创建预览Scene的参数
            Bundle arguments = FindTaskDetailsScene.createArguments(recommendTask);

            // 创建预览Scene实例
            FindTaskDetailsScene previewScene = new FindTaskDetailsScene();
            previewScene.setArguments(arguments);

            // 跳转到预览页面
            requireNavigationScene(this).push(previewScene);

            MyLog.d(TAG, "跳转到任务预览页面: " + recommendTask.getTaskName());

        } catch (Exception e) {
            MyLog.e(TAG, "跳转到任务预览页面失败: " + e.getMessage(), e);
            MToast.makeTextShort("打开任务预览失败");
        }
    }

    /**
     * 显示快速添加确认对话框
     */
    private void showQuickAddConfirmation(RecommendTaskModel recommendTask) {
        if (recommendTask == null) return;

        String message = "快速添加「" + recommendTask.getTaskName() + "」到我的任务列表？\n\n" +
                        "提示：长按可快速添加，点击可查看详情";
        FragmentActivity activity = (FragmentActivity) requireActivity();
        GeneralDialog.showGeneralDialog(
                activity.getSupportFragmentManager(),
            requireActivity(),
            "快速添加任务",
            message,
            "立即添加",
            "取消",
            new GeneralDialog.DialogClickListener() {
                @Override
                public void onPositiveClick() {
                    addRecommendTaskToMyTasks(recommendTask);
                }

                @Override
                public void onNegativeClick() {
                    // 用户取消，不做任何操作
                }
            }
        );
    }

    /**
     * 将推荐任务添加到我的任务列表
     */
    private void addRecommendTaskToMyTasks(RecommendTaskModel recommendTask) {
        if (recommendTask == null) {
            MToast.makeTextShort("任务信息无效");
            return;
        }

        try {
            // 转换推荐任务为普通任务
            TaskModel newTask = convertRecommendTaskToTask(recommendTask);

            // 获取当前任务列表
            String taskListJson = DataManager.getTask();
            List<TaskModel> taskList = new ArrayList<>();
            if (taskListJson != null && !taskListJson.isEmpty()) {
                try {
                    taskList = new Gson().fromJson(taskListJson, new TypeToken<List<TaskModel>>() {}.getType());
                } catch (JsonSyntaxException e) {
                    MyLog.e(TAG, "解析任务列表JSON失败: " + e.getMessage());
                    taskList = new ArrayList<>();
                }
            }

            // 检查是否已存在相同任务
            boolean exists = taskList.stream().anyMatch(task ->
                task.getTaskName().equals(newTask.getTaskName()));

            if (exists) {
                MToast.makeTextShort("该任务已存在于我的任务中");
                return;
            }

            // 添加新任务
            taskList.add(newTask);

            // 保存到本地
            DataManager.setTask(new Gson().toJson(taskList));

            // 发送通知
            TaskNotificationScopeBus.eventBean().post(new TaskNotificationModel(1, newTask));

            // 显示成功提示
            MToast.makeTextShort("「" + recommendTask.getTaskName() + "」已添加成功");
            MyLog.d(TAG, "成功添加推荐任务: " + recommendTask.getTaskName());

        } catch (Exception e) {
            MToast.makeTextShort("添加任务失败，请稍后重试");
            MyLog.e(TAG, "添加推荐任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将推荐任务转换为普通任务
     */
    private TaskModel convertRecommendTaskToTask(RecommendTaskModel recommendTask) {
        TaskModel task = new TaskModel();
        task.setId(String.valueOf(System.currentTimeMillis()));
        task.setSaveTime(String.valueOf(System.currentTimeMillis()));
        task.setStatus(false); // 默认关闭状态
        task.setModel(recommendTask.isRepeatable()); // 根据推荐任务设置是否可重复
        task.setTaskName(recommendTask.getTaskName());
        task.setConditions(recommendTask.getConditions());
        task.setResults(recommendTask.getResults());
        return task;
    }


    /**
     * 更新任务数量显示
     */
    private void updateTaskCount(int count) {
        if (mHeaderAdapter != null) {
            mHeaderAdapter.updateTaskCount(count, false);
            MyLog.d(TAG, "更新任务数量显示: " + count);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        MyLog.d(TAG, "onResume: 发现页面恢复");

        // 如果还没有加载过数据，则加载
        if (mRecommendTaskList == null || mRecommendTaskList.isEmpty()) {
            MyLog.d(TAG, "首次加载推荐任务数据");
            loadRecommendTasks();
        } else {
            MyLog.d(TAG, "推荐任务数据已存在，跳过加载");
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        MyLog.d(TAG, "onDestroyView: 清理发现页面资源");

        // 清理资源
        if (mRecommendTaskAdapter != null) {
            mRecommendTaskAdapter = null;
        }
        if (mHeaderAdapter != null) {
            mHeaderAdapter = null;
        }
        mRecommendTaskList = null;
        mBinding = null;
    }


}
