package com.smartcar.easylauncher.modules.screenclean

import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import androidx.core.view.isVisible
import com.bytedance.scene.ktx.requireNavigationScene
import com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene
import com.smartcar.easylauncher.R
import com.smartcar.easylauncher.core.base.BaseScene
import com.smartcar.easylauncher.databinding.SceneScreenCleanBinding
import com.smartcar.easylauncher.shared.utils.MyLog

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 屏幕清洁Scene - Kotlin版本
 * 
 * 功能特性：
 * - 自动开始清洁倒计时（30秒）
 * - 完全拦截返回键，防止误操作
 * - 长按退出按钮3秒退出
 * - 优雅的动画效果
 * - 空安全的ViewBinding使用
 * 
 * <AUTHOR>
 * @date 2024/12/23
 */
class ScreenCleanScene : BaseScene() {
    
    companion object {
        private const val TAG = "ScreenCleanScene"
        
        // 清洁状态常量
        private const val STATE_READY = 0
        private const val STATE_CLEANING = 1
        private const val STATE_COMPLETED = 2
        
        // 清洁倒计时相关常量
        private const val CLEANING_DURATION_SECONDS = 30 // 建议清洁时长30秒
        private const val EXIT_HOLD_DURATION = 3000L // 长按退出时长3秒
    }
    
    // ViewBinding - 可空类型，确保空安全
    private var binding: SceneScreenCleanBinding? = null
    
    // 状态管理
    private var currentState = STATE_READY
    private var isExitHolding = false
    private var remainingSeconds = CLEANING_DURATION_SECONDS
    private var cleaningStartTime = 0L
    
    // 协程作用域 - 生命周期感知
    private val sceneScope = CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)
    private var cleaningJob: Job? = null
    private var exitCountdownJob: Job? = null
    private var completionJob: Job? = null
    
    // 清洁提示轮换
    private var tipRotationJob: Job? = null
    
    // 清洁提示
    private var cleaningTips: Array<String>? = null
    private var currentTipIndex = 0

    override fun onCreateNewView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): ViewGroup {
        MyLog.d(TAG, "onCreateNewView: 创建屏幕清洁Scene")

        return try {
            // 使用ViewBinding创建视图
            binding = SceneScreenCleanBinding.inflate(inflater, container, false)

            MyLog.d(TAG, "屏幕清洁Scene创建完成")

            binding?.root ?: throw IllegalStateException("ViewBinding创建失败")
        } catch (e: Exception) {
            MyLog.e(TAG, "创建屏幕清洁Scene失败", e)
            throw e
        }
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        MyLog.d(TAG, "onViewCreated: 初始化屏幕清洁Scene")

        initData()
        setupListeners()
        setupBackPressedListener()
        startEnterAnimation()

        // 页面打开后自动开始清洁倒计时
        autoStartCleaning()

        MyLog.d(TAG, "屏幕清洁Scene初始化完成")
    }
    
    /**
     * 初始化数据
     */
    private fun initData() {
        MyLog.d(TAG, "initData: 初始化数据")

        // 初始化清洁提示数组
        cleaningTips = arrayOf(
            requireActivity().getString(R.string.screen_clean_tip_1),
            requireActivity().getString(R.string.screen_clean_tip_2),
            requireActivity().getString(R.string.screen_clean_tip_3),
            requireActivity().getString(R.string.screen_clean_tip_4)
        )

        MyLog.d(TAG, "数据初始化完成")
    }
    
    /**
     * 设置事件监听器
     */
    private fun setupListeners() {
        MyLog.d(TAG, "setupListeners: 设置事件监听器")
        
        binding?.apply {
            // 全屏触摸监听 - 清洁过程中阻止所有触摸，避免误操作
            touchBlocker.setOnTouchListener { _, _ -> true }
            
            // 退出按钮长按监听
            exitButton.setOnTouchListener(::handleExitButtonTouch)
        }
        
        MyLog.d(TAG, "事件监听器设置完成")
    }
    
    /**
     * 设置返回键拦截监听器
     */
    private fun setupBackPressedListener() {
        MyLog.d(TAG, "setupBackPressedListener: 设置返回键拦截")

        requireNavigationScene().addOnBackPressedListener(this) {
            // 完全拦截返回键，防止误操作退出清洁模式
            MyLog.d(TAG, "onBackPressed: 拦截返回键，清洁模式中不允许返回")

            // 给用户提示正确的退出方式
            showExitHint()

            true // 返回true表示拦截了返回键事件
        }

        MyLog.d(TAG, "返回键拦截设置完成")
    }
    
    /**
     * 自动开始清洁 - 页面打开后立即开始
     */
    private fun autoStartCleaning() {
        MyLog.d(TAG, "autoStartCleaning: 页面打开后自动开始清洁")

        // 使用协程延迟，生命周期安全
        sceneScope.launch {
            delay(1000) // 延迟1秒，给用户准备时间

            // 检查Scene是否仍然活跃
            if (isSceneActive() && currentState == STATE_READY) {
                startCleaning()
            }
        }
    }
    
    /**
     * 开始清洁模式
     */
    private fun startCleaning() {
        MyLog.d(TAG, "startCleaning: 开始清洁模式")

        currentState = STATE_CLEANING
        remainingSeconds = CLEANING_DURATION_SECONDS
        cleaningStartTime = System.currentTimeMillis()
        
        updateUIForState(STATE_CLEANING)
        startCleaningCountdown()
        startTipRotation()
        
        // 显示清洁状态指示器
        binding?.cleaningIndicator?.isVisible = true
        
        MyLog.d(TAG, "清洁模式已启动，建议清洁时长: ${CLEANING_DURATION_SECONDS}秒")
    }
    
    /**
     * 开始清洁倒计时 - 使用协程替代ValueAnimator，更简洁高效
     */
    private fun startCleaningCountdown() {
        MyLog.d(TAG, "startCleaningCountdown: 开始清洁倒计时")

        cleaningJob = sceneScope.launch {
            repeat(CLEANING_DURATION_SECONDS) { second ->
                remainingSeconds = CLEANING_DURATION_SECONDS - second

                // 更新UI
                binding?.updateCleaningProgress(remainingSeconds)

                // 延迟1秒
                delay(1000)

                // 检查状态
                if (currentState != STATE_CLEANING || !isSceneActive()) {
                    return@launch
                }
            }

            // 倒计时完成
            remainingSeconds = 0
            binding?.updateCleaningProgress(0)

            if (currentState == STATE_CLEANING && isSceneActive()) {
                completeCleaning()
            }
        }
    }

    /**
     * ViewBinding扩展函数 - 更新清洁进度
     */
    private fun SceneScreenCleanBinding.updateCleaningProgress(remaining: Int) {
        // 计算进度百分比
        val progress = ((CLEANING_DURATION_SECONDS - remaining) * 100) / CLEANING_DURATION_SECONDS
        progressBar.progress = progress

        // 更新文本
        progressText.text = if (remaining > 0) {
            requireActivity().getString(R.string.screen_clean_countdown, remaining)
        } else {
            requireActivity().getString(R.string.screen_clean_countdown_complete)
        }
    }

    /**
     * 完成清洁
     */
    private fun completeCleaning() {
        MyLog.d(TAG, "completeCleaning: 清洁完成")

        currentState = STATE_COMPLETED
        updateUIForState(STATE_COMPLETED)

        binding.safeExecute {
            // 使用with作用域函数简化代码
            with(this) {
                // 准备完成元素
                listOf(completionOverlay, completionIcon, completionTitle, completionMessage)
                    .forEach { view ->
                        view.isVisible = true
                        view.alpha = 0f
                    }

                // 背景淡入，完成后显示内容
                completionOverlay.fadeIn(300) {
                    showCompletionContent()
                }
            }
        }

        MyLog.d(TAG, "清洁完成动画已启动")
    }

    /**
     * 显示完成内容 - 依次显示图标、标题、描述
     * 使用ViewBinding直接访问，避免findViewById
     */
    private fun showCompletionContent() {
        MyLog.d(TAG, "showCompletionContent: 显示完成内容")

        binding?.let { binding ->
            // 直接使用ViewBinding访问完成相关的View
            val completionViews = listOf(
                binding.completionIcon,
                binding.completionTitle,
                binding.completionMessage
            )

            // 显示完成元素并设置初始透明度
            completionViews.forEach { view ->
                view.isVisible = true
                view.alpha = 0f
            }

            // 使用协程实现更优雅的依次显示动画
            showCompletionAnimationSequence(completionViews)
        } ?: run {
            MyLog.e(TAG, "showCompletionContent: binding为null，无法显示完成内容")
            safeDelayedExit(1000)
        }
    }

    /**
     * 使用协程和扩展函数实现优雅的动画序列
     */
    private fun showCompletionAnimationSequence(views: List<View>) {
        sceneScope.launch {
            views.forEachIndexed { index, view ->
                // 每个动画间隔200ms
                delay(index * 200L)

                if (isSceneActive()) {
                    view.fadeIn(200)
                }
            }

            // 所有动画完成后，延迟3秒退出
            delay(3000)
            if (isSceneActive()) {
                exitDirectly()
            }
        }
    }

    /**
     * 处理退出按钮触摸事件
     */
    private fun handleExitButtonTouch(v: View, event: MotionEvent): Boolean {
        return when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                if (!isExitHolding) {
                    startExitCountdown()
                }
                true
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                if (isExitHolding) {
                    cancelExitCountdown()
                }
                true
            }
            else -> false
        }
    }

    /**
     * 开始退出倒计时 - 带倒计时显示
     */
    private fun startExitCountdown() {
        if (isExitHolding) return

        MyLog.d(TAG, "startExitCountdown: 开始退出倒计时")
        isExitHolding = true

        // 显示倒计时的退出逻辑
        exitCountdownJob = sceneScope.launch {
            val totalSeconds = (EXIT_HOLD_DURATION / 1000).toInt()

            repeat(totalSeconds) { second ->
                val remaining = totalSeconds - second

                // 更新按钮文本显示倒计时
                binding?.exitButton?.text = "退出 ($remaining)"

                // 延迟1秒
                delay(1000)

                // 检查是否仍在长按状态
                if (!isExitHolding || !isSceneActive()) {
                    return@launch
                }
            }

            // 倒计时完成，执行退出
            if (isExitHolding && isSceneActive()) {
                exitDirectly()
            }
        }
    }

    /**
     * 取消退出倒计时
     */
    private fun cancelExitCountdown() {
        if (!isExitHolding) return

        MyLog.d(TAG, "cancelExitCountdown: 取消退出倒计时")
        isExitHolding = false

        // 取消协程
        exitCountdownJob?.cancel()

        // 恢复按钮原始文本
        binding?.exitButton?.text = requireActivity().getString(R.string.screen_clean_exit)
    }

    /**
     * 显示退出提示 - 简化版本
     */
    private fun showExitHint() {
        MyLog.d(TAG, "showExitHint: 显示退出提示")

        // 简单的视觉反馈，不改变文案
        binding?.exitButton?.apply {
            animate()
                .scaleX(1.05f)
                .scaleY(1.05f)
                .setDuration(100)
                .withEndAction {
                    animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                }
        }
    }

    /**
     * 直接退出清洁模式
     */
    private fun exitDirectly() {
        MyLog.d(TAG, "exitDirectly: 直接退出清洁模式")

        try {
            // 停止所有动画和定时器
            stopAllAnimations()

            // 直接退出
            requireNavigationScene().pop()
            MyLog.d(TAG, "直接退出成功")
        } catch (e: Exception) {
            MyLog.e(TAG, "直接退出失败", e)
        }
    }

    /**
     * 更新UI状态
     */
    private fun updateUIForState(state: Int) {
        MyLog.d(TAG, "updateUIForState: 更新UI状态为 $state")

        // 直接更新UI，无需Handler
        binding?.apply {
            when (state) {
                STATE_READY -> {
                    statusText.setText(R.string.screen_clean_ready)
                    instructionText.setText(R.string.screen_clean_instruction_start)
                    progressContainer.isVisible = false
                    cleaningIndicator.isVisible = false
                }

                STATE_CLEANING -> {
                    statusText.setText(R.string.screen_clean_in_progress)
                    instructionText.setText(R.string.screen_clean_instruction_cleaning_countdown)
                    progressContainer.isVisible = true
                }

                STATE_COMPLETED -> {
                    statusText.setText(R.string.screen_clean_completed)
                    instructionText.setText(R.string.screen_clean_instruction_exit)
                }
            }
        }
    }

    /**
     * 开始提示轮换 - 简化版本
     */
    private fun startTipRotation() {
        cleaningTips?.let { tips ->
            tipRotationJob = sceneScope.launch {
                while (currentState == STATE_CLEANING && isSceneActive()) {
                    delay(4000) // 每4秒轮换一次，稍微慢一点

                    if (currentState == STATE_CLEANING && isSceneActive()) {
                        currentTipIndex = (currentTipIndex + 1) % tips.size
                        binding?.tipText?.text = tips[currentTipIndex]
                    }
                }
            }
        }
    }

    /**
     * 开始进入动画
     */
    private fun startEnterAnimation() {
        val enterAnimation = AnimationUtils.loadAnimation(requireActivity(), R.anim.screen_clean_enter)
        binding?.root?.startAnimation(enterAnimation)
    }

    /**
     * 停止所有协程 - 简化版本
     */
    private fun stopAllAnimations() {
        MyLog.d(TAG, "stopAllAnimations: 停止所有协程")

        // 取消所有协程
        cleaningJob?.cancel()
        exitCountdownJob?.cancel()
        completionJob?.cancel()
        tipRotationJob?.cancel()

        MyLog.d(TAG, "所有协程已停止")
    }

    // ==================== 生命周期方法 ====================

    override fun onDestroyView() {
        MyLog.d(TAG, "onDestroyView: 清理资源")

        // 停止所有协程
        stopAllAnimations()

        // 取消协程作用域
        sceneScope.cancel()

        // 清理资源
        cleaningTips = null

        // 清理ViewBinding
        binding = null

        super.onDestroyView()
        MyLog.d(TAG, "资源清理完成")
    }

    override fun onPause() {
        super.onPause()
        MyLog.d(TAG, "onPause: 暂停屏幕清洁Scene")
    }

    override fun onResume() {
        super.onResume()
        MyLog.d(TAG, "onResume: 恢复屏幕清洁Scene")
    }

    // ==================== Kotlin扩展函数 ====================

    /**
     * View淡入动画扩展函数
     */
    private fun View.fadeIn(duration: Long = 200, onComplete: (() -> Unit)? = null) {
        alpha = 0f
        isVisible = true
        animate()
            .alpha(1f)
            .setDuration(duration)
            .withEndAction { onComplete?.invoke() }
    }



    /**
     * 安全执行扩展函数
     */
    private inline fun <T> T?.safeExecute(action: T.() -> Unit) {
        this?.action()
    }

    // ==================== 辅助方法 ====================

    /**
     * 检查Scene是否仍然活跃
     */
    private fun isSceneActive(): Boolean {
        return try {
            binding != null &&
            view != null &&
            getNavigationScene(this) != null
        } catch (e: Exception) {
            MyLog.w(TAG, "检查Scene状态时发生异常: ${e.message}")
            false
        }
    }

    /**
     * 安全的延迟退出 - 使用协程和生命周期检查
     */
    private fun safeDelayedExit(delayMs: Long) {
        completionJob = sceneScope.launch {
            delay(delayMs)

            // 检查Scene是否仍然活跃
            if (isSceneActive()) {
                exitDirectly()
            } else {
                MyLog.d(TAG, "Scene已不活跃，跳过退出操作")
            }
        }
    }

}
