package com.smartcar.easylauncher.modules.tpms.protocol.base.model;

/**
 * 轮胎状态数据模型
 */
public class TireStatusData implements TpmsData {
    
    /**
     * 轮胎位置枚举
     */
    public enum Position {
        LEFT_FRONT(0),    // 左前 0x00
        RIGHT_FRONT(1),   // 右前 0x01
        LEFT_BACK(16),     // 左后 0x10
        RIGHT_BACK(17),    // 右后 0x11
        SPARE(5);         // 备胎 0x05
        
        private final int value;
        
        Position(int value) {
            this.value = value;
        }
        
        public int getValue() {
            return value;
        }
        
        public static Position fromValue(int value) {
            for (Position position : values()) {
                if (position.value == value) {
                    return position;
                }
            }
            throw new IllegalArgumentException("Unknown position value: " + value);
        }
    }
    
    /**
     * 警告状态位定义
     * 使用位掩码表示多个告警状态
     */
    public static final int WARNING_NONE = 0x00;         // 0000 0000 无告警
    public static final int WARNING_SIGNAL_LOST = 0x20;  // 0010 0000 信号丢失
    public static final int WARNING_HIGH_TEMP = 0x10;    // 0001 0000 高温
    public static final int WARNING_HIGH_PRESSURE = 0x08;// 0000 1000 高压
    public static final int WARNING_LOW_PRESSURE = 0x04; // 0000 0100 低压
    public static final int WARNING_LEAKAGE = 0x02;      // 0000 0010 漏气
    public static final int WARNING_LOW_BATTERY = 0x01;  // 0000 0001 低电量

    /**
     * 轮胎状态码定义
     */
    public static final int STATUS_NORMAL = 0;           // 正常状态
    public static final int STATUS_PRESSURE_LOW = 1;     // 胎压过低
    public static final int STATUS_PRESSURE_HIGH = 2;    // 胎压过高
    public static final int STATUS_LEAKAGE = 3;         // 漏气状态
    public static final int STATUS_BATTERY_LOW = 4;      // 电池电量低
    public static final int STATUS_TEMPERATURE_HIGH = 5; // 温度过高
    
    /**
     * 轮胎位置
     */
    private final Position position;
    /**
     * 压力值 (单位: kPa)
     */
    private final float pressure;
    /**
     * 压力单位
     */
    private final String pressureUnit;
    /**
     * 温度值 (单位: ℃)
     */
    private final int temperature;
    /**
     * 温度单位
     */
    private final String temperatureUnit;
    /**
     * 电压值 (单位: V)
     */
    private final String voltage;
    /**
     * 电量百分比 (0-100)
     */
    private final int battery;
    /**
     * 传感器ID
     */
    private final String sensorId;
    /**
     * 警告信息
     */
    private final int warningStatus;  // 原始告警状态
    private final int displayStatus;  // 显示状态码
    // 协议版本
    private final int protocolVersion;
    
    private TireStatusData(Builder builder) {
        this.position = builder.position;
        this.pressure = builder.pressure;
        this.pressureUnit = builder.pressureUnit;
        this.temperature = builder.temperature;
        this.temperatureUnit = builder.temperatureUnit;
        this.voltage = builder.voltage;
        this.battery = builder.battery;
        this.sensorId = builder.sensorId;
        this.warningStatus = builder.warningStatus;
        this.displayStatus = builder.displayStatus;
        this.protocolVersion = builder.protocolVersion;
    }
    
    @Override
    public DataType getType() {
        return DataType.TIRE_STATUS;
    }
    
    // Getters
    public Position getPosition() { return position; }
    public float getPressure() { return pressure; }
    public int getTemperature() { return temperature; }
    public String getVoltage() { return voltage; }
    public int getBattery() { return battery; }
    public String getSensorId() { return sensorId; }
    public int getWarningStatus() { return warningStatus; }
    public int getDisplayStatus() { return displayStatus; }
    public int getProtocolVersion() { return protocolVersion; }

    public String getPressureUnit() {
        return pressureUnit;
    }

    public String getTemperatureUnit() {
        return temperatureUnit;
    }

    /**
     * Builder模式构建器
     */
    public static class Builder {
        private Position position;
        private float pressure;
        private String pressureUnit;
        private int temperature;
        private String temperatureUnit;
        private String voltage;
        private int battery;
        private String sensorId;
        private int warningStatus = WARNING_NONE;  // 改为int类型
        private int displayStatus = STATUS_NORMAL;
        private int protocolVersion;
        
        public Builder position(Position position) {
            this.position = position;
            return this;
        }
        
        public Builder pressure(float pressure) {
            this.pressure = pressure;
            return this;
        }
        public Builder pressureUnit(String pressureUnit) {
            this.pressureUnit = pressureUnit;
            return this;
        }
        
        public Builder temperature(int temperature) {
            this.temperature = temperature;
            return this;
        }

        public Builder temperatureUnit(String temperatureUnit) {
            this.temperatureUnit = temperatureUnit;
            return this;
        }
        
        public Builder voltage(String voltage) {
            this.voltage = voltage;
            return this;
        }
        
        public Builder battery(int battery) {
            this.battery = battery;
            return this;
        }
        
        public Builder sensorId(String sensorId) {
            this.sensorId = sensorId;
            return this;
        }
        
        public Builder warningStatus(byte warningStatus) {  // 接受byte类型参数
            this.warningStatus = warningStatus & 0xFF;  // 转换为无符号int
            return this;
        }
        
        public Builder displayStatus(int displayStatus) {
            this.displayStatus = displayStatus;
            return this;
        }
        
        public Builder protocolVersion(int protocolVersion) {
            this.protocolVersion = protocolVersion;
            return this;
        }
        
        public TireStatusData build() {
            return new TireStatusData(this);
        }
    }
    
    @Override
    public String toString() {
        return String.format("TireStatusData{position=%s, pressure=%.1f, pressureUnit='%s', " +
                "temperature=%d, temperatureUnit='%s', voltage='%s', battery=%d, sensorId='%s', " +
                "warningStatus=%d, displayStatus=%d, protocolVersion=%d}",
                position, pressure, pressureUnit, temperature, temperatureUnit, 
                voltage, battery, sensorId, warningStatus, displayStatus, protocolVersion);
    }
} 