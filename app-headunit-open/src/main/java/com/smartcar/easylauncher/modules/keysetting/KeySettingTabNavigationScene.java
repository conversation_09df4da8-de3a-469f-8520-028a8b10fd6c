package com.smartcar.easylauncher.modules.keysetting;

import android.annotation.SuppressLint;
import android.os.Bundle;

import androidx.annotation.NonNull;

import com.bytedance.scene.Scene;
import com.bytedance.scene.navigation.NavigationScene;
import com.bytedance.scene.navigation.NavigationSceneOptions;
import com.bytedance.scene.utlity.SceneInstanceUtility;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.widget.navigation.TabNavigationViewScene;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;


/**
 *
 * 按键设置导航场景
 * <AUTHOR>
 *
 */
public class KeySettingTabNavigationScene extends TabNavigationViewScene {

    @Override
    protected int getTabItemLayout() {
        return R.layout.layout_text_view;
    }

    @Override
    protected List<String> getNavigationTitles() {
        List<String> titles = new ArrayList<>();
        titles.add("发现");
        titles.add("我的按键");
        return titles;
    }

    @Override
    protected String getTitleName() {
        return "智能按键";
    }

    @NonNull
    @Override
    @SuppressLint("RestrictedApi")
    public LinkedHashMap<Integer, Scene> getSceneMap() {
        LinkedHashMap<Integer, Scene> linkedHashMap = new LinkedHashMap<>();

        // 为发现页面创建并配置场景
        NavigationScene navigationScene = (NavigationScene) SceneInstanceUtility.getInstanceFromClass(NavigationScene.class,
                new NavigationSceneOptions(FindKeyMappingScene.class, getBundle(0)).toBundle()
        );
        linkedHashMap.put(0, navigationScene);

        // 为我的按键页面创建并配置场景
        navigationScene = (NavigationScene) SceneInstanceUtility.getInstanceFromClass(NavigationScene.class,
                new NavigationSceneOptions(KeyHomeScene.class, getBundle(1)).toBundle()
        );
        linkedHashMap.put(1, navigationScene);

        return linkedHashMap;
    }

    /**
     * 创建一个包含索引的Bundle对象
     *
     * @param index 要放入Bundle的索引值
     * @return 包含索引的Bundle
     */
    private Bundle getBundle(int index) {
        Bundle bundle = new Bundle();
        bundle.putInt("index", index);
        return bundle;
    }
}
