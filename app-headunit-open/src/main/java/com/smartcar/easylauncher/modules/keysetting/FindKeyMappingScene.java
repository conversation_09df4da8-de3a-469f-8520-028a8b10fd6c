package com.smartcar.easylauncher.modules.keysetting;

import static com.bytedance.scene.navigation.NavigationSceneGetter.requireNavigationScene;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.chad.library.adapter4.QuickAdapterHelper;
import com.chad.library.adapter4.layoutmanager.QuickGridLayoutManager;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.setting.FindKeyMappingTopHeaderAdapter;
import com.smartcar.easylauncher.shared.adapter.setting.RecommendKeyMapAdapter;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.databinding.SceneFindKeyMappingCoordinatorBinding;
import com.smartcar.easylauncher.data.database.entity.KeyMapModel;
import com.smartcar.easylauncher.shared.dialog.GeneralDialog;
import com.smartcar.easylauncher.core.manager.KeyMapManager;
import com.smartcar.easylauncher.data.model.system.RecommendKeyMapModel;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.RecommendKeyMapDataGenerator;

import java.util.ArrayList;
import java.util.List;

/**
 * 发现推荐按键映射页面
 * 参考智能任务发现页面设计
 * 
 * <AUTHOR>
 * @date 2024/12/18
 */
public class FindKeyMappingScene extends BaseScene {
    public static final String TAG = FindKeyMappingScene.class.getSimpleName();

    private RecommendKeyMapAdapter mRecommendKeyMapAdapter;
    private FindKeyMappingTopHeaderAdapter mHeaderAdapter;
    private List<RecommendKeyMapModel> mRecommendKeyMapList;
    private SceneFindKeyMappingCoordinatorBinding mBinding;
    private Handler mMainHandler;

    @NonNull
    @Override
    protected ViewGroup onCreateNewView(@NonNull LayoutInflater layoutInflater, @Nullable ViewGroup viewGroup, @Nullable Bundle bundle) {
        mBinding = SceneFindKeyMappingCoordinatorBinding.inflate(layoutInflater, viewGroup, false);
        return mBinding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mMainHandler = new Handler(Looper.getMainLooper());

        setupViews();
        loadRecommendKeyMaps();
    }

    /**
     * 设置视图组件
     */
    private void setupViews() {
        setupRecyclerView();
        setupAdapter();
        setupSwipeRefresh();
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerView() {
        // 设置网格布局 - 2列显示，与智能任务页面保持一致
        QuickGridLayoutManager layoutManager = new QuickGridLayoutManager(requireActivity(), 2);
        mBinding.rvKeyMapping.setLayoutManager(layoutManager);

        // RecyclerView性能优化配置
        mBinding.rvKeyMapping.setHasFixedSize(true);
        mBinding.rvKeyMapping.setItemViewCacheSize(20);
    }

    /**
     * 设置适配器和空视图
     */
    private void setupAdapter() {
        mRecommendKeyMapAdapter = new RecommendKeyMapAdapter();
        mHeaderAdapter = new FindKeyMappingTopHeaderAdapter();
        QuickAdapterHelper helper = new QuickAdapterHelper.Builder(mRecommendKeyMapAdapter).build();
        helper.addBeforeAdapter(mHeaderAdapter);
        
        // 设置点击事件 - 打开按键映射详情页面
        mRecommendKeyMapAdapter.setOnItemClickListener((adapter, view, position) -> {
            RecommendKeyMapModel recommendKeyMap = adapter.getItem(position);
            navigateToKeyMappingDetails(recommendKeyMap);
        });

        // 设置长按事件 - 显示预览对话框
        mRecommendKeyMapAdapter.setOnItemLongClickListener((adapter, view, position) -> {
            RecommendKeyMapModel recommendKeyMap = adapter.getItem(position);
            showKeyMapPreviewDialog(recommendKeyMap);
            return true;
        });

        // 设置添加按钮点击事件 - 快速添加按键映射
        mRecommendKeyMapAdapter.addOnItemChildClickListener(R.id.tv_add_button, (adapter, view, position) -> {
            RecommendKeyMapModel recommendKeyMap = adapter.getItem(position);
            showQuickAddConfirmation(recommendKeyMap);
        });

        // 初始状态禁用空视图，避免在加载时显示
        mRecommendKeyMapAdapter.setStateViewEnable(false);
        mRecommendKeyMapAdapter.setUseStateViewSize(true);
        View emptyView = LayoutInflater.from(requireActivity()).inflate(R.layout.find_key_mapping_empty_view, mBinding.rvKeyMapping, false);
        mRecommendKeyMapAdapter.setStateView(emptyView);

        mBinding.rvKeyMapping.setAdapter(helper.getAdapter());
    }

    /**
     * 设置下拉刷新
     */
    private void setupSwipeRefresh() {
        mBinding.swipeRefreshLayout.setOnRefreshListener(this::loadRecommendKeyMaps);
        mBinding.swipeRefreshLayout.setColorSchemeResources(
                R.color.bluetooth_connect_color,
                R.color.compass_circle_left_bottom_color
        );
    }

    /**
     * 加载推荐按键映射数据
     */
    private void loadRecommendKeyMaps() {
        MyLog.d(TAG, "loadRecommendKeyMaps: 开始加载推荐按键映射");
        
        // 显示加载状态
        mBinding.swipeRefreshLayout.setRefreshing(true);
        mHeaderAdapter.updateKeyMapCount(0, true);

        // 模拟网络请求延迟
        mMainHandler.postDelayed(() -> {
            try {
                // 生成推荐按键映射数据
                mRecommendKeyMapList = RecommendKeyMapDataGenerator.generateRecommendKeyMaps();
                
                // 更新UI
                updateUI();
                
                MyLog.d(TAG, "loadRecommendKeyMaps: 加载完成，数量=" + mRecommendKeyMapList.size());
            } catch (Exception e) {
                MyLog.e(TAG, "loadRecommendKeyMaps: 加载失败", e);
                handleLoadError();
            }
        }, 1000); // 延迟1秒模拟网络请求
    }

    /**
     * 更新UI显示
     */
    private void updateUI() {
        mMainHandler.post(() -> {
            // 停止刷新动画
            mBinding.swipeRefreshLayout.setRefreshing(false);
            
            if (mRecommendKeyMapList != null && !mRecommendKeyMapList.isEmpty()) {
                // 有数据时更新适配器
                mRecommendKeyMapAdapter.submitList(mRecommendKeyMapList);
                mRecommendKeyMapAdapter.setStateViewEnable(false);
                mHeaderAdapter.updateKeyMapCount(mRecommendKeyMapList.size(), false);
                MyLog.d(TAG, "updateUI: 显示" + mRecommendKeyMapList.size() + "个推荐按键映射");
            } else {
                // 无数据时显示空视图
                mRecommendKeyMapAdapter.submitList(new ArrayList<>());
                mRecommendKeyMapAdapter.setStateViewEnable(true);
                mHeaderAdapter.updateKeyMapCount(0, false);
                MyLog.d(TAG, "updateUI: 无推荐按键映射数据，显示空视图");
            }
        });
    }

    /**
     * 处理加载错误
     */
    private void handleLoadError() {
        mMainHandler.post(() -> {
            mBinding.swipeRefreshLayout.setRefreshing(false);
            mRecommendKeyMapAdapter.submitList(new ArrayList<>());
            mRecommendKeyMapAdapter.setStateViewEnable(true);
            mHeaderAdapter.updateKeyMapCount(0, false);
            MToast.makeTextShort("加载推荐按键映射失败，请稍后重试");
        });
    }

    /**
     * 导航到按键映射详情页面
     */
    private void navigateToKeyMappingDetails(RecommendKeyMapModel recommendKeyMap) {
        MyLog.d(TAG, "navigateToKeyMappingDetails: " + recommendKeyMap.getKeyMapName());

        // 创建Bundle参数
        Bundle arguments = FindKeyMappingDetailsScene.createArguments(recommendKeyMap);

        // 导航到详情页面
        requireNavigationScene(this).push(FindKeyMappingDetailsScene.class, arguments);
    }

    /**
     * 显示按键映射预览对话框（用于长按快速预览）
     */
    private void showKeyMapPreviewDialog(RecommendKeyMapModel recommendKeyMap) {
        MyLog.d(TAG, "showKeyMapPreviewDialog: " + recommendKeyMap.getKeyMapName());

        FragmentActivity fragmentActivity = (FragmentActivity) requireActivity();
        GeneralDialog.showGeneralDialog(
                fragmentActivity.getSupportFragmentManager(),
                requireActivity(),
                "按键映射预览",
                buildKeyMapPreviewMessage(recommendKeyMap),
                "添加到我的按键",
                "取消",
                new GeneralDialog.DialogClickListener() {
                    @Override
                    public void onPositiveClick() {
                        addKeyMapToMyKeys(recommendKeyMap);
                    }

                    @Override
                    public void onNegativeClick() {
                        // 取消操作，不做任何处理
                    }
                }
        );
    }

    /**
     * 构建按键映射预览消息
     */
    private String buildKeyMapPreviewMessage(RecommendKeyMapModel recommendKeyMap) {
        StringBuilder message = new StringBuilder();
        message.append("按键映射名称：").append(recommendKeyMap.getKeyMapName()).append("\n\n");
        message.append("按键：").append(recommendKeyMap.getKeyName()).append("\n\n");
        message.append("功能描述：").append(recommendKeyMap.getDescription()).append("\n\n");
        message.append("分类：").append(recommendKeyMap.getCategory()).append("\n\n");
        message.append("评分：").append(recommendKeyMap.getRatingStars()).append("\n\n");
        message.append("使用人数：").append(recommendKeyMap.getUsageCount()).append("人");
        return message.toString();
    }

    /**
     * 显示快速添加确认对话框
     */
    private void showQuickAddConfirmation(RecommendKeyMapModel recommendKeyMap) {
        MyLog.d(TAG, "showQuickAddConfirmation: " + recommendKeyMap.getKeyMapName());

        FragmentActivity fragmentActivity = (FragmentActivity) requireActivity();
        GeneralDialog.showGeneralDialog(
                fragmentActivity.getSupportFragmentManager(),
                requireActivity(),
                "快速添加",
                "确定要添加按键映射「" + recommendKeyMap.getKeyMapName() + "」吗？",
                "确定",
                "取消",
                new GeneralDialog.DialogClickListener() {
                    @Override
                    public void onPositiveClick() {
                        addKeyMapToMyKeys(recommendKeyMap);
                    }

                    @Override
                    public void onNegativeClick() {
                        // 取消操作，不做任何处理
                    }
                }
        );
    }

    /**
     * 添加按键映射到我的按键
     */
    private void addKeyMapToMyKeys(RecommendKeyMapModel recommendKeyMap) {
        MyLog.d(TAG, "addKeyMapToMyKeys: " + recommendKeyMap.getKeyMapName());
        
        try {
            // 转换为KeyMapModel
            KeyMapModel keyMapModel = convertToKeyMapModel(recommendKeyMap);
            
            // 保存到数据库
            boolean success = KeyMapManager.getInstance(requireActivity()).saveKeyMap(keyMapModel);
            
            if (success) {
                MToast.makeTextShort("按键映射「" + recommendKeyMap.getKeyMapName() + "」添加成功");
                MyLog.d(TAG, "addKeyMapToMyKeys: 添加成功");
            } else {
                MToast.makeTextShort("添加失败，请重试");
                MyLog.e(TAG, "addKeyMapToMyKeys: 添加失败");
            }
        } catch (Exception e) {
            MyLog.e(TAG, "addKeyMapToMyKeys: 添加异常", e);
            MToast.makeTextShort("添加失败：" + e.getMessage());
        }
    }

    /**
     * 转换推荐按键映射为按键映射模型
     */
    private KeyMapModel convertToKeyMapModel(RecommendKeyMapModel recommendKeyMap) {
        KeyMapModel keyMapModel = new KeyMapModel();
        keyMapModel.setKeyCode(recommendKeyMap.getKeyCode());
        keyMapModel.setKeyName(recommendKeyMap.getKeyName());
        keyMapModel.setActionType(recommendKeyMap.getActionType());
        keyMapModel.setActionData(recommendKeyMap.getActionData());
        keyMapModel.setExtraData(recommendKeyMap.getExtraData());
        keyMapModel.setEnabled(1); // 默认启用
        keyMapModel.setCreateTime(System.currentTimeMillis());
        keyMapModel.setUpdateTime(System.currentTimeMillis());
        return keyMapModel;
    }

    @Override
    public void onResume() {
        super.onResume();
        MyLog.d(TAG, "onResume: 发现按键映射页面恢复");

        // 如果还没有加载过数据，则加载
        if (mRecommendKeyMapList == null || mRecommendKeyMapList.isEmpty()) {
            MyLog.d(TAG, "首次加载推荐按键映射数据");
            loadRecommendKeyMaps();
        } else {
            MyLog.d(TAG, "推荐按键映射数据已存在，跳过加载");
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        MyLog.d(TAG, "onDestroyView: 清理发现按键映射页面资源");

        // 清理资源
        if (mRecommendKeyMapAdapter != null) {
            mRecommendKeyMapAdapter = null;
        }
        if (mHeaderAdapter != null) {
            mHeaderAdapter = null;
        }
        mRecommendKeyMapList = null;
        mBinding = null;
    }
}
