package com.smartcar.easylauncher.modules.tpms.protocol;

import com.smartcar.easylauncher.modules.tpms.device.TpmsDevice;
import com.smartcar.easylauncher.modules.tpms.protocol.base.Protocol;
import com.smartcar.easylauncher.modules.tpms.protocol.v3.V3Protocol;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.thread.ThreadPoolUtil;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 协议检测器
 */
public class ProtocolDetector {
    private static final String TAG = "ProtocolDetector";
    
    // V3协议帧头
    private static final byte V3_FRAME_HEADER_1 = (byte) 0x55;
    private static final byte V3_FRAME_HEADER_2 = (byte) 0xAA;
    
    // 协议检测超时时间
    private static final int DETECT_TIMEOUT = 5000; // 5秒超时
    private static final int DETECT_RETRY_INTERVAL = 1000; // 重试间隔1秒
    private static final int MAX_DETECT_RETRIES = 3; // 最大重试次数
    private static final ThreadPoolExecutor executor = ThreadPoolUtil.getThreadPoolExecutor();
    
    /**
     * 检测并创建合适的协议实现
     */
    public static Protocol detect(TpmsDevice device) throws UnsupportedProtocolException {
        final CountDownLatch latch = new CountDownLatch(1);
        final Protocol[] detectedProtocol = new Protocol[1];
        final AtomicInteger retryCount = new AtomicInteger(0);
        
        // 创建协议实例
        V3Protocol protocol = new V3Protocol(device);
        
        // 设置数据回调
        device.setDataCallback(buffer -> {
            try {
                byte[] data = new byte[buffer.remaining()];
                buffer.get(data);
                
                // 打印接收到的数据
                StringBuilder sb = new StringBuilder("接收数据: ");
                for (byte b : data) {
                    sb.append(String.format("%02X ", b));
                }
                MyLog.d(TAG, sb.toString());
                
                // 检查帧头
                if (data.length >= 2 && 
                    data[0] == V3_FRAME_HEADER_1 && 
                    data[1] == V3_FRAME_HEADER_2) {
                    
                    MyLog.d(TAG, "检测到V3协议");
                    detectedProtocol[0] = protocol;
                    latch.countDown();
                }
                
            } catch (Exception e) {
                MyLog.e(TAG, "数据处理异常: " + e.getMessage());
            }
        });
        
        // 使用线程池执行检测任务
        executor.execute(() -> {
            while (true) {
                try {
                    if (!(retryCount.get() < MAX_DETECT_RETRIES && !latch.await(0, TimeUnit.MILLISECONDS)))
                        break;
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                try {
                    // 发送检测命令
                    byte[] detectCommand = new byte[] {V3_FRAME_HEADER_1, V3_FRAME_HEADER_2};
                    device.send(detectCommand);
                    
                    // 等待响应
                    if (latch.await(DETECT_TIMEOUT, TimeUnit.MILLISECONDS)) {
                        break;
                    }
                    
                    // 增加重试计数
                    int retries = retryCount.incrementAndGet();
                    if (retries < MAX_DETECT_RETRIES) {
                        MyLog.w(TAG, String.format("协议检测超时，第%d次重试", retries));
                        Thread.sleep(DETECT_RETRY_INTERVAL);
                    }
                    
                } catch (Exception e) {
                    MyLog.e(TAG, "协议检测异常: " + e.getMessage());
                    retryCount.incrementAndGet();
                }
            }
        });
        
        try {
            // 等待检测完成
            if (!latch.await(DETECT_TIMEOUT * MAX_DETECT_RETRIES, TimeUnit.MILLISECONDS)) {
                throw new UnsupportedProtocolException("协议检测超时");
            }
            
            if (detectedProtocol[0] == null) {
                throw new UnsupportedProtocolException("未检测到支持的协议");
            }
            
            return detectedProtocol[0];
            
        } catch (InterruptedException e) {
            throw new UnsupportedProtocolException("协议检测被中断");
        }
    }
}

/**
 * 不支持的协议异常
 */
class UnsupportedProtocolException extends Exception {
    public UnsupportedProtocolException(String message) {
        super(message);
    }
} 