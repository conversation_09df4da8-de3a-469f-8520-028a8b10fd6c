package com.smartcar.easylauncher.modules.touch.service;

import android.app.Notification;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;
import android.view.KeyEvent;
import android.widget.Toast;

import androidx.annotation.Nullable;

import com.smartcar.easylauncher.app.App;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.manager.FloatManager;
import com.smartcar.easylauncher.modules.touch.FloatTimeTaskHolder;
import com.smartcar.easylauncher.modules.touch.FloatViewLiveData;
import com.smartcar.easylauncher.modules.touch.constant.AccessibilityConstant;
import com.smartcar.easylauncher.modules.touch.controller.FloatButtonWindowController;
import com.smartcar.easylauncher.modules.touch.controller.FloatPanelWindowController;
import com.smartcar.easylauncher.modules.touch.floating.FloatWindowManager;
import com.smartcar.easylauncher.modules.touch.setting.FloatWindowSetting;
import com.smartcar.easylauncher.modules.touch.util.AppBroadcastManager;
import com.smartcar.easylauncher.modules.touch.util.json.GsonHandlerImpl;
import com.smartcar.easylauncher.modules.touch.widget.ControlPanelView;
import com.smartcar.easylauncher.home.main.HomeActivity;
import com.smartcar.easylauncher.shared.utils.music.MusicControllerTools;
import com.smartcar.easylauncher.shared.utils.MyLog;

public class FloatWindowService extends Service {
    private FloatButtonWindowController mFloatButtonVC;
    private FloatPanelWindowController mFloatPanelVC;
    private FloatTimeTaskHolder mFloatTimeTaskHolder;
    private boolean isFirst = true;
    private BroadcastReceiver mActionReceiver;
    private static final int NOTIFICATION_ID = 1;
    public static class Action {
        public static final String ACTION_SHOW_FLOATING_WINDOW = "com.zh.touchassistant.SHOW_FLOATING_WINDOW";
        public static final String ACTION_HIDE_FLOATING_WINDOW = "com.zh.touchassistant.HIDE_FLOATING_WINDOW";
        public static final String ACTION_START_LOOP_CHECK = "com.zh.touchassistant.START_LOOP_CHECK";
        public static final String ACTION_STOP_LOOP_CHECK = "com.zh.touchassistant.STOP_LOOP_CHECK";
    }

    @Override
    public void onCreate() {
        super.onCreate();
        mActionReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (intent.getAction() == null) {
                    return;
                }
                int mode = FloatManager.getFloatMode();
                if (mode == 0) { // 普通模式
                    switch (intent.getAction()) {
                        case AccessibilityConstant.Action.ACTION_DO_BACK:
                            // 模拟返回点击
                            // 这里需要您自己实现模拟返回点击的逻辑
                            MusicControllerTools.sendKeyEvent(KeyEvent.KEYCODE_BACK);
                            break;
                        case AccessibilityConstant.Action.ACTION_DO_GO_HOME:
                            // 主页模拟回到HomeActivity
                            Intent homeIntent = new Intent(context, HomeActivity.class);
                            homeIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            context.startActivity(homeIntent);
                            break;
                        default:
                            // 提示普通模式不可用
                            Toast.makeText(context, "普通模式不支持此操作", Toast.LENGTH_SHORT).show();
                            break;
                    }
                }
            }
        };
        AppBroadcastManager.registerReceiver(this,
                mActionReceiver,
                AccessibilityConstant.Action.ACTION_DO_BACK,
                AccessibilityConstant.Action.ACTION_PULL_DOWN_NOTIFICATION_BAR,
                AccessibilityConstant.Action.ACTION_DO_GO_HOME,
                AccessibilityConstant.Action.ACTION_DO_GO_TASK);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // 创建一个通知
        Notification notification = new Notification.Builder(this)
                .setContentTitle("悬浮球服务")
                .setContentText("悬浮球服务正在运行")
                .setSmallIcon(R.mipmap.ic_launcher)
                .build();

        // 将服务变成前台服务
        startForeground(NOTIFICATION_ID, notification);

        if (intent == null) {
            return super.onStartCommand(intent, flags, startId);
        }
        if (FloatWindowService.Action.ACTION_SHOW_FLOATING_WINDOW.equals(intent.getAction())) {
            showFloatWindow();
            MyLog.v("FloatWindowService", "onStartCommand: 显示悬浮球");
        } else if (FloatWindowService.Action.ACTION_HIDE_FLOATING_WINDOW.equals(intent.getAction())) {
            hideFloatWindow();
            MyLog.v("FloatWindowService", "onStartCommand: 隐藏悬浮球");
        } else if (FloatWindowService.Action.ACTION_START_LOOP_CHECK.equals(intent.getAction())) {
            if (mFloatTimeTaskHolder != null) {
                mFloatTimeTaskHolder.startLoopCheck();
            }
        } else if (FloatWindowService.Action.ACTION_START_LOOP_CHECK.equals(intent.getAction())) {
            if (mFloatTimeTaskHolder != null) {
                mFloatTimeTaskHolder.cancelLoopCheck();
            }
        }

        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mFloatTimeTaskHolder != null) {
            mFloatTimeTaskHolder.dispatchDestroy();
        }

    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private void showFloatWindow() {
        FloatViewLiveData floatViewLiveData = FloatWindowSetting.getFloatViewLiveData();
        if (floatViewLiveData == null) {
            FloatWindowSetting
                    .getInstance()
                    .initFloatWindowActions(new GsonHandlerImpl());
            floatViewLiveData = FloatWindowSetting.getFloatViewLiveData();
        }
        if (isFirst) {
            App assistantApp = (App) getApplication();
            FloatWindowManager floatWindowManager = new FloatWindowManager(this);
            //填充和浮动面板浮动按钮
            mFloatPanelVC = new FloatPanelWindowController(this, floatWindowManager);
            mFloatButtonVC = new FloatButtonWindowController(this, floatWindowManager, mFloatPanelVC);
            //mFloatForegroundVC = new FloatForegroundWindowController(this, floatWindowManager);
            mFloatButtonVC.setOnFloatButtonPositionUpdateListener(new FloatButtonWindowController.OnFloatButtonPositionUpdateListener() {
                @Override
                public void onFloatButtonPositionUpdate(int newX, int newY) {
                    mFloatPanelVC.followButtonPosition(newX, newY);
                }
            });
            mFloatPanelVC.setOnPanelSizeChangeCallback(new ControlPanelView.OnPanelSizeChangeCallback() {
                @Override
                public void onPanelSizeChange(int newWidth, int newHeight) {
                    //使用该监听，主要是为了解决第一次进入时，没有手动移动过悬浮球，控制面板没有跟随位置的问题
                    int buttonX = mFloatButtonVC.getFloatWindow().getX();
                    int buttonY = mFloatButtonVC.getFloatWindow().getY();
                    mFloatPanelVC.followButtonPosition(buttonX, buttonY);
                }
            });
            mFloatButtonVC.setOnStatusChangeListener(new FloatButtonWindowController.OnStatusChangeListener() {
                @Override
                public boolean onPrepareStatusChange(int prepareStatus) {
                    return mFloatPanelVC.isCanChangeStatus();
                }

                @Override
                public void onStatusChange(int newStatus) {
                }
            });
            mFloatTimeTaskHolder = FloatTimeTaskHolder.create(FloatWindowService.this.getApplicationContext(), mFloatButtonVC);
            // FloatViewLiveData floatViewLiveData = assistantApp.getFloatViewLiveData();

            floatViewLiveData.addOnDataChangeCallback(new FloatViewLiveData.OnDataChangeCallback() {
                @Override
                public void onDataChange(boolean isOpen) {
                    //这里统一做UI切换
                    if (isOpen) {
                        mFloatButtonVC.open();
                        mFloatPanelVC.open();
                        AppBroadcastManager
                                .sendBroadcast(FloatWindowService.this,
                                        AccessibilityConstant.Action.ACTION_FLOAT_BUTTON_OPEN);
                    } else {
                        mFloatButtonVC.off();
                        mFloatPanelVC.off();
                        AppBroadcastManager
                                .sendBroadcast(FloatWindowService.this,
                                        AccessibilityConstant.Action.ACTION_FLOAT_BUTTON_CLOSE);
                    }
                }
            });
            isFirst = false;
        } else {
            if (mFloatButtonVC != null) {
                mFloatButtonVC.showFloatWindow();
            }

        }
    }

    private void hideFloatWindow() {
        if (mFloatButtonVC != null) {
            mFloatButtonVC.hideFloatWindow();
        }

    }
}