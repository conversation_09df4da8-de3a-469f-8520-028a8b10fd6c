package com.smartcar.easylauncher.modules.expense;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;
import static com.bytedance.scene.navigation.NavigationSceneGetter.requireNavigationScene;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.bytedance.scene.Scene;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.databinding.SceneCostDetailsBinding;
import com.smartcar.easylauncher.data.database.dbmager.CostDbManager;
import com.smartcar.easylauncher.data.database.entity.Expense;
import com.smartcar.easylauncher.shared.dialog.GeneralDialog;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.data.model.common.ItemNode;

public class ExpenseDetailsScene extends Scene {
    private SceneCostDetailsBinding binding;

    public static ItemNode mItemNode;
    private Bundle arguments;
    private Expense expense;
    public ExpenseDetailsScene(ItemNode itemNode) {
        mItemNode = itemNode;
    }

    @NonNull
    @Override
    public View onCreateView(@NonNull LayoutInflater layoutInflater, @NonNull ViewGroup viewGroup, @Nullable Bundle bundle) {
        binding = SceneCostDetailsBinding.inflate(getLayoutInflater());
        return binding.getRoot();
    }


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initData();
        initView();
    }

    private void initView() {
        binding.btCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                getNavigationScene(ExpenseDetailsScene.this).pop();
            }
        });
        binding.btnSingleExpenseUpdate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                updateExpense();
            }
        });

        binding.btnSingleExpenseDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showAlertDialog();
            }
        });
    }

    private void showAlertDialog() {
        FragmentActivity activity = (FragmentActivity) requireActivity();
        GeneralDialog.showConfirmDialog(activity.getSupportFragmentManager(), requireActivity(), getString(R.string.confirm_delete), new GeneralDialog.DialogClickListener() {
            @Override
            public void onPositiveClick() {
                deleteExpense();
            }

            @Override
            public void onNegativeClick() {

            }
        });

    }

    private void deleteExpense() {
        long selectedCarId = DataManager.getCarId();
        long expenseId = arguments.getLong("expenseId");

        if (selectedCarId != -1L && expenseId != -1L) {
            CostDbManager.getInstance().deleteExpenseByExpenseId(expenseId);
            requireNavigationScene(ExpenseDetailsScene.this).pop();
        }
    }

    private void updateExpense() {
        Bundle bundle = new Bundle();
        bundle.putLong("expenseId", mItemNode.getExpenseId());
        bundle.putInt(Const.FUEL_TYPE, mItemNode.getType());
        requireNavigationScene(ExpenseDetailsScene.this).push(AddExpenseScene.class, bundle);

    }

    private void initData() {
//        arguments = getArguments();
//        long expenseId = arguments.getLong("expenseId");
//        CostDbManager.getInstance().getExpenseByCarIdAndExpenseId(DataManager.getCarId(), expenseId).subscribe(expense1 -> {
         //   expense = expense1;
            setupViews();
            setupData();
   //     });

    }

    private void setupViews() {
        if (!(Const.FUEL_TYPE_GAS == mItemNode.getType())) {
            binding.containerSingleExpensePricePerLiter.setVisibility(View.GONE);
            binding.containerSingleExpenseKm.setVisibility(View.GONE);
        } else {
            binding.containerSingleExpenseDescription.setVisibility(View.GONE);
        }

        if (!(Const.FUEL_TYPE_INSURANCE == mItemNode.getType())) {
            binding.containerSingleExpenseYear.setVisibility(View.GONE);
        }
    }

    private void setupData() {
        binding.tvSingleExpenseTitle.setText(mItemNode.getTitle());
        binding.tvSingleExpenseDate.setText(mItemNode.getDate());
        binding.tvSingleExpenseTotalSpent.setText(String.valueOf(mItemNode.getSpent()));

        if (Const.FUEL_TYPE_GAS == mItemNode.getType()) {
            binding.tvSingleExpensePricePerLiter.setText(String.valueOf(mItemNode.getPricePerLiter()));
            binding.tvSingleExpenseKm.setText(String.valueOf(mItemNode.getTotalKm()));
        } else if (Const.FUEL_TYPE_INSURANCE == mItemNode.getType()) {
            binding.tvSingleExpenseKm.setText(mItemNode.getTitle().split(" ")[1]);
        }

        binding.tvSingleExpenseDescription.setText(mItemNode.getDescription());
    }

}
