package com.smartcar.easylauncher.modules.apport

import android.Manifest
import android.os.Build
import android.os.Environment
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import com.smartcar.easylauncher.shared.utils.apportutil.get
import com.smartcar.easylauncher.shared.utils.apportutil.mmkv
import com.smartcar.easylauncher.shared.utils.apportutil.put
import com.smartcar.easylauncher.shared.utils.apportutil.toast
import com.demon.qfsolution.utils.getExternalOrFilesDirPath
import com.demon.qfsolution.utils.uriToFile
import com.smartcar.easylauncher.app.App
import com.smartcar.easylauncher.core.constants.Constants
import com.smartcar.easylauncher.databinding.ActivityChangePathBinding
import com.smartcar.easylauncher.infrastructure.system.service.WebHelper
import java.io.File


/**
 * <AUTHOR>
 * date 2023/5/12
 * email <EMAIL>
 * desc
 */
class ChangePathActivity : BaseActivity<ActivityChangePathBinding>() {


    private var changePath = ""

    private val openDocumentTree = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
        registerForActivityResult(ActivityResultContracts.OpenDocumentTree()) {
            it ?: return@registerForActivityResult
            changePath = it.uriToFile()?.absolutePath ?: ""
            binding.tvChange.text = "修改后的路径：$changePath"
        }
    } else {
        TODO("VERSION.SDK_INT < LOLLIPOP")
    }

    override fun initData() {
        setToolbar("修改存储路径")
        val def: String = App.getContextInstance().getExternalOrFilesDirPath(Environment.DIRECTORY_DCIM)
        val nowPath = "当前存储路径：${mmkv.get(Constants.MMKV_STORAGE_PATH, def)}"

         if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            arrayOf(Manifest.permission.MANAGE_EXTERNAL_STORAGE)
        } else {
            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }
        binding.run {
            toolbar.ivSetting.visibility = View.GONE
            toolbar.ivWifi.visibility = View.GONE
            tvNow.text = nowPath

            btnChange.setOnClickListener {
//                PermissionX.init(this@ChangePathActivity)
//                    .permissions(*permissions)
//                    .onExplainRequestReason { scope, deniedList ->
//                        scope.showRequestReasonDialog(deniedList, "修改存储路径需要文件存储、管理权限", "好的", "取消")
//                    }
//                    .request { allGranted, _, _ ->
//                        if (!allGranted) {
//                            "没有文件存储、管理权限~".toast()
//                        } else {
//                            openDocumentTree.launch(null)
//                        }
//                    }
                openDocumentTree.launch(null)
            }

            btnSave.setOnClickListener {
                if (changePath.isEmpty()) {
                    "修改路径失败！".toast()
                    return@setOnClickListener
                }
                mmkv.put(Constants.MMKV_STORAGE_PATH, changePath)
                WebHelper.instance.dir = File(changePath)
                finish()
            }
        }
    }
}