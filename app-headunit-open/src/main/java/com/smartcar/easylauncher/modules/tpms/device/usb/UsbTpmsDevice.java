package com.smartcar.easylauncher.modules.tpms.device.usb;

import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.hardware.usb.*;
import android.os.Build;

import com.elvishew.xlog.XLog;
import com.smartcar.easylauncher.modules.tpms.device.TpmsDevice;

import java.nio.ByteBuffer;
import java.util.HashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import com.smartcar.easylauncher.shared.utils.thread.ThreadPoolUtil;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * USB TPMS设备实现类
 * <p>
 * 主要职责：
 * 1. 管理USB设备的连接和通信
 * 2. 处理USB权限申请
 * 3. 数据的收发处理
 * 4. 设备状态管理
 * </p>
 * 
 * <p>
 * 注意事项：
 * 1. USB操作需要在主线程中进行权限申请
 * 2. 数据收发使用单独的线程
 * 3. 需要处理USB连接异常和断开情况
 * 4. 资源释放需要确保完整性
 * </p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class UsbTpmsDevice implements TpmsDevice {
    
    /**
     * 日志标签
     * 用于标识日志来源
     */
    private static final String TAG = "UsbTpmsDevice";
    
    /**
     * USB权限请求Action
     * 用于注册广播接收器接收权限请求结果
     * 必须与应用包名保持一致
     */
    private static final String ACTION_USB_PERMISSION = "com.smartcar.easylauncher.USB_PERMISSION";
    
    /**
     * 支持的USB设备VID/PID列表
     * 二维数组，每组包含vendorId和productId
     * {vendorId, productId}
     * 目前支持的设备包括FTDI和CH340系列
     */
    public static final int[][] SUPPORTED_DEVICES = {
        {0x0403, 0x6001},  // FTDI FT232R
        {0x0403, 0x6015},  // FTDI FT231X
        {0x1A86, 0x7523}   // CH340
    };
    
    /**
     * 默认的VendorId
     * 使用第一个支持的设备的VendorId
     * 用于快速匹配设备
     */
    private static final int VENDOR_ID = SUPPORTED_DEVICES[0][0];
    
    /**
     * 默认的ProductId
     * 使用第一个支持的设备的ProductId
     * 用于快速匹配设备
     */
    private static final int PRODUCT_ID = SUPPORTED_DEVICES[0][1];
    
    /**
     * 应用上下文
     * 用于获取系统服务和注册广播
     * 生命周期与应用一致
     */
    private final Context context;
    
    /**
     * USB管理器
     * 用于管理USB设备的连接和通信
     * 通过系统服务获取
     */
    private final UsbManager usbManager;
    
    /**
     * USB设备实例
     * 当前连接的USB设备对象
     * 在设备连接成功后设置
     */
    private UsbDevice usbDevice;
    
    /**
     * USB设备连接
     * 用于与USB设备进行通信
     * 在设备打开成功后设置
     */
    private UsbDeviceConnection connection;
    
    /**
     * USB输入端点
     * 用于接收数据
     * 在设备打开成功后设置
     */
    private UsbEndpoint endpointIn;
    
    /**
     * USB输出端点
     * 用于发送数据
     * 在设备打开成功后设置
     */
    private UsbEndpoint endpointOut;
    
    /**
     * 数据回调接口
     * 用于向上层传递接收到的数据
     * 由上层应用设置
     */
    private DataCallback dataCallback;
    
    /**
     * 状态回调接口
     * 用于向上层报告设备状态变化
     * 由上层应用设置
     */
    private StateCallback stateCallback;
    
    /**
     * 线程池
     * 用于执行数据收发任务
     * 使用全局线程池避免资源浪费
     */
    private final ThreadPoolExecutor executor = ThreadPoolUtil.getThreadPoolExecutor();
    
    /**
     * 运行状态标志
     * volatile确保多线程可见性
     * true表示设备正在运行，false表示设备已停止
     */
    private volatile boolean isRunning;
    
    /**
     * USB权限请求同步锁
     * 用于等待权限请求结果
     * 初始值为1，权限请求完成后计数减为0
     */
    private final CountDownLatch permissionLatch = new CountDownLatch(1);
    
    /**
     * USB权限授予标志
     * 记录权限请求的结果
     * true表示权限已授予，false表示权限被拒绝
     */
    private boolean permissionGranted = false;
    
    /**
     * USB权限广播接收器
     * 处理USB权限请求的响应
     * 在权限请求时注册，请求完成后注销
     */
    private final BroadcastReceiver usbReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (ACTION_USB_PERMISSION.equals(action)) {
                synchronized (this) {
                    UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                    permissionGranted = intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false);
                    permissionLatch.countDown();
                    
                    if (permissionGranted) {
                        XLog.tag(TAG).d(  "USB权限已授予");
                    } else {
                        XLog.tag(TAG).e(  "USB权限被拒绝");
                    }
                }
            }
        }
    };
    
    /**
     * 接收缓冲区大小
     * 单位：字节
     * 根据设备协议设置
     */
    private static final int BUFFER_SIZE = 1024;
    
    /**
     * 接收超时时间
     * 单位：毫秒
     * 超时后会重试
     */
    private static final int RECEIVE_TIMEOUT = 100;
    
    /**
     * 最大重试次数
     * 连续接收超时超过此次数后尝试重新连接
     */
    private static final int MAX_RETRY_COUNT = 3;
    
    /**
     * 接收缓冲区
     * 用于存储接收到的数据
     * 使用ByteBuffer提高性能
     */
    private final ByteBuffer receiveBuffer = ByteBuffer.allocate(BUFFER_SIZE);
    
    /**
     * 重试计数器
     * 记录连续接收超时的次数
     * 使用原子操作确保线程安全
     */
    private final AtomicInteger retryCount = new AtomicInteger(0);
    
    /**
     * 构造函数
     * 初始化USB TPMS设备
     * 
     * @param context 应用上下文，不能为null
     */
    public UsbTpmsDevice(Context context) {
        this.context = context;
        this.usbManager = (UsbManager) context.getSystemService(Context.USB_SERVICE);
        XLog.tag(TAG).d(  "创建USB TPMS设备实例");
    }
    
    /**
     * 查找TPMS设备
     * <p>
     * 遍历所有已连接的USB设备，查找匹配的TPMS设备
     * </p>
     * 
     * 可能的问题：
     * 1. 设备未连接
     * 2. 找到多个匹配设备时只返回第一个
     * 3. 设备可能在查找过程中被拔出
     * 
     * @return 找到的TPMS设备，如果未找到返回null
     */
    private UsbDevice findTpmsDevice() {
        HashMap<String, UsbDevice> deviceList = usbManager.getDeviceList();
        XLog.tag(TAG).d(  "查找USB设备，当前连接设备数：" + deviceList.size());
        
        if (deviceList.isEmpty()) {
            XLog.tag(TAG).e(  "没有连接任何USB设备");
            if (stateCallback != null) {
                stateCallback.onError(ErrorCode.DEVICE_NOT_FOUND, "没有连接任何USB设备");
            }
            return null;
        }
        
        // 打印所有连接的USB设备信息
        for (UsbDevice device : deviceList.values()) {
            int vendorId = device.getVendorId();
            int productId = device.getProductId();
            String manufacturer = null;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                manufacturer = device.getManufacturerName();
            }
            String productName = null;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                productName = device.getProductName();
            }

            XLog.tag(TAG).d(  String.format("USB设备信息:\n" +
                "VendorId: 0x%04X\n" +
                "ProductId: 0x%04X\n" +
                "Manufacturer: %s\n" +
                "Product: %s\n" +
                "DeviceName: %s\n" +
                "DeviceClass: %d\n" +
                "DeviceSubClass: %d\n" +
                "InterfaceCount: %d",
                vendorId, productId,
                manufacturer, productName,
                device.getDeviceName(),
                device.getDeviceClass(),
                device.getDeviceSubclass(),
                device.getInterfaceCount()));
            
            // 检查是否是支持的设备
            for (int[] ids : SUPPORTED_DEVICES) {
                if (vendorId == ids[0] && productId == ids[1]) {
                    XLog.tag(TAG).d(  String.format("找到TPMS设备: VID=0x%04X, PID=0x%04X",
                        vendorId, productId));
                    return device;
                }
            }
        }
        
        XLog.tag(TAG).e(  "未找到支持的TPMS设备");
        if (stateCallback != null) {
            stateCallback.onError(ErrorCode.DEVICE_NOT_FOUND, "未找到支持的TPMS设备");
        }
        return null;
    }
    
    /**
     * 请求USB权限
     * <p>
     * 如果已有权限则直接返回true
     * 否则请求权限并等待结果
     * </p>
     * 
     * @param device 需要请求权限的USB设备
     * @return 权限是否已授予
     */
    private boolean requestUsbPermission(UsbDevice device) {
        if (usbManager.hasPermission(device)) {
            XLog.tag(TAG).d(  "已有USB权限");
            return true;
        }
        
        XLog.tag(TAG).d(  "请求USB权限");
        IntentFilter filter = new IntentFilter(ACTION_USB_PERMISSION);
        context.registerReceiver(usbReceiver, filter);
        
        PendingIntent permissionIntent = PendingIntent.getBroadcast(context, 0,
            new Intent(ACTION_USB_PERMISSION), PendingIntent.FLAG_IMMUTABLE);
        usbManager.requestPermission(device, permissionIntent);
        
        try {
            // 等待权限请求结果
            permissionLatch.await(3, TimeUnit.SECONDS);
            context.unregisterReceiver(usbReceiver);
            return permissionGranted;
        } catch (InterruptedException e) {
            XLog.tag(TAG).e(  "USB权限请求超时");
            context.unregisterReceiver(usbReceiver);
            return false;
        }
    }
    
    /**
     * 查找USB端点
     * <p>
     * 遍历设备的所有接口和端点
     * 查找指定方向的端点
     * </p>
     * 
     * @param isOutput true表示查找输出端点，false表示查找输入端点
     * @return 找到的端点，如果未找到返回null
     */
    private UsbEndpoint findEndpoint(boolean isOutput) {
        if (usbDevice == null) {
            XLog.tag(TAG).e(  "设备未连接");
            return null;
        }
        
        // 遍历接口
        for (int i = 0; i < usbDevice.getInterfaceCount(); i++) {
            UsbInterface intf = usbDevice.getInterface(i);
            
            // 遍历端点
            for (int j = 0; j < intf.getEndpointCount(); j++) {
                UsbEndpoint endpoint = intf.getEndpoint(j);
                
                if (isOutput && endpoint.getDirection() == UsbConstants.USB_DIR_OUT) {
                    XLog.tag(TAG).d(  "找到输出端点");
                    return endpoint;
                } else if (!isOutput && endpoint.getDirection() == UsbConstants.USB_DIR_IN) {
                    XLog.tag(TAG).d(  "找到输入端点");
                    return endpoint;
                }
            }
        }
        
        XLog.tag(TAG).e(  isOutput ? "未找到输出端点" : "未找到输入端点");
        return null;
    }
    
    /**
     * 打开USB设备
     * <p>
     * 执行以下步骤：
     * 1. 查找TPMS设备
     * 2. 请求USB权限
     * 3. 打开设备连接
     * 4. 声明接口
     * 5. 查找端点
     * 6. 启动数据接收线程
     * </p>
     * 
     * @return 设备是否成功打开
     */
    @Override
    public boolean open() {
        XLog.tag(TAG).d(  "打开USB设备");
        
        // 查找TPMS设备
        UsbDevice device = findTpmsDevice();
        if (device == null) {
            XLog.tag(TAG).e(  "未找到TPMS设备");
            return false;
        }
        
        // 请求USB权限
        if (!requestUsbPermission(device)) {
            XLog.tag(TAG).e(  "USB权限请求失败");
            if (stateCallback != null) {
                stateCallback.onError(ErrorCode.PERMISSION_DENIED, "USB权限请求失败");
            }
            return false;
        }
        
        // 打开设备连接
        connection = usbManager.openDevice(device);
        if (connection == null) {
            XLog.tag(TAG).e(  "打开USB设备连接失败");
            if (stateCallback != null) {
                stateCallback.onError(ErrorCode.CONNECTION_FAILED, "打开USB设备连接失败");
            }
            return false;
        }
        
        // 获取接口
        UsbInterface usbInterface = device.getInterface(0);
        if (!connection.claimInterface(usbInterface, true)) {
            XLog.tag(TAG).e(  "USB接口声明失败");
            connection.close();
            connection = null;
            if (stateCallback != null) {
                stateCallback.onError(ErrorCode.CONNECTION_FAILED, "USB接口声明失败");
            }
            return false;
        }
        
        // 查找端点
        for (int i = 0; i < usbInterface.getEndpointCount(); i++) {
            UsbEndpoint endpoint = usbInterface.getEndpoint(i);
            if (endpoint.getType() == UsbConstants.USB_ENDPOINT_XFER_BULK) {
                if (endpoint.getDirection() == UsbConstants.USB_DIR_IN) {
                    endpointIn = endpoint;
                    XLog.tag(TAG).d(  String.format("找到输入端点: address=0x%02X", endpoint.getAddress()));
                } else if (endpoint.getDirection() == UsbConstants.USB_DIR_OUT) {
                    endpointOut = endpoint;
                    XLog.tag(TAG).d(  String.format("找到输出端点: address=0x%02X", endpoint.getAddress()));
                }
            }
        }
        
        if (endpointIn == null || endpointOut == null) {
            XLog.tag(TAG).e(  "未找到USB端点");
            connection.releaseInterface(usbInterface);
            connection.close();
            connection = null;
            if (stateCallback != null) {
                stateCallback.onError(ErrorCode.ENDPOINT_NOT_FOUND, "未找到USB端点");
            }
            return false;
        }
        
        // 启动数据接收线程
        isRunning = true;
        startReceiving();
        
        usbDevice = device;
        if (stateCallback != null) {
            stateCallback.onConnectionStateChanged(true);
        }
        
        XLog.tag(TAG).d(  "USB设备打开成功");
        return true;
    }
    
    /**
     * 关闭USB设备
     * <p>
     * 执行以下步骤：
     * 1. 停止数据接收线程
     * 2. 关闭设备连接
     * 3. 清空设备引用
     * 4. 通知状态变化
     * </p>
     */
    @Override
    public void close() {
        XLog.tag(TAG).d("关闭USB设备");
        synchronized (this) {
            isRunning = false;
            
            if (connection != null) {
                try {
                    connection.close();
                } catch (Exception e) {
                    XLog.tag(TAG).e("关闭连接时出错: " + e.getMessage());
                } finally {
                    connection = null;
                }
            }
            
            endpointIn = null;
            endpointOut = null;
            usbDevice = null;
        }
        
        if (stateCallback != null) {
            stateCallback.onConnectionStateChanged(false);
        }
        // 不需要关闭线程池，因为使用的是全局线程池
    }
    
    /**
     * 发送数据
     * <p>
     * 通过USB输出端点发送数据
     * 超时时间设置为1秒
     * </p>
     * 
     * @param data 要发送的数据，不能为null
     * @return 数据是否发送成功
     */
    @Override
    public boolean send(byte[] data) {
        if (data == null) {
            XLog.tag(TAG).w("发送失败：数据为空");
            return false;
        }
        
        UsbDeviceConnection localConnection = null;
        UsbEndpoint localEndpoint = null;
        
        synchronized (this) {
            if (connection == null || endpointOut == null) {
                XLog.tag(TAG).w("发送失败：设备未连接");
                return false;
            }
            localConnection = connection;
            localEndpoint = endpointOut;
        }
        
        try {
            // 再次检查连接状态
            if (localConnection == null || localEndpoint == null) {
                XLog.tag(TAG).w("发送失败：设备连接已变化");
                return false;
            }
            
            int sent = localConnection.bulkTransfer(localEndpoint, data, data.length, 1000);
            boolean success = sent == data.length;
            XLog.tag(TAG).d(String.format("发送数据: length=%d, sent=%d, success=%b", data.length, sent, success));
            return success;
        } catch (Exception e) {
            XLog.tag(TAG).e("发送数据异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 设置数据回调
     * <p>
     * 用于接收设备发送的数据
     * </p>
     * 
     * @param callback 数据回调接口，可以为null
     */
    @Override
    public void setDataCallback(DataCallback callback) {
        this.dataCallback = callback;
    }
    
    /**
     * 设置状态回调
     * <p>
     * 用于接收设备状态变化
     * </p>
     * 
     * @param callback 状态回调接口，可以为null
     */
    @Override
    public void setStateCallback(StateCallback callback) {
        this.stateCallback = callback;
    }
    
    /**
     * 启动数据接收线程
     * <p>
     * 在单独的线程中循环接收数据
     * 处理接收超时和异常情况
     * </p>
     */
    private void startReceiving() {
        executor.execute(() -> {
            XLog.tag(TAG).d("启动数据接收线程");
            while (isRunning) {
                UsbDeviceConnection localConnection = null;
                UsbEndpoint localEndpoint = null;

                try {
                    // 1. 安全地获取本地引用
                    synchronized (this) {
                        if (!isRunning) break;
                        if (connection == null || endpointIn == null) {
                            Thread.sleep(1000);
                            continue;
                        }
                        localConnection = connection;
                        localEndpoint = endpointIn;
                    }

                    // 2. 使用临时缓冲区
                    byte[] buffer = new byte[BUFFER_SIZE];

                    // 检查连接是否仍然有效
                    if (localConnection == null || localEndpoint == null) {
                        safeSleep(1000);
                        continue;
                    }

                    // 3. 使用本地引用进行传输
                    int received = localConnection.bulkTransfer(
                            localEndpoint,
                            buffer,
                            buffer.length,
                            RECEIVE_TIMEOUT);

                    // 4. 再次检查连接状态
                    synchronized (this) {
                        if (!isRunning || connection == null) {
                            continue;
                        }

                        if (received > 0) {
                            retryCount.set(0);
                            if (dataCallback != null) {
                                ByteBuffer data = ByteBuffer.allocate(received);
                                data.put(buffer, 0, received);
                                data.flip();
                                dataCallback.onDataReceived(data);
                            }
                        }
                    }

                } catch (IllegalStateException e) {
                    // 5. 设备已断开连接
                    XLog.tag(TAG).w("设备已断开连接: " + e.getMessage());
                    safeClose();
                    break;

                } catch (Exception e) {
                    // 6. 其他异常
                    XLog.tag(TAG).e("接收数据异常: " + e.getMessage());
                    if (isRunning) {
                        safeClose();
                        safeSleep(1000);
                        safeReconnect();
                    }
                }
            }
        });
    }

    private synchronized void safeClose() {
        try {
            if (connection != null) {
                connection.close();
                connection = null;
            }
            endpointIn = null;
            endpointOut = null;

            if (stateCallback != null) {
                stateCallback.onConnectionStateChanged(false);
            }
        } catch (Exception e) {
            XLog.tag(TAG).e("关闭连接异常: " + e.getMessage());
        }
    }

    private void safeSleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private synchronized void safeReconnect() {
        try {
            if (!isRunning) return;

            boolean success = open();
            if (!success && stateCallback != null) {
                stateCallback.onError(ErrorCode.CONNECTION_FAILED, "重新连接失败");
            }
        } catch (Exception e) {
            XLog.tag(TAG).e("重新连接异常: " + e.getMessage());
        }
    }
    
    /**
     * 重新连接设备
     * <p>
     * 在单独的线程中执行以下步骤：
     * 1. 关闭当前连接
     * 2. 等待一段时间
     * 3. 重新打开设备
     * </p>
     */
    private void reconnect() {
        executor.execute(() -> {
            XLog.tag(TAG).d(  "开始重新连接设备");
            try {
                // 关闭当前连接
                if (connection != null) {
                    connection.close();
                    connection = null;
                }
                
                // 等待一段时间后重新连接
                XLog.tag(TAG).d(  "等待1秒后重新连接");
                Thread.sleep(1000);
                
                // 重新打开设备
                if (isRunning) {
                    XLog.tag(TAG).d(  "尝试重新打开设备");
                    open();
                } else {
                    XLog.tag(TAG).d(  "设备已停止运行，不再重新连接");
                }
            } catch (Exception e) {
                XLog.tag(TAG).e(  "重新连接失败: " + e.getMessage());
            }
        });
    }
} 