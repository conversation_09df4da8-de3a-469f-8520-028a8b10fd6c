package com.smartcar.easylauncher.modules.gesture.other;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;

import androidx.fragment.app.Fragment;

import com.angcyo.tablayout.DslTabLayoutConfig;
import com.angcyo.tablayout.delegate2.ViewPager2Delegate;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.setting.SettingFragmentPagerAdapter;
import com.smartcar.easylauncher.shared.adapter.setting.SettingFragmentPagerAdapters;
import com.smartcar.easylauncher.core.base.BaseActivity;
import com.smartcar.easylauncher.databinding.ActivityGestureSettingBinding;
import com.smartcar.easylauncher.modules.gesture.fragments.FragmentBasic;
import com.smartcar.easylauncher.modules.gesture.fragments.FragmentOther;
import com.smartcar.easylauncher.modules.gesture.fragments.FragmentSimple;
import com.smartcar.easylauncher.modules.gesture.fragments.FragmentWhiteBar;
import com.smartcar.easylauncher.modules.gesture.util.GlobalState;
import com.smartcar.easylauncher.infrastructure.interfaces.SkinChangeListener;
import com.smartcar.easylauncher.data.model.theme.SkinModel;
import com.smartcar.easylauncher.infrastructure.skin.SkinManager;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import me.jessyan.autosize.AutoSizeCompat;
import me.jessyan.autosize.AutoSizeConfig;

public class GestureSettingActivity extends BaseActivity implements SkinChangeListener {
    private SettingFragmentPagerAdapter settingFragmentPagerAdapter;
    private ActivityGestureSettingBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityGestureSettingBinding.inflate(getLayoutInflater());
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON, WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        setContentView(binding.getRoot());
        initTabLayout();
        ViewPager2Delegate.Companion.install(binding.myViewPager, binding.tabLayout, true);
        initTab(savedInstanceState == null);
    }



    @Override
    public void initImmersionBar() {
        super.initImmersionBar();
    }

    @Override
    public void onSkinChange(SkinModel messageEvent) {
        initTabLayout();
    }

    private void initTabLayout() {
        binding.tabLayout.getTabIndicator().setIndicatorDrawable(SkinManager.getInstance().getDrawable(R.drawable.indicator_bottom_line));
        binding.tabLayout.invalidate();
        binding.tabLayout.getTabLayoutConfig().setTabSelectColor(SkinManager.getInstance().getColor(R.color.tab_select_color));
        binding.tabLayout.getTabLayoutConfig().setTabDeselectColor(SkinManager.getInstance().getColor(R.color.tab_deselect_color));
        binding.tabLayout.getDslSelector().updateStyle();
    }

    private void initTab(boolean state) {
        binding.tabLayout.configTabLayoutConfig(new Function1<DslTabLayoutConfig, Unit>() {
            @Override
            public Unit invoke(DslTabLayoutConfig dslTabLayoutConfig) {
                return null;
            }
        });

        List<Fragment> list = new ArrayList<>();
        Class[] fragmentClasses = new Class[]{FragmentBasic.class, FragmentWhiteBar.class, FragmentSimple.class, /*Fragment3Section.class,*/ FragmentOther.class};
//        for (Class fragmentClass : fragmentClasses) {
//            Fragment fragment = null;
//            if (state) {
//                try {
//                    fragment = (Fragment) fragmentClass.newInstance();
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            } else {
//                List<Fragment> fragmentList = getSupportFragmentManager().getFragments();
//                for (Fragment item : fragmentList) {
//                    String fragmentTag = item.getClass().getSimpleName();
//                    if (fragmentTag.compareTo(fragmentClass.getSimpleName()) == 0) {
//                        fragment = item;
//                        break;
//                    }
//                }
//            }
//            if (fragment == null) {
//                try {
//                    fragment = (Fragment) fragmentClass.newInstance();
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//            list.add(fragment);
//        }

        SettingFragmentPagerAdapters settingFragmentPagerAdapter = new SettingFragmentPagerAdapters(
                getSupportFragmentManager(),
                getLifecycle(),
                fragmentClasses
        );
        binding.myViewPager.setAdapter(settingFragmentPagerAdapter);
        binding.myViewPager.setCurrentItem(0);
        binding.myViewPager.setOffscreenPageLimit(1);
        binding.btBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });
    }


    @Override
    public Resources getResources() {
        //需要升级到 v1.1.2 及以上版本才能使用 AutoSizeCompat
        int width = AutoSizeConfig.getInstance().getScreenWidth();
        int height = AutoSizeConfig.getInstance().getScreenHeight();
        AutoSizeCompat.autoConvertDensity(super.getResources(), 900, width > height);
        return super.getResources();
    }

    private void updateView() {
        try {
            Intent intent = new Intent(getString(R.string.action_config_changed));
            sendBroadcast(intent);
        } catch (Exception ignored) {
        }
    }

    @Override
    protected void onResume() {
        super.onResume();

        GlobalState.testMode = true;
        updateView();
    }

    @Override
    protected void onPause() {
        super.onPause();
        GlobalState.testMode = false;
        updateView();
    }

    private void setExcludeFromRecents(boolean excludeFromRecents) {
        try {
            ActivityManager service = (ActivityManager) this.getSystemService(Context.ACTIVITY_SERVICE);
            int taskId = this.getTaskId();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                for (ActivityManager.AppTask task : service.getAppTasks()) {
                    if (task.getTaskInfo().id == taskId) {
                        task.setExcludeFromRecents(excludeFromRecents);
                    }
                }
            }
        } catch (Exception ex) {
        }
    }

    @Override
    public void onBackPressed() {
        setExcludeFromRecents(true);
        super.onBackPressed();
    }
}