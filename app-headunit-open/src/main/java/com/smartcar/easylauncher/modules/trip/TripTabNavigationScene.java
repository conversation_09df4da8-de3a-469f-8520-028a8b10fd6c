package com.smartcar.easylauncher.modules.trip;

import android.annotation.SuppressLint;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bytedance.scene.Scene;
import com.bytedance.scene.navigation.NavigationScene;
import com.bytedance.scene.navigation.NavigationSceneOptions;
import com.bytedance.scene.utlity.SceneInstanceUtility;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.widget.navigation.TabNavigationViewScene;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;


/**
 * 行程中心的底部导航场景
 *
 * 支持状态恢复的Scene实现，使用Bundle传递参数以兼容ByteDance Scene框架的状态恢复机制
 *
 * <AUTHOR>
 */
public class TripTabNavigationScene extends TabNavigationViewScene {

    private static final String TAG = "TripTabNavigationScene";
    private static final String KEY_POSITION = "position";
    private static final int DEFAULT_POSITION = 0;

    private int mPosition = DEFAULT_POSITION;

    /**
     * 无参构造函数，用于支持Scene框架的状态恢复机制
     * 当supportRestore()返回true时，框架会通过反射调用此构造函数重新创建Scene实例
     *
     * 注意：ByteDance Scene框架要求Scene类只能有一个无参构造函数，
     * 不能有其他构造函数，否则状态恢复会失败
     */
    public TripTabNavigationScene() {
        MyLog.d(TAG, "TripTabNavigationScene() - 无参构造函数被调用");
    }

    /**
     * 创建TripTabNavigationScene实例的推荐方式
     * 使用Bundle传递参数，完全兼容Scene框架的状态恢复机制
     *
     * @param position 默认选中的tab位置
     * @return TripTabNavigationScene实例
     */
    public static TripTabNavigationScene newInstance(int position) {
        MyLog.d(TAG, "newInstance() - 创建实例，position: " + position);
        TripTabNavigationScene scene = new TripTabNavigationScene();
        Bundle args = new Bundle();
        args.putInt(KEY_POSITION, position);
        scene.setArguments(args);
        return scene;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        MyLog.d(TAG, "onCreate() - 开始");

        // 从Bundle中获取参数
        Bundle arguments = getArguments();
        if (arguments != null) {
            mPosition = arguments.getInt(KEY_POSITION, DEFAULT_POSITION);
            MyLog.d(TAG, "onCreate() - 从arguments获取position: " + mPosition);
        } else {
            MyLog.d(TAG, "onCreate() - arguments为null，使用默认position: " + DEFAULT_POSITION);
            mPosition = DEFAULT_POSITION;
        }

        MyLog.d(TAG, "onCreate() - 完成，最终position: " + mPosition);
    }

    @Override
    protected int getTabItemLayout() {
        return R.layout.layout_text_view;
    }

    @Override
    protected List<String> getNavigationTitles() {
        List<String> titles = new ArrayList<>();
        titles.add("今日行程");
        titles.add("陪伴里程");
        return titles;
    }

    @Override
    protected int getDefaultItemIndex() {
        MyLog.d(TAG, "getDefaultItemIndex() - 返回position: " + mPosition);
        return mPosition;
    }

    @Override
    protected String getTitleName() {
        return "行程中心";
    }

    @NonNull
    @Override
    @SuppressLint("RestrictedApi")
    public LinkedHashMap<Integer, Scene> getSceneMap() {
        MyLog.d(TAG, "getSceneMap() - 开始创建Scene映射");
        LinkedHashMap<Integer, Scene> linkedHashMap = new LinkedHashMap<>();

        try {
            // 为第一个底部导航项创建并配置场景
            NavigationScene navigationScene = (NavigationScene) SceneInstanceUtility.getInstanceFromClass(
                    NavigationScene.class,
                    new NavigationSceneOptions(TodayTripScene.class, getBundle(0)).toBundle()
            );
            linkedHashMap.put(0, navigationScene);
            MyLog.d(TAG, "getSceneMap() - 成功创建TodayTripScene");

            navigationScene = (NavigationScene) SceneInstanceUtility.getInstanceFromClass(
                    NavigationScene.class,
                    new NavigationSceneOptions(TotalTripScene.class, getBundle(1)).toBundle()
            );
            linkedHashMap.put(1, navigationScene);
            MyLog.d(TAG, "getSceneMap() - 成功创建TotalTripScene");

        } catch (Exception e) {
            MyLog.e(TAG, "getSceneMap() - 创建Scene失败", e);
        }

        MyLog.d(TAG, "getSceneMap() - 完成，返回" + linkedHashMap.size() + "个Scene");
        return linkedHashMap;
    }

    /**
     * 创建一个包含索引的Bundle对象
     *
     * @param index 要放入Bundle的索引值
     * @return 包含索引的Bundle
     */
    private Bundle getBundle(int index) {
        Bundle bundle = new Bundle();
        bundle.putInt("index", index);
        return bundle;
    }
}
