package com.smartcar.easylauncher.modules.setting;


import static android.content.Context.POWER_SERVICE;
import static com.bytedance.scene.navigation.NavigationSceneGetter.requireNavigationScene;
import static com.smartcar.easylauncher.shared.utils.AppConfig.getPackageName;

import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.PowerManager;
import android.provider.Settings;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;

import com.bytedance.scene.group.UserVisibleHintGroupScene;
import com.gyf.immersionbar.BarHide;
import com.gyf.immersionbar.ImmersionBar;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.Constants;
import com.smartcar.easylauncher.databinding.FragmentSystemSettingBinding;
import com.smartcar.easylauncher.shared.dialog.GeneralDialog;
import com.smartcar.easylauncher.infrastructure.event.scope.notice.cody.GeneralNotificationScopeBus;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.data.model.common.GeneralNoticeModel;
import com.smartcar.easylauncher.shared.utils.permission.BackgroundActivityPermissionUtils;
import com.smartcar.easylauncher.shared.utils.permission.LocationPermissionUtil;
import com.smartcar.easylauncher.shared.utils.permission.OverlayPermissionUtils;
import com.smartcar.easylauncher.shared.utils.system.DesktopUtils;
import com.smartcar.easylauncher.shared.view.segmentcontrlo.SegmentedControlItem;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * 系统设置页面
 *
 * <AUTHOR>
 */
public class SystemSettingsScene extends UserVisibleHintGroupScene {
    public static final String TAG = SystemSettingsScene.class.getSimpleName();
    private FragmentSystemSettingBinding binding;
    private static final String PREF_SCREEN_ORIENTATION = "screen_orientation";
    private FragmentActivity fragmentActivity;
    private final CompositeDisposable disposables = new CompositeDisposable();


    @Override
    public ViewGroup onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = FragmentSystemSettingBinding.inflate(inflater, container, false);
        fragmentActivity = (FragmentActivity) requireActivity();

        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        // 1. 初始化基本UI结构
        initBasicUI();
        // 2. 加载可选数据
        loadOptionalData();
    }

    private void initBasicUI() {
        // 设置系统导航栏
        setupNavigationBar();

        // 设置默认桌面
        setupDesktop();

        // 设置自动打开WIFI
        setupWifi();

        // 设置屏幕方向
        setupScreenOrientation();

        // 初始化各个权限设置按钮
        setupPermissionButtons();
    }

    private void setupNavigationBar() {
        binding.navigationField.tvFieldTitle.setText(getString(R.string.system_navigation_bar));
        binding.navigationField.tvFieldDesc.setText(getString(R.string.system_navigation_bar_desc));

        // 先设置初始状态为false，等异步加载完成后再更新
        binding.navigationField.switchFieldAction.setChecked(false);
        binding.navigationField.switchFieldAction.setOnCheckedChangeListener((buttonView, isChecked) ->
                updateNavigationBarState(isChecked));
    }

    private void setupDesktop() {
        binding.desktopField.tvFieldTitle.setText(getString(R.string.default_desktop));
        binding.desktopField.tvFieldDesc.setText(getString(R.string.default_desktop_desc));

        // 根据系统版本动态调整第二个方法的描述
        updateDesktopConfigDescription();

        // 设置卡片点击监听器
        binding.desktopField.cardMethod1.setOnClickListener(v -> {
            // 添加点击反馈效果
            v.animate().scaleX(0.95f).scaleY(0.95f).setDuration(100)
                .withEndAction(() -> {
                    v.animate().scaleX(1.0f).scaleY(1.0f).setDuration(100);
                    clearAndOpenLauncher();
                });
        });

        binding.desktopField.cardMethod2.setOnClickListener(v -> {
            // 添加点击反馈效果
            v.animate().scaleX(0.95f).scaleY(0.95f).setDuration(100)
                .withEndAction(() -> {
                    v.animate().scaleX(1.0f).scaleY(1.0f).setDuration(100);
                    openHomeSettings();
                });
        });
    }

    private void updateDesktopConfigDescription() {
        TextView descView = binding.desktopField.tvMethod2Desc;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // Android 5.0+ 支持直接打开桌面设置
            descView.setText(getString(R.string.desktop_config_method_desc));
        } else {
            // Android 4.2-4.4 降级到应用管理
            descView.setText("打开应用管理页面\n手动设置默认桌面");
        }
    }

    private void setupWifi() {
        binding.wifiField.tvFieldTitle.setText(getString(R.string.auto_open_wifi));
        binding.wifiField.tvFieldDesc.setText(getString(R.string.auto_open_wifi_desc));

        // 先设置初始状态为false，等异步加载完成后再更新
        binding.wifiField.switchFieldAction.setChecked(false);
        binding.wifiField.switchFieldAction.setOnCheckedChangeListener((buttonView, isChecked) ->
                updateWifiState(isChecked));
    }

    private void setupScreenOrientation() {
        // 初始化选项列表
        List<SegmentedControlItem> orientationItems = new ArrayList<>();
        orientationItems.add(new SegmentedControlItem(getString(R.string.screen_orientation_auto)));
        orientationItems.add(new SegmentedControlItem(getString(R.string.screen_orientation_landscape)));
        orientationItems.add(new SegmentedControlItem(getString(R.string.screen_orientation_portrait)));

        // 添加items到控件
        binding.scvOrientation.addItems(orientationItems);

        // 获取保存的屏幕方向设置，默认为横屏(1)
        int savedOrientation = SettingsManager.getScreenOrientation();
        binding.scvOrientation.setSelectedItem(savedOrientation);

        // 设置监听器
        binding.scvOrientation.setOnSegItemClickListener((item, position) -> {
            // 保存选择
            SettingsManager.setScreenOrientation(position);
            MToast.makeTextShort("正在切换屏幕方向，请稍等...");

            Runnable resetAction = () -> {
                // 先发送通知，让其他 Activity 可以准备好处理方向变化
                // 发送屏幕方向变更事件通知，在 content 中传递方向值

                GeneralNotificationScopeBus.eventBean().post(new GeneralNoticeModel(
                        Constants.NoticeType.SCREEN_ORIENTATION_CHANGE,
                        String.valueOf(position),
                        true
                ));

                // 设置屏幕方向
                switch (position) {
                    case 0: // 自动
                        //  requireActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR);
                        requireNavigationScene(requireParentScene()).popToRoot();
                        break;
                    case 1: // 横屏 - 直接强制横屏
                        //  requireActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
                        requireNavigationScene(requireParentScene()).popToRoot();
                        break;
                    case 2: // 竖屏 - 直接强制竖屏
                        //  requireActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
                        requireNavigationScene(requireParentScene()).popToRoot();
                        break;
                }
            };

            // 延迟执行，给用户足够的视觉反馈时间
            new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(resetAction, 800);

        });
    }

    private void setupPermissionButtons() {
        // 电池优化
        setupBatteryOptimization();

        // 始终定位
        setupLocationPermission();

        // 后台打开应用
        setupBackgroundPermission();

        // 悬浮权限
        setupOverlayPermission();

        // 系统设置修改权限
        setupSystemSettingsPermission();
    }

    private void setupBatteryOptimization() {
        binding.batteryField.tvFieldTitle.setText(getString(R.string.battery_optimization));
        binding.batteryField.btnFieldAction.setText(getString(R.string.open));
        binding.batteryField.tvFieldDesc.setText(getString(R.string.battery_optimization_desc));
        binding.batteryField.btnFieldAction.setOnClickListener(v -> checkBatteryOptimization());
    }

    private void setupLocationPermission() {
        binding.locationField.tvFieldTitle.setText(getString(R.string.always_location));
        binding.locationField.btnFieldAction.setText(getString(R.string.open));
        binding.locationField.tvFieldDesc.setText(getString(R.string.always_location_desc));
        binding.locationField.btnFieldAction.setOnClickListener(v -> checkLocationPermission());
    }

    private void setupBackgroundPermission() {
        binding.backgroundField.tvFieldTitle.setText(getString(R.string.background_app_open));
        binding.backgroundField.btnFieldAction.setText(getString(R.string.open));
        binding.backgroundField.tvFieldDesc.setText(getString(R.string.background_app_open_desc));
        binding.backgroundField.btnFieldAction.setOnClickListener(v -> checkBackgroundPermission());
    }

    private void setupOverlayPermission() {
        binding.overlayField.tvFieldTitle.setText(getString(R.string.overlay_permission));
        binding.overlayField.btnFieldAction.setText(getString(R.string.open));
        binding.overlayField.tvFieldDesc.setText(getString(R.string.overlay_permission_desc));
        binding.overlayField.btnFieldAction.setOnClickListener(v -> checkOverlayPermission());
    }

    private void setupSystemSettingsPermission() {
        binding.systemSettingsField.tvFieldTitle.setText(getString(R.string.system_settings_permission));
        binding.systemSettingsField.btnFieldAction.setText(getString(R.string.open));
        binding.systemSettingsField.tvFieldDesc.setText(getString(R.string.system_settings_permission_desc));
        binding.systemSettingsField.btnFieldAction.setOnClickListener(v -> checkSystemSettingsPermission());
    }

    private void loadOptionalData() {
        // 异步加载各种设置状态
        disposables.add(Observable.fromCallable(() -> {
                    Map<String, Boolean> states = new HashMap<>();
                    states.put("navBar", SettingsManager.getSystNavigationBarShow());
                    states.put("wifi", SettingsManager.getWifiOpen());
                    return states;
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(states -> {
                    // 更新导航栏状态
                    binding.navigationField.switchFieldAction.setChecked(states.get("navBar"));
                    // 更新WIFI状态
                    binding.wifiField.switchFieldAction.setChecked(states.get("wifi"));
                    // 更新权限状态
                    updatePermissionStates();
                }));
    }

    private void updateNavigationBarState(boolean isChecked) {
        disposables.add(Observable.fromCallable(() -> {
                    SettingsManager.setSystNavigationBarShow(isChecked);
                    return isChecked;
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(checked -> {
                    if (checked) {
                        ImmersionBar.with(requireActivity())
                                .hideBar(BarHide.FLAG_SHOW_BAR)
                                .init();
                        ImmersionBar.with(requireActivity())
                                .hideBar(BarHide.FLAG_HIDE_STATUS_BAR)
                                .init();
                    } else {
                        ImmersionBar.with(requireActivity())
                                .hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR)
                                .init();
                    }
                    GeneralNotificationScopeBus.eventBean()
                            .post(new GeneralNoticeModel(Constants.NoticeType.NAVIGATION_EVENT, "", checked));
                }));
    }

    private void updateWifiState(boolean isChecked) {
        disposables.add(Observable.fromCallable(() -> {
                    SettingsManager.setWifiOpen(isChecked);
                    return isChecked;
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe());
    }

    private void clearAndOpenLauncher() {
        disposables.add(Observable.fromCallable(() -> {
                    DesktopUtils.clearDefaultLauncher(requireActivity());
                    return true;
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(result ->
                        DesktopUtils.openResolverLauncher(requireActivity())
                ));
    }

    private void openHomeSettings() {
        try {
            // ACTION_HOME_SETTINGS 需要 API 21+
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                Intent homeSettingsIntent = new Intent("android.settings.HOME_SETTINGS");
                if (homeSettingsIntent.resolveActivity(requireActivity().getPackageManager()) != null) {
                    requireActivity().startActivity(homeSettingsIntent);
                    return;
                }
            }

            // 降级方案：打开应用管理页面
            openApplicationSettings();

        } catch (Exception e) {
            // 最终降级方案：使用清除默认设置的方式
            MToast.makeTextShort("系统不支持桌面设置，将使用清除默认设置方式");
            clearAndOpenLauncher();
        }
    }

    private void openApplicationSettings() {
        try {
            // 尝试打开应用管理页面
            Intent intent = new Intent(android.provider.Settings.ACTION_APPLICATION_SETTINGS);
            requireActivity().startActivity(intent);
            MToast.makeTextShort("请在应用管理中找到桌面应用进行设置");
        } catch (Exception e) {
            // 如果应用管理也打不开，尝试打开设置主页
            Intent intent = new Intent(android.provider.Settings.ACTION_SETTINGS);
            requireActivity().startActivity(intent);
            MToast.makeTextShort("请在设置中找到应用管理进行桌面设置");
        }
    }

    private void updatePermissionStates() {
        // 检查电池优化状态
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PowerManager powerManager = (PowerManager) requireActivity().getSystemService(POWER_SERVICE);
            boolean hasIgnored = powerManager.isIgnoringBatteryOptimizations(requireActivity().getPackageName());
            binding.batteryField.btnFieldAction.setText(hasIgnored ? "已开启" : "打开");
        }

        // 检查位置权限状态
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            boolean hasLocationPermission = LocationPermissionUtil.hasBackgroundLocationPermission(requireActivity());
            binding.locationField.btnFieldAction.setText(hasLocationPermission ? "已开启" : "打开");
        }

        // 检查后台活动权限
        boolean hasBackgroundPermission = BackgroundActivityPermissionUtils.hasBackgroundActivityPermission(requireActivity());
        binding.backgroundField.btnFieldAction.setText(hasBackgroundPermission ? "已开启" : "打开");

        // 检查悬浮窗权限
        boolean hasOverlayPermission = OverlayPermissionUtils.hasOverlayPermission(requireActivity());
        binding.overlayField.btnFieldAction.setText(hasOverlayPermission ? "已开启" : "打开");

        // 检查系统设置权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            boolean hasWriteSettings = Settings.System.canWrite(requireActivity());
            binding.systemSettingsField.btnFieldAction.setText(hasWriteSettings ? "已开启" : "打开");
        }
    }

    private void checkBatteryOptimization() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PowerManager powerManager = (PowerManager) requireActivity().getSystemService(POWER_SERVICE);
            boolean hasIgnored = powerManager.isIgnoringBatteryOptimizations(requireActivity().getPackageName());
            if (!hasIgnored) {
                try {
                    Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
                    intent.setData(Uri.parse("package:" + requireActivity().getPackageName()));
                    if (intent.resolveActivity(requireActivity().getPackageManager()) != null) {
                        requireActivity().startActivity(intent);
                    }
                } catch (Exception e) {
                    MToast.makeTextShort("无法直接打开设置页面");
                }
            } else {
                MToast.makeTextShort("已经开启忽略电池优化");
            }
        } else {
            MToast.makeTextShort("已经开启忽略电池优化");
        }
    }

    private void checkLocationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            if (!LocationPermissionUtil.hasBackgroundLocationPermission(requireActivity())) {
                LocationPermissionUtil.navigateToAppLocationSetting(requireActivity());
            }
        }
    }

    private void checkBackgroundPermission() {
        if (BackgroundActivityPermissionUtils.hasBackgroundActivityPermission(requireActivity())) {
            MToast.makeTextShort("已经开启后台弹窗权限");
        } else {
            BackgroundActivityPermissionUtils.requestBackgroundActivityPermission(requireActivity(), 1);
        }
    }

    private void checkOverlayPermission() {
        if (OverlayPermissionUtils.hasOverlayPermission(requireActivity())) {
            MToast.makeTextShort("已经开启悬浮窗权限");
        } else {
            OverlayPermissionUtils.requestOverlayPermission(requireActivity(), 1);
        }
    }

    private void checkSystemSettingsPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.System.canWrite(requireActivity())) {
                try {
                    Intent intent = new Intent(Settings.ACTION_MANAGE_WRITE_SETTINGS);
                    intent.setData(Uri.parse("package:" + getPackageName()));
                    requireActivity().startActivity(intent);
                } catch (Exception e) {
                    MToast.makeTextShort("无法直接打开设置页面");
                }
            } else {
                MToast.makeTextShort("已经开启修改系统设置权限");
            }
        } else {
            MToast.makeTextShort("已经开启修改系统设置权限");
        }
    }

    /**
     * 选择桌面弹窗
     */
    public void desktopDialog(int type, String title, String content, String ok, String cancel) {
        GeneralDialog.showGeneralDialog(fragmentActivity.getSupportFragmentManager(), requireActivity(), title, content, ok, cancel, new GeneralDialog.DialogClickListener() {
            @Override
            public void onPositiveClick() {
                if (DesktopUtils.isComponentEnabled(requireActivity(), "com.smartcar.easylauncher.ui.home.HomeActivity")) {
                    if (type == 1) {
                        DesktopUtils.clearDefaultLauncher(requireActivity());
                        DesktopUtils.openResolverLauncher(requireActivity());
                    } else {
                        openHomeSettings();
                    }
                } else {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        DesktopUtils.modifyComponent(requireActivity());
                    } else {
                        DesktopUtils.modifyComponent(requireActivity());
                        DesktopUtils.clearDefaultLauncher(requireActivity());
                        DesktopUtils.openResolverLauncher(requireActivity());
                    }
                }
            }

            @Override
            public void onNegativeClick() {
                // do something
            }
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        // 页面恢复时更新权限状态
        updatePermissionStates();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 清理所有订阅，防止内存泄漏
        disposables.clear();
        binding = null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        // 清理所有订阅，防止内存泄漏
        disposables.dispose();
    }
}
