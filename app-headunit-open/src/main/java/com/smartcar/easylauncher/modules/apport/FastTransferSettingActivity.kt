package com.smartcar.easylauncher.modules.apport

import android.content.Intent
import android.os.Environment
import android.view.View
import com.smartcar.easylauncher.shared.utils.apportutil.FileUtils
import com.smartcar.easylauncher.shared.utils.apportutil.get
import com.smartcar.easylauncher.shared.utils.apportutil.mmkv
import com.demon.qfsolution.utils.getExternalOrFilesDirPath
import com.smartcar.easylauncher.app.App
import com.smartcar.easylauncher.R
import com.smartcar.easylauncher.core.constants.Constants
import com.smartcar.easylauncher.databinding.ActivityApportSettingBinding

class FastTransferSettingActivity : BaseActivity<ActivityApportSettingBinding>() {


    override fun initData() {
        setToolbar(getString(R.string.settings_title))
        binding.run {
            toolbar.ivSetting.visibility = View.GONE
            toolbar.ivWifi.visibility = View.GONE
            tvPath.setOnClickListener {
                val intent =
                    Intent(this@FastTransferSettingActivity, ChangePathActivity::class.java)
                startActivity(intent)
            }
            tvDelete.setOnClickListener {
                val def: String = App.getContextInstance()
                    .getExternalOrFilesDirPath(Environment.DIRECTORY_DCIM)
                FileUtils.deleteAll(mmkv.get(Constants.MMKV_STORAGE_PATH, def))
            }
            tvLog.setOnClickListener {
                val intent = Intent(this@FastTransferSettingActivity, LogListActivity::class.java)
                startActivity(intent)
            }
        }
    }
}