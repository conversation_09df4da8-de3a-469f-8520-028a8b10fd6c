package com.smartcar.easylauncher.modules.keysetting;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;

import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RadioButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bytedance.scene.group.UserVisibleHintGroupScene;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.common.KeyValueAdapter;
import com.smartcar.easylauncher.core.constants.BroadcastPresets;
import com.smartcar.easylauncher.core.constants.KeyMapConstants;
import com.smartcar.easylauncher.databinding.SceneEditKeyMappingBinding;
import com.smartcar.easylauncher.data.database.entity.KeyMapModel;
import com.smartcar.easylauncher.shared.dialog.HorizontalAppSelector;
import com.smartcar.easylauncher.infrastructure.event.KeyEventDefine;
import com.smartcar.easylauncher.infrastructure.event.cody.KeyEventScopeBus;
import com.smartcar.easylauncher.infrastructure.interfaces.AppSelectionCallback;
import com.smartcar.easylauncher.core.manager.KeyMapManager;
import com.smartcar.easylauncher.data.model.common.KeyValuePair;
import com.smartcar.easylauncher.data.model.system.AppInfo;
import com.smartcar.easylauncher.shared.utils.KeyValueUtils;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.dialog.JsonInputDialog;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import cody.bus.ObserverWrapper;

/**
 * 按键映射编辑场景
 * 用于创建或编辑按键映射
 *
 * <AUTHOR>
 * @date 2024/07/09
 */
public class KeyMappingEditScene extends UserVisibleHintGroupScene {
    private static final String TAG = KeyMappingEditScene.class.getSimpleName();

    // 视图绑定对象
    private SceneEditKeyMappingBinding binding;

    // 按键映射管理器
    private KeyMapManager keyMapManager;

    // 当前编辑的按键映射
    private KeyMapModel keyMapModel;

    // 是否是新建模式
    private boolean isCreateMode;

    // 当前选中的按键代码
    private int selectedKeyCode = -1;

    // 应用列表
    private List<AppInfo> appList = new ArrayList<>();

    // 选中的应用包名
    private String selectedPackageName;

    private boolean isCapturingKey = false;

    // 键值对适配器
    private KeyValueAdapter keyValueAdapter;

    // 按键事件观察者
    private ObserverWrapper<KeyEventDefine.KeyCodeEvent> keyEventObserver;

    /**
     * 创建Bundle参数
     *
     * @param keyMapModel 要编辑的按键映射，如果为null则表示创建新映射
     * @return Bundle参数
     */
    public static Bundle createArguments(KeyMapModel keyMapModel) {
        Bundle bundle = new Bundle();
        if (keyMapModel != null) {
            bundle.putSerializable("keyMapModel", keyMapModel);
        }
        return bundle;
    }

    @NonNull
    @Override
    public ViewGroup onCreateView(@NonNull LayoutInflater inflater, @NonNull ViewGroup container, Bundle savedInstanceState) {
        binding = SceneEditKeyMappingBinding.inflate(inflater, container, false);
        keyMapManager = KeyMapManager.getInstance(getActivity());
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // 从Bundle中获取数据
        Bundle arguments = getArguments();
        if (arguments != null && arguments.containsKey("keyMapModel")) {
            keyMapModel = (KeyMapModel) arguments.getSerializable("keyMapModel");
            isCreateMode = false;
        } else {
            // 创建模式，初始化一个新的KeyMapModel
            keyMapModel = new KeyMapModel();
            isCreateMode = true;
        }

        // 初始化视图
        initView();

        // 初始化按键捕获
        initKeyCapture();

        // 初始化动作类型选择
        initActionTypeSelection();

        // 初始化键值对编辑器
        initKeyValueEditor();

        // 如果是编辑模式，填充数据
        if (!isCreateMode) {
            fillDataToUI();
        }

        // 初始化应用列表
        loadAppList();

        // 设置按键事件监听
        setupKeyEventListener();

        // 检查布局可见性
        checkLayoutVisibility();
    }

    /**
     * 初始化视图
     */
    private void initView() {
        MyLog.d(TAG, "初始化视图");

        // 设置标题和图标
        if (isCreateMode) {
            binding.ivKeyIcon.setImageResource(R.drawable.ic_key);
            // 新建模式下，初始化按键显示
            binding.tvSelectedKey.setText("点击选择按键");
            MyLog.d(TAG, "新建模式，初始化按键显示");
        } else {
            // 编辑模式下，显示已选择的按键
            if (keyMapModel != null) {
                MyLog.d(TAG, "编辑模式，按键代码=" + keyMapModel.getKeyCode() + ", 按键名称=" + keyMapModel.getKeyName());
            }

            // 根据动作类型设置不同图标
            int imageResId = R.drawable.ic_key;
            switch (keyMapModel.getActionType()) {
                case KeyMapConstants.ActionType.BROADCAST:
                    imageResId = R.drawable.ic_broadcast;
                    break;

                case KeyMapConstants.ActionType.LAUNCH_APP:
                    imageResId = R.drawable.ic_apps;
                    break;

                case KeyMapConstants.ActionType.MEDIA_CONTROL:
                    imageResId = R.drawable.ic_music_note;
                    break;

                case KeyMapConstants.ActionType.SYSTEM_ACTION:
                    imageResId = R.drawable.ic_setting;
                    break;
            }
            binding.ivKeyIcon.setImageResource(imageResId);
        }

        // 设置按键选择点击事件
        binding.layoutKeySelect.setOnClickListener(v -> {
            // 切换按键捕获模式
            isCapturingKey = !isCapturingKey;

            if (isCapturingKey) {
                // 开始捕获，修改提示文本
                binding.tvSelectedKey.setText("正在等待按键输入...");
                binding.tvKeyHint.setText("请按下要映射的方向盘按键，或再次点击此处取消");
                binding.tvKeyHint.setTextColor(getResources().getColor(R.color.bg_channel_name));
                // 显示提示消息
                MToast.makeTextShort("请按下要映射的方向盘按键");
                MyLog.d(TAG, "开始捕获按键");
            } else {
                // 取消捕获，恢复原来的显示
                if (selectedKeyCode != -1) {
                    binding.tvSelectedKey.setText(KeyMapConstants.getKeyCodeName(selectedKeyCode));
                } else {
                    binding.tvSelectedKey.setText("点击选择按键");
                }
                binding.tvKeyHint.setText("请按下要映射的方向盘按键或选择已知按键");
                binding.tvKeyHint.setTextColor(getResources().getColor(R.color.field_remarks_color));
                MyLog.d(TAG, "取消捕获按键");
            }
        });

        // 设置按钮点击事件
        binding.btnBack.setOnClickListener(v -> {
            // 返回上一页
            MyLog.d(TAG, "点击返回按钮");
            getNavigationScene(KeyMappingEditScene.this).pop();
        });

        binding.btnSave.setOnClickListener(v -> {
            // 保存按键映射
            MyLog.d(TAG, "点击保存按钮");
            saveKeyMapping();
        });

        binding.btnDelete.setOnClickListener(v -> {
            // 删除按键映射
            MyLog.d(TAG, "点击删除按钮");
            deleteKeyMapping();
        });

        // 只有编辑模式才显示删除按钮
        binding.btnDelete.setVisibility(isCreateMode ? View.GONE : View.VISIBLE);

        // 设置应用选择点击事件
        binding.layoutAppSelect.setOnClickListener(v -> {
            // 显示应用选择对话框
            MyLog.d(TAG, "点击应用选择");
            showAppSelectDialog();
        });

        // 设置媒体控制选项点击事件
        setupMediaControlActions();

        // 设置系统操作选项点击事件
        setupSystemActions();

        MyLog.d(TAG, "视图初始化完成");
    }

    /**
     * 设置媒体控制选项点击事件
     */
    private void setupMediaControlActions() {
        // 获取媒体控制网格中的选项
        View mediaPlayPause = binding.layoutMediaControl.findViewById(R.id.media_play_pause);
        View mediaPrevious = binding.layoutMediaControl.findViewById(R.id.media_previous);
        View mediaNext = binding.layoutMediaControl.findViewById(R.id.media_next);
        View mediaStop = binding.layoutMediaControl.findViewById(R.id.media_stop);

        // 获取原始RadioButton
        RadioButton rbPlayPause = binding.layoutMediaControl.findViewById(R.id.rb_play_pause);
        RadioButton rbPrevious = binding.layoutMediaControl.findViewById(R.id.rb_previous);
        RadioButton rbNext = binding.layoutMediaControl.findViewById(R.id.rb_next);
        RadioButton rbStop = binding.layoutMediaControl.findViewById(R.id.rb_stop);

        // 设置点击事件
        mediaPlayPause.setOnClickListener(v -> {
            updateMediaControlSelection(mediaPlayPause, mediaPrevious, mediaNext, mediaStop);
            rbPlayPause.setChecked(true);
        });

        mediaPrevious.setOnClickListener(v -> {
            updateMediaControlSelection(mediaPrevious, mediaPlayPause, mediaNext, mediaStop);
            rbPrevious.setChecked(true);
        });

        mediaNext.setOnClickListener(v -> {
            updateMediaControlSelection(mediaNext, mediaPlayPause, mediaPrevious, mediaStop);
            rbNext.setChecked(true);
        });

        mediaStop.setOnClickListener(v -> {
            updateMediaControlSelection(mediaStop, mediaPlayPause, mediaPrevious, mediaNext);
            rbStop.setChecked(true);
        });
    }

    /**
     * 更新媒体控制选项选中状态
     *
     * @param selectedView 选中的视图
     * @param otherViews   其他视图
     */
    private void updateMediaControlSelection(View selectedView, View... otherViews) {
        // 设置选中的选项背景
        selectedView.setBackgroundResource(R.drawable.tab_background_selector);
        selectedView.setSelected(true);

        // 设置其他选项背景
        for (View view : otherViews) {
            view.setBackgroundResource(R.drawable.tab_background_selector);
            view.setSelected(false);
        }
    }

    /**
     * 设置系统操作选项点击事件
     */
    private void setupSystemActions() {
        // 获取系统操作网格中的选项
        View systemBack = binding.layoutSystemAction.findViewById(R.id.system_back);
        View systemHome = binding.layoutSystemAction.findViewById(R.id.system_home);
        View systemMenu = binding.layoutSystemAction.findViewById(R.id.system_menu);
        View systemNotifications = binding.layoutSystemAction.findViewById(R.id.system_notifications);

        // 获取原始RadioButton
        RadioButton rbBack = binding.layoutSystemAction.findViewById(R.id.rb_back);
        RadioButton rbHome = binding.layoutSystemAction.findViewById(R.id.rb_home);
        RadioButton rbMenu = binding.layoutSystemAction.findViewById(R.id.rb_menu);
        RadioButton rbNotifications = binding.layoutSystemAction.findViewById(R.id.rb_notifications);

        // 设置点击事件
        systemBack.setOnClickListener(v -> {
            updateSystemActionSelection(systemBack, systemHome, systemMenu, systemNotifications);
            rbBack.setChecked(true);
        });

        systemHome.setOnClickListener(v -> {
            updateSystemActionSelection(systemHome, systemBack, systemMenu, systemNotifications);
            rbHome.setChecked(true);
        });

        systemMenu.setOnClickListener(v -> {
            updateSystemActionSelection(systemMenu, systemBack, systemHome, systemNotifications);
            rbMenu.setChecked(true);
        });

        systemNotifications.setOnClickListener(v -> {
            updateSystemActionSelection(systemNotifications, systemBack, systemHome, systemMenu);
            rbNotifications.setChecked(true);
        });
        
        // 默认选中"返回"选项
        updateSystemActionSelection(systemBack, systemHome, systemMenu, systemNotifications);
        rbBack.setChecked(true);
    }

    /**
     * 更新系统操作选项选中状态
     *
     * @param selectedView 选中的视图
     * @param otherViews   其他视图
     */
    private void updateSystemActionSelection(View selectedView, View... otherViews) {
        // 设置选中的选项背景
        selectedView.setBackgroundResource(R.drawable.tab_background_selector);
        selectedView.setSelected(true);

        // 设置其他选项背景
        for (View view : otherViews) {
            view.setBackgroundResource(R.drawable.tab_background_selector);
            view.setSelected(false);
        }
    }

    /**
     * 初始化按键捕获
     */
    private void initKeyCapture() {
        // 获取当前视图的根视图
        View rootView = getView();
        if (rootView == null) return;

        MyLog.d(TAG, "初始化按键捕获");

        // 设置按键监听器
        rootView.setFocusableInTouchMode(true);
        rootView.requestFocus();
        rootView.setOnKeyListener((v, keyCode, event) -> {
            // 只处理按键按下事件，避免重复处理
            if (event.getAction() != KeyEvent.ACTION_DOWN) {
                return false;
            }

            MyLog.d(TAG, "直接接收到按键事件: keyCode=" + keyCode + ", 捕获模式=" + isCapturingKey);

            // 如果不是捕获模式，不处理按键
            if (!isCapturingKey) {
                return false;
            }

//            // 排除一些系统按键，如音量键、返回键等
//            if (keyCode == KeyEvent.KEYCODE_BACK ||
//                keyCode == KeyEvent.KEYCODE_VOLUME_UP ||
//                keyCode == KeyEvent.KEYCODE_VOLUME_DOWN ||
//                keyCode == KeyEvent.KEYCODE_HOME) {
//                MyLog.d(TAG, "排除系统按键: keyCode=" + keyCode);
//                return false;
//            }

            // 处理按键事件
            return handleKeyEvent(keyCode, event);
        });

        MyLog.d(TAG, "按键捕获初始化完成");
    }

    /**
     * 处理按键事件
     *
     * @param keyCode 按键代码
     * @param event   按键事件
     * @return 是否处理了按键事件
     */
    private boolean handleKeyEvent(int keyCode, KeyEvent event) {
        // 更新选中的按键代码
        selectedKeyCode = keyCode;

        // 更新UI显示
        String keyName = KeyMapConstants.getKeyCodeName(keyCode);
        binding.tvSelectedKey.setText(keyName);

        // 显示提示信息
        MToast.makeTextShort("已选择按键：" + keyName);

        // 自动关闭捕获模式
        isCapturingKey = false;
        binding.tvKeyHint.setText("请按下要映射的方向盘按键或选择已知按键");
        binding.tvKeyHint.setTextColor(getResources().getColor(R.color.field_remarks_color));

        MyLog.d(TAG, "按键选择成功: keyCode=" + keyCode + ", keyName=" + keyName);

        return true;
    }

    /**
     * 删除按键映射
     */
    private void deleteKeyMapping() {
        // 创建确认对话框
        new AlertDialog.Builder(getActivity())
                .setTitle("删除确认")
                .setMessage("确定要删除这个按键映射吗？")
                .setPositiveButton("确定", (dialog, which) -> {
                    // 执行删除操作
                    if (keyMapManager.deleteKeyMap(keyMapModel)) {
                        MToast.makeTextShort("删除成功");

                        // 返回上一页
                        getNavigationScene(KeyMappingEditScene.this).pop();
                    } else {
                        MToast.makeTextShort("删除失败");
                    }
                })
                .setNegativeButton("取消", null)
                .show();
    }

    /**
     * 初始化动作类型选择
     */
    private void initActionTypeSelection() {
        // 获取水平选项卡的视图
        View horizontalTabsView = binding.horizontalTabs.getRoot();
        View tabBroadcast = horizontalTabsView.findViewById(R.id.tab_broadcast);
        View tabLaunchApp = horizontalTabsView.findViewById(R.id.tab_launch_app);
        View tabMediaControl = horizontalTabsView.findViewById(R.id.tab_media_control);
        View tabSystemAction = horizontalTabsView.findViewById(R.id.tab_system_action);

        // 设置选项卡点击事件
        tabBroadcast.setOnClickListener(v -> {
            updateTabSelection(tabBroadcast, tabLaunchApp, tabMediaControl, tabSystemAction);
            binding.rbBroadcast.setChecked(true);
        });

        tabLaunchApp.setOnClickListener(v -> {
            updateTabSelection(tabLaunchApp, tabBroadcast, tabMediaControl, tabSystemAction);
            binding.rbLaunchApp.setChecked(true);
        });

        tabMediaControl.setOnClickListener(v -> {
            updateTabSelection(tabMediaControl, tabBroadcast, tabLaunchApp, tabSystemAction);
            binding.rbMediaControl.setChecked(true);
        });

        tabSystemAction.setOnClickListener(v -> {
            updateTabSelection(tabSystemAction, tabBroadcast, tabLaunchApp, tabMediaControl);
            binding.rbSystemAction.setChecked(true);
        });

        // 设置动作类型单选按钮组的监听器
        binding.rgActionType.setOnCheckedChangeListener((group, checkedId) -> {
            // 隐藏所有配置界面
            hideAllActionLayouts();

            // 更新选项卡选中状态
            if (checkedId == R.id.rb_broadcast) {
                updateTabSelection(tabBroadcast, tabLaunchApp, tabMediaControl, tabSystemAction);
                // 发送广播
                binding.layoutBroadcast.setVisibility(View.VISIBLE);
                MyLog.d(TAG, "选择了广播类型，显示广播配置界面");
            } else if (checkedId == R.id.rb_launch_app) {
                updateTabSelection(tabLaunchApp, tabBroadcast, tabMediaControl, tabSystemAction);
                // 启动应用
                binding.layoutLaunchApp.setVisibility(View.VISIBLE);
                MyLog.d(TAG, "选择了启动应用类型，显示应用配置界面，当前可见性: " + (binding.layoutLaunchApp.getVisibility() == View.VISIBLE ? "可见" : "不可见"));
            } else if (checkedId == R.id.rb_media_control) {
                updateTabSelection(tabMediaControl, tabBroadcast, tabLaunchApp, tabSystemAction);
                // 媒体控制
                binding.layoutMediaControl.setVisibility(View.VISIBLE);
                MyLog.d(TAG, "选择了媒体控制类型，显示媒体控制配置界面");
            } else if (checkedId == R.id.rb_system_action) {
                updateTabSelection(tabSystemAction, tabBroadcast, tabLaunchApp, tabMediaControl);
                // 系统操作
                binding.layoutSystemAction.setVisibility(View.VISIBLE);
                MyLog.d(TAG, "选择了系统操作类型，显示系统操作配置界面");
                
                // 确保有一个系统操作类型被选中
                if (binding.rgSystemAction.getCheckedRadioButtonId() == -1) {
                    // 获取系统操作网格中的选项
                    View systemBack = binding.layoutSystemAction.findViewById(R.id.system_back);
                    View systemHome = binding.layoutSystemAction.findViewById(R.id.system_home);
                    View systemMenu = binding.layoutSystemAction.findViewById(R.id.system_menu);
                    View systemNotifications = binding.layoutSystemAction.findViewById(R.id.system_notifications);
                    
                    // 默认选中"返回"选项
                    RadioButton rbBack = binding.layoutSystemAction.findViewById(R.id.rb_back);
                    updateSystemActionSelection(systemBack, systemHome, systemMenu, systemNotifications);
                    rbBack.setChecked(true);
                }
            }
        });

        // 设置媒体控制类型单选按钮组的监听器
        binding.rgMediaControl.setOnCheckedChangeListener((group, checkedId) -> {
            // 不需要额外处理
        });

        // 设置系统操作类型单选按钮组的监听器
        binding.rgSystemAction.setOnCheckedChangeListener((group, checkedId) -> {
            // 不需要额外处理
        });

        // 默认选中第一个动作类型
        binding.rbBroadcast.setChecked(true);
        updateTabSelection(tabBroadcast, tabLaunchApp, tabMediaControl, tabSystemAction);
    }

    /**
     * 更新选项卡选中状态
     *
     * @param selectedTab 选中的选项卡
     * @param otherTabs   其他选项卡
     */
    private void updateTabSelection(View selectedTab, View... otherTabs) {
        // 设置选中的选项卡背景
        selectedTab.setBackgroundResource(R.drawable.tab_background_selector);
        selectedTab.setSelected(true);

        // 设置其他选项卡背景
        for (View tab : otherTabs) {
            tab.setBackgroundResource(R.drawable.tab_background_selector);
            tab.setSelected(false);
        }
    }

    /**
     * 隐藏所有动作配置界面
     */
    private void hideAllActionLayouts() {
        MyLog.d(TAG, "隐藏所有动作配置界面");

        // 记录布局可见性
        int broadcastVisibility = binding.layoutBroadcast.getVisibility();
        int launchAppVisibility = binding.layoutLaunchApp.getVisibility();
        int mediaControlVisibility = binding.layoutMediaControl.getVisibility();
        int systemActionVisibility = binding.layoutSystemAction.getVisibility();

        MyLog.d(TAG, "隐藏前布局可见性: 广播=" + (broadcastVisibility == View.VISIBLE ? "可见" : "不可见") +
                ", 启动应用=" + (launchAppVisibility == View.VISIBLE ? "可见" : "不可见") +
                ", 媒体控制=" + (mediaControlVisibility == View.VISIBLE ? "可见" : "不可见") +
                ", 系统操作=" + (systemActionVisibility == View.VISIBLE ? "可见" : "不可见"));

        // 隐藏所有布局
        binding.layoutBroadcast.setVisibility(View.GONE);
        binding.layoutLaunchApp.setVisibility(View.GONE);
        binding.layoutMediaControl.setVisibility(View.GONE);
        binding.layoutSystemAction.setVisibility(View.GONE);

        // 记录隐藏后的布局可见性
        broadcastVisibility = binding.layoutBroadcast.getVisibility();
        launchAppVisibility = binding.layoutLaunchApp.getVisibility();
        mediaControlVisibility = binding.layoutMediaControl.getVisibility();
        systemActionVisibility = binding.layoutSystemAction.getVisibility();

        MyLog.d(TAG, "隐藏后布局可见性: 广播=" + (broadcastVisibility == View.VISIBLE ? "可见" : "不可见") +
                ", 启动应用=" + (launchAppVisibility == View.VISIBLE ? "可见" : "不可见") +
                ", 媒体控制=" + (mediaControlVisibility == View.VISIBLE ? "可见" : "不可见") +
                ", 系统操作=" + (systemActionVisibility == View.VISIBLE ? "可见" : "不可见"));
    }

    /**
     * 加载应用列表
     */
    private void loadAppList() {
        // 获取应用列表
        new Thread(() -> {
            try {
                PackageManager pm = getActivity().getPackageManager();
                List<ApplicationInfo> apps = pm.getInstalledApplications(PackageManager.GET_META_DATA);

                appList.clear();

                for (ApplicationInfo app : apps) {
                    // 检查应用是否有启动意图
                    if (pm.getLaunchIntentForPackage(app.packageName) != null) {
                        AppInfo appInfo = new AppInfo();
                        appInfo.setAppName(pm.getApplicationLabel(app).toString());
                        appInfo.setPackageName(app.packageName);

                        // 判断是否是系统应用
                        boolean isSystemApp = (app.flags & ApplicationInfo.FLAG_SYSTEM) != 0;
                        appInfo.setSystem(isSystemApp);

                        appList.add(appInfo);
                    }
                }

                // 排序，非系统应用在前，系统应用在后，按名称排序
                Collections.sort(appList, (app1, app2) -> {
                    if (app1.isSystem() != app2.isSystem()) {
                        return app1.isSystem() ? 1 : -1;
                    } else {
                        return app1.getAppName().compareTo(app2.getAppName());
                    }
                });

                // 通知UI线程更新
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        // 如果正在显示应用选择对话框，刷新列表
                        if (binding != null && binding.tvSelectedApp.getText().toString().equals("点击选择应用")) {
                            MToast.makeTextShort("应用列表已更新");
                        }
                    });
                }

            } catch (Exception e) {
                MyLog.e(TAG, "Error loading app list: " + e.getMessage());
                // 通知UI线程显示错误
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        MToast.makeTextShort("加载应用列表失败：" + e.getMessage());
                    });
                }
            }
        }).start();
    }

    /**
     * 显示应用选择对话框
     */
    private void showAppSelectDialog() {
        MyLog.d(TAG, "显示应用选择对话框");

        // 显示提示消息
        MToast.makeTextShort("请先选择应用，然后点击确定按钮");

        // 使用系统统一的应用选择器
        HorizontalAppSelector.selectApp(getActivity(), 0, 0, new AppSelectionCallback() {
            @Override
            public void onAppSelected(AppInfo appInfo) {
                // 更新选中的应用包名和显示名称
                selectedPackageName = appInfo.getPackageName();
                binding.tvSelectedApp.setText(appInfo.getAppName());
                MyLog.d(TAG, "应用已选择: " + appInfo.getAppName() + ", 包名: " + appInfo.getPackageName());

                // 显示提示消息
                MToast.makeTextShort("已选择应用：" + appInfo.getAppName());
            }

            @Override
            public void onAppUninstalled(int position) {
                // 在此场景中不需要处理应用卸载
                MyLog.d(TAG, "应用卸载回调，位置: " + position);
            }
        }, true); // 排除主页应用
    }

    /**
     * 初始化键值对编辑器
     */
    private void initKeyValueEditor() {
        // 获取include中的控件
        View keyValueEditorView = binding.includeKeyValueEditor.getRoot();
        RecyclerView rvKeyValuePairs = keyValueEditorView.findViewById(R.id.rv_key_value_pairs);
        TextView tvEmptyList = keyValueEditorView.findViewById(R.id.tv_empty_list);
        TextView tvJsonPreview = keyValueEditorView.findViewById(R.id.tv_json_preview);
        Button btnAddParam = keyValueEditorView.findViewById(R.id.btn_add_param);
        Button btnImportJson = keyValueEditorView.findViewById(R.id.btn_import_json);
        Button btnPreset = keyValueEditorView.findViewById(R.id.btn_preset);
        Button btnTestBroadcast = keyValueEditorView.findViewById(R.id.btn_test_broadcast);

        // 创建键值对列表
        List<KeyValuePair> keyValuePairs = new ArrayList<>();

        // 创建适配器
        keyValueAdapter = new KeyValueAdapter(getActivity(), keyValuePairs, new KeyValueAdapter.OnKeyValueChangeListener() {
            @Override
            public void onKeyValueChanged() {
                // 更新JSON预览
                String json = KeyValueUtils.keyValuePairsToJson(keyValueAdapter.getKeyValuePairs());
                tvJsonPreview.setText(json);

                // 如果列表为空，显示空提示
                if (keyValueAdapter.getItemCount() == 0) {
                    tvEmptyList.setVisibility(View.VISIBLE);
                } else {
                    tvEmptyList.setVisibility(View.GONE);
                }
            }

            @Override
            public void onKeyValueDeleted(int position) {
                // 删除键值对
                keyValueAdapter.removeKeyValuePair(position);
            }
        });

        // 设置RecyclerView
        rvKeyValuePairs.setLayoutManager(new LinearLayoutManager(getActivity()));
        rvKeyValuePairs.setAdapter(keyValueAdapter);

        // 添加参数按钮
        btnAddParam.setOnClickListener(v -> {
            keyValueAdapter.addKeyValuePair();
            tvEmptyList.setVisibility(View.GONE);
        });

        // 导入JSON按钮
        btnImportJson.setOnClickListener(v -> {
            showJsonImportDialog(json -> {
                List<KeyValuePair> pairs = KeyValueUtils.jsonToKeyValuePairs(json);
                keyValueAdapter.setKeyValuePairs(pairs);
            });
        });

        // 使用预设按钮
        btnPreset.setOnClickListener(v -> {
            showPresetDialog(preset -> {
                // 设置广播Action
                binding.etBroadcastAction.setText(preset.getAction());

                // 设置参数列表
                keyValueAdapter.setKeyValuePairs(preset.getParams());
            });
        });

        // 测试广播按钮
        btnTestBroadcast.setOnClickListener(v -> {
            // 验证广播参数
            if (!keyValueAdapter.validateAllPairs()) {
                return;
            }

            String action = binding.etBroadcastAction.getText().toString().trim();
            if (action.isEmpty()) {
                MToast.makeTextShort("请输入广播Action");
                return;
            }

            // 获取JSON数据
            String json = KeyValueUtils.keyValuePairsToJson(keyValueAdapter.getKeyValuePairs());

            // 发送测试广播
            try {
                Intent intent = new Intent(action);
                Map<String, Object> extras = KeyValueUtils.keyValuePairsToMap(keyValueAdapter.getKeyValuePairs());

                for (Map.Entry<String, Object> entry : extras.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();

                    if (value instanceof String) {
                        intent.putExtra(key, (String) value);
                    } else if (value instanceof Integer) {
                        intent.putExtra(key, (Integer) value);
                    } else if (value instanceof Long) {
                        intent.putExtra(key, (Long) value);
                    } else if (value instanceof Float) {
                        intent.putExtra(key, (Float) value);
                    } else if (value instanceof Double) {
                        intent.putExtra(key, (Double) value);
                    } else if (value instanceof Boolean) {
                        intent.putExtra(key, (Boolean) value);
                    }
                }

                getActivity().sendBroadcast(intent);
                MToast.makeTextShort("已发送测试广播");
            } catch (Exception e) {
                MToast.makeTextShort("发送广播失败：" + e.getMessage());
            }
        });
    }

    /**
     * 显示JSON导入对话框
     */
    private void showJsonImportDialog(OnJsonImportListener listener) {
        FragmentActivity fragmentActivity = (FragmentActivity) requireActivity();
        JsonInputDialog.showDialog(getActivity(), fragmentActivity.getSupportFragmentManager(), new JsonInputDialog.OnJsonInputListener() {
            @Override
            public void onJsonInput(String json) {
                if (listener != null) {
                    listener.onJsonImported(json);
                }
            }
        });
    }

    /**
     * JSON导入监听器
     */
    private interface OnJsonImportListener {
        void onJsonImported(String json);
    }

    /**
     * 显示预设模板对话框
     */
    private void showPresetDialog(OnPresetSelectedListener listener) {
        // 创建广播预设场景
        BroadcastPresetScene presetScene = new BroadcastPresetScene(new BroadcastPresetScene.OnPresetSelectedListener() {
            @Override
            public void onPresetSelected(BroadcastPresets.PresetTemplate preset) {
                if (listener != null) {
                    listener.onPresetSelected(preset);
                }
            }
        });
        
        // 导航到广播预设场景
        getNavigationScene(this).push(presetScene);
    }

    /**
     * 预设模板选择监听器
     */
    private interface OnPresetSelectedListener {
        void onPresetSelected(BroadcastPresets.PresetTemplate preset);
    }

    /**
     * 将数据填充到UI
     */
    private void fillDataToUI() {
        if (keyMapModel == null) return;

        // 设置按键名称
        binding.tvSelectedKey.setText(keyMapModel.getKeyName());
        selectedKeyCode = keyMapModel.getKeyCode();

        // 设置描述
        binding.etDescription.setText(keyMapModel.getDescription());

        // 根据动作类型设置单选按钮
        int actionType = keyMapModel.getActionType();
        switch (actionType) {
            case KeyMapConstants.ActionType.BROADCAST:
                binding.rbBroadcast.setChecked(true);
                binding.etBroadcastAction.setText(keyMapModel.getActionData());

                // 解析extraData到键值对编辑器
                String extraData = keyMapModel.getExtraData();
                if (extraData != null && !extraData.isEmpty() && keyValueAdapter != null) {
                    List<KeyValuePair> pairs = KeyValueUtils.jsonToKeyValuePairs(extraData);
                    keyValueAdapter.setKeyValuePairs(pairs);
                }
                break;

            case KeyMapConstants.ActionType.LAUNCH_APP:
                binding.rbLaunchApp.setChecked(true);
                selectedPackageName = keyMapModel.getActionData();

                // 查找应用名称
                for (AppInfo app : appList) {
                    if (app.getPackageName().equals(selectedPackageName)) {
                        binding.tvSelectedApp.setText(app.getAppName());
                        break;
                    }
                }

                if (binding.tvSelectedApp.getText().toString().equals("点击选择应用")) {
                    binding.tvSelectedApp.setText(selectedPackageName);
                }
                break;

            case KeyMapConstants.ActionType.MEDIA_CONTROL:
                binding.rbMediaControl.setChecked(true);

                // 根据媒体控制类型设置单选按钮
                String mediaControlType = keyMapModel.getActionData();
                if (KeyMapConstants.MediaControlType.PLAY_PAUSE.equals(mediaControlType)) {
                    binding.rbPlayPause.setChecked(true);

                    // 更新视觉选中状态
                    View mediaPlayPause = binding.layoutMediaControl.findViewById(R.id.media_play_pause);
                    View mediaPrevious = binding.layoutMediaControl.findViewById(R.id.media_previous);
                    View mediaNext = binding.layoutMediaControl.findViewById(R.id.media_next);
                    View mediaStop = binding.layoutMediaControl.findViewById(R.id.media_stop);
                    updateMediaControlSelection(mediaPlayPause, mediaPrevious, mediaNext, mediaStop);

                } else if (KeyMapConstants.MediaControlType.PREVIOUS.equals(mediaControlType)) {
                    binding.rbPrevious.setChecked(true);

                    // 更新视觉选中状态
                    View mediaPlayPause = binding.layoutMediaControl.findViewById(R.id.media_play_pause);
                    View mediaPrevious = binding.layoutMediaControl.findViewById(R.id.media_previous);
                    View mediaNext = binding.layoutMediaControl.findViewById(R.id.media_next);
                    View mediaStop = binding.layoutMediaControl.findViewById(R.id.media_stop);
                    updateMediaControlSelection(mediaPrevious, mediaPlayPause, mediaNext, mediaStop);

                } else if (KeyMapConstants.MediaControlType.NEXT.equals(mediaControlType)) {
                    binding.rbNext.setChecked(true);

                    // 更新视觉选中状态
                    View mediaPlayPause = binding.layoutMediaControl.findViewById(R.id.media_play_pause);
                    View mediaPrevious = binding.layoutMediaControl.findViewById(R.id.media_previous);
                    View mediaNext = binding.layoutMediaControl.findViewById(R.id.media_next);
                    View mediaStop = binding.layoutMediaControl.findViewById(R.id.media_stop);
                    updateMediaControlSelection(mediaNext, mediaPlayPause, mediaPrevious, mediaStop);

                } else if (KeyMapConstants.MediaControlType.STOP.equals(mediaControlType)) {
                    binding.rbStop.setChecked(true);

                    // 更新视觉选中状态
                    View mediaPlayPause = binding.layoutMediaControl.findViewById(R.id.media_play_pause);
                    View mediaPrevious = binding.layoutMediaControl.findViewById(R.id.media_previous);
                    View mediaNext = binding.layoutMediaControl.findViewById(R.id.media_next);
                    View mediaStop = binding.layoutMediaControl.findViewById(R.id.media_stop);
                    updateMediaControlSelection(mediaStop, mediaPlayPause, mediaPrevious, mediaNext);
                }
                break;

            case KeyMapConstants.ActionType.SYSTEM_ACTION:
                binding.rbSystemAction.setChecked(true);

                // 根据系统操作类型设置单选按钮
                String systemActionType = keyMapModel.getActionData();
                if (KeyMapConstants.SystemActionType.BACK.equals(systemActionType)) {
                    binding.rbBack.setChecked(true);

                    // 更新视觉选中状态
                    View systemBack = binding.layoutSystemAction.findViewById(R.id.system_back);
                    View systemHome = binding.layoutSystemAction.findViewById(R.id.system_home);
                    View systemMenu = binding.layoutSystemAction.findViewById(R.id.system_menu);
                    View systemNotifications = binding.layoutSystemAction.findViewById(R.id.system_notifications);
                    updateSystemActionSelection(systemBack, systemHome, systemMenu, systemNotifications);

                } else if (KeyMapConstants.SystemActionType.HOME.equals(systemActionType)) {
                    binding.rbHome.setChecked(true);

                    // 更新视觉选中状态
                    View systemBack = binding.layoutSystemAction.findViewById(R.id.system_back);
                    View systemHome = binding.layoutSystemAction.findViewById(R.id.system_home);
                    View systemMenu = binding.layoutSystemAction.findViewById(R.id.system_menu);
                    View systemNotifications = binding.layoutSystemAction.findViewById(R.id.system_notifications);
                    updateSystemActionSelection(systemHome, systemBack, systemMenu, systemNotifications);

                } else if (KeyMapConstants.SystemActionType.MENU.equals(systemActionType)) {
                    binding.rbMenu.setChecked(true);

                    // 更新视觉选中状态
                    View systemBack = binding.layoutSystemAction.findViewById(R.id.system_back);
                    View systemHome = binding.layoutSystemAction.findViewById(R.id.system_home);
                    View systemMenu = binding.layoutSystemAction.findViewById(R.id.system_menu);
                    View systemNotifications = binding.layoutSystemAction.findViewById(R.id.system_notifications);
                    updateSystemActionSelection(systemMenu, systemBack, systemHome, systemNotifications);

                } else if (KeyMapConstants.SystemActionType.NOTIFICATIONS.equals(systemActionType)) {
                    binding.rbNotifications.setChecked(true);

                    // 更新视觉选中状态
                    View systemBack = binding.layoutSystemAction.findViewById(R.id.system_back);
                    View systemHome = binding.layoutSystemAction.findViewById(R.id.system_home);
                    View systemMenu = binding.layoutSystemAction.findViewById(R.id.system_menu);
                    View systemNotifications = binding.layoutSystemAction.findViewById(R.id.system_notifications);
                    updateSystemActionSelection(systemNotifications, systemBack, systemHome, systemMenu);
                }
                break;
        }
    }

    /**
     * 保存按键映射
     */
    private void saveKeyMapping() {
        // 验证数据
        if (!validateData()) {
            return;
        }

        // 设置按键代码和名称
        keyMapModel.setKeyCode(selectedKeyCode);
        keyMapModel.setKeyName(KeyMapConstants.getKeyCodeName(selectedKeyCode));

        // 设置描述
        keyMapModel.setDescription(binding.etDescription.getText().toString().trim());

        // 设置动作类型和数据
        int checkedActionTypeId = binding.rgActionType.getCheckedRadioButtonId();

        if (checkedActionTypeId == R.id.rb_broadcast) {
            // 发送广播
            keyMapModel.setActionType(KeyMapConstants.ActionType.BROADCAST);
            String broadcastAction = binding.etBroadcastAction.getText().toString().trim();
            keyMapModel.setActionData(broadcastAction);

            // 设置额外数据
            String extraData = "";
            if (keyValueAdapter != null) {
                // 验证键值对是否有效
                if (!keyValueAdapter.validateAllPairs()) {
                    return;
                }

                extraData = KeyValueUtils.keyValuePairsToJson(keyValueAdapter.getKeyValuePairs());
            }

            keyMapModel.setExtraData(extraData);

        } else if (checkedActionTypeId == R.id.rb_launch_app) {
            // 启动应用
            keyMapModel.setActionType(KeyMapConstants.ActionType.LAUNCH_APP);
            keyMapModel.setActionData(selectedPackageName);
            keyMapModel.setExtraData("");

        } else if (checkedActionTypeId == R.id.rb_media_control) {
            // 媒体控制
            keyMapModel.setActionType(KeyMapConstants.ActionType.MEDIA_CONTROL);

            // 根据选中的单选按钮设置媒体控制类型
            int checkedMediaControlId = binding.rgMediaControl.getCheckedRadioButtonId();
            String mediaControlType = KeyMapConstants.MediaControlType.PLAY_PAUSE;

            if (checkedMediaControlId == R.id.rb_play_pause) {
                mediaControlType = KeyMapConstants.MediaControlType.PLAY_PAUSE;
            } else if (checkedMediaControlId == R.id.rb_previous) {
                mediaControlType = KeyMapConstants.MediaControlType.PREVIOUS;
            } else if (checkedMediaControlId == R.id.rb_next) {
                mediaControlType = KeyMapConstants.MediaControlType.NEXT;
            } else if (checkedMediaControlId == R.id.rb_stop) {
                mediaControlType = KeyMapConstants.MediaControlType.STOP;
            }

            keyMapModel.setActionData(mediaControlType);
            keyMapModel.setExtraData("");

        } else if (checkedActionTypeId == R.id.rb_system_action) {
            // 系统操作
            keyMapModel.setActionType(KeyMapConstants.ActionType.SYSTEM_ACTION);

            // 根据选中的单选按钮设置系统操作类型
            int checkedSystemActionId = binding.rgSystemAction.getCheckedRadioButtonId();
            String systemActionType = KeyMapConstants.SystemActionType.BACK;

            if (checkedSystemActionId == R.id.rb_back) {
                systemActionType = KeyMapConstants.SystemActionType.BACK;
            } else if (checkedSystemActionId == R.id.rb_home) {
                systemActionType = KeyMapConstants.SystemActionType.HOME;
            } else if (checkedSystemActionId == R.id.rb_menu) {
                systemActionType = KeyMapConstants.SystemActionType.MENU;
            } else if (checkedSystemActionId == R.id.rb_notifications) {
                systemActionType = KeyMapConstants.SystemActionType.NOTIFICATIONS;
            }

            keyMapModel.setActionData(systemActionType);
            keyMapModel.setExtraData("");
        }

        // 设置启用状态
        keyMapModel.setEnabled(1);

        // 保存数据
        if (keyMapManager.saveKeyMap(keyMapModel)) {
            MToast.makeTextShort("保存成功");

            // 设置结果并返回上一页
            Bundle result = new Bundle();
            result.putInt("result", 1);
            getNavigationScene(KeyMappingEditScene.this).setResult(KeyMappingEditScene.this, result);
            getNavigationScene(KeyMappingEditScene.this).pop();
        } else {
            MToast.makeTextShort("保存失败");
        }
    }

    /**
     * 验证数据
     *
     * @return 数据是否有效
     */
    private boolean validateData() {
        // 验证按键代码
        if (selectedKeyCode == -1) {
            MToast.makeTextShort("请选择按键");
            return false;
        }

        // 根据动作类型验证数据
        int checkedActionTypeId = binding.rgActionType.getCheckedRadioButtonId();

        if (checkedActionTypeId == R.id.rb_broadcast) {
            // 验证广播Action
            String broadcastAction = binding.etBroadcastAction.getText().toString().trim();
            if (broadcastAction.isEmpty()) {
                MToast.makeTextShort("请输入广播Action");
                return false;
            }

        } else if (checkedActionTypeId == R.id.rb_launch_app) {
            // 验证应用包名
            if (selectedPackageName == null || selectedPackageName.isEmpty()) {
                MToast.makeTextShort("请选择应用");
                return false;
            }

        } else if (checkedActionTypeId == R.id.rb_media_control) {
            // 验证媒体控制类型
            if (binding.rgMediaControl.getCheckedRadioButtonId() == -1) {
                MToast.makeTextShort("请选择媒体控制类型");
                return false;
            }

        } else if (checkedActionTypeId == R.id.rb_system_action) {
            // 验证系统操作类型
            if (binding.rgSystemAction.getCheckedRadioButtonId() == -1) {
                MToast.makeTextShort("请选择系统操作类型");
                return false;
            }
        } else {
            // 未选择动作类型
            MToast.makeTextShort("请选择动作类型");
            return false;
        }

        return true;
    }

    /**
     * 设置按键事件监听
     */
    private void setupKeyEventListener() {
        MyLog.d(TAG, "设置按键事件监听");

        // 移除旧的观察者（如果有）
        if (keyEventObserver != null) {
            KeyEventScopeBus.keyCodeEvent().removeObserver(keyEventObserver);
            keyEventObserver = null;
            MyLog.d(TAG, "移除旧的按键事件观察者");
        }

        try {
            // 创建并注册新的观察者
            keyEventObserver = new ObserverWrapper<KeyEventDefine.KeyCodeEvent>(false) {
                @Override
                public void onChanged(KeyEventDefine.KeyCodeEvent keyCodeEvent) {
                    if (keyCodeEvent == null) {
                        MyLog.e(TAG, "接收到空的按键事件");
                        return;
                    }

                    int keyCode = keyCodeEvent.getKeyCode();
                    KeyEvent event = keyCodeEvent.getKeyEvent();

                    MyLog.d(TAG, "从ElegantBus接收到按键事件: keyCode=" + keyCode + ", isCapturingKey=" + isCapturingKey);

                    // 只处理按键按下事件
                    if (event.getAction() != KeyEvent.ACTION_DOWN) {
                        return;
                    }

                    // 如果不是捕获模式，不处理按键
                    if (!isCapturingKey) {
                        return;
                    }

                    // 排除一些系统按键
                    if (keyCode == KeyEvent.KEYCODE_BACK ||
                            keyCode == KeyEvent.KEYCODE_VOLUME_UP ||
                            keyCode == KeyEvent.KEYCODE_VOLUME_DOWN ||
                            keyCode == KeyEvent.KEYCODE_HOME) {
                        MyLog.d(TAG, "排除系统按键: keyCode=" + keyCode);
                        return;
                    }

                    // 在UI线程中处理
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            if (binding != null) {
                                handleKeyEvent(keyCode, event);
                            } else {
                                MyLog.e(TAG, "binding为空，无法更新UI");
                            }
                        });
                    } else {
                        MyLog.e(TAG, "getActivity()为空，无法在UI线程中处理");
                    }
                }
            };

            // 注册观察者
            KeyEventScopeBus.keyCodeEvent().observe(this, keyEventObserver);
            MyLog.d(TAG, "已设置ElegantBus按键事件监听");
        } catch (Exception e) {
            MyLog.e(TAG, "设置按键事件监听失败: " + e.getMessage());
        }
    }

    /**
     * 当页面销毁时释放资源
     */
    @Override
    public void onDestroyView() {
        super.onDestroyView();

        // 移除按键事件监听
        if (keyEventObserver != null) {
            KeyEventScopeBus.keyCodeEvent().removeObserver(keyEventObserver);
            keyEventObserver = null;
        }

        binding = null;
    }

    /**
     * 处理按键事件
     *
     * @param keyCode 按键代码
     * @param event   按键事件
     * @return 是否处理了按键事件
     */
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        MyLog.d(TAG, "onKeyDown: keyCode=" + keyCode + ", isCapturingKey=" + isCapturingKey);

        // 如果不是捕获模式，不处理按键
        if (!isCapturingKey) {
            return false;
        }

        // 排除一些系统按键，如音量键、返回键等
        if (keyCode == KeyEvent.KEYCODE_BACK ||
                keyCode == KeyEvent.KEYCODE_VOLUME_UP ||
                keyCode == KeyEvent.KEYCODE_VOLUME_DOWN ||
                keyCode == KeyEvent.KEYCODE_HOME) {
            MyLog.d(TAG, "排除系统按键: keyCode=" + keyCode);
            return false;
        }

        // 处理按键事件
        if (binding != null) {
            return handleKeyEvent(keyCode, event);
        } else {
            MyLog.e(TAG, "binding为空，无法更新UI");
            return false;
        }
    }

    /**
     * 检查布局可见性
     */
    private void checkLayoutVisibility() {
        // 延迟200ms执行，确保布局已经完成初始化
        new Handler().postDelayed(() -> {
            if (getActivity() == null || binding == null) return;

            // 检查各个布局的可见性
            int broadcastVisibility = binding.layoutBroadcast.getVisibility();
            int launchAppVisibility = binding.layoutLaunchApp.getVisibility();
            int mediaControlVisibility = binding.layoutMediaControl.getVisibility();
            int systemActionVisibility = binding.layoutSystemAction.getVisibility();

            MyLog.d(TAG, "布局可见性检查: 广播=" + (broadcastVisibility == View.VISIBLE ? "可见" : "不可见") +
                    ", 启动应用=" + (launchAppVisibility == View.VISIBLE ? "可见" : "不可见") +
                    ", 媒体控制=" + (mediaControlVisibility == View.VISIBLE ? "可见" : "不可见") +
                    ", 系统操作=" + (systemActionVisibility == View.VISIBLE ? "可见" : "不可见"));

            // 检查当前选中的动作类型
            int checkedId = binding.rgActionType.getCheckedRadioButtonId();
            MyLog.d(TAG, "当前选中的动作类型: " +
                    (checkedId == R.id.rb_broadcast ? "广播" :
                            (checkedId == R.id.rb_launch_app ? "启动应用" :
                                    (checkedId == R.id.rb_media_control ? "媒体控制" :
                                            (checkedId == R.id.rb_system_action ? "系统操作" : "未知")))));

            // 如果启动应用布局应该可见但实际不可见，尝试强制显示
            if (checkedId == R.id.rb_launch_app && launchAppVisibility != View.VISIBLE) {
                MyLog.d(TAG, "启动应用布局应该可见但实际不可见，尝试强制显示");
                binding.layoutLaunchApp.setVisibility(View.VISIBLE);
            }
        }, 200);
    }
} 