package com.smartcar.easylauncher.modules.theme;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.bumptech.glide.Glide;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.databinding.SceneMyThemeBinding;
import com.smartcar.easylauncher.data.database.dbmager.DbManager;
import com.smartcar.easylauncher.data.database.entity.ThemeInfoModel;
import com.smartcar.easylauncher.shared.dialog.LoginDialog;
import com.smartcar.easylauncher.core.manager.UserManager;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.util.List;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.disposables.CompositeDisposable;


/**
 * 我的主题Scene
 * 个人中心页面，显示用户信息、本地主题数量、壁纸数量等
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
public class MyThemeScene extends BaseScene {

    private static final String TAG = "MyThemeScene";

    private SceneMyThemeBinding binding;
    private final CompositeDisposable disposables = new CompositeDisposable();
    private DbManager dbManager;

    @NonNull
    @Override
    public ViewGroup onCreateNewView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = SceneMyThemeBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initViews();
        initData();
        loadUserInfo();
        loadThemeStatistics();
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        // 设置点击事件
        binding.cardLocalThemes.setOnClickListener(v -> openLocalThemeScene());
        binding.cardWallpapers.setOnClickListener(v -> openWallpaperScene());
        binding.tvThemeManagement.setOnClickListener(v -> openLocalThemeScene());
        binding.tvWallpaperManagement.setOnClickListener(v -> showWallpaperComingSoon());
        binding.tvThemeHelp.setOnClickListener(v -> openThemeHelp());
        binding.tvLoginStatus.setOnClickListener(v -> handleLoginClick());
    }

    /**
     * 初始化数据
     */
    private void initData() {
        dbManager = DbManager.getInstance();
    }

    /**
     * 加载用户信息
     */
    private void loadUserInfo() {
        MyLog.v(TAG, "加载用户信息");

        // 检查登录状态
        boolean isLogin = UserManager.isLogin();

        if (isLogin) {
            // 已登录，显示用户信息
            String userName = UserManager.getUserName();
            String nickName = UserManager.getNickName();
            String userAvatar = UserManager.getUserAvatar();

            // 显示用户名（优先显示昵称）
            String displayName = !TextUtils.isEmpty(nickName) ? nickName : userName;
            if (TextUtils.isEmpty(displayName)) {
                displayName = "智车用户";
            }
            binding.tvUserName.setText(displayName);
            binding.tvUserStatus.setText("已登录");
            binding.tvLoginStatus.setText("已登录");
            binding.tvLoginStatus.setClickable(false);

            // 加载用户头像
            loadUserAvatar(userAvatar);
        } else {
            // 未登录，显示默认信息
            binding.tvUserName.setText("智车用户");
            binding.tvUserStatus.setText("未登录");
            binding.tvLoginStatus.setText("登录");
            binding.tvLoginStatus.setClickable(true);
            binding.ivUserAvatar.setImageResource(R.drawable.ic_user_default_avatar);
        }
    }

    /**
     * 加载用户头像
     */
    private void loadUserAvatar(String avatarUrl) {
        try {
            if (!TextUtils.isEmpty(avatarUrl)) {
                String fullUrl = avatarUrl.startsWith("http") ? avatarUrl : Const.DEV_BASEURL + avatarUrl;
                Glide.with(requireActivity())
                        .load(fullUrl)
                        .placeholder(R.drawable.ic_user_default_avatar)
                        .error(R.drawable.ic_user_default_avatar)
                        .into(binding.ivUserAvatar);
            } else {
                binding.ivUserAvatar.setImageResource(R.drawable.ic_user_default_avatar);
            }
        } catch (Exception e) {
            MyLog.e(TAG, "加载用户头像失败", e);
            binding.ivUserAvatar.setImageResource(R.drawable.ic_user_default_avatar);
        }
    }

    /**
     * 加载主题统计信息
     */
    @SuppressLint("CheckResult")
    private void loadThemeStatistics() {
        MyLog.v(TAG, "加载主题统计信息");

        disposables.add(
            dbManager.getAll()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    this::onThemeStatisticsLoaded,
                    this::onThemeStatisticsError
                )
        );
    }

    /**
     * 主题统计信息加载成功
     */
    private void onThemeStatisticsLoaded(List<ThemeInfoModel> themes) {
        MyLog.v(TAG, "主题统计信息加载成功，总数量: " + themes.size());

        // 计算本地主题数量（排除默认主题）
        int localThemeCount = 0;
        for (ThemeInfoModel theme : themes) {
            if (theme.getClassify() != 0) { // 非默认主题
                localThemeCount++;
            }
        }

        // 更新UI
        binding.tvLocalThemeCount.setText(String.valueOf(localThemeCount));

        // 壁纸数量暂时设为0（功能未实现）
        binding.tvWallpaperCount.setText("0");
    }

    /**
     * 主题统计信息加载失败
     */
    private void onThemeStatisticsError(Throwable throwable) {
        MyLog.e(TAG, "主题统计信息加载失败", throwable);

        // 设置默认值
        binding.tvLocalThemeCount.setText("0");
        binding.tvWallpaperCount.setText("0");
    }

    /**
     * 处理登录点击事件
     */
    private void handleLoginClick() {
        if (!UserManager.isLogin()) {
            showLoginDialog();
        }
    }

    /**
     * 显示登录对话框
     */
    private void showLoginDialog() {
        try {
            // Scene框架中通过requireActivity()获取Activity，然后获取FragmentManager
            FragmentActivity activity = (FragmentActivity) requireActivity();
            LoginDialog loginDialog = LoginDialog.createDialog(activity.getSupportFragmentManager(), requireActivity());

            // 设置登录成功回调
            loginDialog.setOnLoginSuccessListener(userModel -> {
                MyLog.d(TAG, "登录成功，刷新用户信息");
                loadUserInfo();
            });

            loginDialog.showDialog();
        } catch (Exception e) {
            MyLog.e(TAG, "显示登录对话框失败", e);
            MToast.makeTextShort("登录功能暂时不可用");
        }
    }

    /**
     * 打开本地主题页面
     */
    private void openLocalThemeScene() {
        try {
            LocalThemeScene localThemeScene = new LocalThemeScene();
            getNavigationScene(this).push(localThemeScene);
            MyLog.d(TAG, "跳转到本地主题页面");
        } catch (Exception e) {
            MyLog.e(TAG, "跳转到本地主题页面失败", e);
            MToast.makeTextShort("跳转失败，请重试");
        }
    }

    /**
     * 打开壁纸页面
     */
    private void openWallpaperScene() {
        showWallpaperComingSoon();
    }

    /**
     * 显示壁纸功能敬请期待
     */
    private void showWallpaperComingSoon() {
        MToast.makeTextShort("壁纸功能敬请期待");
    }

    /**
     * 打开主题帮助页面
     */
    private void openThemeHelp() {
        try {
            ThemeHelpScene themeHelpScene = new ThemeHelpScene();
            getNavigationScene(this).push(themeHelpScene);
            MyLog.d(TAG, "跳转到主题帮助页面");
        } catch (Exception e) {
            MyLog.e(TAG, "跳转到主题帮助页面失败", e);
            MToast.makeTextShort("跳转失败，请重试");
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // 页面恢复时刷新数据
        loadUserInfo();
        loadThemeStatistics();
    }





    @Override
    public void onDestroyView() {
        super.onDestroyView();
        disposables.clear();
        binding = null;
    }
}
