package com.smartcar.easylauncher.modules.expense;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;
import static com.bytedance.scene.navigation.NavigationSceneGetter.requireNavigationScene;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;

import com.bytedance.scene.group.UserVisibleHintGroupScene;
import com.bytedance.scene.interfaces.PushOptions;
import com.bytedance.scene.interfaces.PushResultCallback;
import com.github.AAChartModel.AAChartCore.AAChartCreator.AAChartModel;
import com.github.AAChartModel.AAChartCore.AAChartCreator.AASeriesElement;
import com.github.AAChartModel.AAChartCore.AAChartEnum.AAChartType;
import com.github.AAChartModel.AAChartCore.AAOptionsModel.AADataLabels;
import com.github.AAChartModel.AAChartCore.AAOptionsModel.AAPie;
import com.github.AAChartModel.AAChartCore.AATools.AAGradientColor;
import com.google.android.material.appbar.AppBarLayout;
import com.google.gson.Gson;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.core.constants.StatusCodeModel;
import com.smartcar.easylauncher.databinding.ActivityCostStatisticsBinding;
import com.smartcar.easylauncher.data.database.dbmager.CostDbManager;
import com.smartcar.easylauncher.data.database.entity.Expense;
import com.smartcar.easylauncher.core.manager.DataManager;
import com.smartcar.easylauncher.core.manager.UserManager;
import com.smartcar.easylauncher.model.ExpenseView;
import com.smartcar.easylauncher.data.model.vehicle.ExpenseListModel;
import com.smartcar.easylauncher.modules.car.AddCarScene;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.Utilities;
import com.smartcar.easylauncher.shared.view.behavior.AppBarLayoutOverScrollViewBehavior;
import com.smartcar.easylauncher.shared.view.segmentcontrlo.SegmentedControlItem;
import com.smartcar.easylauncher.shared.view.segmentcontrlo.SegmentedControlView;
import com.smartcar.easylauncher.shared.widget.button.AllAngleExpandableButton;
import com.smartcar.easylauncher.shared.widget.button.ButtonData;
import com.smartcar.easylauncher.shared.widget.button.ButtonEventListener;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import rxhttp.RxHttp;


/**
 * <AUTHOR>
 * @date 2024/07/04
 */
public class ExpenseHomeScene extends UserVisibleHintGroupScene implements View.OnClickListener {
    private static final String TAG = "CostStatisticsScene";
    private ActivityCostStatisticsBinding binding;
    private ArrayList<ExpenseView> arrayList = new ArrayList<>();
    private List<Expense> expensesList;

    @NonNull
    @Override
    public ViewGroup onCreateView(@NonNull LayoutInflater inflater, @NonNull ViewGroup container, Bundle savedInstanceState) {
        binding = ActivityCostStatisticsBinding.inflate(getLayoutInflater());
        binding.csContent.AAChartView.setIsClearBackgroundColor(true);
        binding.csContent.AALineChart.setIsClearBackgroundColor(true);

        initData();
        // setupListView();
        initDatabase();
        //初始化折叠头部
        initHeaderView();
        initView();
        //初始化添加按钮
        initAddButton();
        return binding.getRoot();
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @SuppressLint("CheckResult")
    private void initDatabase() {
        String url = Const.VEHICLE_EXPENSE_LIST;
        MyLog.v(TAG, "请求地址：" + url);
        RxHttp.get(url)
                .toObservable(ExpenseListModel.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(expenseListModel -> {
                    if (expenseListModel.getCode() == StatusCodeModel.SUCCESS) {
                        // 请求成功
                        MyLog.v(TAG, "请求成功" + new Gson().toJson(expenseListModel));
                        List<Expense> expenses = expenseListModel.getRows();
                        expensesList = expenses.stream()
                                .sorted((e1, e2) -> e1.getDate().compareTo(e2.getDate()))
                                .collect(Collectors.toUnmodifiableList());

                        // 按费用ID降序排序
                        arrayList.sort((e1, e2) -> Long.compare(e2.getExpenseId(), e1.getExpenseId()));
                        setupUIData();  // 设置UI数据
                        Object[][] pieData = getPieData(); // 获取饼图数据
                        AAChartModel chartModel = configurePieChart(pieData); // 配置饼图模型


                        binding.csContent.AAChartView.aa_drawChartWithChartModel(chartModel);
                        binding.csContent.AALineChart.aa_drawChartWithChartModel(configureColorfulGradientAreaChart());
                    }

                }, throwable -> {
                    // 请求失败

                    MyLog.v(TAG, "请求失败" + throwable.getMessage());
                });
    }

    private void setupListView() {

        CostDbManager.getInstance().getExpensesByCarId(DataManager.getCarId()).subscribe(new Consumer<List<Expense>>() {
            @Override
            public void accept(List<Expense> expenses) throws Exception {
                expensesList = expenses.stream()
                        .sorted((e1, e2) -> e1.getDate().compareTo(e2.getDate()))
                        .collect(Collectors.toUnmodifiableList());

                // 按费用ID降序排序
                arrayList.sort((e1, e2) -> Long.compare(e2.getExpenseId(), e1.getExpenseId()));
                setupUIData();  // 设置UI数据
                Object[][] pieData = getPieData(); // 获取饼图数据
                AAChartModel chartModel = configurePieChart(pieData); // 配置饼图模型


                binding.csContent.AAChartView.aa_drawChartWithChartModel(chartModel);
                binding.csContent.AALineChart.aa_drawChartWithChartModel(configureColorfulGradientAreaChart());
            }
        });

    }

    public static AAChartModel configurePieChart(Object[][] pieData) {
        Object[][] stopsArr = {
                {0.00, "#febc0f"},
                {0.50, "#FF14d4"},
                {1.00, "#0bf8f5"},
        };//颜色字符串设置支持十六进制类型和 rgba 类型

        return new AAChartModel()
                .chartType(AAChartType.Pie)
                .backgroundColor("#E6E7E9")
                .colorsTheme(new String[]{"#A5D63F", "#2A82E4", "#06caf4", "#7dffc0"})
                .dataLabelsEnabled(true)//是否直接显示扇形图数据
                .tooltipEnabled(false)
                .series(new AAPie[]{
                        new AAPie()
                                .name("费用分类")
                                .innerSize("60%")
                                .dataLabels(new AADataLabels()
                                        .enabled(true)
                                        .useHTML(true)
                                        .distance(5)
                                        .format("<b>{point.name}</b>: <br> {point.percentage:.1f} %"))
                                .data(pieData)
                });
    }

    private Object[][] getPieData() {
        List<Expense> thisMonthExpenses = Utilities.Companion.getThisMonthExpenses(expensesList);
        double spentRefuel = 0.0;
        double spentInsurance = 0.0;
        double spentTax = 0.0;
        double spentMaintenance = 0.0;

        for (Expense e : thisMonthExpenses) {
            switch (e.getType()) {
                case Const.FUEL_TYPE_GAS:
                    spentRefuel += e.getSpent();
                    break;
                case Const.FUEL_TYPE_REPAIR:
                    spentMaintenance += e.getSpent();
                    break;
                case Const.FUEL_TYPE_TAXES:
                    spentTax += e.getSpent();
                    break;
                default:
                    spentInsurance += e.getSpent();
                    break;
            }
        }

        double total = spentRefuel + spentMaintenance + spentInsurance + spentTax;
        List<Object[]> pieDataList = new ArrayList<>();
        if (spentRefuel > 0.0) {
            pieDataList.add(new Object[]{getString(R.string.refuel_title), (spentRefuel / total) * 100});
        }
        if (spentMaintenance > 0.0) {
            pieDataList.add(new Object[]{getString(R.string.maintenance_title), (spentMaintenance / total) * 100});
        }
        if (spentInsurance > 0.0) {
            pieDataList.add(new Object[]{getString(R.string.insurance_title), (spentInsurance / total) * 100});
        }
        if (spentTax > 0.0) {
            pieDataList.add(new Object[]{getString(R.string.tax_title), (spentTax / total) * 100});
        }

        // 将List<Object[]>转换为Object[][]
        Object[][] pieData = new Object[pieDataList.size()][2];
        for (int i = 0; i < pieDataList.size(); i++) {
            pieData[i] = pieDataList.get(i);
        }

        return pieData;
    }


    private AAChartModel configureColorfulGradientAreaChart() {
        List<Expense> expensesThisMonth = Utilities.Companion.getThisMonthExpenses(expensesList);

        // 提取日期和费用数据
        List<String> dateCategories = new ArrayList<>();
        List<Double> dataPoints = new ArrayList<>();
        double totalSpent = 0.0;

        for (Expense expense : expensesThisMonth) {
            dateCategories.add(expense.getDate()); // 假设expense.getDate()返回的是日期字符串
            totalSpent += expense.getSpent();
            dataPoints.add(totalSpent);
        }

        return new AAChartModel()
                .chartType(AAChartType.Areaspline)
                .categories(dateCategories.toArray(new String[0]))
                .markerRadius(0)
                .subtitle("费用趋势")
                .yAxisLineWidth(0)
                .yAxisGridLineWidth(0)
                .legendEnabled(false)
                .series(new AASeriesElement[]{
                                new AASeriesElement()
                                        .name("Tokyo Hot")
                                        .lineWidth(3)
                                        .color(AAGradientColor.MysticMauve)
                                        .data(dataPoints.toArray(new Object[0]))
                        }
                );
    }


    private void setupUIData() {
        List<Expense> thisMonthExpenses = Utilities.Companion.getThisMonthExpenses(expensesList);
        double spent = Utilities.Companion.getTotalSpent(thisMonthExpenses);
        double emitted = Utilities.Companion.getEmitted(thisMonthExpenses, DataManager.getPowerType(), DataManager.getEmission());
        double consumption = Utilities.Companion.getAvgConsumption(thisMonthExpenses);

        binding.middleLayout.tvWelcomeName.setText(UserManager.getNickName());
        binding.middleLayout.tvCarName.setText(DataManager.getBrand() + " " + DataManager.getModel());
        binding.middleLayout.tvSpentThisMonth.setText(String.valueOf(spent));
        binding.middleLayout.tvEmittedThisMonth.setText(String.valueOf(emitted));
        binding.middleLayout.tvAvgConsumptionThisMonth.setText(String.valueOf(consumption));

    }


    /**
     * 初始化折叠头部
     */
    private void initHeaderView() {
        // 为appbarLayout添加滚动偏移变化的监听器
        binding.appbarLayout.addOnOffsetChangedListener(new AppBarLayout.OnOffsetChangedListener() {
            @Override
            public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {
                // 计算滚动百分比
                float percent = Math.abs(verticalOffset) / (float) appBarLayout.getTotalScrollRange();

                // 根据滚动百分比设置titleCenterLayout的透明度
                binding.csHeadTitle.titleCenterLayout.setAlpha(percent);

            }
        });

        /* 获取AppBarLayout的OverScrollViewBehavior，并为其添加进度变化的监听器*/
        AppBarLayoutOverScrollViewBehavior myAppBarLayoutBehavoir = (AppBarLayoutOverScrollViewBehavior) ((CoordinatorLayout.LayoutParams) binding.appbarLayout.getLayoutParams()).getBehavior();
        assert myAppBarLayoutBehavoir != null;
        myAppBarLayoutBehavoir.setOnProgressChangeListener((progress, isRelease) -> {
            // 根据进度更新进度条的显示
            binding.csHeadTitle.ucProgressbar.setProgress((int) (progress * 360));

            // 如果进度为100%且未旋转且是释放状态，则可能需要刷新ViewPager中的Fragment
            if (progress == 1 && !binding.csHeadTitle.ucProgressbar.isSpinning && isRelease) {
                // 刷新viewpager里的fragment
                // ...
                Toast.makeText(getApplicationContext(), "正在刷新用户信息", Toast.LENGTH_SHORT).show();
            }

        });
    }


    /**
     * 初始化数据
     */
    private void initData() {
        List<SegmentedControlItem> model = model();

        binding.csContent.scv3.addItems(model);

        // 设置默认选中的项目
        binding.csContent.scv3.setSelectedItem(0);
        binding.csHeadTitle.titleUcTitle.setText(DataManager.getBrand() + " " + DataManager.getModel());
    }

    private List<SegmentedControlItem> model() {
        List<SegmentedControlItem> items = new ArrayList<>();
        items.add(new SegmentedControlItem("总览"));
        items.add(new SegmentedControlItem("趋势"));
        return items;
    }

    private void initView() {

        binding.ucAvater.setOnClickListener(this);
        binding.csContent.scv3.setOnSegItemClickListener(new SegmentedControlView.OnSegItemClickListener() {
            @Override
            public void onItemClick(SegmentedControlItem item, int position) {
                switch (position) {
                    case 0:
                        binding.csContent.AAChartView.setVisibility(View.VISIBLE);
                        binding.csContent.AALineChart.setVisibility(View.GONE);
                        break;
                    case 1:
                        binding.csContent.AAChartView.setVisibility(View.GONE);
                        binding.csContent.AALineChart.setVisibility(View.VISIBLE);
                        break;

                    default:

                }
            }
        });
    }





    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.bt_back) {
            requireNavigationScene(this).pop();
        } else if (view.getId() == R.id.el_mycar) {
            requireNavigationScene(this).push(new AddCarScene(null));
        }

    }

    private void initAddButton() {
        final List<ButtonData> buttonDatas = new ArrayList<>();
        int[] drawable = {R.drawable.icon_add, R.drawable.icon_refuel, R.drawable.icon_upkeep, R.drawable.icon_auto};
        for (int i = 0; i < 4; i++) {
            ButtonData buttonData;
            if (i == 0) {
                buttonData = ButtonData.buildIconButton(requireActivity(), drawable[i], 15);
                buttonData.setBackgroundColorId(requireActivity(), R.color.black);
            } else {
                buttonData = ButtonData.buildIconButton(requireActivity(), drawable[i], 0);
            }
            buttonDatas.add(buttonData);
        }
        binding.fabAddExpense.setButtonDatas(buttonDatas);
        setListener(binding.fabAddExpense);
    }

    /**
     * 创建一个包含索引的Bundle对象
     *
     * @param index 要放入Bundle的索引值
     * @return 包含索引的Bundle
     */
    private Bundle getBundle(int index) {
        Bundle bundle = new Bundle();
        bundle.putInt(Const.FUEL_TYPE, index); // 将索引值放入Bundle
        return bundle;
    }

    private void setListener(AllAngleExpandableButton button) {
        button.setButtonEventListener(new ButtonEventListener() {
            @Override
            public void onButtonClicked(int index) {
                PushOptions pushOptions = new PushOptions.Builder().setAnimation(requireActivity(), R.anim.slide_in_from_right, R.anim.slide_out_to_left)
                        .setPushResultCallback(new PushResultCallback() {
                            @Override
                            public void onResult(@Nullable Object o) {
                                if (o != null) {
                                    MyLog.v(TAG, "返回结果：" + o);
                                    initDatabase();
                                }
                            }
                        }).build();
                switch (index) {
                    case 1:

                        getNavigationScene(ExpenseHomeScene.this).push(AddExpenseScene.class, getBundle(Const.FUEL_TYPE_GAS), pushOptions);
                        break;
                    case 2:
                        getNavigationScene(ExpenseHomeScene.this).push(AddExpenseScene.class, getBundle(Const.FUEL_TYPE_REPAIR), pushOptions);
                        break;
                    case 3:
                        getNavigationScene(ExpenseHomeScene.this).push(AddExpenseScene.class, getBundle(Const.FUEL_TYPE_INSURANCE), pushOptions);
                        break;
                    default:
                }
            }

            @Override
            public void onExpand() {
//                showToast("onExpand");
            }

            @Override
            public void onCollapse() {
//                showToast("onCollapse");
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

}