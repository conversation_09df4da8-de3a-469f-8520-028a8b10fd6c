package com.smartcar.easylauncher.modules.tpms.test;

import android.content.Context;
import com.smartcar.easylauncher.modules.tpms.device.TpmsDevice;
import com.smartcar.easylauncher.modules.tpms.device.usb.UsbTpmsDevice;
import com.smartcar.easylauncher.shared.utils.MyLog;
import java.nio.ByteBuffer;

/**
 * TPMS USB设备测试类
 */
public class UsbDeviceTest {
    private static final String TAG = "UsbDeviceTest";
    
    private final Context context;
    private final TpmsDevice device;
    
    public UsbDeviceTest(Context context) {
        this.context = context;
        this.device = new UsbTpmsDevice(context);
        
        // 设置数据接收回调
        device.setDataCallback(new TpmsDevice.DataCallback() {
            @Override
            public void onDataReceived(ByteBuffer buffer) {
                byte[] data = new byte[buffer.remaining()];
                buffer.get(data);
                MyLog.d(TAG, String.format("收到数据: %s", bytesToHexString(data)));
            }
        });
        
        // 设置状态回调
        device.setStateCallback(new TpmsDevice.StateCallback() {
            @Override
            public void onConnectionStateChanged(boolean connected) {
                MyLog.d(TAG, "连接状态改变: " + (connected ? "已连接" : "已断开"));
            }
            
            @Override
            public void onError(int errorCode, String message) {
                MyLog.e(TAG, String.format("设备错误: code=%d, message=%s", errorCode, message));
            }
        });
    }
    
    /**
     * 运行测试
     */
    public void runTest() {
        MyLog.d(TAG, "开始测试...");
        
        // 测试打开设备
        if (!testOpenDevice()) {
            MyLog.e(TAG, "设备打开失败，终止测试");
            return;
        }
        
        // 测试发送数据
        if (!testSendData()) {
            MyLog.e(TAG, "数据发送失败");
        }
        
        // 等待接收数据
        try {
            MyLog.d(TAG, "等待接收数据...");
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            MyLog.e(TAG, "等待中断: " + e.getMessage());
        }
        
        // 测试关闭设备
        testCloseDevice();
    }
    
    /**
     * 测试打开设备
     */
    private boolean testOpenDevice() {
        MyLog.d(TAG, "测试打开设备");
        return device.open();
    }
    
    /**
     * 测试发送数据
     */
    private boolean testSendData() {
        MyLog.d(TAG, "测试发送数据");
        byte[] testData = new byte[]{0x55, (byte)0xAA, 0x03, 0x01, 0x59};
        MyLog.d(TAG, "发送数据: " + bytesToHexString(testData));
        return device.send(testData);
    }
    
    /**
     * 测试关闭设备
     */
    private void testCloseDevice() {
        MyLog.d(TAG, "测试关闭设备");
        device.close();
    }
    
    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHexString(byte[] data) {
        StringBuilder sb = new StringBuilder();
        for (byte b : data) {
            sb.append(String.format("%02X ", b));
        }
        return sb.toString().trim();
    }
} 