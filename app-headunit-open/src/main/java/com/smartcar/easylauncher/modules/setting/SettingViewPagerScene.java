package com.smartcar.easylauncher.modules.setting;

import static com.bytedance.scene.navigation.NavigationSceneGetter.requireNavigationScene;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.angcyo.tablayout.delegate.ViewPager1Delegate;
import com.bytedance.scene.group.UserVisibleHintGroupScene;
import com.bytedance.scene.ui.GroupSceneUIUtility;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.databinding.SceneSettingViewPagerBinding;
import com.smartcar.easylauncher.infrastructure.interfaces.SkinChangeListener;
import com.smartcar.easylauncher.data.model.theme.SkinModel;
import com.smartcar.easylauncher.infrastructure.skin.SkinManager;

import java.util.LinkedHashMap;

public class SettingViewPagerScene extends BaseScene implements SkinChangeListener {
    private SceneSettingViewPagerBinding binding;

    @NonNull
    @Override
    public ViewGroup onCreateNewView(@NonNull LayoutInflater layoutInflater, @NonNull ViewGroup viewGroup, @Nullable Bundle bundle) {
        binding = SceneSettingViewPagerBinding.inflate(getLayoutInflater());
        return binding.getRoot();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        registerSkinChangeListener();

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initTabLayout();
        ViewPager1Delegate.Companion.install(binding.myViewPager, binding.tabLayout, true);
        initTab(savedInstanceState == null);
    }

    private void initTabLayout() {
        binding.tabLayout.getTabIndicator().setIndicatorDrawable(SkinManager.getInstance().getDrawable(R.drawable.indicator_bottom_line));
        binding.tabLayout.invalidate();
        assert binding.tabLayout.getTabLayoutConfig() != null;
        binding.tabLayout.getTabLayoutConfig().setTabSelectColor(SkinManager.getInstance().getColor(R.color.tab_select_color));
        binding.tabLayout.getTabLayoutConfig().setTabDeselectColor(SkinManager.getInstance().getColor(R.color.tab_deselect_color));
        binding.tabLayout.getDslSelector().updateStyle();
        binding.btBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                requireNavigationScene(SettingViewPagerScene.this).pop();
            }
        });
    }

    private void initTab(boolean state) {
        binding.tabLayout.configTabLayoutConfig(dslTabLayoutConfig -> null);
        LinkedHashMap<String, UserVisibleHintGroupScene> list = new LinkedHashMap<>();

        list.put("BaseSettingScene", new BaseSettingScene());
        list.put("DesktopSettingScene", new DesktopSettingScene());
        list.put("PersonalizedSettingScene", new PersonalizedSettingScene());
        list.put("SystemSettingsScene", new SystemSettingsScene());
        list.put("AboutUsScene", new AboutUsScene());

        GroupSceneUIUtility.setupWithViewPager(binding.myViewPager, this, list);
        binding.myViewPager.setCurrentItem(0);
        binding.myViewPager.setOffscreenPageLimit(1);
    }

    @Override
    public void onSkinChange(SkinModel value) {
        initTabLayout();
    }
}
