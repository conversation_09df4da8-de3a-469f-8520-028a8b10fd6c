package com.smartcar.easylauncher.modules.trip;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;

import com.bytedance.scene.Scene;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.databinding.SceneTodayTripDetailsBinding;
import com.smartcar.easylauncher.data.database.entity.TripModel;
import com.smartcar.easylauncher.data.repository.TripRepository;
import com.smartcar.easylauncher.shared.utils.location.DistanceConverter;
import com.smartcar.easylauncher.shared.utils.time.TimeTools;

import java.util.Objects;


/**
 * 行程详情场景
 *
 * <AUTHOR>
 */
public class TripDetailsScene extends Scene {
    private SceneTodayTripDetailsBinding binding;
    public static final String VIEW_NAME_HEADER_IMAGE = "detail:header:image";
    public static final String VIEW_NAME_HEADER_TITLE = "detail:header:title";
    /**
     * 总里程 view
     */
    public static final String TOTAL_DISTANCE_NUM = "total_distance_num";
    /**
     * 总时间 view
     */
    public static final String TOTAL_TIME_NUM = "total_time_num";
    /**
     * 运动时间 view
     */
    public static final String DURATION_NUM = "duration_num";

    private final TripModel mItem;

    public TripDetailsScene(TripModel item) {
        this.mItem = item;
    }

    @NonNull
    @Override
    public View onCreateView(@NonNull LayoutInflater layoutInflater, @NonNull ViewGroup viewGroup, @Nullable Bundle bundle) {
        binding = SceneTodayTripDetailsBinding.inflate(getLayoutInflater());
        return binding.getRoot();
    }


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initData();
        initView();
    }

    private void initView() {
        binding.btback.setOnClickListener(v -> Objects.requireNonNull(getNavigationScene(TripDetailsScene.this)).pop());
        binding.btDelete.setOnClickListener(v -> {
            //  finish();
            TripRepository.getInstance().deleteTrip(mItem, new TripRepository.OnResultCallback<Boolean>() {
                @Override
                public void onSuccess(Boolean result) {
                    MToast.makeTextShort("删除成功");
                    requireActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            Objects.requireNonNull(getNavigationScene(TripDetailsScene.this)).pop();

                        }
                    });
                }

                @Override
                public void onError(@NonNull Exception e) {

                }
            });
        });
    }

    @Override
    public void onActivityCreated(@Nullable @org.jetbrains.annotations.Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        ViewCompat.setTransitionName(binding.ivIcon, VIEW_NAME_HEADER_IMAGE + mItem.getId());
        ViewCompat.setTransitionName(binding.tvTimeTitle, VIEW_NAME_HEADER_TITLE + mItem.getId());
        ViewCompat.setTransitionName(binding.tvTotalDistanceNum, TOTAL_DISTANCE_NUM + mItem.getId());
        ViewCompat.setTransitionName(binding.tvTotalTimeNum, TOTAL_TIME_NUM + mItem.getId());
        ViewCompat.setTransitionName(binding.tvDurationNum, DURATION_NUM + mItem.getId());
    }

    private void initData() {

        if (mItem != null) {
            //行程时间区间
            binding.tvTimeTitle.setText(TimeTools.getDateToString(mItem.getStartTime(), TimeTools.DATE_TIME_FORMAT_MINUTE)
                    + " - " + TimeTools.getDateToString(mItem.getEndTime(), TimeTools.DATE_TIME_FORMAT_MINUTE));
            // 行程总时间
            binding.tvTotalTimeNum.setText(TimeTools.displayDuration2(requireActivity(), mItem.getTotalTime()));
            // 行程运动时间
            binding.tvDurationNum.setText(TimeTools.displayDuration2(requireActivity(),mItem.getActiveTime()));
            // 行程总距离
            binding.tvTotalDistanceNum.setText(DistanceConverter.formatDistance(requireActivity(), mItem.getTotalDistance()));
            // 最高速度
            binding.tvMaxSpeedNum.setText(DistanceConverter.mpsToKph(requireActivity(),mItem.getMaxSpeed()));
            // 平均速度
            binding.tvAvgSpeedNum.setText(DistanceConverter.mpsToKph(requireActivity(),mItem.getAvgSpeed()));
            //最高海拔
            binding.tvMaxAltitudeNum.setText(DistanceConverter.altitude(requireActivity(),mItem.getMaxAltitude()));
            // 最低海拔
            binding.tvMinAltitudeNum.setText(DistanceConverter.altitude(requireActivity(),mItem.getMinAltitude()));
            // 平均海拔
            binding.tvAvgAltitudeNum.setText(DistanceConverter.altitude(requireActivity(),mItem.getAvgAltitude()));
            // 定位精度
            //   binding.tvPrecisionNum.setText(Math.round(mItem.getAccuracy()) + " m");

        }
    }


}
