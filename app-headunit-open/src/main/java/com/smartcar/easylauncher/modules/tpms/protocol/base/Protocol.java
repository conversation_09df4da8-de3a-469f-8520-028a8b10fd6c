package com.smartcar.easylauncher.modules.tpms.protocol.base;

import com.smartcar.easylauncher.modules.tpms.protocol.base.model.TpmsData;

/**
 * TPMS协议接口
 */
public interface Protocol {
    /**
     * 启动协议
     */
    void start();

    /**
     * 停止协议
     */
    void stop();

    /**
     * 处理接收到的数据
     *
     * @param data 原始数据
     */
    void handleData(byte[] data);

    /**
     * 查询轮胎ID
     */
    void queryTireIds();

    /**
     * 交换轮胎
     *
     * @param pos1 轮胎位置1
     * @param pos2 轮胎位置2
     */
    void exchangeTires(byte pos1, byte pos2);

    /**
     * 胎压学习匹配
     * @param key 轮胎位置值
     *
     */
    void learn(byte key);

    /**
     * 取消
     *
     */
    void cancel();


    /**
     * 数据回调接口
     */
    interface Callback {
        void onData(TpmsData data);
    }

    /**
     * 设置数据回调
     *
     * @param callback 回调接口
     */
    void setCallback(Callback callback);
} 