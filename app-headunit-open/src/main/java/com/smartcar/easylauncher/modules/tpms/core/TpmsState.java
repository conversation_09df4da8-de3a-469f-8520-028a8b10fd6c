package com.smartcar.easylauncher.modules.tpms.core;

/**
 * TPMS设备状态枚举
 */
public enum TpmsState {
    DISCONNECTED("未连接"),
    CONNECTING("连接中..."),
    DETECTING("检测协议中..."),
    INITIALIZING("初始化中..."),
    RUNNING("已连接"),
    ERROR("错误");

    private final String description;

    TpmsState(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
} 