package com.smartcar.easylauncher.modules.personal;

import android.os.Build;

/**
 * PersonalScene 性能配置类
 * 统一优化所有设备的性能和流畅度，不区分设备等级
 *
 * <AUTHOR>
 * @date 2024/12/18
 */
public class PersonalSceneConfig {

    // 简化优化的加载延迟配置（毫秒） - 专注于实际效果
    public static final int STATIC_CONTENT_DELAY = 0;   // 立即执行，不延迟
    public static final int USER_DATA_DELAY = 50;       // 最小延迟
    public static final int CHART_DATA_DELAY = 100;     // 快速显示图表

    // 简化的性能参数
    public static final int CHART_REFRESH_DELAY = 50;
    public static final int FORCE_REFRESH_DELAY = 25;
    public static final float BLUR_RADIUS = 3f;         // 减小模糊半径，提升性能

    // 仅针对非常老旧的设备（API 17-19）进行特殊处理
    private static final int VERY_OLD_API_LEVEL = Build.VERSION_CODES.KITKAT; // API 19
    
    /**
     * 检测是否为非常老旧的设备（仅针对API 17-19进行特殊处理）
     */
    public static boolean isVeryOldDevice() {
        return Build.VERSION.SDK_INT <= VERY_OLD_API_LEVEL;
    }

    /**
     * 获取模糊半径 - 统一使用优化后的数值
     */
    public static float getBlurRadius() {
        return BLUR_RADIUS;
    }

    /**
     * 获取静态内容加载延迟 - 统一优化
     */
    public static int getStaticContentDelay() {
        return STATIC_CONTENT_DELAY;
    }

    /**
     * 获取用户数据加载延迟 - 统一优化
     */
    public static int getUserDataDelay() {
        return USER_DATA_DELAY;
    }

    /**
     * 获取图表数据加载延迟 - 统一优化
     */
    public static int getChartDataDelay() {
        return CHART_DATA_DELAY;
    }
    
    /**
     * 是否启用图表强制刷新 - 仅针对非常老旧的设备
     */
    public static boolean shouldForceChartRefresh() {
        return Build.VERSION.SDK_INT <= Build.VERSION_CODES.JELLY_BEAN_MR1; // API 17
    }

    /**
     * 获取图表刷新延迟 - 统一优化
     */
    public static int getChartRefreshDelay() {
        return CHART_REFRESH_DELAY;
    }

    /**
     * 获取强制刷新延迟 - 统一优化
     */
    public static int getForceRefreshDelay() {
        return FORCE_REFRESH_DELAY;
    }

    /**
     * 是否启用性能监控
     */
    public static boolean isPerformanceMonitoringEnabled() {
        return true; // 统一启用性能监控
    }

    /**
     * 获取设备描述 - 简化版本
     */
    public static String getDevicePerformanceLevel() {
        if (isVeryOldDevice()) {
            return "老旧设备 (API " + Build.VERSION.SDK_INT + ")";
        } else {
            return "现代设备 (API " + Build.VERSION.SDK_INT + ")";
        }
    }

    /**
     * 获取推荐的并发网络请求数量 - 统一优化
     */
    public static int getRecommendedConcurrentRequests() {
        return 3; // 统一使用适中的并发数
    }

    /**
     * 是否启用图表动画 - 统一启用
     */
    public static boolean isChartAnimationEnabled() {
        return true; // 现代设备都能很好地支持动画
    }

    /**
     * 获取图表渲染超时时间（毫秒） - 统一优化
     */
    public static int getChartRenderTimeout() {
        return 6000; // 统一使用适中的超时时间
    }
}
