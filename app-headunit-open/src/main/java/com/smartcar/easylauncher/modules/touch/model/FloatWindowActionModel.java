package com.smartcar.easylauncher.modules.touch.model;

import java.util.Objects;


/**
 * <b>Package:</b> com.zh.touchassistant.model <br>
 * <b>FileName:</b> FloatWindowActionModel <br>
 * <b>Create Date:</b> 2018/12/6  上午11:52 <br>
 * <b>Author:</b> zihe <br>
 * <b>Description:</b>  <br>
 */
public class FloatWindowActionModel extends BaseDataModel {
    // 动作ID
    private int actionId;

    // 默认构造函数
    public FloatWindowActionModel() {
    }

    // 带参数的构造函数，接收动作ID作为参数
    public FloatWindowActionModel(int actionId) {
        this.actionId = actionId;
    }

    // 获取动作ID
    public int getActionId() {
        return actionId;
    }

    // 重写equals方法，比较两个FloatWindowActionModel对象是否相等
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FloatWindowActionModel model = (FloatWindowActionModel) o;
        return actionId == model.actionId;
    }

    // 重写hashCode方法，返回动作ID的哈希值
    @Override
    public int hashCode() {
        return Objects.hash(actionId);
    }

    // 重写toString方法，返回动作ID的字符串表示形式
    @Override
    public String toString() {
        return "FloatWindowActionModel{" +
                "actionId=" + actionId +
                '}';
    }
}