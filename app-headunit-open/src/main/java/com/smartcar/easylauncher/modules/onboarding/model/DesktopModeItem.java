package com.smartcar.easylauncher.modules.onboarding.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 桌面模式数据模型
 *
 * <AUTHOR>
 */
public class DesktopModeItem {
    private int mode;           // 模式类型（0-卡片模式，1-沉浸模式，2-混合模式）
    private int previewResId;   // 预览图资源ID
    private int iconResId;      // 图标资源ID
    private String title;       // 标题
    private String description; // 描述
    private List<String> features; // 特点列表

    public DesktopModeItem(int mode, int previewResId, int iconResId, String title, String description) {
        this.mode = mode;
        this.previewResId = previewResId;
        this.iconResId = iconResId;
        this.title = title;
        this.description = description;
        this.features = new ArrayList<>();
    }

    public int getMode() {
        return mode;
    }

    public void setMode(int mode) {
        this.mode = mode;
    }

    public int getPreviewResId() {
        return previewResId;
    }

    public void setPreviewResId(int previewResId) {
        this.previewResId = previewResId;
    }

    public int getIconResId() {
        return iconResId;
    }

    public void setIconResId(int iconResId) {
        this.iconResId = iconResId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getFeatures() {
        return features;
    }

    public void setFeatures(List<String> features) {
        this.features = features;
    }

    public void addFeature(String feature) {
        this.features.add(feature);
    }
} 