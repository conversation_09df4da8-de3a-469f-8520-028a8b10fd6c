package com.smartcar.easylauncher.modules.tpms.core;

/**
 * TPMS错误类型枚举
 */
public enum TpmsError {
    USB_PERMISSION_DENIED(
        "USB权限被拒绝",
        "请在系统设置中授予USB权限",
        true
    ),
    DEVICE_NOT_SUPPORTED(
        "设备不支持",
        "当前设备不是受支持的TPMS设备",
        false
    ),
    PROTOCOL_DETECT_FAILED(
        "协议检测失败",
        "无法识别设备协议,请确认设备是否正常",
        true
    ),
    DEVICE_BUSY(
        "设备忙",
        "设备正被其他应用占用",
        true
    ),
    COMMUNICATION_ERROR(
        "通信错误",
        "与设备通信失败,请重新连接",
        true
    ),
    UNKNOWN(
        "未知错误",
        "请尝试重新连接设备",
        true
    );

    private final String title;
    private final String solution;
    private final boolean retryable;

    TpmsError(String title, String solution, boolean retryable) {
        this.title = title;
        this.solution = solution;
        this.retryable = retryable;
    }

    public String getTitle() {
        return title;
    }

    public String getSolution() {
        return solution;
    }

    public boolean isRetryable() {
        return retryable;
    }

    /**
     * 从异常中获取对应的错误类型
     */
    public static TpmsError fromException(Exception e) {
        // TODO: 根据不同异常类型返回对应的错误
        return UNKNOWN;
    }
} 