package com.smartcar.easylauncher.modules.apport

import android.util.Log
import android.view.View.GONE
import com.smartcar.easylauncher.databinding.ActivityLogBinding
import com.smartcar.easylauncher.shared.utils.apportutil.FileUtils
import java.io.File

/**
 * <AUTHOR>
 * Created on 2022/7/11.
 * E-mail <EMAIL>
 * Desc:
 */
class LogActivity : BaseActivity<ActivityLogBinding>() {


    override fun initData() {
        val path = intent.getStringExtra("path")
        setToolbar(File(path.toString()).name)
        Log.i(TAG, "initData: $path")
        binding.run {
            tvContent.text = FileUtils.readText(path)
            toolbar.ivSetting.visibility = GONE
            toolbar.ivWifi.visibility = GONE
        }


    }
}