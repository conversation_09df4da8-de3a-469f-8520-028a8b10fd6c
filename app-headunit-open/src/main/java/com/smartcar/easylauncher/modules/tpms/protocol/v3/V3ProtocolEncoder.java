package com.smartcar.easylauncher.modules.tpms.protocol.v3;

import com.smartcar.easylauncher.modules.tpms.utils.Tools;
import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * V3协议编码器
 */
public class V3ProtocolEncoder {
    private static final String TAG = "V3ProtocolEncoder";
    
    // 协议帧头
    private static final byte FRAME_HEADER_1 = (byte) 0x55;  // 85
    private static final byte FRAME_HEADER_2 = (byte) 0xAA;  // -86
    
    // 命令类型
    private static final byte FRAME_TYPE = 0x06;  // 帧类型
    private static final byte CMD_HANDSHAKE = (byte) 0x5A;  // 90 握手命令
    private static final byte CMD_HEARTBEAT = (byte) 0x19;  // 25 心跳命令
    private byte time = (byte) (255 & System.currentTimeMillis());
    /**
     * 编码握手命令
     * 格式: 55 AA 06 5A time CS
     */
    public byte[] encodeHandshake() {
        // 获取时间戳的低字节作为时间种子

        
        byte[] data = new byte[]{
            FRAME_HEADER_1,    // 帧头1
            FRAME_HEADER_2,    // 帧头2
            FRAME_TYPE,        // 帧类型
            CMD_HANDSHAKE,     // 握手命令
            time              // 时间种子
        };
        byte[] bytes = Tools.sum(data);

        MyLog.d(TAG, "握手命令: " + bytesToHex(bytes));
        return bytes;
    }
    
    /**
     * 编码心跳命令
     * 格式: 55 AA 06 19 CS
     */
    public byte[] encodeHeartbeat() {
        byte[] data = new byte[]{
            FRAME_HEADER_1,    // 帧头1
            FRAME_HEADER_2,    // 帧头2
            FRAME_TYPE,        // 帧类型
            CMD_HEARTBEAT      // 心跳命令
        };
        byte[] bytes = Tools.sum(data);
        return bytes;
    }

    /**
     * 胎压编码查询命令
     */
    public byte[] encodeTirePressureQuery() {
        byte[] data = new byte[6];
        data[0] = 85;
        data[1] = -86;
        data[2] = 6;
        data[3] = 7;
        byte[] bytes = Tools.sum(data);
        return bytes;
    }

    /**
     * 胎压学习命令
     */
    public byte[] encodeLearn(byte key) {
        byte[] data = new byte[6];
        data[0] = 85;
        data[1] = -86;
        data[2] = 6;
        data[3] = 1;
        data[4] = key;
        byte[] bytes = Tools.sum(data);
        return bytes;
    }

    /**
     * 取消操作
     */
    public byte[] encodeCancel() {
        byte[] data = new byte[6];
        data[0] = 85;
        data[1] = -86;
        data[2] = 6;
        data[3] = 6;
        byte[] bytes = Tools.sum(data);
        return bytes;
    }

    /**
     * 轮胎换位命令格式
     */
    public byte[] encodeTireExchange(byte tp1, byte tp2) {
        byte[] data = new byte[7];
        data[0] = 85;    // 帧头
        data[1] = -86;   // 帧头
        data[2] = 7;     // 长度
        data[3] = 3;     // 命令类型
        data[4] = tp1;    // 第一个轮胎位置
        data[5] = tp2;    // 第二个轮胎位置
        byte[] bytes = Tools.sum(data);
        return bytes;
    }


    /**
     * 字节数组转16进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b));
        }
        return sb.toString().trim();
    }
} 