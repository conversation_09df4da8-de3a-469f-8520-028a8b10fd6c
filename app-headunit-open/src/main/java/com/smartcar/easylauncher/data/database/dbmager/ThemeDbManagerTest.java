package com.smartcar.easylauncher.data.database.dbmager;

import com.google.gson.Gson;
import com.smartcar.easylauncher.data.model.theme.ThemeDetailModel;

/**
 * 测试NewThemeDbManager处理ThemeDetailModel数据的功能
 */
public class ThemeDbManagerTest {
    
    public static void testThemeDetailModelConversion() {
        // 用户提供的JSON数据
        String jsonData = "{\n" +
                "  \"msg\": \"操作成功\",\n" +
                "  \"code\": 200,\n" +
                "  \"data\": {\n" +
                "    \"createBy\": \"admin\",\n" +
                "    \"createTime\": \"2025-08-02 22:22:37\",\n" +
                "    \"updateBy\": \"\",\n" +
                "    \"updateTime\": \"2025-08-03 20:33:31\",\n" +
                "    \"id\": 1,\n" +
                "    \"themeName\": \"心静\",\n" +
                "    \"themeDescription\": \"心之所往，便是远方\\n以心为起点，愿美好风景伴你一路同行\\n心静，志远\",\n" +
                "    \"themeType\": 1,\n" +
                "    \"themeTypeName\": \"夜间主题\",\n" +
                "    \"downloadCount\": 7954,\n" +
                "    \"heat\": 7950,\n" +
                "    \"authorId\": 1,\n" +
                "    \"author\": \"默\",\n" +
                "    \"authorImg\": \"http://**************:8888/down/nJ7x7QZ9vLXT.jpg\",\n" +
                "    \"price\": 0,\n" +
                "    \"isTrialEnabled\": 1,\n" +
                "    \"status\": 1,\n" +
                "    \"releaseStatus\": 0,\n" +
                "    \"isVip\": 0,\n" +
                "    \"label\": \"热门\",\n" +
                "    \"coverImage\": \"http://**************:8888/down/NqnT2NIVsOoI.jpg\",\n" +
                "    \"sortOrder\": 1,\n" +
                "    \"previewImages\": [\n" +
                "      {\n" +
                "        \"createBy\": \"admin\",\n" +
                "        \"createTime\": \"2025-08-02 22:22:37\",\n" +
                "        \"updateBy\": \"\",\n" +
                "        \"id\": 11,\n" +
                "        \"themeId\": 1,\n" +
                "        \"title\": \"地图模式画中画\",\n" +
                "        \"description\": \"\",\n" +
                "        \"imageUrl\": \"http://**************:8888/down/NqnT2NIVsOoI.jpg\",\n" +
                "        \"sortOrder\": 1,\n" +
                "        \"imageType\": 2\n" +
                "      },\n" +
                "      {\n" +
                "        \"createBy\": \"admin\",\n" +
                "        \"createTime\": \"2025-08-02 22:22:37\",\n" +
                "        \"updateBy\": \"\",\n" +
                "        \"id\": 12,\n" +
                "        \"themeId\": 1,\n" +
                "        \"title\": \"地图音乐窗口\",\n" +
                "        \"description\": \"\",\n" +
                "        \"imageUrl\": \"http://**************:8888/down/xHLStO9GMjnp.jpg\",\n" +
                "        \"sortOrder\": 2,\n" +
                "        \"imageType\": 2\n" +
                "      }\n" +
                "    ],\n" +
                "    \"themePackage\": {\n" +
                "      \"createBy\": \"admin\",\n" +
                "      \"createTime\": \"2025-08-02 22:22:37\",\n" +
                "      \"updateBy\": \"\",\n" +
                "      \"id\": 1,\n" +
                "      \"themeId\": 1,\n" +
                "      \"versionCode\": 2,\n" +
                "      \"versionName\": \"1.1.0\",\n" +
                "      \"packagePath\": \"http://**************:8888/down/4BueORFsP8Ln.skin\",\n" +
                "      \"fileName\": \"心静主题包_v1.1.0.skin\",\n" +
                "      \"fileSize\": 2048576,\n" +
                "      \"updateDescription\": \"适配主题资源\\n更新素材\",\n" +
                "      \"status\": 1,\n" +
                "      \"statusName\": \"发布\",\n" +
                "      \"publishTime\": \"2025-08-02 22:22:37\",\n" +
                "      \"downloadCount\": 7950,\n" +
                "      \"isLatest\": 1,\n" +
                "      \"downloadUrl\": \"http://**************:8888/down/4BueORFsP8Ln.skin\"\n" +
                "    }\n" +
                "  }\n" +
                "}";
        
        try {
            // 解析JSON数据
            Gson gson = new Gson();
            ThemeDetailModel themeDetailModel = gson.fromJson(jsonData, ThemeDetailModel.class);
            
            // 获取数据部分
            ThemeDetailModel.DataDTO themeData = themeDetailModel.getData();
            
            // 使用NewThemeDbManager转换数据
            NewThemeDbManager dbManager = NewThemeDbManager.getInstance();
            
            // 测试转换功能
            System.out.println("=== 测试主题数据转换 ===");
            System.out.println("主题名称: " + themeData.getThemeName());
            System.out.println("主题描述: " + themeData.getThemeDescription());
            System.out.println("预览图片数量: " + (themeData.getPreviewImages() != null ? themeData.getPreviewImages().size() : 0));
            System.out.println("主题包版本: " + (themeData.getThemePackage() != null ? themeData.getThemePackage().getVersionName() : "无"));
            
            // 注意：实际保存到数据库需要在Android环境中进行
            // dbManager.saveTheme(themeData).subscribe(
            //     result -> System.out.println("保存成功，ID: " + result),
            //     error -> System.err.println("保存失败: " + error.getMessage())
            // );
            
            System.out.println("数据转换测试完成！");
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static void main(String[] args) {
        testThemeDetailModelConversion();
    }
}
