package com.smartcar.easylauncher.data.database.dbmager;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.smartcar.easylauncher.data.database.AppDatabase;
import com.smartcar.easylauncher.data.database.entity.NewThemeEntity;
import com.smartcar.easylauncher.data.model.theme.ThemeDetailModel;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.util.List;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Flowable;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * 新主题数据库管理器
 * 直接操作新API数据结构，不进行转换
 */
public class NewThemeDbManager {
    private static final String TAG = "NewThemeDbManager";
    private final Gson gson = new Gson();

    private NewThemeDbManager() {}

    public static NewThemeDbManager getInstance() {
        return Holder.INSTANCE;
    }

    private static class Holder {
        private static final NewThemeDbManager INSTANCE = new NewThemeDbManager();
    }

    // -------------------------数据转换方法--------------------------------------------

    /**
     * 将ThemeDetailModel.PreviewImagesDTO转换为NewPreviewImage
     */
    private NewPreviewImage convertPreviewImageDTO(com.smartcar.easylauncher.data.model.theme.ThemeDetailModel.DataDTO.PreviewImagesDTO previewDTO) {
        NewPreviewImage newPreview = new NewPreviewImage();

        // 转换基本字段
        newPreview.setId(previewDTO.getId() != null ? previewDTO.getId().longValue() : null);
        newPreview.setThemeId(previewDTO.getThemeId() != null ? previewDTO.getThemeId().longValue() : null);
        newPreview.setTitle(previewDTO.getTitle());
        newPreview.setDescription(previewDTO.getDescription());
        newPreview.setImageUrl(previewDTO.getImageUrl());
        newPreview.setSortOrder(previewDTO.getSortOrder());
        newPreview.setImageType(previewDTO.getImageType());
        newPreview.setCreateTime(previewDTO.getCreateTime());
        newPreview.setUpdateTime(previewDTO.getUpdateBy()); // 注意：这里可能需要调整字段映射

        return newPreview;
    }

    /**
     * 将ThemeDetailModel.ThemePackageDTO转换为NewThemePackage
     */
    private NewThemePackage convertThemePackageDTO(com.smartcar.easylauncher.data.model.theme.ThemeDetailModel.DataDTO.ThemePackageDTO packageDTO) {
        NewThemePackage newPackage = new NewThemePackage();

        // 转换基本字段
        newPackage.setId(packageDTO.getId() != null ? packageDTO.getId().longValue() : null);
        newPackage.setThemeId(packageDTO.getThemeId() != null ? packageDTO.getThemeId().longValue() : null);
        newPackage.setVersionCode(packageDTO.getVersionCode());
        newPackage.setVersionName(packageDTO.getVersionName());
        newPackage.setPackagePath(packageDTO.getPackagePath());
        newPackage.setFileName(packageDTO.getFileName());
        newPackage.setFileSize(packageDTO.getFileSize() != null ? packageDTO.getFileSize().longValue() : null);
        newPackage.setUpdateDescription(packageDTO.getUpdateDescription());
        newPackage.setStatus(packageDTO.getStatus());
        newPackage.setStatusName(packageDTO.getStatusName());
        newPackage.setPublishTime(packageDTO.getPublishTime());
        newPackage.setDownloadCount(packageDTO.getDownloadCount() != null ? packageDTO.getDownloadCount().longValue() : null);
        newPackage.setIsLatest(packageDTO.getIsLatest());
        newPackage.setDownloadUrl(packageDTO.getDownloadUrl());
        newPackage.setCreateTime(packageDTO.getCreateTime());
        newPackage.setUpdateTime(packageDTO.getUpdateBy()); // 注意：这里可能需要调整字段映射

        return newPackage;
    }

    /**
     * 将ThemeDetailModel.DataDTO转换为NewThemeEntity
     */
    public NewThemeEntity convertToEntity(ThemeDetailModel.DataDTO themeInfo) {
        NewThemeEntity entity = new NewThemeEntity();
        
        // 基本信息
        entity.setId(themeInfo.getId());
        entity.setThemeName(themeInfo.getThemeName());
        entity.setThemeDescription(themeInfo.getThemeDescription());
        entity.setThemeType(themeInfo.getThemeType());
        entity.setDownloadCount(themeInfo.getDownloadCount());
        entity.setHeat(themeInfo.getHeat());
        entity.setAuthorId(themeInfo.getAuthorId());
        entity.setAuthor(themeInfo.getAuthor());
        entity.setAuthorImg(themeInfo.getAuthorImg());
        // 转换Integer到Double
        entity.setPrice(themeInfo.getPrice() != null ? themeInfo.getPrice().doubleValue() : null);
        entity.setIsTrialEnabled(themeInfo.getIsTrialEnabled());
        entity.setStatus(themeInfo.getStatus());
        entity.setReleaseStatus(themeInfo.getReleaseStatus());
        entity.setIsVip(themeInfo.getIsVip());
        entity.setLabel(themeInfo.getLabel());
        entity.setCoverImage(themeInfo.getCoverImage());
        entity.setSortOrder(themeInfo.getSortOrder());
        entity.setCreateTime(themeInfo.getCreateTime());
        entity.setUpdateTime(themeInfo.getUpdateTime());
        entity.setRemark(themeInfo.getRemark());

        // 序列化复杂对象 - 转换预览图片
        if (themeInfo.getPreviewImages() != null) {
            // 将PreviewImagesDTO转换为NewPreviewImage
            List<NewPreviewImage> newPreviewImages = themeInfo.getPreviewImages().stream()
                    .map(this::convertPreviewImageDTO)
                    .collect(java.util.stream.Collectors.toList());
            entity.setPreviewImagesJson(gson.toJson(newPreviewImages));
        }
        
        // 处理主题包信息 - 支持单个themePackage对象
        List<NewThemePackage> packages = null;

        // 检查是否有themePackage单个对象（ThemeDetailModel.DataDTO的情况）
        if (themeInfo.getThemePackage() != null) {
            // 将单个themePackage转换为NewThemePackage并放入列表
            NewThemePackage newPackage = convertThemePackageDTO(themeInfo.getThemePackage());
            packages = java.util.Collections.singletonList(newPackage);
        }

        if (packages != null && !packages.isEmpty()) {
            entity.setPackagesJson(gson.toJson(packages));

            // 提取最新版本信息
            NewThemePackage latestPackage = packages.stream()
                    .filter(pkg -> pkg.getIsLatest() != null && pkg.getIsLatest() == 1)
                    .findFirst()
                    .orElse(packages.get(0)); // 如果没有标记为最新的，使用第一个

            if (latestPackage != null) {
                entity.setCurrentVersionName(latestPackage.getVersionName());
                entity.setCurrentVersionCode(latestPackage.getVersionCode() != null ? latestPackage.getVersionCode().toString() : "1");

                // 使用packagePath作为下载URL
                String downloadUrl = latestPackage.getPackagePath();
                if (downloadUrl == null || downloadUrl.isEmpty()) {
                    downloadUrl = latestPackage.getDownloadUrl(); // 回退到downloadUrl字段
                }
                entity.setDownloadUrl(downloadUrl);
            }
        }

        return entity;
    }


    // -------------------------基本CRUD操作--------------------------------------------

    /**
     * 插入或更新主题（从ThemeDetailModel）
     */
    public Observable<Long> saveTheme(ThemeDetailModel.DataDTO themeInfo) {
        return Observable.create((ObservableOnSubscribe<Long>) emitter -> {
            try {
                NewThemeEntity entity = convertToEntity(themeInfo);
                Long result = AppDatabase.getDatabase().getNewThemeDao().insert(entity);
                emitter.onNext(result);
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 插入或更新主题（从NewThemeInfo）
     */
    public Observable<Long> saveTheme(NewThemeInfo themeInfo) {
        return Observable.create((ObservableOnSubscribe<Long>) emitter -> {
            try {
                NewThemeEntity entity = convertToEntity(themeInfo);
                Long result = AppDatabase.getDatabase().getNewThemeDao().insert(entity);
                emitter.onNext(result);
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 批量保存主题
     */
    public Observable<List<Long>> saveThemes(List<NewThemeInfo> themeInfos) {
        return Observable.create((ObservableOnSubscribe<List<Long>>) emitter -> {
            try {
                List<NewThemeEntity> entities = themeInfos.stream()
                        .map(this::convertToEntity)
                        .collect(java.util.stream.Collectors.toList());
                List<Long> results = AppDatabase.getDatabase().getNewThemeDao().insert(entities);
                emitter.onNext(results);
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 根据ID获取主题
     */
    public Observable<NewThemeInfo> getThemeById(Long themeId) {
        return Observable.create((ObservableOnSubscribe<NewThemeInfo>) emitter -> {
            try {
                NewThemeEntity entity = AppDatabase.getDatabase().getNewThemeDao().getById(themeId);
                if (entity != null) {
                    NewThemeInfo themeInfo = convertToThemeInfo(entity);
                    emitter.onNext(themeInfo);
                } else {
                    emitter.onError(new Exception("主题不存在: " + themeId));
                }
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 获取所有主题
     */
    public Observable<List<NewThemeInfo>> getAllThemes() {
        return Observable.create((ObservableOnSubscribe<List<NewThemeInfo>>) emitter -> {
            try {
                List<NewThemeEntity> entities = AppDatabase.getDatabase().getNewThemeDao().getAll();
                List<NewThemeInfo> themeInfos = entities.stream()
                        .map(this::convertToThemeInfo)
                        .collect(java.util.stream.Collectors.toList());
                emitter.onNext(themeInfos);
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 获取所有主题（响应式）
     */
    public Flowable<List<NewThemeInfo>> getAllThemesFlowable() {
        return AppDatabase.getDatabase().getNewThemeDao().getAllFlowable()
                .map(entities -> entities.stream()
                        .map(this::convertToThemeInfo)
                        .collect(java.util.stream.Collectors.toList()))
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 删除主题
     */
    public Observable<Integer> deleteTheme(Long themeId) {
        return Observable.create((ObservableOnSubscribe<Integer>) emitter -> {
            try {
                int result = AppDatabase.getDatabase().getNewThemeDao().deleteById(themeId);
                emitter.onNext(result);
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    // -------------------------状态管理方法--------------------------------------------

    /**
     * 更新主题下载状态
     */
    public Observable<Integer> updateDownloadStatus(Long themeId, boolean isDownloaded, String localFilePath) {
        return Observable.create((ObservableOnSubscribe<Integer>) emitter -> {
            try {
                String downloadTime = isDownloaded ? String.valueOf(System.currentTimeMillis()) : null;
                int result = AppDatabase.getDatabase().getNewThemeDao()
                        .updateLocalStatus(themeId, isDownloaded, false, localFilePath, downloadTime);
                emitter.onNext(result);
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 设置主题为当前使用
     */
    public Observable<Integer> setCurrentTheme(Long themeId) {
        return Observable.create((ObservableOnSubscribe<Integer>) emitter -> {
            try {
                // 先清除所有主题的使用状态
                AppDatabase.getDatabase().getNewThemeDao().clearAllUsageStatus();
                // 设置指定主题为使用状态
                int result = AppDatabase.getDatabase().getNewThemeDao().updateUsageStatus(themeId, true);
                emitter.onNext(result);
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 获取当前使用的主题
     */
    public Observable<NewThemeInfo> getCurrentTheme() {
        return Observable.create((ObservableOnSubscribe<NewThemeInfo>) emitter -> {
            try {
                NewThemeEntity entity = AppDatabase.getDatabase().getNewThemeDao().getCurrentTheme();
                if (entity != null) {
                    NewThemeInfo themeInfo = convertToThemeInfo(entity);
                    emitter.onNext(themeInfo);
                } else {
                    emitter.onError(new Exception("没有当前使用的主题"));
                }
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }
}
