package com.smartcar.easylauncher.data.database.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;


@Entity(tableName = "themeInfo") //设置表名
public class ThemeInfoModel {
    @NonNull //主键必须添加这个注解
    @PrimaryKey(autoGenerate = false) //主键是否自动增长，默认为false
    public String id;
    @ColumnInfo
    public int classify;
    @ColumnInfo
    public String img;
    @ColumnInfo
    public String authorImg;
    @ColumnInfo
    public String sinkUrl;
    @ColumnInfo
    public String content;
    @ColumnInfo
    public int number;
    @ColumnInfo
    public String createdAt;
    @ColumnInfo
    public int state;
    @ColumnInfo
    public String objectId;
    @ColumnInfo
    public String updatedAt;
    @ColumnInfo
    public boolean isAadmire;
    @ColumnInfo
    public String author;
    @ColumnInfo
    public boolean isVip;
    @ColumnInfo
    public String name;
    @ColumnInfo
    public String versionName;
    @ColumnInfo
    public int versionCode;
    @ColumnInfo
    public String updateContent;
    @ColumnInfo
    public String admireImg;
    @ColumnInfo
    public boolean isUpdate;
    @ColumnInfo
    public boolean isUse;
    @ColumnInfo
    public int themeType;
    @ColumnInfo
    public String label;
    @ColumnInfo
    public String apiSource; // 数据来源：old_api 或 new_api
    @ColumnInfo
    public Long newApiId; // 新API的ID（Long类型）
    @ColumnInfo
    public String previewImagesJson; // 预览图片JSON字符串




    @Override
    public String toString() {
        return "ThemeInfoModel{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", authorImg='" + authorImg + '\'' +
                ", img='" + img + '\'' +
                ", phone='" + content + '\'' +
                ", adress='" + classify + '\'' +
                ", sex='" + number + '\'' +
                ", author='" + author + '\'' +
                ", isAadmire='" + isAadmire + '\'' +
                ", admireImg='" + admireImg + '\'' +
                ", sinkUrl='" + sinkUrl + '\'' +
                ", versionName='" + versionName + '\'' +
                ", versionCode='" + versionCode + '\'' +
                ", updateContent='" + updateContent + '\'' +
                ", isUpdate='" + isUpdate + '\'' +
                ", isUse='" + isUse + '\'' +
                ", state='" + state + '\'' +
                ", label='" + label + '\'' +
                ", themeType='" + themeType + '\'' +
                '}';
    }

    public ThemeInfoModel() {

    }

    @Ignore //只允许有一个主构造方法，其他构造方法要使用@Ignore设置为忽略
    public ThemeInfoModel(String name) {
        this.name = name;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setAuthorImg(String authorImg) {
        this.authorImg = authorImg;
    }

    public String getAuthorImg() {
        return authorImg;
    }

    public void setVersionCode(int versionCode) {
        this.versionCode = versionCode;
    }

    public int getVersionCode() {
        return versionCode;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getVersionName() {
        return versionName;
    }

    public int getThemeType() {
        return themeType;
    }

    public void setThemeType(int themeType) {
        this.themeType = themeType;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }


    public int getClassify() {
        return classify;
    }

    public void setClassify(int classify) {
        this.classify = classify;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getSinkUrl() {
        return sinkUrl;
    }

    public void setSinkUrl(String sinkUrl) {
        this.sinkUrl = sinkUrl;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    public boolean isAadmire() {
        return isAadmire;
    }

    public void setAadmire(boolean isAadmire) {
        this.isAadmire = isAadmire;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public boolean isVip() {
        return isVip;
    }

    public void setVip(boolean isVip) {
        this.isVip = isVip;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAdmireImg() {
        return admireImg;
    }

    public void setAdmireImg(String admireImg) {
        this.admireImg = admireImg;
    }

    public boolean isUpdate() {
        return isUpdate;
    }

    public void setUpdate(boolean update) {
        isUpdate = update;
    }

    public void setUpdateContent(String updateContent) {
        this.updateContent = updateContent;
    }

    public String getUpdateContent() {
        return updateContent;
    }

    public boolean isUse() {
        return isUse;
    }

    public void setUse(boolean use) {
        isUse = use;
    }

    public String getApiSource() {
        return apiSource;
    }

    public void setApiSource(String apiSource) {
        this.apiSource = apiSource;
    }

    public Long getNewApiId() {
        return newApiId;
    }

    public void setNewApiId(Long newApiId) {
        this.newApiId = newApiId;
    }

    public String getPreviewImagesJson() {
        return previewImagesJson;
    }

    public void setPreviewImagesJson(String previewImagesJson) {
        this.previewImagesJson = previewImagesJson;
    }
}
