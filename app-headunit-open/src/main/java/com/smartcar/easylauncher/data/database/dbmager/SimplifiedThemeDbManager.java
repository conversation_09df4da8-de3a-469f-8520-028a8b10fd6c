package com.smartcar.easylauncher.data.database.dbmager;

import com.google.gson.Gson;
import com.smartcar.easylauncher.data.model.theme.UnifiedThemeModel;

import java.util.List;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * 简化的主题数据库管理器
 * 使用统一数据模型，无需转换
 */
public class SimplifiedThemeDbManager {
    private static final String TAG = "SimplifiedThemeDbManager";

    private SimplifiedThemeDbManager() {}

    public static SimplifiedThemeDbManager getInstance() {
        return Holder.INSTANCE;
    }

    private static class Holder {
        private static final SimplifiedThemeDbManager INSTANCE = new SimplifiedThemeDbManager();
    }

    /**
     * 直接保存主题 - 无需转换！
     */
    public Observable<Long> saveTheme(UnifiedThemeModel theme) {
        return Observable.create((ObservableOnSubscribe<Long>) emitter -> {
            try {
                // 直接保存，无需任何转换
                Long result = AppDatabase.getDatabase().getUnifiedThemeDao().insert(theme);
                emitter.onNext(result);
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 批量保存主题
     */
    public Observable<List<Long>> saveThemes(List<UnifiedThemeModel> themes) {
        return Observable.create((ObservableOnSubscribe<List<Long>>) emitter -> {
            try {
                List<Long> results = AppDatabase.getDatabase().getUnifiedThemeDao().insert(themes);
                emitter.onNext(results);
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 根据ID获取主题
     */
    public Observable<UnifiedThemeModel> getThemeById(Long themeId) {
        return Observable.create((ObservableOnSubscribe<UnifiedThemeModel>) emitter -> {
            try {
                UnifiedThemeModel theme = AppDatabase.getDatabase().getUnifiedThemeDao().getById(themeId);
                if (theme != null) {
                    emitter.onNext(theme);
                } else {
                    emitter.onError(new Exception("主题不存在: " + themeId));
                }
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 获取所有主题
     */
    public Observable<List<UnifiedThemeModel>> getAllThemes() {
        return Observable.create((ObservableOnSubscribe<List<UnifiedThemeModel>>) emitter -> {
            try {
                List<UnifiedThemeModel> themes = AppDatabase.getDatabase().getUnifiedThemeDao().getAll();
                emitter.onNext(themes);
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 更新下载状态
     */
    public Observable<Integer> updateDownloadStatus(Long themeId, boolean isDownloaded, String localFilePath) {
        return Observable.create((ObservableOnSubscribe<Integer>) emitter -> {
            try {
                String downloadTime = isDownloaded ? String.valueOf(System.currentTimeMillis()) : null;
                int result = AppDatabase.getDatabase().getUnifiedThemeDao()
                        .updateDownloadStatus(themeId, isDownloaded, localFilePath, downloadTime);
                emitter.onNext(result);
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 设置当前主题
     */
    public Observable<Integer> setCurrentTheme(Long themeId) {
        return Observable.create((ObservableOnSubscribe<Integer>) emitter -> {
            try {
                // 清除所有主题的使用状态
                AppDatabase.getDatabase().getUnifiedThemeDao().clearAllCurrentTheme();
                // 设置指定主题为当前主题
                int result = AppDatabase.getDatabase().getUnifiedThemeDao().setCurrentTheme(themeId, true);
                emitter.onNext(result);
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 获取当前使用的主题
     */
    public Observable<UnifiedThemeModel> getCurrentTheme() {
        return Observable.create((ObservableOnSubscribe<UnifiedThemeModel>) emitter -> {
            try {
                UnifiedThemeModel theme = AppDatabase.getDatabase().getUnifiedThemeDao().getCurrentTheme();
                if (theme != null) {
                    emitter.onNext(theme);
                } else {
                    emitter.onError(new Exception("没有当前使用的主题"));
                }
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 从API响应JSON直接解析并保存
     */
    public Observable<Long> saveThemeFromJson(String jsonResponse) {
        return Observable.create((ObservableOnSubscribe<Long>) emitter -> {
            try {
                Gson gson = new Gson();
                
                // 解析API响应
                ApiResponse response = gson.fromJson(jsonResponse, ApiResponse.class);
                
                if (response.getCode() == 200 && response.getData() != null) {
                    // 直接保存，无需转换！
                    Long result = AppDatabase.getDatabase().getUnifiedThemeDao().insert(response.getData());
                    emitter.onNext(result);
                } else {
                    emitter.onError(new Exception("API响应错误: " + response.getMsg()));
                }
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * API响应包装类
     */
    public static class ApiResponse {
        private String msg;
        private Integer code;
        private UnifiedThemeModel data;

        public String getMsg() { return msg; }
        public void setMsg(String msg) { this.msg = msg; }

        public Integer getCode() { return code; }
        public void setCode(Integer code) { this.code = code; }

        public UnifiedThemeModel getData() { return data; }
        public void setData(UnifiedThemeModel data) { this.data = data; }
    }
}
