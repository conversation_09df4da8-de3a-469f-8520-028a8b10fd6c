package com.smartcar.easylauncher.data.model.system;

/**
 * 新版本更新接口数据模型
 * <AUTHOR>
 * @date 2025/08/02
 */
public class NewVersionInfo {
    private Integer id;
    private String platform;
    private Integer versionCode;
    private String versionName;
    private String title;
    private String description;
    private String realDownloadUrl;
    private String fileName;
    private Long fileSize;
    private String fileMd5;
    private Integer isForceUpdate;
    private Integer minSupportVersion;
    private String urlExpireTime;
    private Integer currentActiveIndex;
    private String backupUrls;
    private String backupPasswords;
    private String backupDiskTypes;
    private String versionType;
    private String channelType;
    private String channelName;

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Integer getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(Integer versionCode) {
        this.versionCode = versionCode;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRealDownloadUrl() {
        return realDownloadUrl;
    }

    public void setRealDownloadUrl(String realDownloadUrl) {
        this.realDownloadUrl = realDownloadUrl;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileMd5() {
        return fileMd5;
    }

    public void setFileMd5(String fileMd5) {
        this.fileMd5 = fileMd5;
    }

    public Integer getIsForceUpdate() {
        return isForceUpdate;
    }

    public void setIsForceUpdate(Integer isForceUpdate) {
        this.isForceUpdate = isForceUpdate;
    }

    public Integer getMinSupportVersion() {
        return minSupportVersion;
    }

    public void setMinSupportVersion(Integer minSupportVersion) {
        this.minSupportVersion = minSupportVersion;
    }

    public String getUrlExpireTime() {
        return urlExpireTime;
    }

    public void setUrlExpireTime(String urlExpireTime) {
        this.urlExpireTime = urlExpireTime;
    }

    public Integer getCurrentActiveIndex() {
        return currentActiveIndex;
    }

    public void setCurrentActiveIndex(Integer currentActiveIndex) {
        this.currentActiveIndex = currentActiveIndex;
    }

    public String getBackupUrls() {
        return backupUrls;
    }

    public void setBackupUrls(String backupUrls) {
        this.backupUrls = backupUrls;
    }

    public String getBackupPasswords() {
        return backupPasswords;
    }

    public void setBackupPasswords(String backupPasswords) {
        this.backupPasswords = backupPasswords;
    }

    public String getBackupDiskTypes() {
        return backupDiskTypes;
    }

    public void setBackupDiskTypes(String backupDiskTypes) {
        this.backupDiskTypes = backupDiskTypes;
    }

    public String getVersionType() {
        return versionType;
    }

    public void setVersionType(String versionType) {
        this.versionType = versionType;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    /**
     * 判断是否为强制更新
     */
    public boolean isForceUpdate() {
        return isForceUpdate != null && isForceUpdate == 1;
    }

    /**
     * 获取格式化的文件大小
     */
    public String getFormattedFileSize() {
        if (fileSize == null) return "未知大小";
        
        double size = fileSize.doubleValue();
        if (size < 1024) {
            return String.format("%.0f B", size);
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024 * 1024));
        } else {
            return String.format("%.1f GB", size / (1024 * 1024 * 1024));
        }
    }
}
