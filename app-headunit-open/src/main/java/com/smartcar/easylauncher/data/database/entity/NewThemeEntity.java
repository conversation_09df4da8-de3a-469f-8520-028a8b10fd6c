package com.smartcar.easylauncher.data.database.entity;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;



/**
 * 新主题API数据库实体
 * 直接对应新API的数据结构，不再进行转换
 */
@Entity(tableName = "new_theme")
public class NewThemeEntity {
    
    @PrimaryKey
    @ColumnInfo(name = "id")
    public Long id;
    
    @ColumnInfo(name = "theme_name")
    public String themeName;
    
    @ColumnInfo(name = "theme_description")
    public String themeDescription;
    
    @ColumnInfo(name = "theme_type")
    public Integer themeType;
    
    @ColumnInfo(name = "download_count")
    public Long downloadCount;
    
    @ColumnInfo(name = "heat")
    public Long heat;
    
    @ColumnInfo(name = "author_id")
    public Long authorId;
    
    @ColumnInfo(name = "author")
    public String author;
    
    @ColumnInfo(name = "author_img")
    public String authorImg;
    
    @ColumnInfo(name = "price")
    public Double price;
    
    @ColumnInfo(name = "is_trial_enabled")
    public Integer isTrialEnabled;
    
    @ColumnInfo(name = "status")
    public Integer status;
    
    @ColumnInfo(name = "release_status")
    public Integer releaseStatus;
    
    @ColumnInfo(name = "is_vip")
    public Integer isVip;
    
    @ColumnInfo(name = "label")
    public String label;
    
    @ColumnInfo(name = "cover_image")
    public String coverImage;
    
    @ColumnInfo(name = "sort_order")
    public Integer sortOrder;
    
    @ColumnInfo(name = "create_time")
    public String createTime;
    
    @ColumnInfo(name = "update_time")
    public String updateTime;
    
    @ColumnInfo(name = "remark")
    public String remark;
    
    // 序列化存储的JSON字段
    @ColumnInfo(name = "preview_images_json")
    public String previewImagesJson;
    
    @ColumnInfo(name = "packages_json")
    public String packagesJson;
    
    // 本地状态字段
    @ColumnInfo(name = "is_downloaded")
    public boolean isDownloaded = false;
    
    @ColumnInfo(name = "is_installed")
    public boolean isInstalled = false;
    
    @ColumnInfo(name = "is_in_use")
    public boolean isInUse = false;
    
    @ColumnInfo(name = "local_file_path")
    public String localFilePath;
    
    @ColumnInfo(name = "download_time")
    public String downloadTime;
    
    @ColumnInfo(name = "install_time")
    public String installTime;
    
    // 版本信息（从packages中提取的最新版本）
    @ColumnInfo(name = "current_version_name")
    public String currentVersionName;
    
    @ColumnInfo(name = "current_version_code")
    public String currentVersionCode;
    
    @ColumnInfo(name = "download_url")
    public String downloadUrl;
    
    @ColumnInfo(name = "has_update")
    public boolean hasUpdate = false;

    // 构造方法
    public NewThemeEntity() {
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getThemeName() {
        return themeName;
    }

    public void setThemeName(String themeName) {
        this.themeName = themeName;
    }

    public String getThemeDescription() {
        return themeDescription;
    }

    public void setThemeDescription(String themeDescription) {
        this.themeDescription = themeDescription;
    }

    public Integer getThemeType() {
        return themeType;
    }

    public void setThemeType(Integer themeType) {
        this.themeType = themeType;
    }

    public Long getDownloadCount() {
        return downloadCount;
    }

    public void setDownloadCount(Long downloadCount) {
        this.downloadCount = downloadCount;
    }

    public Long getHeat() {
        return heat;
    }

    public void setHeat(Long heat) {
        this.heat = heat;
    }

    public Long getAuthorId() {
        return authorId;
    }

    public void setAuthorId(Long authorId) {
        this.authorId = authorId;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getAuthorImg() {
        return authorImg;
    }

    public void setAuthorImg(String authorImg) {
        this.authorImg = authorImg;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Integer getIsTrialEnabled() {
        return isTrialEnabled;
    }

    public void setIsTrialEnabled(Integer isTrialEnabled) {
        this.isTrialEnabled = isTrialEnabled;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getReleaseStatus() {
        return releaseStatus;
    }

    public void setReleaseStatus(Integer releaseStatus) {
        this.releaseStatus = releaseStatus;
    }

    public Integer getIsVip() {
        return isVip;
    }

    public void setIsVip(Integer isVip) {
        this.isVip = isVip;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getCoverImage() {
        return coverImage;
    }

    public void setCoverImage(String coverImage) {
        this.coverImage = coverImage;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPreviewImagesJson() {
        return previewImagesJson;
    }

    public void setPreviewImagesJson(String previewImagesJson) {
        this.previewImagesJson = previewImagesJson;
    }

    public String getPackagesJson() {
        return packagesJson;
    }

    public void setPackagesJson(String packagesJson) {
        this.packagesJson = packagesJson;
    }

    public boolean isDownloaded() {
        return isDownloaded;
    }

    public void setDownloaded(boolean downloaded) {
        isDownloaded = downloaded;
    }

    public boolean isInstalled() {
        return isInstalled;
    }

    public void setInstalled(boolean installed) {
        isInstalled = installed;
    }

    public boolean isInUse() {
        return isInUse;
    }

    public void setInUse(boolean inUse) {
        isInUse = inUse;
    }

    public String getLocalFilePath() {
        return localFilePath;
    }

    public void setLocalFilePath(String localFilePath) {
        this.localFilePath = localFilePath;
    }

    public String getDownloadTime() {
        return downloadTime;
    }

    public void setDownloadTime(String downloadTime) {
        this.downloadTime = downloadTime;
    }

    public String getInstallTime() {
        return installTime;
    }

    public void setInstallTime(String installTime) {
        this.installTime = installTime;
    }

    public String getCurrentVersionName() {
        return currentVersionName;
    }

    public void setCurrentVersionName(String currentVersionName) {
        this.currentVersionName = currentVersionName;
    }

    public String getCurrentVersionCode() {
        return currentVersionCode;
    }

    public void setCurrentVersionCode(String currentVersionCode) {
        this.currentVersionCode = currentVersionCode;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public boolean isHasUpdate() {
        return hasUpdate;
    }

    public void setHasUpdate(boolean hasUpdate) {
        this.hasUpdate = hasUpdate;
    }
}
