package com.smartcar.easylauncher.data.model.theme;

import java.util.List;

public class ThemeDetailModel {

    private String msg;
    private Integer code;
    private DataDTO data;

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public DataDTO getData() {
        return data;
    }

    public void setData(DataDTO data) {
        this.data = data;
    }

    public static class DataDTO {
        private String createBy;
        private String createTime;
        private String updateBy;
        private String updateTime;
        private Long id;
        private String themeName;
        private String themeDescription;
        private Integer themeType;
        private String themeTypeName;
        private Long downloadCount;
        private Long heat;
        private Long authorId;
        private String author;
        private String authorImg;
        private Integer price;
        private Integer isTrialEnabled;
        private Integer status;
        private Integer releaseStatus;
        private Integer isVip;
        private String label;
        private String coverImage;
        private String remark;
        private Integer sortOrder;
        private List<PreviewImagesDTO> previewImages;
        private ThemePackageDTO themePackage;

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getCreateBy() {
            return createBy;
        }

        public void setCreateBy(String createBy) {
            this.createBy = createBy;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getUpdateBy() {
            return updateBy;
        }

        public void setUpdateBy(String updateBy) {
            this.updateBy = updateBy;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getThemeName() {
            return themeName;
        }

        public void setThemeName(String themeName) {
            this.themeName = themeName;
        }

        public String getThemeDescription() {
            return themeDescription;
        }

        public void setThemeDescription(String themeDescription) {
            this.themeDescription = themeDescription;
        }

        public Integer getThemeType() {
            return themeType;
        }

        public void setThemeType(Integer themeType) {
            this.themeType = themeType;
        }

        public String getThemeTypeName() {
            return themeTypeName;
        }

        public void setThemeTypeName(String themeTypeName) {
            this.themeTypeName = themeTypeName;
        }

        public Long getDownloadCount() {
            return downloadCount;
        }

        public void setDownloadCount(Long downloadCount) {
            this.downloadCount = downloadCount;
        }

        public Long getHeat() {
            return heat;
        }

        public void setHeat(Long heat) {
            this.heat = heat;
        }

        public Long getAuthorId() {
            return authorId;
        }

        public void setAuthorId(Long authorId) {
            this.authorId = authorId;
        }

        public String getAuthor() {
            return author;
        }

        public void setAuthor(String author) {
            this.author = author;
        }

        public String getAuthorImg() {
            return authorImg;
        }

        public void setAuthorImg(String authorImg) {
            this.authorImg = authorImg;
        }

        public Integer getPrice() {
            return price;
        }

        public void setPrice(Integer price) {
            this.price = price;
        }

        public Integer getIsTrialEnabled() {
            return isTrialEnabled;
        }

        public void setIsTrialEnabled(Integer isTrialEnabled) {
            this.isTrialEnabled = isTrialEnabled;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public Integer getReleaseStatus() {
            return releaseStatus;
        }

        public void setReleaseStatus(Integer releaseStatus) {
            this.releaseStatus = releaseStatus;
        }

        public Integer getIsVip() {
            return isVip;
        }

        public void setIsVip(Integer isVip) {
            this.isVip = isVip;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public String getCoverImage() {
            return coverImage;
        }

        public void setCoverImage(String coverImage) {
            this.coverImage = coverImage;
        }

        public Integer getSortOrder() {
            return sortOrder;
        }

        public void setSortOrder(Integer sortOrder) {
            this.sortOrder = sortOrder;
        }

        public List<PreviewImagesDTO> getPreviewImages() {
            return previewImages;
        }

        public void setPreviewImages(List<PreviewImagesDTO> previewImages) {
            this.previewImages = previewImages;
        }

        public ThemePackageDTO getThemePackage() {
            return themePackage;
        }

        public void setThemePackage(ThemePackageDTO themePackage) {
            this.themePackage = themePackage;
        }

        public static class ThemePackageDTO {
            private String createBy;
            private String createTime;
            private String updateBy;
            private Integer id;
            private Integer themeId;
            private Integer versionCode;
            private String versionName;
            private String packagePath;
            private String fileName;
            private Integer fileSize;
            private String updateDescription;
            private Integer status;
            private String statusName;
            private String publishTime;
            private Integer downloadCount;
            private Integer isLatest;
            private String downloadUrl;

            public String getCreateBy() {
                return createBy;
            }

            public void setCreateBy(String createBy) {
                this.createBy = createBy;
            }

            public String getCreateTime() {
                return createTime;
            }

            public void setCreateTime(String createTime) {
                this.createTime = createTime;
            }

            public String getUpdateBy() {
                return updateBy;
            }

            public void setUpdateBy(String updateBy) {
                this.updateBy = updateBy;
            }

            public Integer getId() {
                return id;
            }

            public void setId(Integer id) {
                this.id = id;
            }

            public Integer getThemeId() {
                return themeId;
            }

            public void setThemeId(Integer themeId) {
                this.themeId = themeId;
            }

            public Integer getVersionCode() {
                return versionCode;
            }

            public void setVersionCode(Integer versionCode) {
                this.versionCode = versionCode;
            }

            public String getVersionName() {
                return versionName;
            }

            public void setVersionName(String versionName) {
                this.versionName = versionName;
            }

            public String getPackagePath() {
                return packagePath;
            }

            public void setPackagePath(String packagePath) {
                this.packagePath = packagePath;
            }

            public String getFileName() {
                return fileName;
            }

            public void setFileName(String fileName) {
                this.fileName = fileName;
            }

            public Integer getFileSize() {
                return fileSize;
            }

            public void setFileSize(Integer fileSize) {
                this.fileSize = fileSize;
            }

            public String getUpdateDescription() {
                return updateDescription;
            }

            public void setUpdateDescription(String updateDescription) {
                this.updateDescription = updateDescription;
            }

            public Integer getStatus() {
                return status;
            }

            public void setStatus(Integer status) {
                this.status = status;
            }

            public String getStatusName() {
                return statusName;
            }

            public void setStatusName(String statusName) {
                this.statusName = statusName;
            }

            public String getPublishTime() {
                return publishTime;
            }

            public void setPublishTime(String publishTime) {
                this.publishTime = publishTime;
            }

            public Integer getDownloadCount() {
                return downloadCount;
            }

            public void setDownloadCount(Integer downloadCount) {
                this.downloadCount = downloadCount;
            }

            public Integer getIsLatest() {
                return isLatest;
            }

            public void setIsLatest(Integer isLatest) {
                this.isLatest = isLatest;
            }

            public String getDownloadUrl() {
                return downloadUrl;
            }

            public void setDownloadUrl(String downloadUrl) {
                this.downloadUrl = downloadUrl;
            }
        }

        public static class PreviewImagesDTO {
            private String createBy;
            private String createTime;
            private String updateBy;
            private Integer id;
            private Integer themeId;
            private String title;
            private String description;
            private String imageUrl;
            private Integer sortOrder;
            private Integer imageType;

            public String getCreateBy() {
                return createBy;
            }

            public void setCreateBy(String createBy) {
                this.createBy = createBy;
            }

            public String getCreateTime() {
                return createTime;
            }

            public void setCreateTime(String createTime) {
                this.createTime = createTime;
            }

            public String getUpdateBy() {
                return updateBy;
            }

            public void setUpdateBy(String updateBy) {
                this.updateBy = updateBy;
            }

            public Integer getId() {
                return id;
            }

            public void setId(Integer id) {
                this.id = id;
            }

            public Integer getThemeId() {
                return themeId;
            }

            public void setThemeId(Integer themeId) {
                this.themeId = themeId;
            }

            public String getTitle() {
                return title;
            }

            public void setTitle(String title) {
                this.title = title;
            }

            public String getDescription() {
                return description;
            }

            public void setDescription(String description) {
                this.description = description;
            }

            public String getImageUrl() {
                return imageUrl;
            }

            public void setImageUrl(String imageUrl) {
                this.imageUrl = imageUrl;
            }

            public Integer getSortOrder() {
                return sortOrder;
            }

            public void setSortOrder(Integer sortOrder) {
                this.sortOrder = sortOrder;
            }

            public Integer getImageType() {
                return imageType;
            }

            public void setImageType(Integer imageType) {
                this.imageType = imageType;
            }
        }
    }
}
