package com.smartcar.easylauncher.data.database.converter;

import androidx.room.TypeConverter;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.smartcar.easylauncher.data.model.theme.UnifiedThemeModel;

import java.lang.reflect.Type;
import java.util.List;

/**
 * Room数据库类型转换器
 * 用于处理复杂对象的序列化和反序列化
 */
public class ThemeTypeConverters {
    
    private static final Gson gson = new Gson();

    /**
     * 预览图片列表转JSON字符串
     */
    @TypeConverter
    public static String previewImagesToJson(List<UnifiedThemeModel.PreviewImage> previewImages) {
        if (previewImages == null) {
            return null;
        }
        return gson.toJson(previewImages);
    }

    /**
     * JSON字符串转预览图片列表
     */
    @TypeConverter
    public static List<UnifiedThemeModel.PreviewImage> jsonToPreviewImages(String json) {
        if (json == null) {
            return null;
        }
        Type listType = new TypeToken<List<UnifiedThemeModel.PreviewImage>>(){}.getType();
        return gson.fromJson(json, listType);
    }

    /**
     * 主题包对象转JSON字符串
     */
    @TypeConverter
    public static String themePackageToJson(UnifiedThemeModel.ThemePackage themePackage) {
        if (themePackage == null) {
            return null;
        }
        return gson.toJson(themePackage);
    }

    /**
     * JSON字符串转主题包对象
     */
    @TypeConverter
    public static UnifiedThemeModel.ThemePackage jsonToThemePackage(String json) {
        if (json == null) {
            return null;
        }
        return gson.fromJson(json, UnifiedThemeModel.ThemePackage.class);
    }
}
