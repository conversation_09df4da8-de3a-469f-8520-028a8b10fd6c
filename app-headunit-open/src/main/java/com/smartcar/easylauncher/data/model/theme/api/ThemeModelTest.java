package com.smartcar.easylauncher.data.model.theme.api;

import com.google.gson.Gson;

/**
 * 主题数据模型测试类
 * 用于验证数据模型是否能正确解析JSON示例
 */
public class ThemeModelTest {
    
    /**
     * 测试JSON示例
     */
    private static final String TEST_JSON = "{\n" +
            "  \"code\": 200,\n" +
            "  \"msg\": \"查询成功\",\n" +
            "  \"data\": {\n" +
            "    \"id\": 1,\n" +
            "    \"themeName\": \"心静\",\n" +
            "    \"themeDescription\": \"心之所往，便是远方\\n以心为起点，愿美好风景伴你一路同行\\n心静，志远\",\n" +
            "    \"themeType\": 1,\n" +
            "    \"themeTypeName\": \"夜间主题\",\n" +
            "    \"downloadCount\": 7950,\n" +
            "    \"heat\": 7950,\n" +
            "    \"authorId\": 1,\n" +
            "    \"author\": \"默\",\n" +
            "    \"authorImg\": \"http://**************:8888/down/nJ7x7QZ9vLXT.jpg\",\n" +
            "    \"price\": 0.00,\n" +
            "    \"isTrialEnabled\": 1,\n" +
            "    \"status\": 1,\n" +
            "    \"releaseStatus\": 2,\n" +
            "    \"isVip\": 0,\n" +
            "    \"label\": \"hot\",\n" +
            "    \"coverImage\": \"http://**************:8888/down/NqnT2NIVsOoI.jpg\",\n" +
            "    \"sortOrder\": 1,\n" +
            "    \"createTime\": \"2025-08-02 22:22:37\",\n" +
            "    \"updateTime\": null,\n" +
            "    \"remark\": null,\n" +
            "    \"themePackage\": {\n" +
            "      \"id\": 1,\n" +
            "      \"themeId\": 1,\n" +
            "      \"versionCode\": 100,\n" +
            "      \"versionName\": \"1.0.0\",\n" +
            "      \"packagePath\": \"/themes/theme1_v1.0.0.zip\",\n" +
            "      \"minAppVersion\": \"1.0.0\",\n" +
            "      \"fileName\": \"theme1_v1.0.0.zip\",\n" +
            "      \"fileSize\": 2048576,\n" +
            "      \"fileMd5\": \"d41d8cd98f00b204e9800998ecf8427e\",\n" +
            "      \"updateDescription\": \"修复已知问题\",\n" +
            "      \"status\": 1,\n" +
            "      \"statusName\": \"发布\",\n" +
            "      \"publishTime\": \"2025-08-03 12:00:00\",\n" +
            "      \"downloadCount\": 1000,\n" +
            "      \"isLatest\": 1,\n" +
            "      \"downloadUrl\": \"/theme/themePackage/download/1\",\n" +
            "      \"createTime\": \"2025-08-03 12:00:00\",\n" +
            "      \"updateTime\": null\n" +
            "    },\n" +
            "    \"previewImages\": [\n" +
            "      {\n" +
            "        \"id\": 1,\n" +
            "        \"themeId\": 1,\n" +
            "        \"title\": \"主界面\",\n" +
            "        \"description\": \"主题主界面展示\",\n" +
            "        \"imageUrl\": \"http://**************:8888/down/NqnT2NIVsOoI.jpg\",\n" +
            "        \"sortOrder\": 1,\n" +
            "        \"imageType\": 1,\n" +
            "        \"createTime\": \"2025-08-02 22:22:37\",\n" +
            "        \"updateTime\": null\n" +
            "      }\n" +
            "    ]\n" +
            "  }\n" +
            "}";
    
    /**
     * 测试数据模型解析
     */
    public static void testModelParsing() {
        try {
            Gson gson = new Gson();
            
            // 解析API响应
            ApiResponse<NewThemeInfo> response = gson.fromJson(TEST_JSON, 
                new com.google.gson.reflect.TypeToken<ApiResponse<NewThemeInfo>>(){}.getType());
            
            if (response != null && response.getData() != null) {
                NewThemeInfo themeInfo = response.getData();
                
                System.out.println("=== 主题信息解析测试 ===");
                System.out.println("主题ID: " + themeInfo.getId());
                System.out.println("主题名称: " + themeInfo.getThemeName());
                System.out.println("主题类型: " + themeInfo.getThemeType());
                System.out.println("主题类型名称: " + themeInfo.getThemeTypeName());
                System.out.println("下载量: " + themeInfo.getDownloadCount());
                System.out.println("作者: " + themeInfo.getAuthor());
                
                // 测试主题包解析
                NewThemePackage themePackage = themeInfo.getThemePackage();
                if (themePackage != null) {
                    System.out.println("\n=== 主题包信息解析测试 ===");
                    System.out.println("包ID: " + themePackage.getId());
                    System.out.println("版本名称: " + themePackage.getVersionName());
                    System.out.println("状态名称: " + themePackage.getStatusName());
                    System.out.println("文件大小: " + themePackage.getFileSize());
                    System.out.println("创建时间: " + themePackage.getCreateTime());
                }
                
                // 测试预览图片解析
                if (themeInfo.getPreviewImages() != null && !themeInfo.getPreviewImages().isEmpty()) {
                    System.out.println("\n=== 预览图片解析测试 ===");
                    NewPreviewImage previewImage = themeInfo.getPreviewImages().get(0);
                    System.out.println("图片ID: " + previewImage.getId());
                    System.out.println("图片标题: " + previewImage.getTitle());
                    System.out.println("图片描述: " + previewImage.getDescription());
                    System.out.println("图片URL: " + previewImage.getImageUrl());
                    System.out.println("创建时间: " + previewImage.getCreateTime());
                }
                
                System.out.println("\n✅ 数据模型解析测试成功！");
            } else {
                System.out.println("❌ 解析失败：响应为空");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 解析失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 主方法，用于运行测试
     */
    public static void main(String[] args) {
        testModelParsing();
    }
}
