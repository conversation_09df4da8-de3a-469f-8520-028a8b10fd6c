package com.smartcar.easylauncher.data.model.system;

/**
 * 新版本更新接口响应模型
 * <AUTHOR>
 * @date 2025/08/02
 */
public class NewUpdateResponse {
    private Integer code;
    private String msg;
    private NewVersionInfo data;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public NewVersionInfo getData() {
        return data;
    }

    public void setData(NewVersionInfo data) {
        this.data = data;
    }

    /**
     * 判断是否有更新
     */
    public boolean hasUpdate() {
        return code != null && code == 200 && data != null;
    }

    /**
     * 判断是否无更新
     */
    public boolean noUpdate() {
        return code != null && code == 200 && data == null;
    }

    /**
     * 判断请求是否成功
     */
    public boolean isSuccess() {
        return code != null && code == 200;
    }
}
