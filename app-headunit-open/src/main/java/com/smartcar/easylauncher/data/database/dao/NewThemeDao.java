package com.smartcar.easylauncher.data.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.smartcar.easylauncher.data.database.entity.NewThemeEntity;

import java.util.List;

import io.reactivex.rxjava3.core.Flowable;

/**
 * 新主题数据库操作接口
 */
@Dao
public interface NewThemeDao {

    // -------------------------插入--------------------------------------------

    /**
     * 插入主题，若已存在相同ID则覆盖
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    Long insert(NewThemeEntity theme);

    /**
     * 批量插入主题
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    List<Long> insert(List<NewThemeEntity> themes);

    // -------------------------更新--------------------------------------------

    /**
     * 更新主题
     */
    @Update
    int update(NewThemeEntity theme);

    /**
     * 批量更新主题
     */
    @Update
    int update(List<NewThemeEntity> themes);

    /**
     * 更新主题的本地状态
     */
    @Query("UPDATE new_theme SET is_downloaded = :isDownloaded, is_installed = :isInstalled, " +
           "local_file_path = :localFilePath, download_time = :downloadTime WHERE id = :themeId")
    int updateLocalStatus(Long themeId, boolean isDownloaded, boolean isInstalled, 
                         String localFilePath, String downloadTime);

    /**
     * 更新主题使用状态
     */
    @Query("UPDATE new_theme SET is_in_use = :isInUse WHERE id = :themeId")
    int updateUsageStatus(Long themeId, boolean isInUse);

    /**
     * 清除所有主题的使用状态
     */
    @Query("UPDATE new_theme SET is_in_use = 0")
    int clearAllUsageStatus();

    /**
     * 更新主题的更新状态
     */
    @Query("UPDATE new_theme SET has_update = :hasUpdate WHERE id = :themeId")
    int updateHasUpdate(Long themeId, boolean hasUpdate);

    // -------------------------查询--------------------------------------------

    /**
     * 根据ID查询主题
     */
    @Query("SELECT * FROM new_theme WHERE id = :themeId")
    NewThemeEntity getById(Long themeId);

    /**
     * 根据主题名称查询
     */
    @Query("SELECT * FROM new_theme WHERE theme_name = :themeName")
    NewThemeEntity getByName(String themeName);

    /**
     * 查询所有主题
     */
    @Query("SELECT * FROM new_theme ORDER BY update_time DESC")
    List<NewThemeEntity> getAll();

    /**
     * 查询所有主题（响应式）
     */
    @Query("SELECT * FROM new_theme ORDER BY update_time DESC")
    Flowable<List<NewThemeEntity>> getAllFlowable();

    /**
     * 查询已下载的主题
     */
    @Query("SELECT * FROM new_theme WHERE is_downloaded = 1 ORDER BY download_time DESC")
    List<NewThemeEntity> getDownloadedThemes();

    /**
     * 查询已安装的主题
     */
    @Query("SELECT * FROM new_theme WHERE is_installed = 1 ORDER BY install_time DESC")
    List<NewThemeEntity> getInstalledThemes();

    /**
     * 查询当前使用的主题
     */
    @Query("SELECT * FROM new_theme WHERE is_in_use = 1")
    NewThemeEntity getCurrentTheme();

    /**
     * 查询有更新的主题
     */
    @Query("SELECT * FROM new_theme WHERE has_update = 1 AND is_installed = 1")
    List<NewThemeEntity> getThemesWithUpdate();

    /**
     * 根据主题类型查询
     */
    @Query("SELECT * FROM new_theme WHERE theme_type = :themeType ORDER BY heat DESC")
    List<NewThemeEntity> getByThemeType(int themeType);

    /**
     * 根据标签查询
     */
    @Query("SELECT * FROM new_theme WHERE label = :label ORDER BY heat DESC")
    List<NewThemeEntity> getByLabel(String label);

    /**
     * 搜索主题（按名称和描述）
     */
    @Query("SELECT * FROM new_theme WHERE theme_name LIKE :keyword OR theme_description LIKE :keyword " +
           "ORDER BY heat DESC")
    List<NewThemeEntity> searchThemes(String keyword);

    /**
     * 查询热门主题
     */
    @Query("SELECT * FROM new_theme WHERE status = 1 AND release_status = 2 ORDER BY heat DESC LIMIT :limit")
    List<NewThemeEntity> getHotThemes(int limit);

    /**
     * 查询最新主题
     */
    @Query("SELECT * FROM new_theme WHERE status = 1 AND release_status = 2 ORDER BY create_time DESC LIMIT :limit")
    List<NewThemeEntity> getLatestThemes(int limit);

    // -------------------------删除--------------------------------------------

    /**
     * 删除主题
     */
    @Delete
    int delete(NewThemeEntity theme);

    /**
     * 根据ID删除主题
     */
    @Query("DELETE FROM new_theme WHERE id = :themeId")
    int deleteById(Long themeId);

    /**
     * 根据主题名称删除
     */
    @Query("DELETE FROM new_theme WHERE theme_name = :themeName")
    int deleteByName(String themeName);

    /**
     * 删除所有主题
     */
    @Query("DELETE FROM new_theme")
    int deleteAll();

    /**
     * 删除未安装的主题
     */
    @Query("DELETE FROM new_theme WHERE is_installed = 0")
    int deleteUninstalledThemes();

    // -------------------------统计--------------------------------------------

    /**
     * 统计主题总数
     */
    @Query("SELECT COUNT(*) FROM new_theme")
    int getThemeCount();

    /**
     * 统计已下载主题数
     */
    @Query("SELECT COUNT(*) FROM new_theme WHERE is_downloaded = 1")
    int getDownloadedThemeCount();

    /**
     * 统计已安装主题数
     */
    @Query("SELECT COUNT(*) FROM new_theme WHERE is_installed = 1")
    int getInstalledThemeCount();

    /**
     * 统计有更新的主题数
     */
    @Query("SELECT COUNT(*) FROM new_theme WHERE has_update = 1 AND is_installed = 1")
    int getUpdateAvailableCount();
}
