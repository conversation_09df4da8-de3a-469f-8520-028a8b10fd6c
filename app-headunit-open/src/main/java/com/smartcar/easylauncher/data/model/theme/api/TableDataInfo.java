package com.smartcar.easylauncher.data.model.theme.api;

import java.util.List;

/**
 * 分页响应格式
 * 对应新主题API的TableDataInfo格式
 */
public class TableDataInfo<T> {
    private long total;
    private List<T> rows;
    private int code;
    private String msg;

    public TableDataInfo() {
    }

    public TableDataInfo(long total, List<T> rows, int code, String msg) {
        this.total = total;
        this.rows = rows;
        this.code = code;
        this.msg = msg;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public List<T> getRows() {
        return rows;
    }

    public void setRows(List<T> rows) {
        this.rows = rows;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    /**
     * 判断响应是否成功
     */
    public boolean isSuccess() {
        return code == 200;
    }

    @Override
    public String toString() {
        return "TableDataInfo{" +
                "total=" + total +
                ", rows=" + rows +
                ", code=" + code +
                ", msg='" + msg + '\'' +
                '}';
    }
}
