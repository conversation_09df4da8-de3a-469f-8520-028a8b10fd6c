package com.smartcar.easylauncher.data.model.theme;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;

import com.google.gson.annotations.SerializedName;
import com.smartcar.easylauncher.data.database.converter.ThemeTypeConverters;

import java.util.List;

/**
 * 统一的主题数据模型
 * 同时支持：
 * 1. API响应解析 (Gson)
 * 2. 数据库存储 (Room)
 * 3. 业务逻辑使用
 * 
 * 这样避免了多个模型之间的转换，简化了代码
 */
@Entity(tableName = "unified_themes")
@TypeConverters(ThemeTypeConverters.class)
public class UnifiedThemeModel {

    @PrimaryKey
    @SerializedName("id")
    @ColumnInfo(name = "id")
    private Long id;

    @SerializedName("themeName")
    @ColumnInfo(name = "theme_name")
    private String themeName;

    @SerializedName("themeDescription")
    @ColumnInfo(name = "theme_description")
    private String themeDescription;

    @SerializedName("themeType")
    @ColumnInfo(name = "theme_type")
    private Integer themeType;

    @SerializedName("themeTypeName")
    @ColumnInfo(name = "theme_type_name")
    private String themeTypeName;

    @SerializedName("downloadCount")
    @ColumnInfo(name = "download_count")
    private Long downloadCount;

    @SerializedName("heat")
    @ColumnInfo(name = "heat")
    private Long heat;

    @SerializedName("authorId")
    @ColumnInfo(name = "author_id")
    private Long authorId;

    @SerializedName("author")
    @ColumnInfo(name = "author")
    private String author;

    @SerializedName("authorImg")
    @ColumnInfo(name = "author_img")
    private String authorImg;

    @SerializedName("price")
    @ColumnInfo(name = "price")
    private Double price;

    @SerializedName("isTrialEnabled")
    @ColumnInfo(name = "is_trial_enabled")
    private Integer isTrialEnabled;

    @SerializedName("status")
    @ColumnInfo(name = "status")
    private Integer status;

    @SerializedName("releaseStatus")
    @ColumnInfo(name = "release_status")
    private Integer releaseStatus;

    @SerializedName("isVip")
    @ColumnInfo(name = "is_vip")
    private Integer isVip;

    @SerializedName("label")
    @ColumnInfo(name = "label")
    private String label;

    @SerializedName("coverImage")
    @ColumnInfo(name = "cover_image")
    private String coverImage;

    @SerializedName("sortOrder")
    @ColumnInfo(name = "sort_order")
    private Integer sortOrder;

    @SerializedName("createTime")
    @ColumnInfo(name = "create_time")
    private String createTime;

    @SerializedName("updateTime")
    @ColumnInfo(name = "update_time")
    private String updateTime;

    @SerializedName("remark")
    @ColumnInfo(name = "remark")
    private String remark;

    // 复杂对象使用TypeConverter自动转换
    @SerializedName("previewImages")
    @ColumnInfo(name = "preview_images")
    private List<PreviewImage> previewImages;

    @SerializedName("themePackage")
    @ColumnInfo(name = "theme_package")
    private ThemePackage themePackage;

    // 本地数据库字段，API不返回
    @Ignore // API响应中忽略
    @ColumnInfo(name = "is_downloaded")
    private Boolean isDownloaded = false;

    @Ignore
    @ColumnInfo(name = "local_file_path")
    private String localFilePath;

    @Ignore
    @ColumnInfo(name = "is_current_theme")
    private Boolean isCurrentTheme = false;

    @Ignore
    @ColumnInfo(name = "download_time")
    private String downloadTime;

    // 构造函数
    public UnifiedThemeModel() {}

    // Getter和Setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getThemeName() { return themeName; }
    public void setThemeName(String themeName) { this.themeName = themeName; }

    public String getThemeDescription() { return themeDescription; }
    public void setThemeDescription(String themeDescription) { this.themeDescription = themeDescription; }

    public Integer getThemeType() { return themeType; }
    public void setThemeType(Integer themeType) { this.themeType = themeType; }

    public String getThemeTypeName() { return themeTypeName; }
    public void setThemeTypeName(String themeTypeName) { this.themeTypeName = themeTypeName; }

    public Long getDownloadCount() { return downloadCount; }
    public void setDownloadCount(Long downloadCount) { this.downloadCount = downloadCount; }

    public Long getHeat() { return heat; }
    public void setHeat(Long heat) { this.heat = heat; }

    public Long getAuthorId() { return authorId; }
    public void setAuthorId(Long authorId) { this.authorId = authorId; }

    public String getAuthor() { return author; }
    public void setAuthor(String author) { this.author = author; }

    public String getAuthorImg() { return authorImg; }
    public void setAuthorImg(String authorImg) { this.authorImg = authorImg; }

    public Double getPrice() { return price; }
    public void setPrice(Double price) { this.price = price; }

    public Integer getIsTrialEnabled() { return isTrialEnabled; }
    public void setIsTrialEnabled(Integer isTrialEnabled) { this.isTrialEnabled = isTrialEnabled; }

    public Integer getStatus() { return status; }
    public void setStatus(Integer status) { this.status = status; }

    public Integer getReleaseStatus() { return releaseStatus; }
    public void setReleaseStatus(Integer releaseStatus) { this.releaseStatus = releaseStatus; }

    public Integer getIsVip() { return isVip; }
    public void setIsVip(Integer isVip) { this.isVip = isVip; }

    public String getLabel() { return label; }
    public void setLabel(String label) { this.label = label; }

    public String getCoverImage() { return coverImage; }
    public void setCoverImage(String coverImage) { this.coverImage = coverImage; }

    public Integer getSortOrder() { return sortOrder; }
    public void setSortOrder(Integer sortOrder) { this.sortOrder = sortOrder; }

    public String getCreateTime() { return createTime; }
    public void setCreateTime(String createTime) { this.createTime = createTime; }

    public String getUpdateTime() { return updateTime; }
    public void setUpdateTime(String updateTime) { this.updateTime = updateTime; }

    public String getRemark() { return remark; }
    public void setRemark(String remark) { this.remark = remark; }

    public List<PreviewImage> getPreviewImages() { return previewImages; }
    public void setPreviewImages(List<PreviewImage> previewImages) { this.previewImages = previewImages; }

    public ThemePackage getThemePackage() { return themePackage; }
    public void setThemePackage(ThemePackage themePackage) { this.themePackage = themePackage; }

    // 本地字段的getter/setter
    public Boolean getIsDownloaded() { return isDownloaded; }
    public void setIsDownloaded(Boolean isDownloaded) { this.isDownloaded = isDownloaded; }

    public String getLocalFilePath() { return localFilePath; }
    public void setLocalFilePath(String localFilePath) { this.localFilePath = localFilePath; }

    public Boolean getIsCurrentTheme() { return isCurrentTheme; }
    public void setIsCurrentTheme(Boolean isCurrentTheme) { this.isCurrentTheme = isCurrentTheme; }

    public String getDownloadTime() { return downloadTime; }
    public void setDownloadTime(String downloadTime) { this.downloadTime = downloadTime; }

    // 内部类
    public static class PreviewImage {
        @SerializedName("id")
        private Long id;
        
        @SerializedName("themeId")
        private Long themeId;
        
        @SerializedName("title")
        private String title;
        
        @SerializedName("description")
        private String description;
        
        @SerializedName("imageUrl")
        private String imageUrl;
        
        @SerializedName("sortOrder")
        private Integer sortOrder;
        
        @SerializedName("imageType")
        private Integer imageType;

        // getter/setter省略...
    }

    public static class ThemePackage {
        @SerializedName("id")
        private Long id;
        
        @SerializedName("versionCode")
        private Integer versionCode;
        
        @SerializedName("versionName")
        private String versionName;
        
        @SerializedName("packagePath")
        private String packagePath;
        
        @SerializedName("downloadUrl")
        private String downloadUrl;
        
        @SerializedName("fileSize")
        private Long fileSize;
        
        @SerializedName("isLatest")
        private Integer isLatest;

        // getter/setter省略...
    }
}
