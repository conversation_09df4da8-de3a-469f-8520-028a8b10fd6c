package com.smartcar.easylauncher.data.model.theme;

import com.google.gson.annotations.SerializedName;

/**
 * 统一主题API响应包装类
 * 对应你提供的JSON响应结构
 */
public class UnifiedThemeResponse {
    
    @SerializedName("msg")
    private String msg;
    
    @SerializedName("code")
    private Integer code;
    
    @SerializedName("data")
    private UnifiedThemeModel data;

    public UnifiedThemeResponse() {}

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public UnifiedThemeModel getData() {
        return data;
    }

    public void setData(UnifiedThemeModel data) {
        this.data = data;
    }

    /**
     * 判断响应是否成功
     */
    public boolean isSuccess() {
        return code != null && code == 200;
    }
}
