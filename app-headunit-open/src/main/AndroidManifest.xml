<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-sdk tools:overrideLibrary="com.pgyersdk,com.zwh.easy.permissions,cody.bus.ipc,cody.bus.core" />

    <uses-feature
        android:name="android.hardware.usb.host"
        android:required="false" />

    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission
        android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />
    <!--阻止休眠-->
    <!--    <uses-permission android:name="android.permission.WAKE_LOCK" />-->
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.WRITE_SECURE_SETTINGS"
        tools:ignore="ProtectedPermissions" />

    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- 用于进行网络定位 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 获取网络状态 -->
    <uses-permission android:name="android.permission.INTERNET" /> <!-- 网络通信 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" /> <!-- 获取设备信息 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 获取MAC地址 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- 读写sdcard，storage等等 -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" /> <!-- 允许程序录制音频 -->
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.REQINTERNETUEST_INSTALL_PACKAGES" />
    <uses-permission
        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- 8.0 安装权限 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES" /> <!-- 卸载应用 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- WIFI操作 -->
    <uses-permission android:name="android.permission.SEND_SMS" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- 用于访问GPS定位 -->
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" /> <!-- 用于申请获取蓝牙信息进行室内定位 -->
    <uses-permission android:name="android.permission.NOTIFICATION_POLICY_ACCESS_GRANTED" />
    <uses-permission android:name="android.permission.MEDIA_CONTENT_CONTROL" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.INJECT_EVENTS" />
    <!-- 监听蓝牙状态消息 -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" /> <!-- 蓝牙操作 -->
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />

    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.BROADCAST_SCREEN_ON" />
    <uses-permission android:name="android.permission.BROADCAST_SCREEN_OFF" />
    <uses-permission
        android:name="android.permission.INSTALL_PACKAGES"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission
        android:name="android.permission.MANAGE_USB"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.FORCE_STOP_PACKAGES"
        tools:ignore="ProtectedPermissions" /> <!-- 关闭其他应用 -->
    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />

    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" /> <!-- 读写权限 -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <uses-permission android:name="huawei.android.permission.HW_SIGNATURE_OR_SYSTEM" />

    <uses-permission android:name="android.car.permission.CAR_CONTROL_AUDIO_VOLUME" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <application
        android:name=".app.App"
        android:allowBackup="true"
        android:clearTaskOnLaunch="true"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:launchMode="singleTask"
        android:networkSecurityConfig="@xml/network_security_config"
        android:stateNotNeeded="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:allowBackup">


        <!-- 崩溃展示（必须在独立进程） -->
        <activity
            android:name=".modules.other.CrashActivity"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:configChanges="screenSize|keyboardHidden"
            android:process=":crash" />

        <!-- 重启应用（必须在独立进程） -->
        <activity
            android:name=".modules.other.RestartActivity"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:process=":restart" />
        <activity
            android:name=".modules.touch.ui.activity.SmartFloatActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:configChanges="screenSize|keyboardHidden"
            android:theme="@style/theme_fullScreen" />

        <activity
            android:name=".modules.other.WebActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:configChanges="screenSize|keyboardHidden"
            android:theme="@style/theme_fullScreen" />
        <activity
            android:name=".modules.apport.FastTransferSettingActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:configChanges="screenSize|keyboardHidden"
            android:theme="@style/theme_fullScreen" />
        <activity
            android:name=".modules.apport.ChangePathActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:configChanges="screenSize|keyboardHidden"
            android:theme="@style/theme_fullScreen" />
        <activity
            android:name=".modules.apport.LogActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:configChanges="screenSize|keyboardHidden"
            android:theme="@style/theme_fullScreen" />
        <activity
            android:name=".modules.apport.LogListActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:configChanges="screenSize|keyboardHidden"
            android:theme="@style/theme_fullScreen" />
        <activity
            android:name=".modules.other.MockHome"
            android:enabled="false"
            android:exported="true"
            android:screenOrientation="behind"
            android:label="MockHome">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.HOME" />
            </intent-filter>
        </activity>
        <activity
            android:name=".home.main.HomeActivity"
            android:clearTaskOnLaunch="true"
            android:configChanges="uiMode|orientation|screenSize|keyboardHidden"
            android:enabled="true"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask"
            android:screenOrientation="user"
            android:taskAffinity=""
            android:windowSoftInputMode="adjustPan">
            <intent-filter android:priority="2">
                <action android:name="shinco.intent.action.LAUNCHER" />
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.HOME" />
                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.MONKEY" />
                <category android:name="android.intent.category.HOME_ONLY" />
            </intent-filter>
            <intent-filter>
                <action
                    android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"
                    android:directBootAware="true"
                    tools:targetApi="n" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="um.61b71120e014255fcbb0f6e4" />
            </intent-filter>

            <meta-data
                android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"
                android:directBootAware="true"
                android:resource="@xml/device_filter"
                tools:targetApi="n" /> <!-- USB弹窗适配 -->
        </activity>
        <activity
            android:name=".modules.gesture.other.FixAppSwitchDelay"
            android:autoRemoveFromRecents="true"
            android:excludeFromRecents="true"
            android:exported="true"
            android:screenOrientation="behind"
            android:configChanges="screenSize|keyboardHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name=".modules.gesture.other.GestureSettingActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:configChanges="screenSize|keyboardHidden">
            <!--            <intent-filter>-->
            <!--                <action android:name="android.intent.action.MAIN" />-->

            <!--                <category android:name="android.intent.category.DEFAULT" />-->
            <!--            </intent-filter>-->
        </activity>
        <activity
            android:name=".modules.gesture.other.AppSwitchActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="standard"
            android:screenOrientation="behind"
            android:configChanges="screenSize|keyboardHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <!--        <activity-->
        <!--            android:name=".gesture.other.StartActivity"-->
        <!--            android:enabled="true"-->
        <!--            android:excludeFromRecents="false"-->
        <!--            android:icon="@drawable/icon_tpms_you"-->
        <!--            android:label="@string/app_shoushi"-->
        <!--            android:launchMode="singleTask">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="android.intent.action.MAIN" />-->

        <!--                <category android:name="android.intent.category.LAUNCHER" />-->
        <!--                <category android:name="android.intent.category.DEFAULT" />-->
        <!--            </intent-filter>-->
        <!--        </activity>-->

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths"
                tools:replace="android:resource" /> <!-- 对应xml文件夹下的provider_paths.xml -->
        </provider>


        <meta-data
            android:name="PGYER_APPID"
            android:value="8ae1e627c5454393a049c92f12638834" />
        <meta-data
            android:name="design_width_in_dp"
            android:value="1000" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="600" />

        <service android:name="com.amap.api.location.APSService" />

        <receiver
            android:name=".modules.gesture.other.BatteryReceiver"
            android:enabled="true"
            android:exported="true" />


        <service
            android:name=".infrastructure.system.service.MusicControlService"
            android:exported="true"
            android:label="音乐推送服务"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE"
            tools:targetApi="18">
            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>
        </service>


        <service
            android:name=".modules.gesture.other.AccessibilityServiceGesture"
            android:exported="true"
            android:label="@string/app_name_long"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
            <intent-filter>
                <category android:name="android.intent.category.LAUNCHER" />

                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>

            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/keyevent_accessibility" />
        </service>
        <service
            android:name=".infrastructure.system.service.WebService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name=".infrastructure.system.service.WebService$GrayInnerService"
            android:enabled="true"
            android:exported="false" />
        <!--        <service-->
        <!--            android:name=".service.MediaControllerService"-->
        <!--            android:label="通知监控"-->
        <!--            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="android.service.notification.NotificationListenerService" />-->
        <!--            </intent-filter>-->
        <!--        </service>-->
        <!--        <service-->
        <!--            android:name=".service.TestMusicNotificationListenerService"-->
        <!--            android:enabled="@bool/remotecontrollerservice_enabled"-->
        <!--            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="android.service.notification.NotificationListenerService" />-->
        <!--            </intent-filter>-->
        <!--        </service>-->
        <!--        <service-->
        <!--            android:name=".service.MusicNotificationListenerService"-->
        <!--            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="android.service.notification.NotificationListenerService" />-->
        <!--            </intent-filter>-->
        <!--        </service>-->
        <receiver android:name=".infrastructure.system.receiver.BootBroadcastReceiver">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>
        <!-- 开机启动 -->
        <!--        <receiver android:name=".touch.receiver.BootCompletedReceiver">-->
        <!--            <intent-filter android:priority="1000">-->
        <!--                <action android:name="android.intent.action.BOOT_COMPLETED" />-->
        <!--            </intent-filter>-->
        <!--        </receiver>-->
        <!-- 全局Context提供者 -->
        <provider
            android:name="com.smartcar.easylauncher.modules.touch.provider.ApplicationContextProvider"
            android:authorities="${applicationId}.contextprovider"
            android:exported="false" />
        <!-- 当前Activity、返回键、Home键辅助服务 -->
        <service
            android:name=".modules.touch.service.CoreAccessibilityService"
            android:exported="true"
            android:label="@string/app_name_float"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
            <intent-filter>
                <category android:name="android.intent.category.LAUNCHER" />

                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>

            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/float_action_accessibility_service" />
        </service>

        <service android:name=".modules.touch.service.FloatWindowService" />

        <receiver
            android:name=".data.network.receiver.SystemStateReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <!-- 网络状态变化 -->
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <!-- WiFi状态变化 -->
                <action android:name="android.net.wifi.WIFI_STATE_CHANGED" />
                <action android:name="android.net.wifi.NETWORK_STATE_CHANGED" />
                <action android:name="android.net.wifi.RSSI_CHANGED" />
                <!-- 蓝牙状态变化 -->
                <action android:name="android.bluetooth.adapter.action.STATE_CHANGED" />
                <action android:name="android.bluetooth.device.action.ACL_CONNECTED" />
                <action android:name="android.bluetooth.device.action.ACL_DISCONNECTED" />
                <!-- 信号强度变化 -->
                <action android:name="android.intent.action.SIG_STR" />
            </intent-filter>
        </receiver>

        <activity
            android:name=".data.network.test.SystemStateTest"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:configChanges="screenSize|keyboardHidden"
            android:theme="@style/theme_fullScreen" />

    </application>

</manifest>