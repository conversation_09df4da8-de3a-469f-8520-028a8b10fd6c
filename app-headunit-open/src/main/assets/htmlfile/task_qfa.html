<head>
    <title>任务帮助</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style type="text/css">
        /* GitHub stylesheet for MarkdownPad (http://markdownpad.com) */
        /* Author: <PERSON> - http://nicolashery.com */
        /* Version: b13fe65ca28d2e568c6ed5d7f06581183df8f2ff */
        /* Source: https://github.com/nicolahery/markdownpad-github */
        /* RESET
=============================================================================*/
        
        html,
        body,
        div,
        span,
        applet,
        object,
        iframe,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p,
        blockquote,
        pre,
        a,
        abbr,
        acronym,
        address,
        big,
        cite,
        code,
        del,
        dfn,
        em,
        img,
        ins,
        kbd,
        q,
        s,
        samp,
        small,
        strike,
        strong,
        sub,
        sup,
        tt,
        var,
        b,
        u,
        i,
        center,
        dl,
        dt,
        dd,
        ol,
        ul,
        li,
        fieldset,
        form,
        label,
        legend,
        table,
        caption,
        tbody,
        tfoot,
        thead,
        tr,
        th,
        td,
        article,
        aside,
        canvas,
        details,
        embed,
        figure,
        figcaption,
        footer,
        header,
        hgroup,
        menu,
        nav,
        output,
        ruby,
        section,
        summary,
        time,
        mark,
        audio,
        video {
            margin: 0;
            padding: 0;
            border: 0;
        }
        /* BODY
=============================================================================*/
        
        body {
            font-family: Helvetica, arial, freesans, clean, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background-color: #fff;
            padding: 20px;
            max-width: 960px;
            margin: 0 auto;
        }
        
        body>*:first-child {
            margin-top: 0 !important;
        }
        
        body>*:last-child {
            margin-bottom: 0 !important;
        }
        /* BLOCKS
=============================================================================*/
        
        p,
        blockquote,
        ul,
        ol,
        dl,
        table,
        pre {
            margin: 15px 0;
        }
        /* HEADERS
=============================================================================*/
        
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin: 20px 0 10px;
            padding: 0;
            font-weight: bold;
            -webkit-font-smoothing: antialiased;
        }
        
        h1 tt,
        h1 code,
        h2 tt,
        h2 code,
        h3 tt,
        h3 code,
        h4 tt,
        h4 code,
        h5 tt,
        h5 code,
        h6 tt,
        h6 code {
            font-size: inherit;
        }
        
        h1 {
            font-size: 28px;
            color: #000;
        }
        
        h2 {
            font-size: 24px;
            border-bottom: 1px solid #ccc;
            color: #000;
        }
        
        h3 {
            font-size: 18px;
        }
        
        h4 {
            text-align: center;
            font-size: 16px;
        }
        
        h5 {
            font-size: 14px;
        }
        
        h6 {
            color: #777;
            font-size: 14px;
        }
        
        body>h2:first-child,
        body>h1:first-child,
        body>h1:first-child+h2,
        body>h3:first-child,
        body>h4:first-child,
        body>h5:first-child,
        body>h6:first-child {
            margin-top: 0;
            padding-top: 0;
        }
        
        a:first-child h1,
        a:first-child h2,
        a:first-child h3,
        a:first-child h4,
        a:first-child h5,
        a:first-child h6 {
            margin-top: 0;
            padding-top: 0;
        }
        
        h1+p,
        h2+p,
        h3+p,
        h4+p,
        h5+p,
        h6+p {
            margin-top: 10px;
        }
        /* LINKS
=============================================================================*/
        
        a {
            color: #4183C4;
            text-decoration: none;
        }
        
        a:hover {
            text-decoration: underline;
        }
        /* LISTS
=============================================================================*/
        
        ul,
        ol {
            padding-left: 30px;
        }
        
        ul li> :first-child,
        ol li> :first-child,
        ul li ul:first-of-type,
        ol li ol:first-of-type,
        ul li ol:first-of-type,
        ol li ul:first-of-type {
            margin-top: 0px;
        }
        
        ul ul,
        ul ol,
        ol ol,
        ol ul {
            margin-bottom: 0;
        }
        
        dl {
            padding: 0;
        }
        
        dl dt {
            font-size: 14px;
            font-weight: bold;
            font-style: italic;
            padding: 0;
            margin: 15px 0 5px;
        }
        
        dl dt:first-child {
            padding: 0;
        }
        
        dl dt>:first-child {
            margin-top: 0px;
        }
        
        dl dt>:last-child {
            margin-bottom: 0px;
        }
        
        dl dd {
            margin: 0 0 15px;
            padding: 0 15px;
        }
        
        dl dd>:first-child {
            margin-top: 0px;
        }
        
        dl dd>:last-child {
            margin-bottom: 0px;
        }
        /* CODE
=============================================================================*/
        
        pre,
        code,
        tt {
            font-size: 12px;
            font-family: Consolas, "Liberation Mono", Courier, monospace;
        }
        
        code,
        tt {
            margin: 0 0px;
            padding: 0px 0px;
            white-space: nowrap;
            border: 1px solid #eaeaea;
            background-color: #f8f8f8;
            border-radius: 3px;
        }
        
        pre>code {
            margin: 0;
            padding: 0;
            white-space: pre;
            border: none;
            background: transparent;
        }
        
        pre {
            background-color: #f8f8f8;
            border: 1px solid #ccc;
            font-size: 13px;
            line-height: 19px;
            overflow: auto;
            padding: 6px 10px;
            border-radius: 3px;
        }
        
        pre code,
        pre tt {
            background-color: transparent;
            border: none;
        }
        
        kbd {
            -moz-border-bottom-colors: none;
            -moz-border-left-colors: none;
            -moz-border-right-colors: none;
            -moz-border-top-colors: none;
            background-color: #DDDDDD;
            background-image: linear-gradient(#F1F1F1, #DDDDDD);
            background-repeat: repeat-x;
            border-color: #DDDDDD #CCCCCC #CCCCCC #DDDDDD;
            border-image: none;
            border-radius: 2px 2px 2px 2px;
            border-style: solid;
            border-width: 1px;
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
            line-height: 10px;
            padding: 1px 4px;
        }
        /* QUOTES
=============================================================================*/
        
        blockquote {
            border-left: 4px solid #DDD;
            padding: 0 15px;
            color: #777;
        }
        
        blockquote>:first-child {
            margin-top: 0px;
        }
        
        blockquote>:last-child {
            margin-bottom: 0px;
        }
        /* HORIZONTAL RULES
=============================================================================*/
        
        hr {
            clear: both;
            margin: 15px 0;
            height: 0px;
            overflow: hidden;
            border: none;
            background: transparent;
            border-bottom: 4px solid #ddd;
            padding: 0;
        }
        /* TABLES
=============================================================================*/
        
        table th {
            font-weight: bold;
        }
        
        table th,
        table td {
            border: 1px solid #ccc;
            padding: 6px 13px;
        }
        
        table tr {
            border-top: 1px solid #ccc;
            background-color: #fff;
        }
        
        table tr:nth-child(2n) {
            background-color: #f8f8f8;
        }
        /* IMAGES
=============================================================================*/
        
        img {
            max-width: 100%
        }
    </style>
</head>
<h4 id="faq">智能任务使用帮助</h4>
<blockquote>
    <p>怎么用？如何设置智能任务？</p>
</blockquote>
<ul>
    <li>任务遵循『条件』——『结果』执行模式</li>
    <li>比如条件为"智车启动"，结果为"延时4秒"-"打开高德"，那就会智车启动后等4秒，就会自动打开高德</li>
</ul>
<blockquote>
    <p>打开软件为什么没有返回智车桌面</p>
</blockquote>
<ul>
    <li>因为你配置的结果没有添加返回"主页"的结果</li>
</ul>
<blockquote>
    <p>为什么设置开机结果没有生效</p>
</blockquote>
<ul>
    <li>检查你设备没有有自启动管理，是否打开了电池优化等</li>
    <li>手机设备因为厂商有不同的限制自启动，并且不是车机设备，请自行研究</li>
</ul>
<blockquote>
    <p>为什么设置加减音量没有反应</p>
</blockquote>
<ul>
    <li>因为你的车机系统屏蔽了Android标准音量API接口</li>
</ul>
<blockquote>
    <p>为什么任务打开软件没有反应</p>
</blockquote>
<ul>
    <li>Android10及以上的版本请打开应用上层显示和悬浮窗权限</li>
</ul>
<blockquote>
    <p>怎么删除某个任务</p>
</blockquote>
<ul>
    <li>任务列表长按后选择删除</li>
</ul>
<blockquote>
    <p>为什么任务一直重复的执行</p>
</blockquote>
<ul>
    <li>任务的执行模式改为单次执行，每一次启动符合条件只会执行一次</li>
</ul>