<head>
    <title>任务帮助</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style type="text/css">
        /* GitHub stylesheet for MarkdownPad (http://markdownpad.com) */
        /* Author: <PERSON> - http://nicolashery.com */
        /* Version: b13fe65ca28d2e568c6ed5d7f06581183df8f2ff */
        /* Source: https://github.com/nicolahery/markdownpad-github */
        /* RESET
=============================================================================*/
        
        html,
        body,
        div,
        span,
        applet,
        object,
        iframe,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p,
        blockquote,
        pre,
        a,
        abbr,
        acronym,
        address,
        big,
        cite,
        code,
        del,
        dfn,
        em,
        img,
        ins,
        kbd,
        q,
        s,
        samp,
        small,
        strike,
        strong,
        sub,
        sup,
        tt,
        var,
        b,
        u,
        i,
        center,
        dl,
        dt,
        dd,
        ol,
        ul,
        li,
        fieldset,
        form,
        label,
        legend,
        table,
        caption,
        tbody,
        tfoot,
        thead,
        tr,
        th,
        td,
        article,
        aside,
        canvas,
        details,
        embed,
        figure,
        figcaption,
        footer,
        header,
        hgroup,
        menu,
        nav,
        output,
        ruby,
        section,
        summary,
        time,
        mark,
        audio,
        video {
            margin: 0;
            padding: 0;
            border: 0;
        }
        /* BODY
=============================================================================*/
        
        body {
            font-family: Helvetica, arial, freesans, clean, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background-color: #fff;
            padding: 20px;
            max-width: 960px;
            margin: 0 auto;
        }
        
        body>*:first-child {
            margin-top: 0 !important;
        }
        
        body>*:last-child {
            margin-bottom: 0 !important;
        }
        /* BLOCKS
=============================================================================*/
        
        p,
        blockquote,
        ul,
        ol,
        dl,
        table,
        pre {
            margin: 15px 0;
        }
        /* HEADERS
=============================================================================*/
        
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin: 20px 0 10px;
            padding: 0;
            font-weight: bold;
            -webkit-font-smoothing: antialiased;
        }
        
        h1 tt,
        h1 code,
        h2 tt,
        h2 code,
        h3 tt,
        h3 code,
        h4 tt,
        h4 code,
        h5 tt,
        h5 code,
        h6 tt,
        h6 code {
            font-size: inherit;
        }
        
        h1 {
            font-size: 28px;
            color: #000;
        }
        
        h2 {
            font-size: 24px;
            border-bottom: 1px solid #ccc;
            color: #000;
        }
        
        h3 {
            font-size: 18px;
        }
        
        h4 {
            text-align: center;
            font-size: 16px;
        }
        
        h5 {
            font-size: 14px;
        }
        
        h6 {
            color: #777;
            font-size: 14px;
        }
        
        body>h2:first-child,
        body>h1:first-child,
        body>h1:first-child+h2,
        body>h3:first-child,
        body>h4:first-child,
        body>h5:first-child,
        body>h6:first-child {
            margin-top: 0;
            padding-top: 0;
        }
        
        a:first-child h1,
        a:first-child h2,
        a:first-child h3,
        a:first-child h4,
        a:first-child h5,
        a:first-child h6 {
            margin-top: 0;
            padding-top: 0;
        }
        
        h1+p,
        h2+p,
        h3+p,
        h4+p,
        h5+p,
        h6+p {
            margin-top: 10px;
        }
        /* LINKS
=============================================================================*/
        
        a {
            color: #4183C4;
            text-decoration: none;
        }
        
        a:hover {
            text-decoration: underline;
        }
        /* LISTS
=============================================================================*/
        
        ul,
        ol {
            padding-left: 30px;
        }
        
        ul li> :first-child,
        ol li> :first-child,
        ul li ul:first-of-type,
        ol li ol:first-of-type,
        ul li ol:first-of-type,
        ol li ul:first-of-type {
            margin-top: 0px;
        }
        
        ul ul,
        ul ol,
        ol ol,
        ol ul {
            margin-bottom: 0;
        }
        
        dl {
            padding: 0;
        }
        
        dl dt {
            font-size: 14px;
            font-weight: bold;
            font-style: italic;
            padding: 0;
            margin: 15px 0 5px;
        }
        
        dl dt:first-child {
            padding: 0;
        }
        
        dl dt>:first-child {
            margin-top: 0px;
        }
        
        dl dt>:last-child {
            margin-bottom: 0px;
        }
        
        dl dd {
            margin: 0 0 15px;
            padding: 0 15px;
        }
        
        dl dd>:first-child {
            margin-top: 0px;
        }
        
        dl dd>:last-child {
            margin-bottom: 0px;
        }
        /* CODE
=============================================================================*/
        
        pre,
        code,
        tt {
            font-size: 12px;
            font-family: Consolas, "Liberation Mono", Courier, monospace;
        }
        
        code,
        tt {
            margin: 0 0px;
            padding: 0px 0px;
            white-space: nowrap;
            border: 1px solid #eaeaea;
            background-color: #f8f8f8;
            border-radius: 3px;
        }
        
        pre>code {
            margin: 0;
            padding: 0;
            white-space: pre;
            border: none;
            background: transparent;
        }
        
        pre {
            background-color: #f8f8f8;
            border: 1px solid #ccc;
            font-size: 13px;
            line-height: 19px;
            overflow: auto;
            padding: 6px 10px;
            border-radius: 3px;
        }
        
        pre code,
        pre tt {
            background-color: transparent;
            border: none;
        }
        
        kbd {
            -moz-border-bottom-colors: none;
            -moz-border-left-colors: none;
            -moz-border-right-colors: none;
            -moz-border-top-colors: none;
            background-color: #DDDDDD;
            background-image: linear-gradient(#F1F1F1, #DDDDDD);
            background-repeat: repeat-x;
            border-color: #DDDDDD #CCCCCC #CCCCCC #DDDDDD;
            border-image: none;
            border-radius: 2px 2px 2px 2px;
            border-style: solid;
            border-width: 1px;
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
            line-height: 10px;
            padding: 1px 4px;
        }
        /* QUOTES
=============================================================================*/
        
        blockquote {
            border-left: 4px solid #DDD;
            padding: 0 15px;
            color: #777;
        }
        
        blockquote>:first-child {
            margin-top: 0px;
        }
        
        blockquote>:last-child {
            margin-bottom: 0px;
        }
        /* HORIZONTAL RULES
=============================================================================*/
        
        hr {
            clear: both;
            margin: 15px 0;
            height: 0px;
            overflow: hidden;
            border: none;
            background: transparent;
            border-bottom: 4px solid #ddd;
            padding: 0;
        }
        /* TABLES
=============================================================================*/
        
        table th {
            font-weight: bold;
        }
        
        table th,
        table td {
            border: 1px solid #ccc;
            padding: 6px 13px;
        }
        
        table tr {
            border-top: 1px solid #ccc;
            background-color: #fff;
        }
        
        table tr:nth-child(2n) {
            background-color: #f8f8f8;
        }
        /* IMAGES
=============================================================================*/
        
        img {
            max-width: 100%
        }
    </style>
</head>
<h4 id="faq">主题使用帮助</h4>
<blockquote>
    <p>怎么使用主题？</p>
</blockquote>
<ul>
    <li>下载主题，使用后，智车的主题模式会自动变更到使用的主题类型</li>
</ul>
<blockquote>
    <p>为什么下载主题没有反应</p>
</blockquote>
<ul>
    <li>检查车机网络</li>
    <li>联系管理员，服务器可能出现问题</li>
</ul>
<blockquote>
    <p>可以自己做主题吗？</p>
</blockquote>
<ul>
    <li>可以，主题群有主题开发规范，修改完可以直接闪传使用测试</li>
</ul>
<blockquote>
    <p>主题怎么修改</p>
</blockquote>
<ul>
    <li>首页要有一定的作图能力，按照文档规范设计不同的图片</li>
    <li>使用Android studio打开主题底包的示例工程，替换里面的素材</li>
    <li>或者使用MT等工具，修改主题示例底包来修改素材</li>
</ul>
<blockquote>
    <p>主题开发规范手册在哪里</p>
</blockquote>
<ul>
    <li>主题开发群公告有文档地址</li>
</ul>
<blockquote>
    <p>主题是否支持有偿定制</p>
</blockquote>
<ul>
    <li>支持定制，可以联系主题开发者进行私人定制，或者车型定制</li>
</ul>