<head>
    <title>激活帮助</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <style type="text/css">
/* GitHub stylesheet for MarkdownPad (http://markdownpad.com) */
/* Author: <PERSON> - http://nicolashery.com */
/* Version: b13fe65ca28d2e568c6ed5d7f06581183df8f2ff */
/* Source: https://github.com/nicolahery/markdownpad-github */

/* RESET
=============================================================================*/

html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
}

/* BODY
=============================================================================*/

body {
  font-family: Helvetica, arial, freesans, clean, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  background-color: #fff;
  padding: 20px;
  max-width: 960px;
  margin: 0 auto;
}

body>*:first-child {
  margin-top: 0 !important;
}

body>*:last-child {
  margin-bottom: 0 !important;
}

/* BLOCKS
=============================================================================*/

p, blockquote, ul, ol, dl, table, pre {
  margin: 15px 0;
}

/* HEADERS
=============================================================================*/

h1, h2, h3, h4, h5, h6 {
  margin: 20px 0 10px;
  padding: 0;
  font-weight: bold;
  -webkit-font-smoothing: antialiased;
}

h1 tt, h1 code, h2 tt, h2 code, h3 tt, h3 code, h4 tt, h4 code, h5 tt, h5 code, h6 tt, h6 code {
  font-size: inherit;
}

h1 {
  font-size: 28px;
  color: #000;
}

h2 {
  font-size: 24px;
  border-bottom: 1px solid #ccc;
  color: #000;
}

h3 {
  font-size: 18px;
}

h4 {
  font-size: 16px;
}

h5 {
  font-size: 14px;
}

h6 {
  color: #777;
  font-size: 14px;
}

body>h2:first-child, body>h1:first-child, body>h1:first-child+h2, body>h3:first-child, body>h4:first-child, body>h5:first-child, body>h6:first-child {
  margin-top: 0;
  padding-top: 0;
}

a:first-child h1, a:first-child h2, a:first-child h3, a:first-child h4, a:first-child h5, a:first-child h6 {
  margin-top: 0;
  padding-top: 0;
}

h1+p, h2+p, h3+p, h4+p, h5+p, h6+p {
  margin-top: 10px;
}

/* LINKS
=============================================================================*/

a {
  color: #4183C4;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* LISTS
=============================================================================*/

ul, ol {
  padding-left: 30px;
}

ul li > :first-child,
ol li > :first-child,
ul li ul:first-of-type,
ol li ol:first-of-type,
ul li ol:first-of-type,
ol li ul:first-of-type {
  margin-top: 0px;
}

ul ul, ul ol, ol ol, ol ul {
  margin-bottom: 0;
}

dl {
  padding: 0;
}

dl dt {
  font-size: 14px;
  font-weight: bold;
  font-style: italic;
  padding: 0;
  margin: 15px 0 5px;
}

dl dt:first-child {
  padding: 0;
}

dl dt>:first-child {
  margin-top: 0px;
}

dl dt>:last-child {
  margin-bottom: 0px;
}

dl dd {
  margin: 0 0 15px;
  padding: 0 15px;
}

dl dd>:first-child {
  margin-top: 0px;
}

dl dd>:last-child {
  margin-bottom: 0px;
}

/* CODE
=============================================================================*/

pre, code, tt {
  font-size: 12px;
  font-family: Consolas, "Liberation Mono", Courier, monospace;
}

code, tt {
  margin: 0 0px;
  padding: 0px 0px;
  white-space: nowrap;
  border: 1px solid #eaeaea;
  background-color: #f8f8f8;
  border-radius: 3px;
}

pre>code {
  margin: 0;
  padding: 0;
  white-space: pre;
  border: none;
  background: transparent;
}

pre {
  background-color: #f8f8f8;
  border: 1px solid #ccc;
  font-size: 13px;
  line-height: 19px;
  overflow: auto;
  padding: 6px 10px;
  border-radius: 3px;
}

pre code, pre tt {
  background-color: transparent;
  border: none;
}

kbd {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    background-color: #DDDDDD;
    background-image: linear-gradient(#F1F1F1, #DDDDDD);
    background-repeat: repeat-x;
    border-color: #DDDDDD #CCCCCC #CCCCCC #DDDDDD;
    border-image: none;
    border-radius: 2px 2px 2px 2px;
    border-style: solid;
    border-width: 1px;
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    line-height: 10px;
    padding: 1px 4px;
}

/* QUOTES
=============================================================================*/

blockquote {
  border-left: 4px solid #DDD;
  padding: 0 15px;
  color: #777;
}

blockquote>:first-child {
  margin-top: 0px;
}

blockquote>:last-child {
  margin-bottom: 0px;
}

/* HORIZONTAL RULES
=============================================================================*/

hr {
  clear: both;
  margin: 15px 0;
  height: 0px;
  overflow: hidden;
  border: none;
  background: transparent;
  border-bottom: 4px solid #ddd;
  padding: 0;
}

/* TABLES
=============================================================================*/

table th {
  font-weight: bold;
}

table th, table td {
  border: 1px solid #ccc;
  padding: 6px 13px;
}

table tr {
  border-top: 1px solid #ccc;
  background-color: #fff;
}

table tr:nth-child(2n) {
  background-color: #f8f8f8;
}

/* IMAGES
=============================================================================*/

img {
  max-width: 100%
}



    </style>
</head>
<h2 id="准备工作">准备工作</h2>
<ul>
    <li>一台电脑</li>
    <li>一条数据线</li>
    <li>以下是教程</li>
</ul>
<h3 id="1-激活开发者设置">1. 激活开发者设置</h3>
<ul>
    <li>在系统设置里连续点击系统版本（软件版本，不是Android版本），直到提示开发者模式已激活</li>
    <li>在系统设置里找到开发者选项，并勾选【USB调试】</li>
</ul>

<h3 id="2-获得电脑上的adb工具">2. 获得电脑上的ADB工具</h3>
<blockquote>
    <p>此处以Windows为例，但不仅限于Windows</p>
</blockquote>
<ul>
    <li>首先，你得下载<code>platform-tools</code>（如果你有其他手机助手软件附带ADB工具，也可以直接使用）</li>
    <li>下载<a>platform-tools</a>，<code>并解压到电脑上</code></li>
    <li>双点击解压目录下的 <code>打开CMD命令行.bat</code> 打开ADB命令窗口</li>
</ul>

<h3 id="3-执行命令">3. 执行命令</h3>
<ul>
    <li><p>点击<strong>智车手势</strong>增强模式右侧的机器人小图标，出现激活提示弹窗后，插上数据线连接电脑</p>
    </li>
    <li><p>复制激活命令</p>
        <pre><code class="language-sh">adb -d shell sh /storage/emulated/0/Android/data/com.smartcar.easylauncher/cache/up.sh</code></pre>
    </li>
    <li><p>并在命令行窗口中点击鼠标右键粘贴代码，并按下回车键</p>
        <ul>
            <li>如果你的手机是首次和电脑连接，会出现授权提示，此时需要你在手机上点击确认</li>
            <li>确认完之后，你可能需要重复刚刚的动作</li>
            <li>如果你小心误操作错过了手机上弹出的授权窗口。你是新手的话，建议重启手机和电脑从头再来一次。经验丰富的老鸟随意。</li>
        </ul>
    </li>
    <li><p>总之，如果激命令执行成功后，手势主页的机器人图标会变成绿色</p>
    </li>
</ul>
<h3 id="4-疑难问题">4. 疑难问题</h3>
<ul>
    <li>拔掉数据线增强模式就失效了？<blockquote>
        <p>这的确是个难以解决的问题，在Oreo+系统上极容易出现。<br />
            可以尝试，手机插上电脑后，把连接模式改成“仅充电”，再执行代码。</p>
    </blockquote>
    </li>
</ul>
<h3 id="其它">其它</h3>
<ul>
    <li>如果我说的不是很清楚，也没有关系，问问你身边有没有高手</li>
    <li>让他们帮帮你也是很简单的</li>
</ul>
