const BASE_URL = ''; // 如果需要可以设置为具体的基础路径

$(function() {
    // 获取当前页面的基础路径
    const currentPath = window.location.pathname;
    const basePath = currentPath.substring(0, currentPath.lastIndexOf('/') + 1);
    
    // 初始化上传区域
    initUploadArea();
    // 初始化文件列表
    refreshFileList();
});

function initUploadArea() {
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('fileInput');
    
    // 点击上传区域触发文件选择
    dropZone.addEventListener('click', () => {
        fileInput.click();
    });

    // 处理文件选择
    fileInput.addEventListener('change', (e) => {
        handleFiles(e.target.files);
    });

    // 处理拖拽
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        e.stopPropagation();
        dropZone.classList.add('dragover');
    });

    dropZone.addEventListener('dragleave', (e) => {
        e.preventDefault();
        e.stopPropagation();
        dropZone.classList.remove('dragover');
    });

    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        e.stopPropagation();
        dropZone.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        handleFiles(files);
    });
}

function handleFiles(files) {
    if (!files || files.length === 0) return;

    // 检查文件数量限制
    if (files.length > 10) {
        showToast('一次最多只能上传10个文件');
        return;
    }

    // 创建上传进度条
    const progressContainer = document.getElementById('uploadProgress');
    const progressCard = document.querySelector('.upload-progress-card');
    progressContainer.innerHTML = '';

    // 如果是移动端，显示进度卡片
    if (isMobileDevice()) {
        progressCard.classList.add('show');
    }

    Array.from(files).forEach((file, index) => {
        const progressBar = createProgressBar(file.name);
        progressContainer.appendChild(progressBar);
        
        uploadFile(file, progressBar);
    });
}

function createProgressBar(fileName) {
    const progressWrapper = document.createElement('div');
    progressWrapper.className = 'progress-wrapper';
    progressWrapper.style.opacity = '0';
    
    progressWrapper.innerHTML = `
        <div class="progress-info">
            <span class="filename" title="${fileName}">${fileName}</span>
            <span class="percent">0%</span>
        </div>
        <div class="progress-bar">
            <div class="progress" style="width: 0%"></div>
        </div>
    `;
    
    // 淡入动画
    setTimeout(() => {
        progressWrapper.style.opacity = '1';
    }, 50);
    
    return progressWrapper;
}

function uploadFile(file, progressBar) {
    const formData = new FormData();
    
    // 使用 Blob 来确保文件名编码正确
    const blobFile = new Blob([file], { type: file.type });
    const fileName = file.name;
    
    // 对文件名进行 Base64 编码
    const base64FileName = btoa(unescape(encodeURIComponent(fileName)));
    
    // 创建新的 File 对象，使用编码后的文件名
    formData.append('file', blobFile, encodeURIComponent(fileName));
    
    $.ajax({
        url: 'files',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-Original-Name': base64FileName,
            'X-File-Encoding': 'base64'
        },
        xhr: function() {
            const xhr = new XMLHttpRequest();
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percent = Math.round((e.loaded * 100) / e.total);
                    updateProgress(progressBar, percent);
                }
            });
            return xhr;
        },
        success: function(response) {
            updateProgress(progressBar, 100);
            refreshFileList();
            showToast('上传成功');
        },
        error: function(xhr, status, error) {
            console.error('Upload failed:', {
                status: xhr.status,
                statusText: xhr.statusText,
                error: error
            });
            handleUploadError(progressBar, error);
            showToast(`上传失败：${error}`);
        }
    });
}

// 添加进度查询函数
function checkProgress(fileName) {
    $.ajax({
        url: `progress/${encodeURIComponent(fileName)}`,
        type: 'GET',
        success: function(response) {
            try {
                const progress = JSON.parse(response);
                console.log('Upload progress:', progress);
            } catch (e) {
                console.error('Parse progress failed:', e);
            }
        }
    });
}

function updateProgress(progressBar, percent) {
    const progressElement = progressBar.querySelector('.progress');
    const percentElement = progressBar.querySelector('.percent');
    const progressCard = document.querySelector('.upload-progress-card');
    
    // 平滑动画过渡
    progressElement.style.width = percent + '%';
    percentElement.textContent = percent + '%';
    
    // 更新状态
    if (percent === 100) {
        progressBar.classList.add('success');
        setTimeout(() => {
            // 淡出动画
            progressBar.style.opacity = '0';
            setTimeout(() => {
                progressBar.remove();
                
                // 检查是否还有其他正在上传的文件
                const remainingBars = document.querySelectorAll('.progress-wrapper').length;
                if (remainingBars === 0 && isMobileDevice()) {
                    // 如果没有正在上传的文件，延迟隐藏进度卡片
                    progressCard.classList.add('hide');
                    setTimeout(() => {
                        progressCard.classList.remove('show', 'hide');
                    }, 300);
                }
            }, 300);
        }, 1000);
    }
}

function refreshFileList(sortBy = '') {
    const url = sortBy ? `files?sort=${sortBy}` : 'files';
    console.log('Refresh URL:', url);

    return $.ajax({
        url: url,
        type: 'GET',
        success: function(response) {
            try {
                console.log('Raw response:', response);
                const files = typeof response === 'string' ? JSON.parse(response) : response;
                console.log('Parsed files:', files);
                console.log('Files count:', files.length);
                renderFileList(files);
                if (sortBy) {
                    showToast('排序完成');
                }
            } catch (error) {
                console.error('Refresh error:', error);
                console.error('Response that caused error:', response);
                showToast('获取文件列表失败：数据格式错误');
            }
        },
        error: function(xhr, status, error) {
            console.error('Refresh failed:', {
                status: xhr.status,
                statusText: xhr.statusText,
                error: error,
                response: xhr.responseText
            });
            showToast('获取文件列表失败：' + error);
        }
    });
}

// 添加设备检测函数
function isMobileDevice() {
    return window.innerWidth <= 768;
}

function renderFileList(files) {
    const fileListContainer = document.getElementById('fileList');
    const fileCount = document.getElementById('fileCount');
    
    if (!files || files.length === 0) {
        fileListContainer.innerHTML = `
            <div class="empty-state">
                <i class="material-icons">folder_open</i>
                <p>还没有上传任何文件</p>
                <span>拖拽文件到这里或点击上传按钮</span>
            </div>
        `;
        fileCount.textContent = '0';
        return;
    }
    
    fileCount.textContent = files.length;
    fileListContainer.innerHTML = '';

    if (!Array.isArray(files)) {
        console.error('Files is not an array:', files);
        return;
    }

    files.forEach(file => {
        const fileElement = document.createElement('div');
        fileElement.className = 'file-item';
        
        const { icon, type } = getFileIconAndType(file.name);
        
        // 根据设备类型使用不同的模板
        if (isMobileDevice()) {
            fileElement.innerHTML = `
                <div class="file-icon ${type}">
                    <i class="material-icons">${icon}</i>
                </div>
                <div class="file-content">
                    <span class="file-name">${file.name || ''}</span>
                    <span class="file-time">${file.time || ''}</span>
                    <div class="file-bottom">
                        <span class="file-size">${file.size || ''}</span>
                        <div class="actions">
                            <button class="action-btn download-btn" onclick="downloadFile('${file.name || ''}')">
                                <i class="material-icons">download</i>
                                <span>下载</span>
                            </button>
                            <button class="action-btn delete-btn" onclick="deleteFile('${file.name || ''}')">
                                <i class="material-icons">delete</i>
                                <span>删除</span>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        } else {
            // 保持电脑端模板不变
            fileElement.innerHTML = `
                <div class="file-icon ${type}">
                    <i class="material-icons">${icon}</i>
                </div>
                <div class="file-content">
                    <div class="file-info-top">
                        <div class="file-name-wrapper">
                            <span class="file-name">${file.name || ''}</span>
                        </div>
                        <span class="file-size">${file.size || ''}</span>
                    </div>
                    <div class="file-info-bottom">
                        <span class="file-time">${file.time || ''}</span>
                        <div class="actions">
                            <button class="action-btn download-btn" onclick="downloadFile('${file.name || ''}')">
                                <i class="material-icons">download</i>
                                <span>下载</span>
                            </button>
                            <button class="action-btn delete-btn" onclick="deleteFile('${file.name || ''}')">
                                <i class="material-icons">delete</i>
                                <span>删除</span>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }
        
        fileListContainer.appendChild(fileElement);
    });
}

// 根据文件类型返回对应的图标和类型
function getFileIconAndType(filename) {
    // 处理类似 .1、.2 这样的后缀
    let ext = filename.split('.').pop().toLowerCase();
    if (/^\d+$/.test(ext)) {
        // 如果最后的扩展名是纯数字，获取真实的文件类型
        const parts = filename.split('.');
        if (parts.length > 2) {
            ext = parts[parts.length - 2].toLowerCase();
        }
    }
    
    // 图片文件
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg', 'ico', 'heic', 'raw', 'tiff'].includes(ext)) {
        return {
            icon: 'image',
            type: 'type-image'
        };
    }
    
    // 视频文件
    if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', '3gp', 'webm', 'm4v', 'rmvb', 'ts'].includes(ext)) {
        return {
            icon: 'movie',
            type: 'type-video'
        };
    }
    
    // 音频文件
    if (['mp3', 'wav', 'ogg', 'aac', 'm4a', 'flac', 'ape', 'wma', 'opus'].includes(ext)) {
        return {
            icon: 'audio_file',
            type: 'type-audio'
        };
    }
    
    // 文档文件
    if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'csv', 'md'].includes(ext)) {
        return {
            icon: ext.startsWith('doc') ? 'description' :
                  ext.startsWith('xls') ? 'table_chart' :
                  ext.startsWith('ppt') ? 'slideshow' :
                  'article',
            type: 'type-document'
        };
    }
    
    // PDF文件
    if (ext === 'pdf') {
        return {
            icon: 'picture_as_pdf',
            type: 'type-pdf'
        };
    }
    
    // 压缩文件
    if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'iso'].includes(ext)) {
        return {
            icon: 'folder_zip',
            type: 'type-archive'
        };
    }
    
    // 代码和配置文件
    if (['js', 'css', 'html', 'java', 'py', 'php', 'json', 'xml', 'yml', 'yaml', 'ini', 'conf', 'sh', 'bat'].includes(ext)) {
        return {
            icon: 'code',
            type: 'type-code'
        };
    }

    // 安装包文件
    if (['apk', 'ipa', 'exe', 'msi', 'deb', 'rpm'].includes(ext)) {
        return {
            icon: 'android',
            type: 'type-app'
        };
    }

    // 字体文件
    if (['ttf', 'otf', 'woff', 'woff2', 'eot'].includes(ext)) {
        return {
            icon: 'font_download',
            type: 'type-font'
        };
    }

    // 主题和皮肤文件
    if (['theme', 'skin', 'style'].includes(ext)) {
        return {
            icon: 'palette',
            type: 'type-theme'
        };
    }

    // 数据库文件
    if (['db', 'sqlite', 'sqlite3', 'mdb', 'bak'].includes(ext)) {
        return {
            icon: 'storage',
            type: 'type-database'
        };
    }
    
    // 未知类型
    return {
        icon: 'insert_drive_file',
        type: 'type-unknown'
    };
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function downloadFile(fileName) {
    window.location.href = `files/${encodeURIComponent(fileName)}`;
}

function deleteFile(fileName) {
    if (!confirm('确定要删除文件 ' + fileName + ' 吗？')) {
        return;
    }

    // 显示加载提示
    showToast('正在删除...', 'info');

    $.ajax({
        url: `files/${encodeURIComponent(fileName)}`,
        type: 'POST',
        data: { _method: 'delete' },
        processData: true, // 让 jQuery 处理数据
        contentType: 'application/x-www-form-urlencoded', // 设置正确的 content-type
        timeout: 10000, // 设置超时时间
        success: function(response) {
            console.log('Delete success:', fileName);
            refreshFileList();
            showToast('文件已删除');
        },
        error: function(xhr, status, error) {
            console.error('Delete failed:', {
                fileName: fileName,
                status: xhr.status,
                statusText: xhr.statusText,
                error: error
            });
            
            // 根据不同的错误类型显示不同的错误信息
            let errorMessage = '删除失败';
            if (status === 'timeout') {
                errorMessage = '删除超时，请重试';
            } else if (xhr.status === 404) {
                errorMessage = '文件不存在';
            } else if (xhr.status === 0 && error === '') {
                // 特殊处理空响应的情况
                console.log('Empty response received, treating as success');
                refreshFileList();
                showToast('文件已删除');
                return;
            }
            
            showToast(errorMessage);
        },
        complete: function() {
            // 无论成功失败都刷新列表
            refreshFileList();
        }
    });
}

function showToast(message, type = 'info') {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.className = `toast show ${type}`;
    
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// 搜索功能
const searchInput = document.getElementById('searchInput');
const searchButton = document.querySelector('.search-button');

function performSearch() {
    const searchTerm = searchInput.value.trim();
    console.log('=== Start Search ===');
    console.log('Search term:', searchTerm);
    
    if (!searchTerm) {
        console.log('Empty search term, refreshing list');
        refreshFileList();
        return;
    }

    const url = `files?search=${encodeURIComponent(searchTerm)}`;
    console.log('Search URL:', url);

    $.ajax({
        url: url,
        type: 'GET',
        success: function(response) {
            try {
                console.log('Raw response:', response);
                const files = typeof response === 'string' ? JSON.parse(response) : response;
                console.log('Parsed files:', files);
                console.log('Found files count:', files.length);
                renderFileList(files);
                showToast(`找到 ${files.length} 个文件`);
            } catch (error) {
                console.error('Search error:', error);
                console.error('Response that caused error:', response);
                showToast('搜索失败：数据格式错误');
            }
        },
        error: function(xhr, status, error) {
            console.error('Search failed:', {
                status: xhr.status,
                statusText: xhr.statusText,
                error: error,
                response: xhr.responseText
            });
            showToast('搜索失败：' + error);
        }
    });
    console.log('=== End Search ===');
}

// 添加防抖功能
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 将所有DOM相关的初始化代码移到一个函数中
function initializeUI() {
    // 搜索相关
    const searchInput = document.getElementById('searchInput');
    const searchButton = document.querySelector('.search-button');
    
    if (searchInput && searchButton) {
        // 搜索按钮点击事件
        searchButton.addEventListener('click', performSearch);
        
        // 搜索输入框事件
        searchInput.addEventListener('input', debounce(performSearch, 300));
        
        // 回车键触发搜索
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }

    // 排序相关
    const sortSelect = document.getElementById('sortSelect');
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            const sortBy = this.value;
            console.log('=== Start Sort ===');
            console.log('Sort type:', sortBy);
            refreshFileList(sortBy);
        });
    }

    // 刷新按钮相关
    const refreshButton = document.getElementById('refreshButton');
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            this.classList.add('rotating');
            const currentSort = sortSelect?.value;
            refreshFileList(currentSort).finally(() => {
                setTimeout(() => {
                    this.classList.remove('rotating');
                }, 500);
            });
        });
    }

    // 主题切换相关
    initThemeToggle();

    // 移动端上传按钮
    const mobileUploadBtn = document.getElementById('mobileUploadBtn');
    const fileInput = document.getElementById('fileInput');
    
    if (mobileUploadBtn && fileInput) {
        mobileUploadBtn.addEventListener('click', () => {
            fileInput.click();
        });
    }
}

// 等待DOM加载完成后再初始化UI
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM Content Loaded, initializing UI...');
    initializeUI();
});

// 如果DOM已经加载完成，直接初始化
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    console.log('Document already loaded, initializing UI...');
    initializeUI();
}

// 修改主题初始化和事件监听逻辑
function initThemeToggle() {
    const themeToggle = document.getElementById('themeToggle');
    if (!themeToggle) {
        console.error('Theme toggle button not found, retrying in 1s...');
        // 如果元素未找到，1秒后重试
        setTimeout(initThemeToggle, 1000);
        return;
    }

    // 初始化主题
    const html = document.documentElement;
    const savedTheme = localStorage.getItem('theme') || 'light';
    
    // 设置初始主题
    html.setAttribute('data-theme', savedTheme);
    
    // 设置正确的图标
    const themeIcon = themeToggle.querySelector('i');
    if (themeIcon) {
        themeIcon.textContent = savedTheme === 'dark' ? 'light_mode' : 'dark_mode';
    }

    // 添加点击事件监听
    themeToggle.addEventListener('click', toggleTheme);
    console.log('Theme toggle initialized:', savedTheme);
}

// 更新主题切换功能
function toggleTheme() {
    const html = document.documentElement;
    const currentTheme = html.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    
    // 添加过渡动画
    html.style.transition = 'background-color 0.3s ease, color 0.3s ease';
    
    // 更新主题
    html.setAttribute('data-theme', newTheme);
    
    // 更新图标
    const themeIcon = document.querySelector('#themeToggle i');
    if (themeIcon) {
        themeIcon.textContent = newTheme === 'dark' ? 'light_mode' : 'dark_mode';
    }
    
    // 保存主题设置
    localStorage.setItem('theme', newTheme);
    
    // 移除过渡动画
    setTimeout(() => {
        html.style.transition = '';
    }, 300);

    console.log('Theme toggled:', newTheme);
}

function refreshFileList() {
    showToast('正在加载文件列表...', 'info');
    
    $.ajax({
        url: 'files',
        type: 'GET',
        success: function(response) {
            try {
                const files = typeof response === 'string' ? JSON.parse(response) : response;
                renderFileList(files);
                console.log('Files loaded:', files);
            } catch (error) {
                console.error('Parse file list failed:', error);
                showToast('加载失败：数据格式错误');
            }
        },
        error: function(xhr, status, error) {
            console.error('Load files failed:', error);
            showToast('加载失败：' + error);
        }
    });
}

function handleUploadError(progressBar, error) {
    progressBar.classList.add('error');
    const percentElement = progressBar.querySelector('.percent');
    percentElement.textContent = '上传失败';
    percentElement.style.color = '#FF3B30';

    // 如果是移动端，3秒后隐藏失败的进度条
    if (isMobileDevice()) {
        setTimeout(() => {
            progressBar.style.opacity = '0';
            setTimeout(() => {
                progressBar.remove();
                
                // 检查是否还有其他正在上传的文件
                const remainingBars = document.querySelectorAll('.progress-wrapper').length;
                if (remainingBars === 0) {
                    const progressCard = document.querySelector('.upload-progress-card');
                    progressCard.classList.add('hide');
                    setTimeout(() => {
                        progressCard.classList.remove('show', 'hide');
                    }, 300);
                }
            }, 300);
        }, 3000);
    }
}

// 根据时间和系统主题自动设置主题
function initThemeByTime() {
    const hour = new Date().getHours();
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const isDayTime = hour >= 6 && hour < 18; // 6点到18点为白天
    
    // 优先使用系统主题设置，如果没有则根据时间判断
    const preferredTheme = prefersDark || !isDayTime ? 'dark' : 'light';
    
    // 如果用户没有手动设置过主题，则使用自动主题
    if (!localStorage.getItem('theme')) {
        document.documentElement.setAttribute('data-theme', preferredTheme);
        localStorage.setItem('theme', preferredTheme);
        
        // 更新切换按钮状态
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.setAttribute('aria-pressed', preferredTheme === 'dark');
        }
    }
}

// 监听系统主题变化
window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
    if (!localStorage.getItem('theme')) {
        const newTheme = e.matches ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        // 更新切换按钮状态
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.setAttribute('aria-pressed', newTheme === 'dark');
        }
    }
});

// 在页面加载时初始化主题
document.addEventListener('DOMContentLoaded', () => {
    initThemeByTime();
    initThemeToggle();
});