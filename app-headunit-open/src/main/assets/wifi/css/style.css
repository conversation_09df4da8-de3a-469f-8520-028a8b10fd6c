/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

/* Webkit browsers like Chrome/Safari */
*::-webkit-scrollbar {
    display: none;
}

body {
    margin: 0;
    padding: 0;
    background: #f2f2f7;
    font-family: -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
}

/* 容器基础样式 */
.container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* PC端样式 */
@media (min-width: 769px) {
    body {
        background: #f5f5f7;
        height: 100vh;
        overflow: hidden; /* 防止整页滚动 */
    }

    .container {
        padding: 1rem;
        height: 100vh;
        padding-top: 88px; /* 增加顶部间距 */
    }

    .content-wrapper {
        max-width: 1600px;
        margin: 0 auto;
        display: grid;
        grid-template-columns: 380px 1fr;
        gap: 24px;
        height: calc(100vh - 100px);
        padding: 0 32px;
    }

    /* 桌面端的内容区域添加上边距 */
    .content-wrapper {
        margin-top: 24px; /* 添加与头部的间距 */
        height: calc(100vh - 124px); /* 减去头部高度(72px)和上边距(24px)以及一些缓冲空间 */
    }

    /* 左侧容器 */
    .left-column {
        display: flex;
        flex-direction: column;
        gap: 24px;
        height: 100%;
    }

    /* 上传卡片样式优化 */
    .upload-card {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        border: 1px solid rgba(0, 0, 0, 0.1);
        flex: 0 0 240px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .upload-area {
        width: calc(100% - 64px);
        height: calc(100% - 64px);
        margin: 32px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border: 2px dashed rgba(0, 0, 0, 0.1);
        border-radius: 16px;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.5);
    }

    .upload-icon {
        width: 64px;
        height: 64px;
        background: #007AFF;
        border-radius: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
    }

    .upload-icon i {
        font-size: 32px;
        color: #fff;
    }

    .select-button {
        background: #007AFF;
        color: #fff;
        border: none;
        padding: 6px 12px;
        border-radius: 8px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .select-button:hover {
        background: #0066CC;
    }

    /* 上传进度卡片样式优化 */
    .upload-progress-card {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        border: 1px solid rgba(0, 0, 0, 0.1);
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .progress-header {
        padding: 16px 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .progress-header h3 {
        font-size: 17px;
        font-weight: 600;
        color: #1d1d1f;
    }

    /* 搜索框样式优化 */
    .search-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        border-radius: 12px;
        padding: 0 12px;
        transition: all 0.2s ease;
        background: none; /* 移除灰色背景 */
    }

    .search-wrapper i {
        color: #8E8E93;
        font-size: 20px;
        margin-right: 8px;
    }

    .search-wrapper input {
        flex: 1;
        height: 40px;
        border: none;
        background: none;
        font-size: 15px;
        color: #1d1d1f;
        padding: 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .search-wrapper input:focus {
        outline: none;
        border-bottom-color: #007AFF;
    }

    .search-button {
        background: #007AFF;
        color: #fff;
        border: none;
        width: 36px;
        height: 36px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        margin-left: 8px;
    }

    .search-button:hover {
        background: #0066CC;
    }

    .search-button i {
        font-size: 20px;
        color: #fff;
    }

    /* 排序和刷新按钮样式优化 */
    .sort-controls {
        display: flex;
        gap: 12px;
        align-items: center;
    }

    .sort-select {
        height: 40px;
        padding: 0 36px 0 16px;
        border-radius: 12px;
        border: none;
        background: rgba(0, 0, 0, 0.05);
        font-size: 15px;
        color: #1d1d1f;
        cursor: pointer;
        transition: all 0.2s ease;
        appearance: none;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 12px center;
        background-size: 16px;
    }

    .sort-select:hover {
        background-color: rgba(0, 0, 0, 0.08);
    }

    .refresh-button {
        width: 36px;
        height: 36px;
        border-radius: 12px;
        border: none;
        background: rgba(0, 0, 0, 0.05);
        color: #007AFF;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .refresh-button:hover {
        background: rgba(0, 0, 0, 0.08);
    }

    .refresh-button i {
        font-size: 20px;
    }

    /* 进度条样式优化 */
    .progress-wrapper {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 12px;
        padding: 12px;
        margin-bottom: 10px;
    }

    .progress-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 14px;
        color: #1d1d1f;
    }

    .progress-bar {
        height: 6px;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 3px;
        overflow: hidden;
    }

    .progress {
        height: 100%;
        background: #007AFF;
        border-radius: 3px;
        transition: width 0.3s ease;
    }

    /* 文件列表项样式优化 */
    .file-item {
        padding: 16px;
        display: flex;
        gap: 16px;
        align-items: flex-start;
    }

    .file-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
    }

    .file-content {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .file-info {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        gap: 6px;
    }

    .file-info-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .file-name-wrapper {
        flex: 1;
        margin-right: 16px;
    }

    .file-name {
        font-size: 16px;
        font-weight: 500;
        color: #1d1d1f;
        display: block;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .file-size {
        font-size: 15px;
        color: #86868b;
        white-space: nowrap;
    }

    .file-info-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .file-time {
        font-size: 14px;
        color: #86868b;
    }

    .file-details {
        display: block;
        color: #86868b;
        font-size: 13px;
        line-height: 1.4;
    }

    .file-time, .file-size {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        margin-right: 12px;
    }

    .file-time i, .file-size i {
        font-size: 16px;
        opacity: 0.7;
    }

    .file-bottom {
        margin-top: 2px;
    }

    .file-size {
        font-size: 13px;
    }

    /* 操作按钮样式优化 */
    .actions {
        display: flex;
        gap: 16px;
        margin-left: auto;
    }

    .action-btn {
        padding: 8px 16px;
        font-size: 15px;
        background: none;
        border: none;
        display: flex;
        align-items: center;
        white-space: nowrap;
        cursor: pointer;
    }

    .action-btn:hover {
        background: rgba(0, 0, 0, 0.08);
    }

    .action-btn i {
        font-size: 20px;
        margin-right: 3px;
    }

    .action-btn.download-btn {
        color: #007AFF;
    }

    .action-btn.delete-btn {
        color: #FF3B30;
    }

    /* 头部样式优化 */
    .header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        height: 72px; /* 增加高度 */
        background: var(--header-background);
        backdrop-filter: var(--backdrop-blur);
        -webkit-backdrop-filter: var(--backdrop-blur);
        border-bottom: 1px solid var(--border-color);
        z-index: 1000;
    }

    .header-content {
        max-width: 1600px;
        margin: 0 auto;
        padding: 0 32px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 24px;
    }

    /* Logo 区域优化 */
    .logo {
        display: flex;
        align-items: center;
        gap: 12px;
        min-width: 200px;
    }

    .logo-img {
        width: 40px;
        height: 40px;
        border-radius: 10px;
    }

    .logo-text {
        display: flex;
        flex-direction: column;
    }

    .logo-text .title {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
    }

    .logo-text .subtitle {
        font-size: 13px;
        color: var(--text-secondary);
        margin-top: 2px;
    }

    /* 新增头部信息区域样式 */
    .header-info {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 32px;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .info-item i {
        font-size: 24px;
        color: var(--accent-color);
        opacity: 0.9;
    }

    .info-text {
        display: flex;
        flex-direction: column;
    }

    .info-title {
        font-size: 15px;
        font-weight: 500;
        color: var(--text-primary);
    }

    .info-desc {
        font-size: 13px;
        color: var(--text-secondary);
        margin-top: 1px;
    }

    .divider {
        width: 1px;
        height: 32px;
        background: var(--border-color);
        opacity: 0.6;
    }

    /* 控制区域样式 */
    .header-controls {
        min-width: 200px;
        display: flex;
        justify-content: flex-end;
    }

    /* 桌面端专用类 */
    .desktop-only {
        display: none;
    }

    /* 响应式布局 */
    @media (min-width: 769px) {
        .desktop-only {
            display: flex;
        }
        
        .container {
            padding-top: 72px; /* 匹配新的header高度 */
        }
    }

    /* 移动端适配 */
    @media (max-width: 768px) {
        .header {
            height: 64px;
        }
        
        .header-content {
            padding: 0 16px;
        }
        
        .logo-img {
            width: 36px;
            height: 36px;
        }
        
        .logo-text .title {
            font-size: 16px;
        }
        
        .logo-text .subtitle {
            font-size: 12px;
        }
        
        .container {
            padding-top: 64px;
        }
    }

    /* 上传卡片样式优化 */
    .upload-card {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        border: 1px solid rgba(0, 0, 0, 0.1);
        height: 100%;
        max-height: calc(100vh - 100px);
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .upload-area {
        flex: 1;
        padding: 1.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border: 2px dashed rgba(0, 0, 0, 0.1);
        margin: 1rem;
        border-radius: 12px;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.5);
    }

    .upload-area:hover {
        border-color: #007AFF;
        background: rgba(0, 122, 255, 0.05);
    }

    .upload-area.dragover {
        border-color: #007AFF;
        background: rgba(0, 122, 255, 0.1);
    }

    .upload-icon {
        width: 64px;
        height: 64px;
        background: #007AFF;
        border-radius: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.5rem;
    }

    .upload-icon i {
        font-size: 32px;
        color: #fff;
    }

    .upload-text h3 {
        font-size: 18px;
        font-weight: 600;
        color: #1d1d1f;
        margin-bottom: 0.5rem;
    }

    .upload-text p {
        font-size: 15px;
        color: #86868b;
    }

    /* 文件列表卡片样式优化 */
    .files-card {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        border: 1px solid rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .card-header {
        padding: 1.25rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        background: rgba(255, 255, 255, 0.9);
    }

    .header-left {
        margin-bottom: 1rem;
    }

    .header-left h2 {
        font-size: 24px;
        font-weight: 600;
        color: #1d1d1f;
    }

    .file-count {
        font-size: 15px;
        color: #86868b;
    }

    .header-controls {
        display: grid;
        grid-template-columns: 1fr auto auto;
        gap: 12px;
        align-items: center;
        margin-top: 12px;
    }

    .search-wrapper {
        position: relative;
    }

    .search-wrapper input {
        width: 100%;
        height: 36px;
        padding: 0 40px;
        border-radius: 8px;
        border: none;
        background: rgba(0, 0, 0, 0.05);
        font-size: 15px;
        transition: all 0.3s ease;
    }

    .search-wrapper input:focus {
        background: rgba(0, 0, 0, 0.08);
        outline: none;
    }

    .sort-select {
        height: 40px;
        padding: 0 32px 0 12px;
        border-radius: 8px;
        border: none;
        background: rgba(0, 0, 0, 0.05);
        font-size: 15px;
        color: #1d1d1f;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .sort-select:hover {
        background: rgba(0, 0, 0, 0.08);
    }

    /* 文件列表样式优化 */
    .files-list {
        flex: 1;
        overflow-y: auto;
        padding: 12px;
        background: rgba(255, 255, 255, 0.5);
        display: flex;
        flex-direction: column;
        gap: 1px;
    }

    /* 文件项样式 */
    .file-item {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        transition: all 0.2s ease;
    }

    .file-item:hover {
        background: var(--hover-background);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px var(--shadow-color);
    }

    .file-item:last-child {
        margin-bottom: 0;
    }

    /* 文件图标 */
    .file-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }

    /* 文件内容区域 */
    .file-content {
        flex: 1;
        min-width: 0;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 16px;
    }

    .file-info {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .file-name {
        font-size: 16px;
        font-weight: 500;
        color: #1d1d1f;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.3;
        margin: 0;
    }

    .file-details {
        display: flex;
        flex-direction: column;
        gap: 4px;
        color: #86868b;
        font-size: 13px;
        line-height: 1.4;
    }

    .file-time, .file-size {
        display: flex;
        align-items: center;
        gap: 6px;
        white-space: nowrap;
    }

    .file-time i, .file-size i {
        font-size: 16px;
        opacity: 0.7;
    }

    /* 底部信息行 */
    .file-bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    /* 文件大小 */
    .file-size {
        font-size: 15px;
        color: #8E8E93;
    }

    /* 操作按钮容器 */
    .actions {
        display: flex;
        gap: 8px;
        flex-shrink: 0;
        align-self: center;
    }

    .file-item:hover .actions {
        opacity: 1;
    }

    /* 操作按钮 */
    .action-btn {
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
        background: rgba(0, 0, 0, 0.05);
        border: none;
        transition: all 0.2s ease;
        white-space: nowrap;
    }

    .action-btn:hover {
        background: rgba(0, 0, 0, 0.08);
    }

    .action-btn i {
        font-size: 20px;
    }

    .action-btn.download-btn {
        color: #007AFF;
    }

    .action-btn.delete-btn {
        color: #FF3B30;
    }

    /* 文件类型图标样式 */
    .file-icon.type-image {
        background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
    }

    .file-icon.type-video {
        background: linear-gradient(135deg, #FF2D55 0%, #FF375F 100%);
    }

    .file-icon.type-audio {
        background: linear-gradient(135deg, #AF52DE 0%, #B44AFF 100%);
    }

    .file-icon.type-document {
        background: linear-gradient(135deg, #007AFF 0%, #0A84FF 100%);
    }

    .file-icon.type-pdf {
        background: linear-gradient(135deg, #FF3B30 0%, #FF453A 100%);
    }

    .file-icon.type-archive {
        background: linear-gradient(135deg, #FF9500 0%, #FF9F0A 100%);
    }

    .file-icon.type-code {
        background: linear-gradient(135deg, #5856D6 0%, #5E5CE6 100%);
    }

    .file-icon.type-app {
        background: linear-gradient(135deg, #3DDC84 0%, #2ECC71 100%);
    }

    .file-icon.type-font {
        background: linear-gradient(135deg, #9B59B6 0%, #8E44AD 100%);
    }

    .file-icon.type-theme {
        background: linear-gradient(135deg, #E74C3C 0%, #C0392B 100%);
    }

    .file-icon.type-database {
        background: linear-gradient(135deg, #1ABC9C 0%, #16A085 100%);
    }

    .file-icon.type-unknown {
        background: linear-gradient(135deg, #8E8E93 0%, #98989D 100%);
    }

    /* 搜索和排序控件优化 */
    .header-controls {
        display: grid;
        grid-template-columns: 1fr auto auto;
        gap: 12px;
        align-items: center;
        margin-top: 12px;
    }

    .search-wrapper input {
        background: rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
    }

    .search-wrapper input:focus {
        background: rgba(0, 0, 0, 0.08);
    }

    .sort-select,
    .refresh-button {
        background: rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
    }

    .sort-select:hover,
    .refresh-button:hover {
        background: rgba(0, 0, 0, 0.08);
    }

    /* 上传进度条样式优化 */
    .upload-progress {
        padding: 1rem;
        max-height: 200px;
        overflow-y: auto;
        background: rgba(255, 255, 255, 0.9);
    }

    .progress-wrapper {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 8px;
    }

    /* 空状态优化 */
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        color: #86868b;
        text-align: center;
    }

    .empty-state i {
        font-size: 48px;
        margin-bottom: 1rem;
        color: #c7c7cc;
    }

    /* 文件列表容器修复 */
    .files-card {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .files-list {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
        background: var(--list-background);
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    /* 上传卡片内容居中修复 */
    .upload-card {
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .upload-area {
        width: 100%;
        height: 160px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .upload-icon {
        margin: 0 auto 16px;
    }

    .upload-text {
        text-align: center;
        width: 100%;
    }

    /* 文件项布局优化 */
    .file-item {
        padding: 16px;
        display: flex;
        align-items: center;
        gap: 16px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 16px;
        border: 1px solid rgba(0, 0, 0, 0.05);
        margin: 0;
    }

    /* 文件内容区域布局优化 */
    .file-content {
        flex: 1;
        min-width: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;
    }

    .file-info {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .file-name {
        font-size: 16px;
        font-weight: 500;
        color: #1d1d1f;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .file-details {
        display: flex;
        align-items: center;
        gap: 12px;
        color: #86868b;
        font-size: 13px;
    }

    /* 操作按钮容器 */
    .actions {
        display: flex;
        gap: 8px;
        flex-shrink: 0;
    }

    .action-btn {
        padding: 6px 12px;
        border-radius: 8px;
        font-size: 13px;
        display: flex;
        align-items: center;
        gap: 4px;
        background: rgba(0, 0, 0, 0.05);
        border: none;
        transition: all 0.2s ease;
        white-space: nowrap;
    }

    /* 上传进度卡片样式 */
    .upload-progress-card {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .upload-progress {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
    }
}

/* 移动端样式 */
@media (max-width: 768px) {
    /* 重置所有容器边距 */
    .container,
    .content-wrapper,
    .files-card,
    .files-list {
        width: 100%;
        max-width: 100%;
        margin: 0;
        padding: 0;
        border-radius: 0;
    }

    /* 头部样式 */
    .header {
        height: 64px; /* 增加高度 */
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .header-content {
        padding: 12px 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .logo {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .logo-img {
        width: 32px; /* 增大图标 */
        height: 32px;
        border-radius: 8px;
    }

    .logo-text {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .logo-text .title {
        font-size: 18px; /* 增大字号 */
        font-weight: 600;
        color: var(--text-primary);
    }

    .logo-text .subtitle {
        font-size: 13px;
        color: var(--text-secondary);
    }

    .theme-toggle {
        width: 36px;
        height: 36px;
        border-radius: 18px;
        border: none;
        background: none;
        color: #007AFF;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* 上传卡片样式 */
    .upload-card {
        margin: 12px 16px;
        background: #fff;
        border-radius: 12px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .upload-area {
        padding: 24px 16px;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .upload-icon {
        width: 56px;
        height: 56px;
        margin: 0 auto 16px;
        background: #007AFF;
        border-radius: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
    }

    .upload-icon i {
        font-size: 28px;
    }

    .upload-text h3 {
        font-size: 17px;
        font-weight: 600;
        color: #000;
        margin-bottom: 8px;
    }

    .upload-text p {
        font-size: 15px;
        color: #8E8E93;
    }

    .select-button {
        background: var(--accent-color);
        color: #fff;
        border: none;
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .select-button:active {
        transform: scale(0.98);
    }

    /* 文件列表区域 */
    .files-card {
        background: #fff;
        border: none;
    }

    .card-header {
        padding: 16px;
        background: #fff;
        border-bottom: 0.5px solid rgba(60, 60, 67, 0.1);
    }

    /* 标题和计数器 */
    .header-left {
        display: flex;
        align-items: baseline;
        gap: 8px;
        margin-bottom: 12px;
    }

    .header-left h2 {
        font-size: 22px;
        font-weight: 600;
        color: #000;
    }

    .file-count {
        font-size: 15px;
        color: #8E8E93;
    }

    /* 控制栏布局 */
    .header-controls {
        display: grid;
        grid-template-columns: 1fr auto auto;
        gap: 8px;
        align-items: center;
    }

    /* 搜索框样式 */
    .search-wrapper {
        position: relative;
        flex: 1;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .search-wrapper input {
        flex: 1;
        height: 36px;
        padding: 0 12px 0 40px;
        border-radius: 10px;
        border: none;
        background: rgba(118, 118, 128, 0.12);
        font-size: 15px;
        color: #000;
    }

    .search-wrapper i:first-child {
        position: absolute;
        left: 12px;
        color: #8E8E93;
        font-size: 20px;
        pointer-events: none;
    }

    /* 搜索按钮样式 */
    .search-button {
        min-width: 36px;
        height: 36px;
        border-radius: 10px;
        border: none;
        background: #007AFF;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .search-button i {
        font-size: 20px;
        color: #fff;
    }

    .search-button:hover {
        background: #0071E3;
    }

    .search-button:active {
        transform: scale(0.95);
    }

    /* 排序下拉框 */
    .sort-select {
        height: 36px;
        padding: 0 32px 0 12px;
        border-radius: 10px;
        border: none;
        background: rgba(118, 118, 128, 0.12);
        font-size: 15px;
        color: #000;
        appearance: none;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 8px center;
        background-size: 16px;
        cursor: pointer;
    }

    /* 刷新按钮 */
    .refresh-button {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        background: var(--button-background);
        color: var(--accent-color);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .refresh-button:hover {
        background: rgba(118, 118, 128, 0.18);
    }

    .refresh-button:active {
        transform: scale(0.95);
    }

    .refresh-button i {
        font-size: 20px;
    }

    /* 移动端适配 */
    @media (max-width: 480px) {
        .header-controls {
            grid-template-columns: 1fr;
        }

        .search-wrapper {
            grid-column: 1 / -1;
        }

        .sort-controls {
            display: flex;
            gap: 8px;
            grid-column: 1 / -1;
        }

        .sort-select {
            flex: 1;
        }
    }

    /* 文件列表 */
    .files-list {
        background: #fff;
        overflow-x: hidden;
        width: 100%;
        -webkit-overflow-scrolling: touch;
    }

    /* 确保内容不会水平溢出 */
    .container,
    .content-wrapper,
    .files-card,
    .card-header,
    .file-item {
        max-width: 100%;
        overflow-x: hidden;
    }

    /* 文件项布局 */
    .file-item {
        display: flex;
        gap: 12px;
        padding: 12px 16px;
        border-bottom: 0.5px solid rgba(60, 60, 67, 0.1);
        width: 100%;
        align-items: flex-start;
    }

    /* 文件图标 */
    .file-icon {
        flex-shrink: 0;
        width: 44px;
        height: 44px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
    }

    /* 不同文件类型的图标样式 */
    .file-icon.type-image {
        background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
    }

    .file-icon.type-video {
        background: linear-gradient(135deg, #FF2D55 0%, #FF375F 100%);
    }

    .file-icon.type-audio {
        background: linear-gradient(135deg, #AF52DE 0%, #B44AFF 100%);
    }

    .file-icon.type-document {
        background: linear-gradient(135deg, #007AFF 0%, #0A84FF 100%);
    }

    .file-icon.type-pdf {
        background: linear-gradient(135deg, #FF3B30 0%, #FF453A 100%);
    }

    .file-icon.type-archive {
        background: linear-gradient(135deg, #FF9500 0%, #FF9F0A 100%);
    }

    .file-icon.type-code {
        background: linear-gradient(135deg, #5856D6 0%, #5E5CE6 100%);
    }

    .file-icon.type-unknown {
        background: linear-gradient(135deg, #8E8E93 0%, #98989D 100%);
    }

    /* 新增文件类型的图标样式 */
    .file-icon.type-app {
        background: linear-gradient(135deg, #3DDC84 0%, #2ECC71 100%);
    }

    .file-icon.type-font {
        background: linear-gradient(135deg, #9B59B6 0%, #8E44AD 100%);
    }

    .file-icon.type-theme {
        background: linear-gradient(135deg, #E74C3C 0%, #C0392B 100%);
    }

    .file-icon.type-database {
        background: linear-gradient(135deg, #1ABC9C 0%, #16A085 100%);
    }

    /* 文件内容区域 */
    .file-content {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    /* 重置PC端的样式 */
    .file-info-top, .file-info-bottom {
        display: block;
        width: 100%;
    }

    /* 文件名 */
    .file-name {
        display: block;
        font-size: 15px;
        font-weight: 500;
        color: #1d1d1f;
        margin-bottom: 4px;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* 文件时间 */
    .file-time {
        display: block;
        font-size: 13px;
        color: #8E8E93;
        margin-bottom: 4px;
    }

    /* 底部信息行 */
    .file-bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin-top: 4px;
    }

    /* 文件大小 */
    .file-size {
        font-size: 13px;
        color: #8E8E93;
    }

    /* 操作按钮容器 */
    .actions {
        display: flex;
        gap: 16px;
        margin-left: auto;
    }

    /* 操作按钮 */
    .action-btn {
        padding: 6px 12px;
        font-size: 13px;
        background: rgba(0, 0, 0, 0.05);
        border: none;
        border-radius: 6px;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: all 0.2s ease;
    }

    .action-btn:active {
        transform: scale(0.95);
    }

    .action-btn i {
        font-size: 18px;
    }

    .action-btn.download-btn {
        color: #007AFF;
    }

    .action-btn.delete-btn {
        color: #FF3B30;
    }

    /* 操作按钮容器 */
    .actions {
        display: flex;
        gap: 12px;
        margin-left: auto;
    }

    /* 上传进度卡片默认隐藏 */
    @media (max-width: 768px) {
        .upload-progress-card {
            margin: 12px 16px;
            border-radius: 12px;
            display: none;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
        }

        /* 显示状态 */
        .upload-progress-card.show {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }

        /* 隐藏动画 */
        .upload-progress-card.hide {
            opacity: 0;
            transform: translateY(10px);
        }
    }
}

/* 添加动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.rotating {
    animation: rotate 0.5s linear;
}

.file-item {
    animation: fadeIn 0.3s ease;
}

/* 通用样式（PC和移动端共用） */
.toast {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.toast.show {
    opacity: 1;
}

/* 隐藏"正在加载文件列表..."的提示 */
.toast:contains("正在加载文件列表...") {
    display: none;
}

/* 上传进度卡片和进度条样式优化 */
.upload-progress-card {
    background: var(--card-background);
    backdrop-filter: var(--backdrop-blur);
    -webkit-backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--border-color);
}

.progress-header {
    background: var(--card-header-background);
    border-bottom: 1px solid var(--card-header-border);
    padding: 16px 20px;
}

.progress-header h3 {
    color: var(--text-primary);
    margin: 0;
}

.upload-progress {
    padding: 16px;
    background: var(--card-background);
    max-height: 300px;
    overflow-y: auto;
}

/* 进度项样式 */
.progress-wrapper {
    background: var(--progress-item-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.filename {
    color: var(--text-primary);
    font-size: 14px;
    flex: 1;
    margin-right: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.percent {
    color: var(--accent-color);
    font-size: 13px;
    font-weight: 500;
}

.progress-bar {
    height: 4px;
    background: var(--progress-background);
    border-radius: 2px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background: var(--accent-color);
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* 成功和失败状态 */
.progress-wrapper.success .progress {
    background: var(--success-color);
}

.progress-wrapper.error .progress {
    background: var(--error-color);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .upload-progress-card {
        margin: 12px 16px;
        border-radius: 12px;
    }

    .progress-header {
        padding: 12px 16px;
    }

    .progress-wrapper {
        padding: 12px;
        margin-bottom: 8px;
    }

    .filename {
        font-size: 13px;
    }

    .percent {
        font-size: 12px;
    }

    .progress-bar {
        height: 4px;
    }
}

/* 添加暗色主题变量 */
:root {
    /* 亮色主题变量 */
    --background-color: #f5f5f7;
    --card-background: rgba(255, 255, 255, 0.8);
    --card-background-solid: #ffffff;
    --text-primary: #1d1d1f;
    --text-secondary: #86868b;
    --text-tertiary: #98989d;
    --border-color: rgba(0, 0, 0, 0.1);
    --hover-background: rgba(0, 0, 0, 0.05);
    --hover-background-dark: rgba(0, 0, 0, 0.08);
    --button-background: rgba(0, 0, 0, 0.05);
    --upload-area-background: rgba(255, 255, 255, 0.5);
    --upload-area-border: rgba(0, 0, 0, 0.1);
    --toast-background: rgba(0, 0, 0, 0.8);
    --toast-color: #fff;
    --header-background: rgba(255, 255, 255, 0.8);
    --backdrop-blur: blur(20px);
    --shadow-color: rgba(0, 0, 0, 0.04);
    --progress-background: rgba(0, 0, 0, 0.05);
    --empty-icon-color: #c7c7cc;
    --search-background: rgba(118, 118, 128, 0.12);
    --accent-color: #007AFF;
    --accent-hover: #0066CC;
    --success-color: #34C759;
    --error-color: #FF3B30;
    --list-background: rgba(255, 255, 255, 0.5);
    --progress-card-background: rgba(255, 255, 255, 0.9);
    --progress-item-background: rgba(0, 0, 0, 0.05);
    --header-border: rgba(0, 0, 0, 0.1);
    --card-header-background: rgba(255, 255, 255, 0.9);
    --card-header-border: rgba(0, 0, 0, 0.1);
}

/* 暗色主题变量 */
[data-theme="dark"] {
    --background-color: #000000;
    --card-background: rgba(30, 30, 32, 0.8);
    --card-background-solid: #1c1c1e;
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    --text-tertiary: rgba(255, 255, 255, 0.5);
    --border-color: rgba(255, 255, 255, 0.15);
    --hover-background: rgba(255, 255, 255, 0.05);
    --hover-background-dark: rgba(255, 255, 255, 0.08);
    --button-background: rgba(255, 255, 255, 0.1);
    --upload-area-background: rgba(30, 30, 32, 0.5);
    --upload-area-border: rgba(255, 255, 255, 0.15);
    --toast-background: rgba(255, 255, 255, 0.8);
    --toast-color: #000;
    --header-background: rgba(30, 30, 32, 0.8);
    --backdrop-blur: blur(20px);
    --shadow-color: rgba(0, 0, 0, 0.2);
    --progress-background: rgba(255, 255, 255, 0.1);
    --empty-icon-color: rgba(255, 255, 255, 0.3);
    --search-background: rgba(255, 255, 255, 0.1);
    --accent-color: #0A84FF;
    --accent-hover: #409CFF;
    --success-color: #30D158;
    --error-color: #FF453A;
    --list-background: rgba(30, 30, 32, 0.5);
    --progress-card-background: rgba(30, 30, 32, 0.9);
    --progress-item-background: rgba(255, 255, 255, 0.05);
    --header-border: rgba(255, 255, 255, 0.1);
    --card-header-background: rgba(30, 30, 32, 0.9);
    --card-header-border: rgba(255, 255, 255, 0.1);
}

/* 应用变量到相应元素 */
body {
    background: var(--background-color);
    color: var(--text-primary);
}

.upload-card,
.upload-progress-card,
.files-card {
    background: var(--card-background);
    backdrop-filter: var(--backdrop-blur);
    -webkit-backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 8px var(--shadow-color);
}

.upload-area {
    background: var(--upload-area-background);
    border: 2px dashed var(--upload-area-border);
}

.upload-area:hover {
    border-color: var(--accent-color);
    background: var(--hover-background);
}

.upload-text h3 {
    color: var(--text-primary);
}

.upload-text p {
    color: var(--text-secondary);
}

.select-button {
    background: var(--accent-color);
    color: #ffffff;
}

.select-button:hover {
    background: var(--accent-hover);
}

.progress-wrapper {
    background: var(--card-background-solid);
    border: 1px solid var(--border-color);
}

.progress-bar {
    background: var(--progress-background);
}

.progress {
    background: var(--accent-color);
}

.search-wrapper input {
    background: var(--search-background);
    color: var(--text-primary);
}

.search-wrapper input::placeholder {
    color: var(--text-secondary);
}

.sort-select {
    background-color: var(--button-background);
    color: var(--text-primary);
}

.refresh-button {
    background: var(--button-background);
    color: var(--accent-color);
}

.file-item {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.file-item:hover {
    background: var(--hover-background);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px var(--shadow-color);
}

/* 暗色主题下的hover效果 */
[data-theme="dark"] .file-item:hover {
    background: var(--hover-background);
    border-color: var(--border-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.file-name {
    color: var(--text-primary);
}

.file-time,
.file-size {
    color: var(--text-secondary);
}

.empty-state {
    color: var(--text-secondary);
}

.empty-state i {
    color: var(--empty-icon-color);
}

/* 文件操作按钮 */
.action-btn {
    background: var(--button-background);
}

.action-btn.download-btn {
    color: var(--accent-color);
}

.action-btn.delete-btn {
    color: var(--error-color);
}

/* 上传进度相关 */
.progress-wrapper.success .progress {
    background: var(--success-color);
}

.progress-wrapper.error .progress {
    background: var(--error-color);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .header {
        background: var(--header-background);
        border-bottom: 1px solid var(--header-border);
    }

    .upload-card,
    .files-card,
    .card-header {
        background: var(--card-background-solid);
    }

    .file-item {
        border-bottom: 1px solid var(--border-color);
        background: var(--card-background-solid);
    }

    .search-wrapper input,
    .sort-select,
    .refresh-button {
        background: var(--search-background);
    }
}

/* 主题切换按钮 */
.theme-toggle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background: none;
    position: relative;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-tap-highlight-color: transparent;
    outline: none;
}

/* 图标容器 */
.theme-toggle i {
    font-size: 24px;
    position: absolute;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 月亮图标（提示可以切换到暗色） */
.theme-toggle i:first-child {
    opacity: 1;  /* 亮色模式下显示月亮 */
    transform: rotate(0) scale(1);
    color: #5C6BC0; /* 柔和的蓝紫色调 */
}

/* 太阳图标（提示可以切换到亮色） */
.theme-toggle i:last-child {
    opacity: 0;  /* 亮色模式下隐藏太阳 */
    transform: rotate(-45deg) scale(0);
    color: #FF9800; /* 温暖的橙色 */
}

/* 按钮悬停效果 */
.theme-toggle:hover {
    background: var(--button-background);
}

/* 点击效果 */
.theme-toggle:active {
    transform: scale(0.95);
}

/* 暗色主题下的样式 */
[data-theme="dark"] .theme-toggle i:first-child {
    opacity: 0;  /* 暗色模式下隐藏月亮 */
    transform: rotate(45deg) scale(0);
}

[data-theme="dark"] .theme-toggle i:last-child {
    opacity: 1;  /* 暗色模式下显示太阳 */
    transform: rotate(0) scale(1);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .theme-toggle {
        width: 36px;
        height: 36px;
    }

    .theme-toggle i {
        font-size: 20px;
    }
}

/* 文件列表标题适配 */
.header-left h2 {
    color: var(--text-primary);
}

.file-count {
    color: var(--text-secondary);
}

[data-theme="dark"] .header-left h2 {
    color: var(--text-primary);
}

[data-theme="dark"] .file-count {
    color: var(--text-secondary);
}

/* 文件列表容器基础样式 */
.files-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background: var(--list-background);
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* 文件列表卡片样式 */
.files-card {
    background: var(--card-background);
    backdrop-filter: var(--backdrop-blur);
    -webkit-backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 8px var(--shadow-color);
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

/* 卡片头部样式 */
.card-header {
    background: var(--card-header-background);
    border-bottom: 1px solid var(--card-header-border);
    padding: 16px 20px;
}

/* 暗色主题特殊处理 */
[data-theme="dark"] .files-list {
    background: var(--list-background);
}

[data-theme="dark"] .files-card {
    background: var(--card-background);
}

[data-theme="dark"] .card-header {
    background: var(--card-header-background);
    border-bottom-color: var(--card-header-border);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .files-list {
        background: var(--card-background-solid);
        padding: 12px;
        gap: 8px;
    }

    .files-card {
        background: var(--card-background-solid);
        border: none;
        box-shadow: none;
    }

    .card-header {
        background: var(--card-background-solid);
        padding: 12px 16px;
    }

    [data-theme="dark"] .files-list {
        background: var(--card-background-solid);
    }

    [data-theme="dark"] .files-card {
        background: var(--card-background-solid);
    }

    [data-theme="dark"] .card-header {
        background: var(--card-background-solid);
    }
}

/* 移动端和桌面端通用的显示控制类 */
.mobile-only {
    display: none;
}

.desktop-only {
    display: flex;
}

/* 移动端样式 */
@media (max-width: 768px) {
    .mobile-only {
        display: flex;
    }

    .desktop-only {
        display: none;
    }

    /* 头部样式优化 */
    .header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        height: 56px; /* 移动端降低高度 */
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        z-index: 1000;
    }

    .header-content {
        padding: 0 16px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    /* Logo 区域优化 */
    .logo {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .logo-img {
        width: 32px; /* 增大图标 */
        height: 32px;
        border-radius: 8px;
    }

    .logo-text {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .logo-text .title {
        font-size: 18px; /* 增大字号 */
        font-weight: 600;
        color: var(--text-primary);
    }

    .logo-text .subtitle {
        font-size: 13px;
        color: var(--text-secondary);
    }

    /* 控制区域样式 */
    .header-controls {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    /* 移动端上传按钮 */
    .upload-button {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        border: none;
        background: var(--accent-color);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .upload-button i {
        font-size: 20px;
    }

    .upload-button:active {
        transform: scale(0.95);
    }

    /* 适配内容区域，防止被固定头部遮挡 */
    .container {
        padding-top: 56px;
    }

    /* 暗色模式适配 */
    [data-theme="dark"] .header {
        background: rgba(30, 30, 30, 0.95);
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }
}