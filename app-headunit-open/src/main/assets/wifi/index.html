<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>智车桌面·闪传</title>
    <link rel="stylesheet" href="css/style.css" type="text/css">
    <script src="scripts/jquery-1.7.2.min.js"></script>
    <script src="scripts/lang.zh-Hans.js"></script>
    <script src="scripts/transfer.js"></script>
    <script src="scripts/ajaxfileupload.js"></script>
    <script src="scripts/bitcandies.upload5.js"></script>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>

<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <img src="images/ic_launcher.webp" alt="logo" class="logo-img">
                    <div class="logo-text">
                        <span class="title desktop-only">智车桌面·闪传</span>
                        <span class="title mobile-only">智车桌面</span>
                        <span class="subtitle mobile-only">高速文件传输</span>
                    </div>
                </div>
                
                <!-- 新增头部信息区域 -->
                <div class="header-info desktop-only">
                    <div class="info-item">
                        <i class="material-icons">wifi</i>
                        <div class="info-text">
                            <span class="info-title">局域网传输</span>
                            <span class="info-desc">零流量 · 高速传输</span>
                        </div>
                    </div>
                    <div class="divider"></div>
                    <div class="info-item">
                        <i class="material-icons">security</i>
                        <div class="info-text">
                            <span class="info-title">安全可靠</span>
                            <span class="info-desc">仅限局域网访问</span>
                        </div>
                    </div>
                    <div class="divider"></div>
                    <div class="info-item">
                        <i class="material-icons">devices</i>
                        <div class="info-text">
                            <span class="info-title">全平台支持</span>
                            <span class="info-desc">支持所有浏览器</span>
                        </div>
                    </div>
                </div>

                <div class="header-controls">
                    <button class="upload-button mobile-only" id="mobileUploadBtn" aria-label="上传文件">
                        <i class="material-icons">upload_file</i>
                    </button>
                    <button class="theme-toggle" id="themeToggle" aria-label="切换主题">
                        <i class="material-icons">nights_stay</i>
                        <i class="material-icons">wb_sunny</i>
                    </button>
                </div>
            </div>
        </header>

        <main class="content-wrapper">
            <div class="left-column">
                <!-- 上传卡片 -->
                <div class="upload-card">
                    <input type="file" id="fileInput" multiple class="file-input" hidden />
                    <div class="upload-area" id="dropZone">
                        <div class="upload-content">
                            <div class="upload-icon">
                                <i class="material-icons">cloud_upload</i>
                            </div>
                            <div class="upload-text">
                                <h3>拖拽文件到此处</h3>
                                <p>或者 <button class="select-button">选择文件</button> 上传</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 上传进度卡片 -->
                <div class="upload-progress-card">
                    <div class="progress-header">
                        <h3>上传进度</h3>
                    </div>
                    <div class="upload-progress" id="uploadProgress">
                        <!-- 进度条将通过 JavaScript 动态插入 -->
                    </div>
                </div>
            </div>

            <!-- 文件列表卡片 -->
            <div class="files-card">
                <div class="card-header">
                    <div class="header-left">
                        <h2>文件列表</h2>
                        <span class="file-count">共 <span id="fileCount">0</span> 个文件</span>
                    </div>
                    <div class="header-controls">
                        <div class="search-wrapper">
                            <i class="material-icons">search</i>
                            <input type="text" placeholder="搜索文件..." id="searchInput">
                            <button class="search-button" aria-label="搜索">
                                <i class="material-icons">search</i>
                            </button>
                        </div>
                        <div class="sort-controls">
                            <select class="sort-select" id="sortSelect">
                                <option value="time">最近上传</option>
                                <option value="name">文件名称</option>
                                <option value="size">文件大小</option>
                            </select>
                            <button class="refresh-button" id="refreshButton" aria-label="刷新列表">
                                <i class="material-icons">refresh</i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="files-list" id="fileList">
                    <!-- 文件列表将通过 JavaScript 动态插入 -->
                </div>
            </div>
        </main>
    </div>

    <!-- 提示弹窗 -->
    <div class="toast" id="toast"></div>
</body>
</html>