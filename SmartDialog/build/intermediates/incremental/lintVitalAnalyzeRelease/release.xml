<variant
    name="release"
    package="com.timmy.tdialog"
    minSdkVersion="17"
    targetSdkVersion="26"
    mergedManifest="build/intermediates/merged_manifest/release/AndroidManifest.xml"
    proguardFiles="build/intermediates/default_proguard_files/global/proguard-android.txt-8.2.0:proguard-rules.pro"
    partialResultsDir="build/intermediates/lint_vital_partial_results/release/out"
    desugaredMethodsFiles="/Users/<USER>/.gradle/caches/transforms-3/2af4aaee8f3bd49d382a8c3307dca76e/transformed/D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src/main/AndroidManifest.xml"
        javaDirectories="src/main/java:src/release/java:src/main/kotlin:src/release/kotlin"
        resDirectories="src/main/res:src/release/res"
        assetsDirectories="src/main/assets:src/release/assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build/intermediates/javac/release/classes:build/intermediates/compile_r_class_jar/release/R.jar"
      type="MAIN"
      applicationId="com.timmy.tdialog"
      generatedSourceFolders="build/generated/ap_generated_sources/release/out"
      generatedResourceFolders="build/generated/res/resValues/release"
      desugaredMethodsFiles="/Users/<USER>/.gradle/caches/transforms-3/2af4aaee8f3bd49d382a8c3307dca76e/transformed/D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
