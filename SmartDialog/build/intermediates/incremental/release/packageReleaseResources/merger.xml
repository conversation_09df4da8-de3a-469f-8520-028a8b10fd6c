<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/StudioProjects/Smart/SmartDialog/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/StudioProjects/Smart/SmartDialog/src/main/res"><file path="/Users/<USER>/StudioProjects/Smart/SmartDialog/src/main/res/values-v21/styles.xml" qualifiers="v21"><style name="DialogFullScreen" parent="Theme.AppCompat.Dialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style></file><file path="/Users/<USER>/StudioProjects/Smart/SmartDialog/src/main/res/values-v27/styles.xml" qualifiers="v27"><style name="DialogFullScreen" parent="Theme.AppCompat.Dialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style></file><file name="dialog_recycler" path="/Users/<USER>/StudioProjects/Smart/SmartDialog/src/main/res/layout/dialog_recycler.xml" qualifiers="" type="layout"/><file name="fragment_setting_nav" path="/Users/<USER>/StudioProjects/Smart/SmartDialog/src/main/res/layout/fragment_setting_nav.xml" qualifiers="" type="layout"/><file path="/Users/<USER>/StudioProjects/Smart/SmartDialog/src/main/res/values/styles.xml" qualifiers=""><style name="DialogFullScreen" parent="Theme.AppCompat.Dialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        
    </style></file><file path="/Users/<USER>/StudioProjects/Smart/SmartDialog/src/main/res/values/strings.xml" qualifiers=""><string name="app_name">tdialog</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/StudioProjects/Smart/SmartDialog/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/StudioProjects/Smart/SmartDialog/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/StudioProjects/Smart/SmartDialog/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/StudioProjects/Smart/SmartDialog/build/generated/res/resValues/release"/></dataSet><mergedItems/></merger>