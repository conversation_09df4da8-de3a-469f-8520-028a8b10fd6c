<dependencies>
  <compile
      roots="androidx.legacy:legacy-support-v4:1.0.0@aar,com.google.android.material:material:1.6.1@aar,androidx.viewpager2:viewpager2:1.0.0@aar,androidx.recyclerview:recyclerview:1.2.1@aar,androidx.recyclerview:recyclerview:1.2.1@aar,com.tencent:mmkv:1.2.12@aar,androidx.media:media:1.0.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.constraintlayout:constraintlayout:2.0.1@aar,androidx.appcompat:appcompat:1.2.0@aar,androidx.fragment:fragment:1.2.5@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.transition:transition:1.2.0@aar,androidx.appcompat:appcompat-resources:1.2.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.activity:activity:1.1.0@aar,androidx.core:core:1.5.0@aar,androidx.core:core:1.5.0@aar,androidx.cardview:cardview:1.0.0@aar,androidx.lifecycle:lifecycle-runtime:2.2.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.collection:collection:1.1.0@jar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.2.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.2.0@aar,androidx.lifecycle:lifecycle-livedata:2.0.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.2.0@aar,androidx.savedstate:savedstate:1.0.0@aar,androidx.lifecycle:lifecycle-common:2.2.0@jar,androidx.arch.core:core-runtime:2.1.0@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.annotation:annotation:1.3.0@jar,androidx.constraintlayout:constraintlayout-solver:2.0.1@jar,androidx.annotation:annotation-experimental:1.0.0@aar">
    <dependency
        name="androidx.legacy:legacy-support-v4:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-v4"/>
    <dependency
        name="com.google.android.material:material:1.6.1@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.2.1@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="com.tencent:mmkv:1.2.12@aar"
        simpleName="com.tencent:mmkv"/>
    <dependency
        name="androidx.media:media:1.0.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.appcompat:appcompat:1.2.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.fragment:fragment:1.2.5@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.transition:transition:1.2.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.2.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.activity:activity:1.1.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.core:core:1.5.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.savedstate:savedstate:1.0.0@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.2.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.1.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation:1.3.0@jar"
        simpleName="androidx.annotation:annotation"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
        simpleName="androidx.constraintlayout:constraintlayout-solver"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.0.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
  </compile>
  <package
      roots="com.google.android.material:material:1.6.1@aar,androidx.viewpager2:viewpager2:1.0.0@aar,androidx.recyclerview:recyclerview:1.2.1@aar,androidx.recyclerview:recyclerview:1.2.1@aar,com.tencent:mmkv:1.2.12@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.constraintlayout:constraintlayout:2.0.1@aar,androidx.appcompat:appcompat:1.2.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.fragment:fragment:1.2.5@aar,androidx.transition:transition:1.2.0@aar,androidx.appcompat:appcompat-resources:1.2.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.activity:activity:1.1.0@aar,androidx.core:core:1.5.0@aar,androidx.core:core:1.5.0@aar,androidx.cardview:cardview:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.2.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.2.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.lifecycle:lifecycle-runtime:2.2.0@aar,androidx.savedstate:savedstate:1.0.0@aar,androidx.lifecycle:lifecycle-livedata:2.0.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.2.0@aar,androidx.lifecycle:lifecycle-common:2.2.0@jar,androidx.arch.core:core-runtime:2.1.0@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.annotation:annotation:1.3.0@jar,androidx.annotation:annotation-experimental:1.0.0@aar,androidx.constraintlayout:constraintlayout-solver:2.0.1@jar">
    <dependency
        name="com.google.android.material:material:1.6.1@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.2.1@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="com.tencent:mmkv:1.2.12@aar"
        simpleName="com.tencent:mmkv"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.appcompat:appcompat:1.2.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.fragment:fragment:1.2.5@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.transition:transition:1.2.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.2.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.activity:activity:1.1.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.core:core:1.5.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.savedstate:savedstate:1.0.0@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.2.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.1.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.annotation:annotation:1.3.0@jar"
        simpleName="androidx.annotation:annotation"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.0.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
        simpleName="androidx.constraintlayout:constraintlayout-solver"/>
  </package>
</dependencies>
