<libraries>
  <library
      name="androidx.legacy:legacy-support-v4:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/dea92ce26cfb4f154bfcaee290a254c0/transformed/legacy-support-v4-1.0.0/jars/classes.jar"
      resolved="androidx.legacy:legacy-support-v4:1.0.0"
      provided="true"
      folder="/Users/<USER>/.gradle/caches/transforms-3/dea92ce26cfb4f154bfcaee290a254c0/transformed/legacy-support-v4-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.6.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/8fc67e64f047853b28f2ae2add895caf/transformed/material-1.6.1/jars/classes.jar"
      resolved="com.google.android.material:material:1.6.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/8fc67e64f047853b28f2ae2add895caf/transformed/material-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/4809326802242bec1307f4b34e8bfc98/transformed/jetified-viewpager2-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/4809326802242bec1307f4b34e8bfc98/transformed/jetified-viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.2.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/275fbe9e445b2baa5f90fee25c9df5bd/transformed/recyclerview-1.2.1/jars/classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.2.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/275fbe9e445b2baa5f90fee25c9df5bd/transformed/recyclerview-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.tencent:mmkv:1.2.12@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/7fb09881f0c8d95be898b64072200993/transformed/jetified-mmkv-1.2.12/jars/classes.jar"
      resolved="com.tencent:mmkv:1.2.12"
      folder="/Users/<USER>/.gradle/caches/transforms-3/7fb09881f0c8d95be898b64072200993/transformed/jetified-mmkv-1.2.12"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/b7f5bab91837c646eaa297c69983f81a/transformed/media-1.0.0/jars/classes.jar"
      resolved="androidx.media:media:1.0.0"
      provided="true"
      folder="/Users/<USER>/.gradle/caches/transforms-3/b7f5bab91837c646eaa297c69983f81a/transformed/media-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/8d60d688bf83dfbec253b5539634184f/transformed/legacy-support-core-ui-1.0.0/jars/classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      provided="true"
      folder="/Users/<USER>/.gradle/caches/transforms-3/8d60d688bf83dfbec253b5539634184f/transformed/legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/fd2b8a04117775d3a40b6701da0b609f/transformed/dynamicanimation-1.0.0/jars/classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/fd2b8a04117775d3a40b6701da0b609f/transformed/dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/b54cb4adab9b8e8be8ecfa8730ccdc4b/transformed/legacy-support-core-utils-1.0.0/jars/classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/b54cb4adab9b8e8be8ecfa8730ccdc4b/transformed/legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/67e2d7901f07e4d10d52b3801810cc7e/transformed/constraintlayout-2.0.1/jars/classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.0.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/67e2d7901f07e4d10d52b3801810cc7e/transformed/constraintlayout-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/3c18a7313543bebeda1c9100afdbfd0e/transformed/appcompat-1.2.0/jars/classes.jar"
      resolved="androidx.appcompat:appcompat:1.2.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/3c18a7313543bebeda1c9100afdbfd0e/transformed/appcompat-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.2.5@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/c6a11b7b286ee794faf0896411d47e95/transformed/fragment-1.2.5/jars/classes.jar"
      resolved="androidx.fragment:fragment:1.2.5"
      folder="/Users/<USER>/.gradle/caches/transforms-3/c6a11b7b286ee794faf0896411d47e95/transformed/fragment-1.2.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/e2dfaabd3f585cab1c092322bd6f1788/transformed/coordinatorlayout-1.1.0/jars/classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/e2dfaabd3f585cab1c092322bd6f1788/transformed/coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/ba2ff3117a4be3c967ee51f01b2b8a69/transformed/drawerlayout-1.1.1/jars/classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/ba2ff3117a4be3c967ee51f01b2b8a69/transformed/drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/0e5c88ef2cf8b33e5e449b2e75308c20/transformed/slidingpanelayout-1.0.0/jars/classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.0.0"
      provided="true"
      folder="/Users/<USER>/.gradle/caches/transforms-3/0e5c88ef2cf8b33e5e449b2e75308c20/transformed/slidingpanelayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/c1a1cba52a49a0330943cf5bbe4f645a/transformed/viewpager-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/c1a1cba52a49a0330943cf5bbe4f645a/transformed/viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/6a6b3421168a7c541edce00eb7015d5e/transformed/customview-1.1.0/jars/classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/6a6b3421168a7c541edce00eb7015d5e/transformed/customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/d4df170be8f6ce4c6db83138d6eea8aa/transformed/transition-1.2.0/jars/classes.jar"
      resolved="androidx.transition:transition:1.2.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/d4df170be8f6ce4c6db83138d6eea8aa/transformed/transition-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/b2b70c6243b915d5c0f07f5f61a20fe2/transformed/jetified-appcompat-resources-1.2.0/jars/classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.2.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/b2b70c6243b915d5c0f07f5f61a20fe2/transformed/jetified-appcompat-resources-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/259ce96aef5338586570c12576d9cca1/transformed/vectordrawable-animated-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/259ce96aef5338586570c12576d9cca1/transformed/vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/c5b2a41667f08e489f566d6284c74291/transformed/vectordrawable-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/c5b2a41667f08e489f566d6284c74291/transformed/vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/d35c3d1bd609f8dcd9ce440ae30429fd/transformed/swiperefreshlayout-1.0.0/jars/classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
      provided="true"
      folder="/Users/<USER>/.gradle/caches/transforms-3/d35c3d1bd609f8dcd9ce440ae30429fd/transformed/swiperefreshlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/aa062a233eda846e67ecdadfd6c33de6/transformed/asynclayoutinflater-1.0.0/jars/classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      provided="true"
      folder="/Users/<USER>/.gradle/caches/transforms-3/aa062a233eda846e67ecdadfd6c33de6/transformed/asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/92677988325cd98234ba85446de55a77/transformed/loader-1.0.0/jars/classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/92677988325cd98234ba85446de55a77/transformed/loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/dd02ca90088708e61559c63df9468566/transformed/jetified-activity-1.1.0/jars/classes.jar"
      resolved="androidx.activity:activity:1.1.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/dd02ca90088708e61559c63df9468566/transformed/jetified-activity-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.5.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/b247e2a55619c014854298bc2099daa8/transformed/core-1.5.0/jars/classes.jar"
      resolved="androidx.core:core:1.5.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/b247e2a55619c014854298bc2099daa8/transformed/core-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/ac4f67dacf2bc40f9ee71b3830e5a324/transformed/cardview-1.0.0/jars/classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/ac4f67dacf2bc40f9ee71b3830e5a324/transformed/cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/b42d90bb7645af5ae96be33044ec3f7a/transformed/lifecycle-runtime-2.2.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.2.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/b42d90bb7645af5ae96be33044ec3f7a/transformed/lifecycle-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/087ff9949f227bcd45b695df8016ad2e/transformed/versionedparcelable-1.1.1/jars/classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="/Users/<USER>/.gradle/caches/transforms-3/087ff9949f227bcd45b695df8016ad2e/transformed/versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/e18868c27d65562f3a7bb8a7176a1a9a/transformed/interpolator-1.0.0/jars/classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/e18868c27d65562f3a7bb8a7176a1a9a/transformed/interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/2badd7b1fbfe987015d72a49f0ad1b58/transformed/cursoradapter-1.0.0/jars/classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/2badd7b1fbfe987015d72a49f0ad1b58/transformed/cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/89fed84314b1d2eecb3b2043b0b92383/transformed/documentfile-1.0.0/jars/classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/89fed84314b1d2eecb3b2043b0b92383/transformed/documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/b18027b3b5dea2dde6c34a56fc155cf3/transformed/localbroadcastmanager-1.0.0/jars/classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/b18027b3b5dea2dde6c34a56fc155cf3/transformed/localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/965fa24ff50bff5480ec9841cbed0788/transformed/print-1.0.0/jars/classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/965fa24ff50bff5480ec9841cbed0788/transformed/print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.collection/collection/1.1.0/1f27220b47669781457de0d600849a5de0e89909/collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/60815d4fbfcfef32b3541e02f9d4a605/transformed/jetified-lifecycle-viewmodel-savedstate-2.2.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.2.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/60815d4fbfcfef32b3541e02f9d4a605/transformed/jetified-lifecycle-viewmodel-savedstate-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/bdd80be471e0a05f6489258294bdd900/transformed/lifecycle-viewmodel-2.2.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.2.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/bdd80be471e0a05f6489258294bdd900/transformed/lifecycle-viewmodel-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/9b9b99d71955afcf1d40d60770ff42c7/transformed/lifecycle-livedata-2.0.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/9b9b99d71955afcf1d40d60770ff42c7/transformed/lifecycle-livedata-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/78fcb1258b8d027c826907104a8452d4/transformed/lifecycle-livedata-core-2.2.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.2.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/78fcb1258b8d027c826907104a8452d4/transformed/lifecycle-livedata-core-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/337b4785e431187ff84c6543713d2461/transformed/jetified-savedstate-1.0.0/jars/classes.jar"
      resolved="androidx.savedstate:savedstate:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/337b4785e431187ff84c6543713d2461/transformed/jetified-savedstate-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.2.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common/2.2.0/4ef09a745007778eef83b92f8f23987a8ea59496/lifecycle-common-2.2.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.2.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/eb4d53be9e14cbaa04f24063e572cc52/transformed/core-runtime-2.1.0/jars/classes.jar"
      resolved="androidx.arch.core:core-runtime:2.1.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/eb4d53be9e14cbaa04f24063e572cc52/transformed/core-runtime-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-common/2.1.0/b3152fc64428c9354344bd89848ecddc09b6f07e/core-common-2.1.0.jar"
      resolved="androidx.arch.core:core-common:2.1.0"/>
  <library
      name="androidx.annotation:annotation:1.3.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.annotation/annotation/1.3.0/21f49f5f9b85fc49de712539f79123119740595/annotation-1.3.0.jar"
      resolved="androidx.annotation:annotation:1.3.0"/>
  <library
      name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.constraintlayout/constraintlayout-solver/2.0.1/30988fe2d77f3fe3bf7551bb8a8b795fad7e7226/constraintlayout-solver-2.0.1.jar"
      resolved="androidx.constraintlayout:constraintlayout-solver:2.0.1"/>
  <library
      name="androidx.annotation:annotation-experimental:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/transforms-3/2293423f21d1f448464988da18c773ed/transformed/jetified-annotation-experimental-1.0.0/jars/classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.0.0"
      folder="/Users/<USER>/.gradle/caches/transforms-3/2293423f21d1f448464988da18c773ed/transformed/jetified-annotation-experimental-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>
