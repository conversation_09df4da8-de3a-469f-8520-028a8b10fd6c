package com.timmy.tdialog.base;

import android.app.Dialog;
import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;


/**
 * <AUTHOR>
 * @date 2020/6/22 14:43
 * @desc 避免内存泄漏的Dialog,在DialogFragment中重写onCreateDialog中返回该dialog
 *
 */
public class WeakDialog extends Dialog {
    protected WeakDialog(@NonNull Context context) {
        super(context);
    }

    public WeakDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected WeakDialog(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    @Override
    public void setOnShowListener(@Nullable OnShowListener listener) {
        super.setOnShowListener(WeakDialogProxy.proxy(listener));
    }

    @Override
    public void setOnDismissListener(@Nullable OnDismissListener listener) {
        super.setOnDismissListener(WeakDialogProxy.proxy(listener));
    }

    @Override
    public void setOnCancelListener(@Nullable OnCancelListener listener) {
        super.setOnCancelListener(WeakDialogProxy.proxy(listener));
    }
}
