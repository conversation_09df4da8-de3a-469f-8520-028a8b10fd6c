package com.timmy.tdialog.base;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.Point;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;

import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import com.tencent.mmkv.MMKV;
import com.timmy.tdialog.HideNavBarUtil;
import com.timmy.tdialog.R;


/**
 * DialogFragment的基类
 * 1.系统默认onCreateDialog方法返回一个Dialog对象,对其不做处理
 * 2.主要操作onCreateView方法
 * 因为DialogFragment继承自Fragment,所以可以在onCreteView()方法返回xml布局,
 * 该布局在onActivityCreated()方法中,设置给系统之前创建的Dialog对象
 * //           @Override
 * //            public void onActivityCreated(Bundle savedInstanceState) {
 * //                super.onActivityCreated(savedInstanceState);
 * //
 * //                if (!mShowsDialog) {
 * //                return;
 * //                }
 * //
 * //                View view = getView();
 * //                if (view != null) {
 * //                if (view.getParent() != null) {
 * //                throw new IllegalStateException(
 * //                "DialogFragment can not be attached to a container view");
 * //                }
 * //                mDialog.setContentView(view);
 * //                }
 * //           }
 * 3.对应使用Dialog不同部分包括
 * a.xml布局
 * b.宽高
 * c.位置
 * d.背景色
 * e.透明度
 * f.是否可以点击空白处隐藏
 * 控制方法在onStart处理,
 * 4.暴露方法:界面中控件处理和点击事件处理
 * 5.监听回调,很多弹窗需要输入信息,然后将输入的信息通过回调的方法返回
 * 6.当设备Configure属性变化时,数据保存和恢复处理
 **/
public abstract class BaseDialogFragment extends DialogFragment {

    public static final String TAG = "TDialog";
    private static final float DEFAULT_DIMAMOUNT = 0.2F;
    private Dialog dialog;
    private String SYST_NAVIGATION_BAR_SHOW = "syst_navigation_bar_show";

    protected abstract int getLayoutRes();

    protected abstract void bindView(View view);

    protected abstract View getDialogView();

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setCancelable(isCancelableOutside());
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        // 创建对话框
        Dialog dialog = new WeakDialog(requireContext(), R.style.DialogFullScreen);
        Window window = dialog.getWindow();

        if (window != null) {
            // 设置全屏并隐藏系统栏（如果需要）
            WindowCompat.setDecorFitsSystemWindows(window, false);

            // 设置无标题

            View decorView = window.getDecorView();
            WindowInsetsControllerCompat windowInsetsController = WindowCompat.getInsetsController(window, decorView);
            if (windowInsetsController != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    window.setNavigationBarColor(getResources().getColor(android.R.color.black));
                }
                // 如果需要隐藏系统栏，可以取消注释下面这行
                // windowInsetsController.hide(WindowInsetsCompat.Type.systemBars());
            }

            // 设置窗口属性，如宽高、背景等（这部分可以在onStart中进一步调整）
        }
        if (getOnKeyListener() != null) {
            dialog.setOnKeyListener(getOnKeyListener());
        }
        return dialog;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        // 这里不再需要设置窗口特性，因为已经在onCreateDialog中设置了
        View view = null;

        // 设置对话框宽高、是否点击外部取消等（这部分逻辑可以保持不变）
        Dialog dialog = getDialog();
        if (dialog != null) {
            Window window = dialog.getWindow();
            if (window != null) {
                // 去掉dialog默认的padding
                window.getDecorView().setPadding(0, 0, 0, 0);
                WindowManager.LayoutParams lp = window.getAttributes();
                lp.width = WindowManager.LayoutParams.MATCH_PARENT;
                lp.height = WindowManager.LayoutParams.MATCH_PARENT;
                window.setAttributes(lp);
                window.setFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON,
                        WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
            }
        }
        Boolean systNavigationBarShow = getBoolean("syst_navigation_bar_show", false);
        Log.v("系统导航栏当前开关  ", " 11212" + systNavigationBarShow);



        if (getLayoutRes() > 0) {
            view = inflater.inflate(getLayoutRes(), container, false);
        }
        if (getDialogView() != null) {
            view = getDialogView();
        }
        bindView(view);
        return view;
    }


    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        //   initImmersionBar();
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        /**
         * 项目中的CartView 当作普同fragment使用  不会创建dialog
         */
        if (getShowsDialog()) {
            //not focus 来避免瞬间弹出
            getDialog().getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
            HideNavBarUtil.hideWhenSystemUiVisible(getDialog().getWindow().getDecorView(), getBoolean(SYST_NAVIGATION_BAR_SHOW, false));
            HideNavBarUtil.hideBottomUIMenu(getDialog().getWindow().getDecorView(), getBoolean(SYST_NAVIGATION_BAR_SHOW, false));
            //重新设置可获取焦点 避免弹不出键盘
            getDialog().getWindow().getDecorView().post(new Runnable() {
                @Override
                public void run() {
                    // 添加空检查，避免在运行时Dialog已被销毁
                    Dialog dialog = BaseDialogFragment.this.getDialog();
                    if (dialog != null && dialog.getWindow() != null) {
                        dialog.getWindow().setFlags(~WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
                    }
                }
            });
        }

    }

    public static Boolean getBoolean(String key, boolean defValue) {
        MMKV mmkv = MMKV.defaultMMKV();
        return mmkv.decodeBool(key, defValue);
    }

    protected DialogInterface.OnKeyListener getOnKeyListener() {
        return null;
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.v(TAG, "onResume()");
        //  initImmersionBar();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        //destroyImmersionBar();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
       // destroyImmersionBar();
        Log.v(TAG, "onDestroy()");


    }

    @Override
    public void onStart() {
        super.onStart();
        Dialog dialog = getDialog();
        if (dialog == null) {
            return;
        }
        Window window = dialog.getWindow();
        if (window != null) {
            //设置窗体背景色透明
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            //设置宽高
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            if (getDialogWidth() > 0) {
                layoutParams.width = getDialogWidth();
            } else {
                layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
            }
            if (getDialogHeight() > 0) {
                layoutParams.height = getDialogHeight();
            } else {
                layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            }
            //透明度
            layoutParams.dimAmount = getDimAmount();
            //位置
            layoutParams.gravity = getGravity();
            window.setAttributes(layoutParams);
        }
    }

    //默认弹窗位置为中心
    public int getGravity() {
        return Gravity.CENTER;
    }

    //默认宽高为包裹内容
    public int getDialogHeight() {
        return WindowManager.LayoutParams.WRAP_CONTENT;
    }

    public int getDialogWidth() {
        return WindowManager.LayoutParams.WRAP_CONTENT;
    }

    //默认透明度为0.2
    public float getDimAmount() {
        return DEFAULT_DIMAMOUNT;
    }

    public String getFragmentTag() {
        return TAG;
    }

    public void show(FragmentManager fragmentManager) {
        show(fragmentManager, getFragmentTag());
    }

    protected boolean isCancelableOutside() {
        return true;
    }

    //获取弹窗显示动画,子类实现
    protected int getDialogAnimationRes() {
        return 0;
    }

    //获取设备屏幕宽度
    public static final int getScreenWidth(Context context) {
        return context.getResources().getDisplayMetrics().widthPixels;
    }

    //获取设备屏幕高度
    public static final int getScreenHeight(Context context) {
        return context.getResources().getDisplayMetrics().heightPixels;
    }

    public static int getWindowWidth(Context context) {
        // 获取屏幕分辨率
//        WindowManager wm = (WindowManager) (context.getSystemService(Context.WINDOW_SERVICE));
//        DisplayMetrics dm = new DisplayMetrics();
//        wm.getDefaultDisplay().getMetrics(dm);
//        int mScreenWidth = dm.widthPixels;


        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);

        Point point = new Point();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            windowManager.getDefaultDisplay().getRealSize(point);
        } else {
            // 获取屏幕的宽度和高度
            int width = windowManager.getDefaultDisplay().getWidth();
            int height = windowManager.getDefaultDisplay().getHeight();
            // 设置Point对象的x和y坐标为屏幕的宽度和高度
            point.set(width, height);
        }
        //屏幕实际宽度（像素个数）
        int width = point.x;
        //屏幕实际高度（像素个数）
        int height = point.y;
        return width;
    }

    public static int getWindowHeigh(Context context) {
        // 获取屏幕分辨率
//        WindowManager wm = (WindowManager) (context.getSystemService(Context.WINDOW_SERVICE));
//        DisplayMetrics dm = new DisplayMetrics();
//        wm.getDefaultDisplay().getMetrics(dm);
//        int mScreenHeigh = dm.heightPixels;

        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        Point point = new Point();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            windowManager.getDefaultDisplay().getRealSize(point);
        } else {
            // 获取屏幕的宽度和高度
            int width = windowManager.getDefaultDisplay().getWidth();
            int height = windowManager.getDefaultDisplay().getHeight();
            // 设置Point对象的x和y坐标为屏幕的宽度和高度
            point.set(width, height);
        }
        //屏幕实际宽度（像素个数）
        int width = point.x;
        //屏幕实际高度（像素个数）
        int height = point.y;

        return height;
    }

    /**
     * 初始化沉浸式
     */
    protected void initImmersionBar() {
        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.KITKAT) {
            this.getDialog().getWindow().getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
        } else {
            if (Build.VERSION.SDK_INT != Build.VERSION_CODES.KITKAT) {
//                ImmersionBar.with(this).transparentStatusBar()
//                        .hideBar(getBoolean(SYST_NAVIGATION_BAR_SHOW, false) ? BarHide.FLAG_HIDE_STATUS_BAR : BarHide.FLAG_HIDE_BAR).init();
            }
        }
    }

    /**
     * 注销沉浸式
     */
    protected void destroyImmersionBar() {
        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.KITKAT) {
            int flags = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;
            getActivity().getWindow().getDecorView().setSystemUiVisibility(flags);
        } else {
            // Caused by: java.lang.IllegalArgumentException: 必须先在宿主Activity初始化
            // ImmersionBar.destroy(BaseDialogFragment.this);
//            if (dialog != null){
//                try {
//                    com.gyf.immersionbar.ImmersionBar.with(this).destroy(this);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
        }
    }

}
